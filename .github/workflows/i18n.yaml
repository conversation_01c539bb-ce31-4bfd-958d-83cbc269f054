name: i18n

on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master

concurrency:
  group: ror-i18n-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  check-locale-files:
    name: Check locale files
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.3.7' # Specify your Ruby version

      - name: Install `i18n-tasks` gem
        run: |
          gem install i18n-tasks

      - name: Check locale files & summarize
        id: check_locales
        run: |
          pwd
          ls -la
          missing_output=$(i18n-tasks missing || true)
          echo "$missing_output"
          echo "missing_output<<EOF" >> $GITHUB_ENV
          echo "$missing_output" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          if [ -n "$missing_output" ]; then
            echo -e "### 🛑 Missing Translation Keys\n\`\`\`\n$missing_output\n\`\`\`\n" >> $GITHUB_STEP_SUMMARY
            exit 1
          else
            echo -e "### ✅ No missing keys found!\n" >> $GITHUB_STEP_SUMMARY
          fi
