name: Deploy Preprod
on:
  push:
    branches:
      - preprod
concurrency:
  cancel-in-progress: true
  group: aws-ror-preprod-deploy-${{ github.ref_name }}
env:
  ENVIRONMENT: preprod
  ENVIRONMENT_NAME: Pre Production
  ECR_REPOSITORY: ror-api-preprod
jobs:
  build:
    name: Build
    if: always()
    runs-on: aws-arc-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - run: echo $SHA
        env:
          SHA: ${{ env.SHA }}

      - name: Slack Notification
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Deployment started for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image-prod
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker buildx create --use
          docker buildx build --push -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:latest --build-arg GITHUB_SHA=${{ github.sha }} --cache-to type=inline --cache-from type=registry,ref=$ECR_REGISTRY/$ECR_REPOSITORY:latest .
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy
    runs-on: aws-arc-runners
    needs:
      - build
    steps:
      - name: Setup tools
        run: |
          export RUNNER_ALLOW_RUNASROOT=1 
          sudo apt-get update
          sudo apt-get install -y curl

      - name: Update newrelic
        if: github.ref == 'refs/heads/master' &&  success()
        run: 'curl -X POST "https://api.newrelic.com/v2/applications/1068089053/deployments.json" -H "X-Api-Key:${{ secrets.NEWRELIC_API_KEY }}" -i -H "Content-Type:application/json" -d ''{"deployment":{"revision":"${{ github.sha }}","changelog":"${{ github.sha }}","description":"${{ github.sha }}","user":"${{ github.sha }}"}}'''

      - name: Update production tag
        run: 'curl -L -X POST -H "Accept:application/vnd.github+json" -H "Authorization:Bearer ${{ secrets.GH_TOKEN }}" -H "X-GitHub-Api-Version:2022-11-28" https://api.github.com/repos/praja/fleet-infra/actions/workflows/72028380/dispatches -d ''{"ref" : "master","inputs": { "imageTag": "${{ github.sha }}","cluster": "aws","env":"${{ env.ENVIRONMENT }}" }}'''

  notifications:
    name: Notifications
    runs-on: aws-arc-runners
    if: always()
    needs:
      - build
      - deploy
    steps:
      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - name: Slack workflow cancelled notification
        uses: slackapi/slack-github-action@v1.24.0
        if: cancelled()
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Deploy cancelled for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }

        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Slack build failed notification
        uses: slackapi/slack-github-action@v1.24.0
        if: failure()
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Build failed for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
