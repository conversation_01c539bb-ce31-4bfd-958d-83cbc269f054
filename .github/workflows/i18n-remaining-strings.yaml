name: i18n-remaining-strings

on:
  push:
    branches:
      - master

concurrency:
  group: ror-i18n-remaining-strings-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  check-untranslated-strings:
    name: Check untranslated strings
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
            ruby-version: '3.3.4' # Specify your Ruby version

      - name: Check for untranslated telugu strings
        run: |
          pwd
          ls -la
          untranslated_strings=$(ruby .github/check_untranslated_strings.rb)
          echo "$untranslated_strings" > untranslated_strings.txt
          echo "untranslated_strings<<EOF" >> $GITHUB_ENV
          echo "$untranslated_strings" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Upload untranslated strings
        uses: actions/upload-artifact@v4
        with:
          name: untranslated-strings
          path: untranslated_strings.txt
          retention-days: 7

      - name: Add job summary
        run: |
          count=$(wc -l < untranslated_strings.txt)
          if [ "$count" -gt 0 ]; then
            echo -e "### 🔢 Total untranslated strings: $count\n" >> $GITHUB_STEP_SUMMARY
          else
            echo -e "### ✅ All strings are translated!\n" >> $GITHUB_STEP_SUMMARY
          fi
