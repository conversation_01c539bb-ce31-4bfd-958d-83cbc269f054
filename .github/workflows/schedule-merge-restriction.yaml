name: Schedule Merge Restriction

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches:
      - master
  workflow_dispatch:
    # Allow manual triggering
  schedule:
    # Run every 5 minutes to update PR status
    # Note: This will only run on the default branch after merge
    - cron: '*/5 * * * *'

jobs:
  check-merge-time-restriction:
    name: Check Merge Time Restriction
    runs-on: ubuntu-latest
    # Run this job even when the workflow is triggered by a schedule
    if: github.event_name != 'schedule' || github.event.pull_request.state == 'open'

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        if: github.event_name != 'schedule'

      - name: Check if current time is within restricted window
        id: time-check
        run: |
          # Get current time in IST (UTC+5:30)
          CURRENT_TIME=$(TZ="Asia/Kolkata" date +%H%M)
          CURRENT_HOUR=$(TZ="Asia/Kolkata" date +%H)
          CURRENT_MINUTE=$(TZ="Asia/Kolkata" date +%M)

          # Convert to minutes since midnight for easier comparison
          CURRENT_MINUTES=$((CURRENT_HOUR * 60 + CURRENT_MINUTE))

          # Restricted window: 2:45PM to 3:45PM IST
          # 2:45PM = 14:45 = 14*60 + 45 = 885 minutes
          # 3:45PM = 15:45 = 15*60 + 45 = 945 minutes
          RESTRICTION_START=885
          RESTRICTION_END=945

          echo "Current time (IST): $(TZ="Asia/Kolkata" date +"%H:%M")"
          echo "Restricted window: 14:45 to 15:45 (IST)"
          echo "Current minutes since midnight: $CURRENT_MINUTES"
          echo "Restriction start: $RESTRICTION_START"
          echo "Restriction end: $RESTRICTION_END"

          if [ $CURRENT_MINUTES -ge $RESTRICTION_START ] && [ $CURRENT_MINUTES -le $RESTRICTION_END ]; then
            echo "::error::Merge is restricted between 2:45PM and 4:15PM IST"
            echo "is_restricted=true" >> $GITHUB_OUTPUT
            echo "MERGE_RESTRICTED=true" >> $GITHUB_ENV
          else
            echo "Current time is outside the restricted window"
            echo "is_restricted=false" >> $GITHUB_OUTPUT
            echo "MERGE_RESTRICTED=false" >> $GITHUB_ENV
          fi

      - name: Fail if merge is restricted
        if: env.MERGE_RESTRICTED == 'true'
        run: |
          echo "::error::Merge is restricted between 2:45PM and 4:15PM IST"
          exit 1
