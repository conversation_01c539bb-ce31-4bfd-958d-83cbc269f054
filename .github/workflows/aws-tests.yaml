name: AWS Tests

on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master

env:
  ENVIRONMENT: test
  ENVIRONMENT_NAME: Test
  ECR_REPOSITORY: ror-api-preprod
  AWS_ECR_REGISTRY: 666527360739.dkr.ecr.ap-south-1.amazonaws.com

concurrency:
  group: aws-ror-tests-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  build_and_test:
    name: Build and Test
    runs-on: aws-arc-runners

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: Create Docker network
        run: |
          docker network create test
      # Start services
      - name: Start MySQL
        run: |
          docker pull $AWS_ECR_REGISTRY/build-tools-mysql:latest
          docker run -d \
            --name=mysql \
            --network=test \
            -p 3306:3306 \
            -e MYSQL_ROOT_PASSWORD=toor \
            -e MYSQL_DATABASE=circle_api_test \
            -e MYSQL_USER=admin \
            -e MYSQL_PASSWORD=toor \
            -e MYSQL_ROOT_HOST='%' \
            $AWS_ECR_REGISTRY/build-tools-mysql:latest

          # Wait for MySQL to be ready
          echo "Waiting for MySQL..."
          while ! docker exec mysql mysqladmin ping -h localhost -u root -ptoor --silent; do
            sleep 2
          done
          echo "MySQL is ready"

      - name: Start Redis
        run: |
          docker run -d \
            --name=redis \
            --network=test \
            -p 6379:6379 \
            redis:7.0

          # Wait for Redis to be ready
          echo "Waiting for Redis..."
          while ! docker exec redis redis-cli ping; do
            sleep 1
          done
          echo "Redis is ready"
          
      - name: Build and tag image
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker buildx create --use
          docker buildx build --push -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:latest-test --build-arg GITHUB_SHA=${{ github.sha }} --cache-to type=inline --cache-from type=registry,ref=$ECR_REGISTRY/$ECR_REPOSITORY:latest-test .
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Run tests and Generate coverage report
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
          RAILS_ENV: test
          DEPLOYMENT: test
          DB_PREFIX: circle_api
          DB_USERNAME: admin
          DB_PASSWORD: toor
          BUNDLE_JOBS: 3
          BUNDLE_RETRY: 3
        run: |
          docker run \
            --network=test \
            --entrypoint="" \
            -e RAILS_ENV \
            -e DEPLOYMENT \
            -e DB_PREFIX \
            -e DB_HOST=mysql \
            -e DB_PORT=3306 \
            -e DB_USERNAME \
            -e DB_PASSWORD \
            -e REDIS_HOST=redis \
            -e BUNDLE_JOBS \
            -e BUNDLE_RETRY \
            -e RUBY_YJIT_ENABLE=1 \
            -e RAILS_LOG_TO_STDOUT=true \
            -e GOOGLE_CLOUD_CREDENTIALS=./config/credentials/development_google_cloud_credentials.json \
            -v ${PWD}:/var/www/app \
            -w /var/www/app \
            $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG \
            sh -c "bundle exec rake db:migrate db:seed && bundle exec rspec && mkdir coverage_results && cp coverage/.resultset.json coverage_results/.resultset.json && bundle exec rake coverage:report && cp coverage/coverage.md /var/www/app/"

      - name: Upload coverage results
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage
          retention-days: 7

      - name: Add job summary
        run: cat coverage.md >> $GITHUB_STEP_SUMMARY

      - name: Cleanup
        if: always()
        run: |
          docker stop mysql redis || true
          docker rm mysql redis || true
