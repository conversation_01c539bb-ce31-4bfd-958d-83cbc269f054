name: Deploy Azure prod

on:
  push:
    branches:
      - azure-master
concurrency:
  cancel-in-progress: true
  group: ror-deploy-${{ github.ref_name }}
env:
  ENVIRONMENT: production
  ENVIRONMENT_NAME: Azure Production
  CHART: ror-api
  REGISTRY: prajaror.azurecr.io
jobs:
  build:
    name: Build
    runs-on: azure-arc-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - run: echo $SHA
        env:
          SHA: ${{ env.SHA }}

      - name: Slack Notification
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Deployment started for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Login to Azure Container Registry
        run: docker login ${{ env.REGISTRY }} --username ${{ secrets.ACR_USERNAME }} --password ${{ secrets.ACR_PASSWORD }}

      - name: Build, tag, and push image to Azure Container Registry
        id: build-image-prod
        env:
          REGISTRY: ${{ env.REGISTRY }}
          IMAGE_TAG: ${{ github.sha }}
          REPOSITORY: ror-api
        run: |
          docker buildx create --use
          docker buildx build --push -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -t $REGISTRY/$REPOSITORY:latest --build-arg GITHUB_SHA=${{ github.sha }} --cache-to type=inline --cache-from type=registry,ref=$REGISTRY/$REPOSITORY:latest .
          echo "image=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy
    runs-on: azure-arc-runners
    needs:
      - build
    steps:
      - name: Setup tools
        run: |
          export RUNNER_ALLOW_RUNASROOT=1 
          sudo apt-get update
          sudo apt-get install -y curl

      - name: Update newrelic
        if: github.ref == 'refs/heads/master' &&  success()
        run: 'curl -X POST "https://api.newrelic.com/v2/applications/1068089053/deployments.json" -H "X-Api-Key:${{ secrets.NEWRELIC_API_KEY }}" -i -H "Content-Type:application/json" -d ''{"deployment":{"revision":"${{ github.sha }}","changelog":"${{ github.sha }}","description":"${{ github.sha }}","user":"${{ github.sha }}"}}'''

      - name: Update production tag
        run: 'curl -L -X POST -H "Accept:application/vnd.github+json" -H "Authorization:Bearer ${{ secrets.GH_TOKEN }}" -H "X-GitHub-Api-Version:2022-11-28" https://api.github.com/repos/praja/fleet-infra/actions/workflows/72028380/dispatches -d ''{"ref" : "master","inputs": { "imageTag": "${{ github.sha }}","cluster": "azure","env":"${{ env.ENVIRONMENT }}" }}'''

  notifications:
    name: Notifications
    runs-on: azure-arc-runners
    if: always()
    needs:
      - build
      - deploy
    steps:
      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - name: Slack workflow cancelled notification
        uses: slackapi/slack-github-action@v1.24.0
        if: cancelled()
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Deploy cancelled for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }

        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Slack build failed notification
        uses: slackapi/slack-github-action@v1.24.0
        if: failure()
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Build failed for repository praja*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

