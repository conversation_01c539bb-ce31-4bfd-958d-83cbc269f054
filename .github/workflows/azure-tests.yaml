name: Tests

on:
  pull_request:
    branches:
      - azure-master
  push:
    branches:
      - azure-master

env:
  REGISTRY: prajarorpreprod.azurecr.io
  REPOSITORY: ror-api-test

concurrency:
  group: ror-tests-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  build_containers:
    name: Build
    runs-on: azure-arc-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - run: echo $SHA
        env:
          SHA: ${{ env.SHA }}


      - name: Login to Azure Container Registry
        run: docker login ${{ env.REGISTRY }} --username ${{ secrets.ACR_USERNAME }} --password ${{ secrets.ACR_PASSWORD }}

      - name: Build, tag, and push image to Azure Container Registry
        id: build-image-prod
        env:
          REGISTRY: ${{ env.REGISTRY }}
          IMAGE_TAG: ${{ github.sha }}
          REPOSITORY: ${{ env.REPOSITORY }}
        run: |
          docker buildx create --use
          docker buildx build --push -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -t $REGISTRY/$REPOSITORY:latest --build-arg GITHUB_SHA=${{ github.sha }} --cache-to type=inline --cache-from type=registry,ref=$REGISTRY/$REPOSITORY:latest .
          echo "image=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - name: Img
        run: echo ${{ steps.build-image-prod.outputs.image }}
  test:
    name: Test
    defaults:
      run:
        working-directory: /var/www/app
    needs:
      - build_containers
    runs-on: azure-arc-runners
    services:
      mysql:
        image: prajaror.azurecr.io/mysql:latest
        credentials:
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}
        env:
          MYSQL_ROOT_PASSWORD: toor
          MYSQL_DATABASE: circle_api_test
          MYSQL_USER: circleci
          MYSQL_PASSWORD: passw0rd
          MYSQL_ROOT_HOST: '%'
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3 --name=mysql
      redis:
        image: redis:6.2.4
        credentials:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    container:
      image: prajarorpreprod.azurecr.io/ror-api-test:${{ github.sha }}
      credentials:
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}
      env:
        IMAGE: ${{ needs.build_containers.outputs.image }}
        RAILS_ENV: test
        DEPLOYMENT: test
        BUNDLE_JOBS: 3
        BUNDLE_RETRY: 3
        DB_PREFIX: circle_api
        DB_HOST: mysql
        DB_USERNAME: circleci
        DB_PASSWORD: passw0rd
        REDIS_HOST: redis
        GIT_SHA: circleci
        GOOGLE_CLOUD_CREDENTIALS: ./config/credentials/development_google_cloud_credentials.json
        RAILS_LOG_TO_STDOUT: true
        RUBY_YJIT_ENABLE: 1
        RUBY_CONFIGURE_OPTS: --enable-yjit
        LD_PRELOAD: /usr/lib/aarch64-linux-gnu/libjemalloc.so.2
    steps:
      - name: Printing System info for debugging
        run: lscpu

      - name: Run tests
        run: |
          bundle exec rake db:migrate db:seed
          bundle exec rspec
          mkdir coverage_results
          cp coverage/.resultset.json coverage_results/.resultset.json

      - name: Generate report
        run: |
          bundle exec rake coverage:report

      - name: Upload coverage results
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: /var/www/app/coverage
          retention-days: 7

      - name: Add job summary
        run: |
          cat coverage/coverage.md >> $GITHUB_STEP_SUMMARY

      - name: Check coverage
        run: |
          ruby .github/compare_coverage.rb
