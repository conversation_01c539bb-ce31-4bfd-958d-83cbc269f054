# compare_coverage.rb
require 'json'

def read_coverage(file_path)
  json_data = File.read(file_path)
  json_obj = JSON.parse(json_data)
  json_obj['result']['line']
end

# master_coverage = read_coverage('coverage-master.json')
# TODO: reduced from 80 for elections related code & autpay. Should revert back to 80 after elections
master_coverage = 75
pr_coverage = read_coverage('coverage/.last_run.json')

# puts "master_coverage - #{master_coverage}"
puts "pr_coverage - #{pr_coverage}"

if pr_coverage < master_coverage
  puts "Code coverage has decreased to #{pr_coverage}%. Should be greater than #{master_coverage}%."
  exit 1
else
  puts "Code coverage is OK."
end
