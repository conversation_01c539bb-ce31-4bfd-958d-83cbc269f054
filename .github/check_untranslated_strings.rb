#!/usr/bin/env ruby

require 'find'

# Directory to search
directory = 'app/'

# Patterns to match
telugu_pattern = /['"][^'"]*[\u0C00-\u0C7F]+[^'"]*['"]/
validation_pattern = /errors\.add\(:[^,]+, ['"][^'"]+['"]\)/

# Exclude patterns
exclude_patterns = [
  /Rails\.cache\.fetch/,
  /Digest::SHA256\.hexdigest/,
  /I18n\.t/,
  /config\./,
  /logger/,
  /puts/,
  /raise/,
  /require/,
  /gem/,
  /\bif\b/,
  /\belse\b/,
  /\belsif\b/,
  /\bwhen\b/,
  /\bcase\b/,
  /\bbegin\b/,
  /\bend\b/,
  /\bdef\b/,
  /\bclass\b/,
  /\bmodule\b/,
  /\bENV\b/,
  /\bVERSION\b/,
  /#/,
  /http/,
  /https/,
  /@/,
  /%/,
  /\{/,
  /\}/
]

# Function to check if a line matches the exclude patterns
def exclude_line?(line, exclude_patterns)
  exclude_patterns.any? { |pattern| line.match(pattern) }
end

# Iterate through all Ruby files in the specified directory
Find.find(directory) do |path|
  next unless path =~ /\.rb$/

  File.foreach(path).with_index do |line, line_num|
    if (line.match(telugu_pattern) || line.match(validation_pattern)) && !exclude_line?(line, exclude_patterns)
      puts "#{path}:#{line_num + 1}: #{line}"
    end
  end
end
