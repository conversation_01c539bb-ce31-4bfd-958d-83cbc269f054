class CreateUserProfessions < ActiveRecord::Migration[7.1]
  def up
    create_table :user_professions do |t|
      t.references :user, null: false, foreign_key: true
      t.references :profession, null: false, foreign_key: true
      t.references :sub_profession, null: true, foreign_key: true

      t.timestamps
    end

    add_index :user_professions, [:user_id, :profession_id], unique: true
  end

  def down
    drop_table :user_professions
  end
end
