class CreateUserVideoFrames < ActiveRecord::Migration[7.1]
  def change
    create_table :user_video_frames do |t|
      t.references :user, null: false, foreign_key: true
      t.references :video_frame, null: false, foreign_key: true
      t.text :identity_photo_url, null: false
      t.boolean :active, default: true
      t.timestamps
    end
    add_index :user_video_frames, [:user_id, :video_frame_id, :active], unique: true
  end
end
