class AddUnverifiedTypeandProofLink < ActiveRecord::Migration[7.1]
  def up
    add_column :user_roles, :proof_link, :string
    add_column :user_roles, :is_self_claimed, :boolean, default: false
    add_column :user_roles, :is_letter_pending, :boolean, default: false
  end

  def down
    remove_column :user_roles, :proof_link
    remove_column :user_roles, :is_self_claimed
    remove_column :user_roles, :is_letter_pending
  end
end
