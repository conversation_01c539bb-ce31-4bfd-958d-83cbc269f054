class UpdateUniqueIndexOnCirclePhotos < ActiveRecord::Migration[7.0]
  def up
    add_index :circle_photos, [:circle_id, :photo_order, :photo_type], unique: true
    add_index :circle_photos, [:circle_id, :photo_type]
    remove_index :circle_photos, [:circle_id, :photo_order]
  end

  def down
    remove_index :circle_photos, [:circle_id, :photo_order, :photo_type]
    remove_index :circle_photos, [:circle_id, :photo_type]
    add_index :circle_photos, [:circle_id, :photo_order], unique: true
  end
end
