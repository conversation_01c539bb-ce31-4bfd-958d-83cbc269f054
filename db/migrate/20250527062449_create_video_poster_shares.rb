class CreateVideoPosterShares < ActiveRecord::Migration[7.1]
  def change
    create_table :video_poster_shares do |t|
      t.references :video_creative, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :video_frame, null: false, foreign_key: true
      t.references :user_video_poster, null: false, foreign_key: true
      t.datetime :actioned_at, null: false
      t.string :method
      t.timestamps
    end
  end
end
