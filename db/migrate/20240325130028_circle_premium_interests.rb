class CirclePremiumInterests < ActiveRecord::Migration[7.0]
  def up
    create_table :circle_premium_interests do |t|
      t.references :user, null: false, foreign_key: true
      t.references :circle, null: false, foreign_key: true
      t.string :key, null: false

      t.timestamps
    end
    add_index :circle_premium_interests, [:user_id, :circle_id, :key], unique: true
  end

  def down
    drop_table :circle_premium_interests
  end
end
