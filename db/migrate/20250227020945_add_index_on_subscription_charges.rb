class AddIndexOnSubscriptionCharges < ActiveRecord::Migration[7.1]
  # Migration to add indexes on the subscription charges table.
  # The indexes are added on the pg_id and pg_reference_id columns to improve the performance of the queries.
  #
  # The pg_id column contains the unique identifier of the subscription charge in the payment gateway
  # and the pg_reference_id contains the reference id of the subscription charge in the payment gateway.
  #
  # The indexes will help to quickly look up the subscription charges based on the pg_id and pg_reference_id.
  def up
    # Add index on the pg_id column
    add_index :subscription_charges, :pg_id

    # Add index on the pg_reference_id column
    add_index :subscription_charges, :pg_reference_id
  end

  # This method is used to revert the changes made by the `up` method.
  # It removes the indexes on the `pg_reference_id` and `pg_id` columns
  # of the `subscription_charges` table.
  def down
    # Remove index on the `pg_reference_id` column
    remove_index :subscription_charges, :pg_reference_id

    # Remove index on the `pg_id` column
    remove_index :subscription_charges, :pg_id
  end
end
