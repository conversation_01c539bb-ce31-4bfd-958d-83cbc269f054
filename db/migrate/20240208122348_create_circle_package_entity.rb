class CreateCirclePackageEntity < ActiveRecord::Migration[7.0]
  def change
    create_table :circle_package_entities do |t|
      t.string :name, null: false
      t.boolean :enable_channel, default: false
      t.boolean :enable_fan_posters, default: false
      t.boolean :eligibile_for_post_push_notifications, default: false
      t.boolean :eligibile_for_wati, default: false
      t.integer :fan_poster_creatives_limit
      t.integer :channel_post_msg_limit
      t.integer :channel_post_msg_notification_limit
      t.timestamps
    end
  end
end
