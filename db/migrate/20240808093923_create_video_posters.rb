class CreateVideoPosters < ActiveRecord::Migration[7.1]
  def change
    create_table :video_posters do |t|
      t.references :video_frame, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :video, null: true, foreign_key: true
      t.references :source_video, null: false, foreign_key: { to_table: :videos }
      t.string :job_id, null: false
      t.integer :status, default: 0
      t.string :error_code
      t.timestamps
      t.index :job_id, unique: true
      t.index [:video_frame_id, :source_video_id], unique: true
    end
  end
end
