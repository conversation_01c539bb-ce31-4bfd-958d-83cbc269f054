class CreateCirclePackage < ActiveRecord::Migration[7.0]
  def change
    create_table :circle_packages do |t|
      t.references :circle, null: false, foreign_key: true, index: true
      t.references :package, null: false, foreign_key: {to_table: :circle_package_entities}, index: true
      t.date :start_date, null: false
      t.date :end_date, null: false
      t.boolean :active, default: true
      t.timestamps
    end

    add_index :circle_packages, [:circle_id, :start_date, :end_date, :active], name: 'index_circle_packages_on_circle_and_active_and_time_period'
    add_index :circle_packages, [:start_date, :end_date, :active], name: 'index_circle_packages_on_start_date_and_end_date_and_active'
  end
end
