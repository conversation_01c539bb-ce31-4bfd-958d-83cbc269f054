class RenameIndicesOnCirclePackages < ActiveRecord::Migration[7.0]
  def up
    rename_index :circle_packages, 'index_circle_packages_on_circle_and_active_and_time_period', 'index_on_circle_tenure_active'
    rename_index :circle_packages, 'index_circle_packages_on_start_date_and_end_date_and_active', 'index_on_tenure_active'
  end

  def down
    rename_index :circle_packages, 'index_on_circle_tenure_active', 'index_circle_packages_on_circle_and_active_and_time_period'
    rename_index :circle_packages, 'index_on_tenure_active', 'index_circle_packages_on_start_date_and_end_date_and_active'
  end
end
