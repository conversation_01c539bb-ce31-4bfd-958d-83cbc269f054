class CreateSubscriptionCharges < ActiveRecord::Migration[7.1]
  def up
    create_table :subscription_charges do |t|
      t.references :subscription, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.integer :amount, null: false
      t.datetime :charge_date, null: false
      t.string :status, null: false
      t.string :pg_id, null: false
      t.string :pg_reference_id, null: false
      t.json :pg_json
      t.timestamps
    end
  end

  def down
    drop_table :subscription_charges
  end
end
