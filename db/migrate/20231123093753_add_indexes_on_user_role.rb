class AddIndexesOnUserRole < ActiveRecord::Migration[7.0]
  def up
    add_index :user_roles, :parent_circle_id
    add_index :user_roles, :purview_circle_id
    remove_index :user_roles, :primary_circle_id
    remove_index :user_roles, :secondary_circle_id
    remove_index :roles, [:name, :primary_circle_type, :primary_circle_level], unique: true
  end

  def down
    remove_index :user_roles, :parent_circle_id
    remove_index :user_roles, :purview_circle_id
    add_index :user_roles, :primary_circle_id
    add_index :user_roles, :secondary_circle_id
    add_index :roles, [:name, :primary_circle_type, :primary_circle_level], name: "index_roles_on_name_and_primary_circle_type_and_level", unique: true
  end
end
