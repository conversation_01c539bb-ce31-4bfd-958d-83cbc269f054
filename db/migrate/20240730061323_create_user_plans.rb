class CreateUserPlans < ActiveRecord::Migration[7.1]
  def up
    create_table :user_plans do |t|
      t.references :user, null: false, foreign_key: true
      t.references :plan, null: false, foreign_key: true
      t.integer :amount, null: false
      t.datetime :end_date, null: false
      t.timestamps
    end
    add_index :user_plans, :user_id, unique: true, name: 'unique_index_for_user_plans_on_user_id'
  end

  def down
    drop_table :user_plans
  end
end
