class CreateTag < ActiveRecord::Migration[6.1]
  def change
    create_table :tags do |t|
      t.string :identifier, null: false, limit: 50
      t.text :description, limit: 200
      t.boolean :active, default: true
      t.string :tag_type, null: false
      t.index [:identifier, :tag_type, :active], unique: true, name: 'index_tags_on_identifier_and_tag_type_and_active'
      t.timestamps
    end
  end
end
