class AddSecondaryPhotoColsOnUsers < ActiveRecord::Migration[7.0]
  def up
    add_reference :users, :family_frame_photo, polymorphic: true, index: true
    add_column :users, :family_frame_name, :string, default: nil
    add_reference :users, :hero_frame_photo, polymorphic: true, index: true
  end

  def down
    remove_reference :users, :family_frame_photo, polymorphic: true, index: true
    remove_column :users, :family_frame_name
    remove_reference :users, :hero_frame_photo, polymorphic: true, index: true
  end
end
