class CreatePosterShares < ActiveRecord::Migration[7.0]
  def change
    create_table :poster_shares do |t|
      t.references :poster_creative, null: false, foreign_key: true
      t.references :frame, null: true, foreign_key: true, default: nil
      t.references :user, null: false, foreign_key: true
      t.datetime :actioned_at, null: false
      t.string :method

      t.timestamps
    end
  end
end
