class CreateExcludedUserCirclePermissions < ActiveRecord::Migration[7.0]
  def up
    create_table :excluded_user_circle_permissions do |t|
      t.references :user, type: :bigint, foreign_key: true
      t.references :circle, type: :bigint, foreign_key: true
      t.string :permission_identifier, null: false
      t.timestamps
    end
    add_index :excluded_user_circle_permissions, [:user_id, :circle_id, :permission_identifier], unique: true, name: 'index_excluded_user_circle_permissions_with_all_cols'
  end

  def down
    drop_table :excluded_user_circle_permissions
  end
end
