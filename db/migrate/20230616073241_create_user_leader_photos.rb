class CreateUserLeaderPhotos < ActiveRecord::Migration[6.1]
  def change
    create_table :user_leader_photos do |t|
      t.references :creator, polymorphic: true, null: false
      t.references :user_poster_layout, null: false, foreign_key: true
      t.references :photo, polymorphic: true, null: false
      t.references :user
      t.references :circle
      t.integer :header_type, limit: 1, null: false
      t.integer :priority, limit: 1
      t.timestamps
    end
  end
end
