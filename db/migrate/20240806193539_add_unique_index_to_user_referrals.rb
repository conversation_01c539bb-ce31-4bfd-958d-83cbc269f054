class AddUniqueIndexToUserReferrals < ActiveRecord::Migration[7.1]
  def up
    remove_index :user_referrals, [:user_id, :referred_user_id]
    add_index :user_referrals, :referred_user_id, name: "unique_index_on_referred_user_id", unique: true
  end

  def down
    remove_index :user_referrals, :referred_user_id, name: "unique_index_on_referred_user_id"
    add_index :user_referrals, [:user_id, :referred_user_id], unique: true
  end
end
