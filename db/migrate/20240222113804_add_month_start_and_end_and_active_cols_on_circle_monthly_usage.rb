class AddMonthStartAndEndAndActiveColsOnCircleMonthlyUsage < ActiveRecord::Migration[7.0]
  def up
    add_column :circle_monthly_usages, :month_start, :date
    add_column :circle_monthly_usages, :month_end, :date
    add_column :circle_monthly_usages, :active, :boolean, default: true
    add_index :circle_monthly_usages, [:circle_id, :month_start, :month_end, :active], name: 'index_on_circle_monthly_usages_on_circle_id_and_active_tenure'
  end

  def down
    remove_index :circle_monthly_usages, [:circle_id, :month_start, :month_end, :active]
    remove_column :circle_monthly_usages, :month_start
    remove_column :circle_monthly_usages, :month_end
    remove_column :circle_monthly_usages, :active
  end
end
