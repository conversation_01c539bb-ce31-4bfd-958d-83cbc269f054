class CreateUserPlanExtensions < ActiveRecord::Migration[7.1]
  def up
    create_table :user_plan_extensions do |t|
      t.references :user, null: false, foreign_key: true
      t.references :user_plan, null: false, foreign_key: true
      t.integer :duration_in_days, null: false
      t.string :reason, null: false
      t.timestamps
    end

    add_index :user_plan_extensions, [:user_id, :created_at]
  end
  def down
    drop_table :user_plan_extensions
  end
end
