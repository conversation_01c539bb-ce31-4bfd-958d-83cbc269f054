class AddUniqueIndexonEntity < ActiveRecord::Migration[7.1]
  def up
    remove_index :user_poster_layouts, name: "index_user_poster_layouts_on_entity"
    add_index :user_poster_layouts, [:entity_id, :entity_type], unique: true, name: "index_on_entity_type_entity_id"
  end

  def down
    remove_index :user_poster_layouts, name: "index_on_entity_type_entity_id"
    add_index :user_poster_layouts, [:entity_id, :entity_type], name: "index_user_poster_layouts_on_entity"
  end
end
