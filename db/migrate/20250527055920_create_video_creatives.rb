class CreateVideoCreatives < ActiveRecord::Migration[7.1]
  def change
    create_table :video_creatives do |t|
      t.string :kind, null: false
      t.boolean :active, default: true
      t.datetime :start_time, null: false
      t.datetime :end_time, null: false
      t.references :event, foreign_key: true
      t.references :creator, polymorphic: true, index: true, null: false
      t.references :designer, foreign_key: { to_table: :admin_users }
      t.references :video, null: false, foreign_key: true
      t.string :mode, null: false
      t.timestamps
    end
    add_index :video_creatives, [:start_time, :end_time, :active]
  end
end
