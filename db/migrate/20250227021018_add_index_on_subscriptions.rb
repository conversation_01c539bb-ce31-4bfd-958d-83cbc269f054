class AddIndexOnSubscriptions < ActiveRecord::Migration[7.1]
  # Adds an index to the subscriptions table on the pg_reference_id column
  #
  # This speeds up queries that search for subscriptions by their pg_reference_id
  def up
    # Add the index to the subscriptions table
    add_index :subscriptions, :pg_reference_id
  end

  # Reverses the changes made in the up method
  def down
    # Remove the index from the subscriptions table
    remove_index :subscriptions, :pg_reference_id
  end
end
