class CreateOrderItems < ActiveRecord::Migration[6.1]
  def change
    create_table :order_items do |t|
      t.references :order, null: false, foreign_key: true
      t.references :item, polymorphic: true, index: true, null: false
      t.references :item_price, null: false, foreign_key: true
      t.references :parent_order_item, foreign_key: { to_table: :order_items }
      t.integer :duration_in_months, null: false
      t.decimal :total_item_price, precision: 10, null: false
      t.timestamps
    end
  end
end
