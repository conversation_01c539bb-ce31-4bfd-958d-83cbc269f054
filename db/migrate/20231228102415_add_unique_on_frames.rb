class AddUniqueOnFrames < ActiveRecord::Migration[7.0]
  def up
    add_index :frames, %i[frame_type gold_border has_shadow_color is_neutral_frame has_footer_party_icon
                          identity_type active badge_strip user_position_back outer_frame font_id],
              unique: true,
              name: 'unique_index_on_frames'
  end

  def down
    remove_index :frames, name: 'unique_index_on_frames'
  end
end
