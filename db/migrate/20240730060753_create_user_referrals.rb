class CreateUserReferrals < ActiveRecord::Migration[7.1]
  def up
    create_table :user_referrals do |t|
      t.references :user, null: false, foreign_key: true
      t.references :referred_user, null: false, foreign_key: { to_table: :users }
      t.string :status, null: false, default: 'created'
      t.timestamps
    end
    add_index :user_referrals, [:user_id, :referred_user_id], unique: true
  end

  def down
    drop_table :user_referrals
  end
end
