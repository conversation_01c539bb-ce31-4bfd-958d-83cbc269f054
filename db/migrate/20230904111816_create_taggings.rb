class CreateTaggings < ActiveRecord::Migration[6.1]
  def change
    create_table :taggings do |t|
      t.references :taggable, polymorphic: true, null: false
      t.references :tag, null: false, foreign_key: true
      t.string :status
      t.index [:taggable_id, :taggable_type, :tag_id], unique: true, name: 'index_taggings_on_taggable_and_tag_id'
      t.index [:taggable_id, :taggable_type, :status], name: 'index_taggings_on_taggable_and_status'
      t.timestamps
    end
  end
end
