class RemoveOldIndexIfExistsFromPosterCreativeViews < ActiveRecord::Migration[7.1]
  def up
    if index_exists?(:poster_creative_views, [:user_id, :id], name: "index_poster_creative_views_on_user_id_and_id")
      remove_index :poster_creative_views, name: "index_poster_creative_views_on_user_id_and_id"
    end
  end

  def down
    unless index_exists?(:poster_creative_views, [:user_id, :id], name: "index_poster_creative_views_on_user_id_and_id")
      add_index :poster_creative_views, [:user_id, :id], name: "index_poster_creative_views_on_user_id_and_id"
    end
  end
end
