class RenamePremiumPitchTableName < ActiveRecord::Migration[7.0]
  def up
    rename_index :premium_pitch, 'index_premium_pitch_on_user_id', 'index_premium_pitches_on_user_id'
    rename_table :premium_pitch, :premium_pitches
  end

  def down
    rename_index :premium_pitches, 'index_premium_pitches_on_user_id', 'index_premium_pitch_on_user_id'
    rename_table :premium_pitches, :premium_pitch
  end
end
