class DeleteUserDeviceGroup < ActiveRecord::Migration[7.0]
  def up
    drop_table :user_device_group_tokens
    drop_table :user_device_groups
  end

  def down
    create_table :user_device_groups do |t|
      t.string :name
      t.references :user, type: :bigint, foreign_key: true
      t.boolean :active, default: true

      t.timestamps
    end
    create_table :user_device_group_tokens do |t|
      t.references :user_device_group, type: :bigint, foreign_key: true
      t.references :user_device_token, type: :bigint, foreign_key: true
      t.boolean :active, default: true

      t.timestamps
    end
  end
end
