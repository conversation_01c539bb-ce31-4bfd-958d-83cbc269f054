class CreateProfileViews < ActiveRecord::Migration[7.1]
  def change
    create_table :profile_views do |t|
      t.references :user, null: false, foreign_key: true
      t.references :viewer, null: false, foreign_key: { to_table: :users }
      t.datetime :viewed_at, null: false
      t.timestamps
    end
    add_index :profile_views, [:user_id, :viewer_id], unique: true
    add_index :profile_views, [:user_id, :viewed_at]
  end
end
