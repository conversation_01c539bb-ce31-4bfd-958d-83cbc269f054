class UpdateUserVideoPostersIndexesAndAddActive < ActiveRecord::Migration[7.1]
  def change
    add_column :user_video_posters, :active, :boolean, default: true

    remove_index :user_video_posters,
                 [:source_video_id, :user_video_frame_id],
                 name: "idx_on_source_video_id_user_video_frame_id_4bcaa93e95",
                 unique: true

    add_index :user_video_posters, [:source_video_id, :user_video_frame_id, :active]
  end
end
