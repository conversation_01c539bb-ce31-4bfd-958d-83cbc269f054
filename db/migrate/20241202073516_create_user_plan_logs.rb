class CreateUserPlanLogs < ActiveRecord::Migration[7.1]
  def change
    create_table :user_plan_logs do |t|
      t.references :user, null: false, foreign_key: true
      t.references :plan, null: false, foreign_key: true
      t.integer :amount, null: false
      t.datetime :start_date, null: false
      t.datetime :end_date, null: false
      t.references :entity, polymorphic: true, index: true, null: false

      t.timestamps
    end
  end
end
