class CreateUserFrames < ActiveRecord::Migration[7.1]
  def up
    create_table :user_frames do |t|
      t.references :user, null: false, foreign_key: true
      t.references :frame, null: false, foreign_key: true
      t.string :status, null: false
      t.timestamps
    end
    add_index :user_frames, [:user_id, :frame_id], unique: true
    add_index :user_frames, [:user_id, :status]
  end

  def down
    drop_table :user_frames
  end
end
