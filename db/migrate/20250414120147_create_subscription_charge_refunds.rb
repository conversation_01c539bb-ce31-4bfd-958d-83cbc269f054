class CreateSubscriptionChargeRefunds < ActiveRecord::Migration[7.1]
  def up
    create_table :subscription_charge_refunds do |t|
      t.references :subscription_charge, null: false, foreign_key: true, index: true
      t.references :user, null: false, foreign_key: true, index: true
      t.string :pg_id
      t.json :pg_json
      t.integer :amount, null: false
      t.string :status, null: false
      t.string :reason
      t.timestamps
    end

  end

  def down
    drop_table :subscription_charge_refunds
  end

end
