class CreateUserProductSubscriptionExtensions < ActiveRecord::Migration[7.1]
  def change
    create_table :user_product_subscription_extensions do |t|
      t.references :user, foreign_key: true, null: false
      t.references :product, foreign_key: true, null: false
      t.string :subscription_type, null: false
      t.references :order, foreign_key: true, null: true
      t.integer :duration_in_days, null: false
      t.timestamps
    end
  end
end
