class CreateSubscriptions < ActiveRecord::Migration[7.1]
  def up
    create_table :subscriptions do |t|
      t.references :user, null: false, foreign_key: true
      t.references :plan, null: false, foreign_key: true
      t.string :status, null: false
      t.integer :max_amount, null: false
      t.integer :initial_charge, default: 0
      t.string :pg_id, null: false
      t.string :pg_reference_id, null: false
      t.json :pg_json
      t.timestamps
    end
    add_index :subscriptions, [:user_id, :status]
    add_index :subscriptions, :pg_id
  end

  def down
    drop_table :subscriptions
  end
end
