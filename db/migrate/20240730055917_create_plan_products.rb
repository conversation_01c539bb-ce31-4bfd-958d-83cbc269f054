class CreatePlanProducts < ActiveRecord::Migration[7.1]
  def up
    create_table :plan_products do |t|
      t.references :plan, null: false, foreign_key: true
      t.references :product, null: false, foreign_key: true
      t.integer :quantity, null: false, default: 1, limit: 2
      t.timestamps
    end
    add_index :plan_products, [:plan_id, :product_id], unique: true
  end

  def down
    drop_table :plan_products
  end
end

