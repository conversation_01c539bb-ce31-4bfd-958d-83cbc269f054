class CreateUserVideoPosters < ActiveRecord::Migration[7.1]
  def change
    create_table :user_video_posters do |t|
      t.references :source_video, null: false, foreign_key: { to_table: :videos }
      t.references :user_video_frame, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :generated_video, foreign_key: { to_table: :videos }
      t.string :status, null: false
      t.string :job_id, null: false
      t.json :metadata

      t.timestamps
    end

    add_index :user_video_posters, [:source_video_id, :user_video_frame_id], unique: true
    add_index :user_video_posters, :job_id, unique: true
  end
end
