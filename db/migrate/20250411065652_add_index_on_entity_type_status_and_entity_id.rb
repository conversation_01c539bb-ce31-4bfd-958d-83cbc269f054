class AddIndexOnEntityTypeStatusAndEntityId < ActiveRecord::Migration[7.1]
  def up
    remove_index :user_poster_layouts, name: "index_on_entity_type_entity_id_and_status"
    add_index :user_poster_layouts, [:entity_type, :status, :entity_id], name: "index_on_entity_type_status_and_entity_id"
  end

  def down
    add_index :user_poster_layouts, [:entity_type, :entity_id, :status], name: "index_on_entity_type_entity_id_and_status"
    remove_index :user_poster_layouts, name: "index_on_entity_type_status_and_entity_id"
  end
end
