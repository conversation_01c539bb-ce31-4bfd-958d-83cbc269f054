class AddIndexesToOptimise < ActiveRecord::Migration[7.1]
  # Adds indexes to the `frame_views` and `videos` tables to improve performance.
  #
  # Adds a composite index on the `user_id` and `created_at` columns of the
  # `frame_views` table. This index is used to query the most recent frame views
  # for a user.
  #
  # Adds an index on the `hash_key` column of the `videos` table. This index is
  # used to query videos by their hash key.
  #
  # @return [void]
  def up
    # Add composite index on the `user_id` and `created_at` columns of the
    # `frame_views` table. This index is used to query the most recent frame views
    # for a user.
    add_index :frame_views, [:user_id, :created_at]

    # Add index on the `hash_key` column of the `videos` table. This index is
    # used to query videos by their hash key.
    add_index :videos, :hash_key
  end

  # Revert the changes made by the `up` method.
  #
  # Removes the added indexes on the `videos` table and the `frame_views` table.
  #
  # @return [void]
  def down
    # Remove index on the `hash_key` column of the `videos` table.
    remove_index :videos, :hash_key

    # Remove index on the `user_id` and `created_at` columns of the `frame_views` table.
    remove_index :frame_views, [:user_id, :created_at]
  end
end
