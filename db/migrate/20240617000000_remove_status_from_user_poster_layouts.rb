class RemoveStatusFromUserPosterLayouts < ActiveRecord::Migration[7.1]
  def up
    # First remove the index that includes the status column
    if index_exists?(:user_poster_layouts, [:entity_type, :status, :entity_id], name: "index_on_entity_type_status_and_entity_id")
      remove_index :user_poster_layouts, name: "index_on_entity_type_status_and_entity_id"
    end

    # Then remove the status column
    if column_exists?(:user_poster_layouts, :status)
      remove_column :user_poster_layouts, :status
    end
  end

  def down
    # Add the status column back
    unless column_exists?(:user_poster_layouts, :status)
      add_column :user_poster_layouts, :status, :string, default: 'active', null: false
    end

    # Add the index back
    unless index_exists?(:user_poster_layouts, [:entity_type, :status, :entity_id], name: "index_on_entity_type_status_and_entity_id")
      add_index :user_poster_layouts, [:entity_type, :status, :entity_id], name: "index_on_entity_type_status_and_entity_id"
    end
  end
end
