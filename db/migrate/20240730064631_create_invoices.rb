class CreateInvoices < ActiveRecord::Migration[7.1]
  def up
    create_table :invoices do |t|
      t.references :user, null: false, foreign_key: true
      t.string :transaction_reference_id, null: false
      t.string :type, null: false
      t.integer :total_amount, null: false
      t.integer :credits, default: 0
      t.integer :amount, null: false
      t.string :description
      t.timestamps
    end
  end

  def down
    drop_table :invoices
  end
end
