class ChangeBackgroundTypeDefaultsToTransparent < ActiveRecord::Migration[7.1]
  def up
    change_column_default :user_poster_layouts, :h1_background_type, from: 'creative_based', to: 'transparent'
    change_column_default :user_poster_layouts, :h2_background_type, from: 'creative_based', to: 'transparent'
  end

  def down
    change_column_default :user_poster_layouts, :h1_background_type, from: 'transparent', to: 'creative_based'
    change_column_default :user_poster_layouts, :h2_background_type, from: 'transparent', to: 'creative_based'
  end
end
