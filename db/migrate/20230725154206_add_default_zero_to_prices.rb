class AddDefaultZeroToPrices < ActiveRecord::Migration[6.1]
  def change
    change_column_default :item_prices, :price, from: nil, to: 0.0
    change_column_default :item_prices, :maintenance_price, from: nil, to: 0.0
    change_column_default :order_items, :total_item_price, from: nil, to: 0.0
    change_column_default :orders, :total_amount, from: nil, to: 0.0
    change_column_default :orders, :payable_amount, from: nil, to: 0.0
    change_column_default :orders, :discount_amount, from: nil, to: 0.0
  end
end
