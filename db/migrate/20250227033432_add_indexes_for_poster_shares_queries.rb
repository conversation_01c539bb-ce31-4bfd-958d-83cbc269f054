class AddIndexesForPosterSharesQueries < ActiveRecord::Migration[7.1]
  # Adds indexes to the `poster_shares` table to improve the performance of the
  # queries in the `PosterShares` model.
  #
  # The first index is on the columns `user_id`, `frame_id`, and `created_at`. This
  # index is used to query the most recent frame shares for a user.
  #
  # The second index is on the columns `user_id` and `created_at`. This index is
  # used to query the most recent frame shares for a user without the frame ID.
  #
  # The third index is on the `frame_type` column of the `frames` table. This index
  # is used to query the frames of a specific type.
  #
  # @return [void]
  def up
    add_index :poster_shares, [:user_id, :frame_id, :created_at]
    add_index :poster_shares, [:user_id, :created_at]
    add_index :frames, :frame_type
  end

  # Reverts the changes made by the `up` method.
  #
  # Removes the added indexes from the `poster_shares` table and the `frames`
  # table.
  #
  # @return [void]
  def down
    # Remove the index from the `frames` table
    remove_index :frames, :frame_type

    # Remove the indexes from the `poster_shares` table
    remove_index :poster_shares, [:user_id, :created_at]
    remove_index :poster_shares, [:user_id, :frame_id, :created_at]
  end
end
