class AddPaymentIdAndAttemptNumberToSubscriptionCharges < ActiveRecord::Migration[7.1]
  def up
    add_column :subscription_charges, :payment_id, :string, default: nil
    add_column :subscription_charges, :attempt_number, :integer, default: nil
    add_index :subscription_charges, [:payment_id, :attempt_number], unique: true
  end

  def down
    remove_index :subscription_charges, [:payment_id, :attempt_number]
    remove_column :subscription_charges, :attempt_number
    remove_column :subscription_charges, :payment_id
  end
end
