class CreateGarudaNotifications < ActiveRecord::Migration[7.0]
  def change
    create_table :garuda_notifications do |t|
      t.string :notification_id, default: nil, unique: true
      t.string :title
      t.string :body
      t.string :path
      t.string :status, default: 'created'
      t.integer :sent_count
      t.datetime :send_at
      t.string :sidekiq_job_id, default: nil

      t.timestamps
    end
  end
end
