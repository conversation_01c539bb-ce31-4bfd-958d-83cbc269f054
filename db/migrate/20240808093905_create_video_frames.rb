class CreateVideoFrames < ActiveRecord::Migration[7.1]
  def change
    create_table :video_frames do |t|
      t.integer :frame_type, null: false
      t.integer :supported_video_type, null: false
      t.boolean :active, default: true
      t.json :data, null: false
      t.references :user, null: false, foreign_key: true
      t.timestamps
      t.index [:user_id, :active]
      t.index [:user_id, :frame_type, :supported_video_type, :active]
    end
  end
end
