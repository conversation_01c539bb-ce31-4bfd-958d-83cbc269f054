class CreatePosterCreatives < ActiveRecord::Migration[6.1]
  def change
    create_table :poster_creatives do |t|
      t.references :event, foreign_key: true
      t.integer :creative_kind, limit: 1, null: false
      t.references :creator, polymorphic: true, index: true, null: false
      t.datetime :start_time
      t.datetime :end_time
      t.boolean :paid, default: false
      t.references :photo_v1, polymorphic: true
      t.references :photo_v2, polymorphic: true, null: false
      t.integer :h1_leader_photo_ring_type, limit: 1, null: false
      t.integer :h2_leader_photo_ring_type, limit: 1, null: false
      t.boolean :primary, default: false
      t.boolean :active, default: true
      t.timestamps
    end
  end
end
