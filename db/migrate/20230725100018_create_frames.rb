class CreateFrames < ActiveRecord::Migration[6.1]
  def change
    create_table :frames do |t|
      t.string :identifier, null: false, index: { unique: true }
      t.string :frame_type, null: false
      t.boolean :gold_border, default: false
      t.boolean :has_shadow_color, default: false
      t.boolean :is_neutral_frame, default: false
      t.boolean :has_footer_party_icon, default: false
      t.string :identity_type
      t.boolean :active, default: true
      t.timestamps
    end
  end
end
