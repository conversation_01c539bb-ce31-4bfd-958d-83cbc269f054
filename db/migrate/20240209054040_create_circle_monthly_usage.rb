class CreateCircleMonthlyUsage < ActiveRecord::Migration[7.0]
  def change
    create_table :circle_monthly_usages do |t|
      t.references :circle, null: false, foreign_key: true, index: true
      t.integer :month, null: false, comment: "YYYYMM"
      t.integer :fan_posters_limit
      t.integer :fan_posters_usage, default: 0
      t.integer :channel_message_limit
      t.integer :channel_message_usage, default: 0
      t.integer :channel_notification_limit
      t.integer :channel_notification_usage, default: 0
      t.timestamps
    end

    add_index :circle_monthly_usages, [:circle_id, :month], unique: true, name: 'index_circle_monthly_usages_on_circle_id_and_month'
  end
end
