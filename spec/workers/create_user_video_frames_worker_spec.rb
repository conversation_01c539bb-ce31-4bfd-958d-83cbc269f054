# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateUserVideoFramesWorker, type: :worker do
  let(:user) { FactoryBot.create(:user) }
  let(:font) { Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") || FactoryBot.create(:font, name_font: "Noto Sans Telugu #{SecureRandom.hex(4)}", badge_font: "Noto Sans Telugu #{SecureRandom.hex(4)}") }
  let(:video_frame_portrait) { FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font, active: true) }
  let(:video_frame_landscape) { FactoryBot.create(:video_frame, video_type: 'LANDSCAPE', font: font, active: true) }
  let(:video_frame_square) { FactoryBot.create(:video_frame, video_type: 'SQUARE', font: font, active: true) }

  subject { described_class.new }

  describe '#perform' do
    context 'when user_id is blank' do
      it 'returns early without processing' do
        expect(Rails.logger).not_to receive(:info)
        subject.perform(nil)
      end
    end

    context 'when user does not exist' do
      it 'returns early without processing' do
        expect(Rails.logger).not_to receive(:info)
        subject.perform(999999)
      end
    end

    context 'when no active video frames exist' do
      before do
        VideoFrame.update_all(active: false)
      end

      it 'returns early without processing' do
        expect(Rails.logger).to receive(:info).with("Creating user video frames for user #{user.id}")
        subject.perform(user.id)
      end
    end

    context 'with valid user and active video frames' do
      before do
        video_frame_portrait
        video_frame_landscape
        video_frame_square
      end

      it 'creates user video frames for all active video frame types' do
        active_video_frames_count = VideoFrame.where(active: true).count

        expect {
          subject.perform(user.id)
        }.to change(UserVideoFrame, :count).by(active_video_frames_count)

        # Check that frames were created for the specific video types we created
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'PORTRAIT' }).count).to be >= 1
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'LANDSCAPE' }).count).to be >= 1
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'SQUARE' }).count).to be >= 1
      end

      it 'creates frames with placeholder identity photo URLs' do
        subject.perform(user.id)

        UserVideoFrame.where(user: user).each do |frame|
          expect(frame.identity_photo_url).to eq('placeholder')
        end
      end

      it 'queues identity photo generation worker' do
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)

        subject.perform(user.id)
      end

      it 'logs successful creation' do
        active_video_frames_count = VideoFrame.where(active: true).count

        expect(Rails.logger).to receive(:info).with("Creating user video frames for user #{user.id}")
        expect(Rails.logger).to receive(:info).with(/Created UserVideoFrame .* for user #{user.id}, video_frame/).exactly(active_video_frames_count).times
        expect(Rails.logger).to receive(:info).with("Successfully created #{active_video_frames_count} video frames for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Queued identity photo generation for user #{user.id}")

        subject.perform(user.id)
      end

      context 'when user video frames already exist' do
        let!(:existing_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame_portrait, active: true) }

        it 'does not create duplicate frames' do
          active_video_frames_count = VideoFrame.where(active: true).count

          expect(Rails.logger).to receive(:info).with("UserVideoFrame already exists for user #{user.id}, video_frame #{existing_frame.video_frame_id}")

          expect {
            subject.perform(user.id)
          }.to change(UserVideoFrame, :count).by(active_video_frames_count - 1) # One less because one already exists
        end

        it 'does not queue identity photo generation if no new frames created' do
          # Create frames for all active video frames
          VideoFrame.where(active: true).each do |vf|
            next if vf.id == existing_frame.video_frame_id # Skip the one that already exists
            FactoryBot.create(:user_video_frame, user: user, video_frame: vf, active: true)
          end

          expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)

          subject.perform(user.id)
        end
      end

      context 'when frame creation fails for one frame' do
        before do
          # Mock to fail on first call, then succeed on subsequent calls
          call_count = 0
          allow(UserVideoFrame).to receive(:create!) do |*args|
            call_count += 1
            if call_count == 1
              raise StandardError.new('Database error')
            else
              UserVideoFrame.new(*args).tap(&:save!)
            end
          end
        end

        it 'logs error and continues with other frames' do
          active_video_frames_count = VideoFrame.where(active: true).count

          expect(Honeybadger).to receive(:notify).once
          expect(Rails.logger).to receive(:error).with(/Failed to create UserVideoFrame for user #{user.id}/).once

          # Should still create the other frames (one less due to the error)
          expect {
            subject.perform(user.id)
          }.to change(UserVideoFrame, :count).by(active_video_frames_count - 1)
        end
      end
    end
  end

  describe 'sidekiq configuration' do
    it 'uses video_posters_generation queue' do
      expect(described_class.sidekiq_options['queue']).to eq(:video_posters_generation)
    end

    it 'has 3 retry attempts' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'has lock until and while executing for deduplication' do
      expect(described_class.sidekiq_options['lock']).to eq(:until_and_while_executing)
    end

    it 'logs conflicts when duplicate jobs are attempted' do
      expect(described_class.sidekiq_options['on_conflict']).to eq(:log)
    end
  end

  describe 'retries exhausted callback' do
    it 'notifies Honeybadger when retries are exhausted' do
      msg = { 'args' => [user.id] }
      ex = StandardError.new('Test error')

      expect(Honeybadger).to receive(:notify).with(ex, context: { args: [user.id] })
      expect(Rails.logger).to receive(:error).with(
        'CreateUserVideoFramesWorker retries exhausted: Test error'
      )

      described_class.sidekiq_retries_exhausted_block.call(msg, ex)
    end
  end
end
