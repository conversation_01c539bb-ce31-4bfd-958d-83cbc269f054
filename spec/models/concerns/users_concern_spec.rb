require 'rails_helper'
require 'spec_helper'

RSpec.describe UsersConcern do
  describe '#usage_counts_feed_item' do
    let!(:user) { create(:user) }

    context 'when user has shared and downloaded posters' do
      before do
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        frame = FactoryBot.create(:frame)
        create_list(:poster_share, 3, actioned_at: Time.zone.now, user: user, poster_creative: @poster_creative,
                    frame: frame)
      end

      it 'returns correct share and download count' do
        result = user.usage_counts_feed_item(user: user)
        expect(result[:items].first[:count]).to eq(3)
        expect(result[:items].first[:text]).to eq(I18n.t('usage_counts_feed_item.share_and_download_count_text'))
      end
    end

    context 'when user has not shared or downloaded any posters' do
      it 'returns zero share and download count' do
        result = user.usage_counts_feed_item(user: user)
        expect(result[:items].first[:count]).to eq(0)
        expect(result[:items].first[:text]).to eq(I18n.t('usage_counts_feed_item.share_and_download_count_text'))
      end
    end
  end

  describe '#eligible_for_user_plan_extension_in_cancellation_flow?' do
    let!(:user) { create(:user) }
    let(:plan) { create(:plan) }
    let(:user_plan) { create(:user_plan, user: user, plan: plan) }

    before do
      allow(user_plan).to receive(:plan).and_return(plan)
    end

    subject { user.eligible_for_user_plan_extension_in_cancellation_flow? }

    context 'when user has no active subscription' do
      before do
        allow(user).to receive(:active_subscription).and_return(nil)
      end

      it 'returns ineligible with message' do
        expect(subject).to eq([false, I18n.t('plan_extension.not_eligible_for_extension')])
      end
    end

    context 'when user has an active subscription' do
      let(:subscription) { create(:subscription, user: user, plan: plan) }

      before do
        allow(user).to receive(:active_subscription).and_return(subscription)
      end

      context 'when active subscription has no user plan' do
        before do
          allow(subscription).to receive(:user_plan).and_return(nil)
        end

        it 'returns ineligible with message' do
          expect(subject).to eq([false, I18n.t('plan_extension.not_eligible_for_extension')])
        end
      end

      context 'when active subscription has a user plan' do
        before do
          allow(subscription).to receive(:user_plan).and_return(user_plan)
          allow(subscription).to receive(:plan).and_return(plan)
        end

        context 'when user is an annual premium user with successful payment' do
          before do
            allow(plan).to receive(:annual?).and_return(true)
            allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
              double(where: double(present?: true))
            )
          end

          it 'returns ineligible with message' do
            expect(subject).to eq([false, I18n.t('plan_extension.not_eligible_for_extension')])
          end
        end

        context 'when user has already used extension with cancellation_flow source in the last year' do
          before do
            allow(plan).to receive(:annual?).and_return(false)
            allow(subscription.subscription_charges).to receive(:success).and_return([])
            allow(user).to receive(:used_extension_with_cancellation_flow_source?).and_return(true)
          end

          it 'returns ineligible with message' do
            expect(subject).to eq([false, I18n.t('plan_extension.already_used_extension')])
          end
        end

        context 'when user is in subscription extension' do
          before do
            allow(plan).to receive(:annual?).and_return(false)
            allow(subscription.subscription_charges).to receive(:success).and_return([])
            allow(user).to receive(:used_extension_with_cancellation_flow_source?).and_return(false)
            allow(user).to receive(:in_subscription_extension?).and_return(true)
          end

          it 'returns ineligible with message' do
            expect(subject).to eq([false, I18n.t('plan_extension.already_in_extension')])
          end
        end

        context 'when user is eligible for extension' do
          before do
            allow(plan).to receive(:annual?).and_return(false)
            allow(subscription.subscription_charges).to receive(:success).and_return([])
            allow(user).to receive(:used_extension_with_cancellation_flow_source?).and_return(false)
            allow(user).to receive(:in_subscription_extension?).and_return(false)
          end

          it 'returns eligible with no message' do
            expect(subject).to eq([true, nil])
          end
        end

        context 'when user has annual plan but no successful payments' do
          before do
            allow(plan).to receive(:annual?).and_return(true)
            allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
              double(where: double(present?: false))
            )
            allow(user).to receive(:used_extension_with_cancellation_flow_source?).and_return(false)
            allow(user).to receive(:in_subscription_extension?).and_return(false)
          end

          it 'returns eligible with no message' do
            expect(subject).to eq([true, nil])
          end
        end
      end
    end
  end

  describe '#used_extension_with_cancellation_flow_source?' do
    let!(:user) { create(:user) }
    let(:plan) { create(:plan) }
    let(:user_plan) { create(:user_plan, user: user, plan: plan) }

    context 'when user has used extension with cancellation_flow source in the last year' do
      it 'returns true' do
        # Create extension and log in the last year
        extension = create(:user_plan_extension, user: user, user_plan: user_plan, reason: 'cancellation_flow')
        create(:user_plan_log, user: user, entity: extension, entity_type: 'UserPlanExtension',
               start_date: 6.months.ago, end_date: 5.months.ago)

        expect(user.used_extension_with_cancellation_flow_source?).to be true
      end
    end

    context 'when user has used extension with a source other than cancellation_flow in the last year' do
      it 'returns false' do
        # Mock the query to simulate a different source
        query_chain = double('query_chain')
        allow(query_chain).to receive(:where).and_return(query_chain)
        allow(query_chain).to receive(:exists?).and_return(false)

        allow(UserPlanLog).to receive(:joins).with(any_args).and_return(query_chain)

        expect(user.used_extension_with_cancellation_flow_source?).to be false
      end
    end

    context 'when user has used extension with cancellation_flow source more than a year ago' do
      it 'returns false' do
        # Create extension with log more than a year ago
        extension = create(:user_plan_extension, user: user, user_plan: user_plan, reason: 'cancellation_flow')
        create(:user_plan_log, user: user, entity: extension, entity_type: 'UserPlanExtension',
               start_date: 13.months.ago, end_date: 12.months.ago)

        expect(user.used_extension_with_cancellation_flow_source?).to be false
      end
    end

    context 'when user has used extension with cancellation_flow source exactly one year ago' do
      it 'returns false' do
        # Create extension with log exactly one year ago
        extension = create(:user_plan_extension, user: user, user_plan: user_plan, reason: 'cancellation_flow')
        create(:user_plan_log, user: user, entity: extension, entity_type: 'UserPlanExtension',
               start_date: 1.year.ago - 1.day, end_date: 1.year.ago)

        expect(user.used_extension_with_cancellation_flow_source?).to be false
      end
    end

    context 'when user has used extension with cancellation_flow source just under one year ago' do
      it 'returns true' do
        # Create extension with log just under one year ago
        extension = create(:user_plan_extension, user: user, user_plan: user_plan, reason: 'cancellation_flow')
        create(:user_plan_log, user: user, entity: extension, entity_type: 'UserPlanExtension',
               start_date: 1.year.ago + 1.day, end_date: 1.year.ago + 2.days)

        expect(user.used_extension_with_cancellation_flow_source?).to be true
      end
    end

    context 'when user has never used any extension' do
      it 'returns false' do
        # No extensions created
        expect(user.used_extension_with_cancellation_flow_source?).to be false
      end
    end

    context 'when user has multiple extensions in the last year' do
      it 'returns true for cancellation_flow source' do
        # Mock for cancellation_flow source
        query_chain1 = double('query_chain1')
        allow(query_chain1).to receive(:where).and_return(query_chain1)
        allow(query_chain1).to receive(:exists?).and_return(true)

        # Mock for unknown_source source
        query_chain2 = double('query_chain2')
        allow(query_chain2).to receive(:where).and_return(query_chain2)
        allow(query_chain2).to receive(:exists?).and_return(false)

        allow(UserPlanLog).to receive(:joins).with(any_args).and_return(query_chain1, query_chain2)

        expect(user.used_extension_with_cancellation_flow_source?).to be true
      end
    end

    context 'when cancellation_flow source is used' do
      it 'returns true' do
        # Mock for cancellation_flow source
        query_chain = double('query_chain')
        allow(query_chain).to receive(:where).and_return(query_chain)
        allow(query_chain).to receive(:exists?).and_return(true)

        allow(UserPlanLog).to receive(:joins).with(any_args).and_return(query_chain)

        expect(user.used_extension_with_cancellation_flow_source?).to be true
      end
    end

  end

  describe '#in_subscription_extension?' do
    let!(:user) { create(:user) }
    let(:plan) { create(:plan) }
    let(:user_plan) { create(:user_plan, user: user, plan: plan) }
    let(:extension) { create(:user_plan_extension, user: user, user_plan: user_plan, duration_in_days: 15, reason: 'cancellation_flow') }

    before do
      allow(user).to receive(:get_active_user_plan).and_return(user_plan)
    end

    context 'when user has no active plan' do
      before do
        allow(user).to receive(:get_active_user_plan).and_return(nil)
      end

      it 'returns false' do
        expect(user.in_subscription_extension?).to be false
      end
    end

    context 'when user plan source does not indicate an extension' do
      before do
        allow(user_plan).to receive(:source).and_return('some-other-source')
      end

      it 'returns false' do
        expect(user.in_subscription_extension?).to be false
      end
    end

    context 'when user plan end date is in the past' do
      before do
        allow(user_plan).to receive(:source).and_return("user-plan-extension-#{extension.id}")
        allow(user_plan).to receive(:end_date).and_return(1.day.ago)
      end

      it 'returns false' do
        expect(user.in_subscription_extension?).to be false
      end
    end

    context 'when extension does not exist' do
      before do
        allow(user_plan).to receive(:source).and_return('user-plan-extension-999')
        allow(user_plan).to receive(:end_date).and_return(1.day.from_now)
        allow(UserPlanExtension).to receive(:find_by).and_return(nil)
      end

      it 'returns false' do
        expect(user.in_subscription_extension?).to be false
      end
    end

    context 'when there is no active user plan log for the extension' do
      before do
        allow(user_plan).to receive(:source).and_return("user-plan-extension-#{extension.id}")
        allow(user_plan).to receive(:end_date).and_return(1.day.from_now)
        allow(UserPlanExtension).to receive(:find_by).and_return(extension)
        allow(UserPlanLog).to receive(:where).and_return(UserPlanLog.none)
      end

      it 'returns false' do
        expect(user.in_subscription_extension?).to be false
      end
    end

    context 'when all conditions are met' do
      before do
        allow(user_plan).to receive(:source).and_return("user-plan-extension-#{extension.id}")
        allow(user_plan).to receive(:end_date).and_return(1.day.from_now)
        allow(UserPlanExtension).to receive(:find_by).and_return(extension)
        allow(extension).to receive(:reason).and_return('cancellation_flow')
        # Mock the UserPlanLog.where query to return the expected result
        # Use a more specific mock that matches the exact query parameters
        allow(UserPlanLog).to receive(:where)
                                .with(hash_including(user_id: user.id, entity_type: 'UserPlanExtension', active: true))
                                .and_return(double(exists?: true))
      end

      it 'returns true' do
        # Fix the mock to match the method implementation
        query_chain = double('query_chain')
        allow(UserPlanLog).to receive(:where).with(user_id: user.id).and_return(query_chain)
        allow(query_chain).to receive(:where).with(entity_type: 'UserPlanExtension').and_return(query_chain)
        allow(query_chain).to receive(:where).with(active: true).and_return(query_chain)
        allow(query_chain).to receive(:where).with('end_date > ?', an_instance_of(ActiveSupport::TimeWithZone)).and_return(query_chain)
        allow(query_chain).to receive(:exists?).and_return(true)

        expect(user.in_subscription_extension?).to be true
      end
    end
  end

  describe '#profile_views_feed_item' do
    let(:user) { create(:user) }

    context 'when user has profile views' do
      before do
        allow_any_instance_of(UsersConcern).to receive(:fetch_views_data).and_return({
                                                                                       viewed_user_ids: [1, 2, 3],
                                                                                       users: ['User1', 'User2', 'User3'],
                                                                                       locked: false
                                                                                     })
      end

      it 'returns profile views feed item with correct data' do
        result = user.profile_views_feed_item(user: user)
        expect(result[:feed_type]).to eq('profile_views')
        expect(result[:viewers]).to match_array(['User1', 'User2', 'User3'])
        expect(result[:locked]).to be false
      end
    end

    context 'when user has no profile views' do
      before do
        allow_any_instance_of(UsersConcern).to receive(:fetch_views_data).and_return({
                                                                                       viewed_user_ids: [],
                                                                                       users: [],
                                                                                       locked: false
                                                                                     })
      end

      it 'returns profile feed item for no profile views also' do
        result = user.profile_views_feed_item(user: user)
        expect(result[:feed_type]).to eq('profile_views')
        expect(result[:viewers]).to be_empty
        expect(result[:locked]).to be false
      end
    end
  end

  describe '#premium_members_feed_item' do
    let!(:user) { create(:user) }

    context 'when user is subscribed to premium' do
      before :each do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:premium_users_list_for_premium_experience).and_return(['User1', 'User2'])
      end

      it 'returns unlocked premium members feed item with users' do
        result = user.premium_members_feed_item(user: user)
        expect(result[:locked]).to be false
        expect(result[:members]).to match_array(['User1', 'User2'])
      end
    end

    context 'when user is not subscribed to premium' do
      before :each do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
      end

      it 'returns locked premium members feed item without users' do
        result = user.premium_members_feed_item(user: user)
        expect(result[:locked]).to be true
        expect(result[:members]).to be_empty
      end
    end

    context 'when premium users list is empty' do
      before :each do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:premium_users_list_for_premium_experience).and_return([])
      end

      it 'returns unlocked premium members feed item with empty list' do
        result = user.premium_members_feed_item(user: user)
        expect(result[:locked]).to be false
        expect(result[:members]).to be_empty
      end
    end
  end

  describe '#upcoming_events_feed_item' do
    let(:user) { create(:user) }

    context 'when user is subscribed to premium' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:is_trial_user?).and_return(false)
        allow(PosterCreative).to receive(:upcoming_event_creatives).and_return(['Event1', 'Event2'])
      end

      it 'returns unlocked upcoming events with creatives' do
        result = user.upcoming_events_feed_item(user: user)
        expect(result[:locked]).to be false
        expect(result[:creatives]).to match_array(['Event1', 'Event2'])
      end
    end

    context 'when user is in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(true)
        allow(PosterCreative).to receive(:upcoming_event_creatives).and_return(['Event1'])
      end

      it 'returns unlocked upcoming events with creatives for trial user' do
        result = user.upcoming_events_feed_item(user: user)
        expect(result[:locked]).to be false
        expect(result[:creatives]).to match_array(['Event1'])
      end
    end

    context 'when user is neither subscribed nor in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(false)
        allow(PosterCreative).to receive(:upcoming_event_creatives).and_return(['Event1'])
      end

      it 'returns locked upcoming events without creatives' do
        result = user.upcoming_events_feed_item(user: user)
        expect(result[:locked]).to be true
        expect(result[:creatives]).to match_array(['Event1'])
      end
    end

    context 'when there are no upcoming events' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(PosterCreative).to receive(:upcoming_event_creatives).and_return([])
      end

      it 'returns nil for no upcoming events' do
        result = user.upcoming_events_feed_item(user: user)
        expect(result).to be_nil
      end
    end
  end

  describe '#my_poster_styles_feed_item' do
    let(:user) { create(:user) }

    context 'when user is subscribed to premium' do
      before :each do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:is_trial_user?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(1)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
        allow(user).to receive(:get_user_layouts).and_return([{ layout_type: 'premium' }])
        allow(AppVersionSupport).to receive(:transparent_background_type_supported?).and_return(true)
      end

      it 'returns unlocked poster styles with premium layouts' do
        result = user.my_poster_styles_feed_item(user:)
        expect(result[:locked]).to be false
        expect(result[:layouts].first[:layout_type]).to eq('premium')
      end
    end

    context 'when user is in trial' do
      before do
        allow(AppVersionSupport).to receive(:transparent_background_type_supported?).and_return(true)
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(true)
        allow(user).to receive(:affiliated_party_circle_id).and_return(1)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
        allow(user).to receive(:get_user_layouts).and_return([{ layout_type: 'trial' }])
      end

      it 'returns unlocked poster styles with trial layouts' do
        result = user.my_poster_styles_feed_item(user:)
        expect(result[:locked]).to be false
        expect(result[:layouts].first[:layout_type]).to eq('trial')
      end
    end

    context 'when user is neither subscribed nor in trial' do
      before do
        AppVersionSupport.new('2407.10.01')
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(false)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns locked poster styles without layouts' do
        result = user.my_poster_styles_feed_item(user:)
        expect(result[:locked]).to be true
        expect(result[:layouts]).to be_empty
      end
    end

    context 'when basic type layouts are present' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:affiliated_party_circle_id).and_return(1)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
        allow(user).to receive(:get_user_layouts).and_return([{ layout_type: 'basic' }, { layout_type: 'premium' }])
      end

      it 'excludes basic type layouts from the result' do
        result = user.my_poster_styles_feed_item(user:)
        expect(result[:layouts].count).to eq(1)
        expect(result[:layouts].first[:layout_type]).to eq('premium')
      end
    end
  end

  describe '#premium_experience_media_carousel' do
    let(:user) { create(:user) }

    context 'when user is subscribed to premium' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:is_trial_user?).and_return(false)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns unlocked media carousel for premium user' do
        result = user.premium_experience_media_carousel(user: user)
        expect(result[:title]).to eq(I18n.t('premium_experience_media_carousel.title'))
        expect(result[:locked]).to be false
        expect(result[:media]).not_to be_empty
        # media json verify
        expect(result[:media].first).to include(type: "poster")
      end
    end

    context 'when user is in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(true)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns unlocked media carousel for trial user' do
        result = user.premium_experience_media_carousel(user: user)
        expect(result[:title]).to eq(I18n.t('premium_experience_media_carousel.title'))
        expect(result[:locked]).to be false
        expect(result[:media]).not_to be_empty
        # media json verify
        expect(result[:media].first).to include(type: "poster")
      end
    end

    context 'when user is neither subscribed nor in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_trial_user?).and_return(false)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns locked media carousel' do
        result = user.premium_experience_media_carousel(user: user)
        expect(result[:title]).to eq(I18n.t('premium_experience_media_carousel.title'))
        expect(result[:locked]).to be true
        expect(result[:media]).not_to be_empty
        # media json verify
        expect(result[:media].first).to include(type: "poster")
      end
    end

    context 'when ignore_title is true' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns media carousel with empty title' do
        result = user.premium_experience_media_carousel(user: user, ignore_title: true)
        expect(result[:title]).to eq('')
        expect(result[:media]).not_to be_empty
        # media json verify
        expect(result[:media].first).to include(type: "poster")
      end
    end

    context 'when media content is available' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:demo_layouts_for_premium_experience).and_return([{ layout: 'layout1' }])
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns media carousel with media content' do
        result = user.premium_experience_media_carousel(user: user)
        expect(result[:media]).not_to be_empty
        expect(result[:media].first).to include(type: "poster")
      end
    end

    context 'when no media content is available' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:demo_layouts_for_premium_experience).and_return([])
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:find_by).and_return(@poster_creative)
      end

      it 'returns nil for media carousel' do
        result = user.premium_experience_media_carousel(user: user)
        expect(result).to be_nil
      end
    end
  end

  describe '#relation_manager_feed_item' do
    let(:user) { create(:user) }
    let(:rm_user) { create(:admin_user, name: 'John Doe', phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i) }
    # @admin_user = FactoryBot.create(:admin_user)
    # data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
    # admin_media = FactoryBot.create(:admin_medium, data: data, admin_user_id: @admin_user.id)
    # let(:rm_user) { create :admin_user, name: 'John Doe', phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i,
    #                        admin_medium_id: admin_media.id }

    context 'when relation manager is present with all required fields' do
      before do
        allow(user).to receive(:get_rm_user).and_return(rm_user)
      end

      it 'returns relation manager feed item with correct data' do
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: rm_user.id)
        rm_user.update(admin_medium_id: admin_media.id)
        # allow(user).to receive(:get_user_poster_layout).and_return(rm_user)

        result = user.relation_manager_feed_item(user:)

        expect(result[:title]).to eq(I18n.t('rm_feed_item.title'))
        expect(result[:feed_item_id]).to eq('relation_manager')
        expect(result[:rm_user][:name]).to eq('John Doe')
        expect(result[:rm_user][:photo_url]).to_not be_empty
        expect(result[:rm_user][:phone]).to eq(rm_user.phone)
      end
    end

    context 'when relation manager is missing' do
      before :all do
        rm_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: rm_user.id)
      end
      before do
        allow(user).to receive(:get_rm_user).and_return(nil)
      end

      it 'returns default values' do
        result = user.relation_manager_feed_item(user: user)
        expect(result[:rm_user][:id]).to eq(0)
        expect(result[:rm_user][:phone]).to eq(Constants.praja_customer_care_phone_number)
        expect(result[:rm_user][:email]).to eq(Constants.praja_customer_care_email)
      end
    end

    context 'when relation manager is present but missing required fields' do
      let(:incomplete_rm_user) { create(:admin_user, name: '', phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i) }

      before do
        allow(user).to receive(:get_rm_user).and_return(incomplete_rm_user)
      end

      it 'returns nil due to missing required fields' do
        result = user.relation_manager_feed_item(user: user)
        expect(result).to be_nil
      end
    end

    context 'when relation manager is present but missing photo' do
      let(:incomplete_rm_user) { create(:admin_user, name: 'John Doe', phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i) }

      before :each do
        allow(user).to receive(:get_rm_user).and_return(incomplete_rm_user)
      end

      it 'returns nil due to missing photo' do
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: incomplete_rm_user.id)
        result = user.relation_manager_feed_item(user: user)
        expect(result).to_not be_nil
        expect(result[:rm_user][:photo_url]).to_not be_empty
        expect(result[:rm_user][:photo_url]).to eq(AdminMedium.last.placeholder_url)
      end
    end
  end

  describe '#get_paywall_deeplink' do
    let(:user) { create(:user) }
    it "returns annual paywall deeplink when should_pitch_yearly_package? is true" do
      allow(user).to receive(:should_pitch_yearly_package?).and_return(true)
      allow(AppVersionSupport).to receive(:is_annual_recharge_paywall_supported?).and_return(true)
      result = user.get_paywall_deeplink
      expect(result).to eq(Constants.annual_paywall_deeplink)
    end

    it "returns paywall deeplink when should_pitch_yearly_package? is false" do
      allow(user).to receive(:should_pitch_yearly_package?).and_return(false)
      allow(AppVersionSupport).to receive(:is_annual_recharge_paywall_supported?).and_return(true)
      expect(user.get_paywall_deeplink).to eq(Constants.paywall_deeplink)
    end
  end

  describe '#premium_user_ids_of_user_contacts' do
    let(:user) { create(:user) }

    context 'when user has premium contacts' do
      it 'returns premium contact user ids sorted by grade level' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)
        create(:user_role, user: premium_contact_user, grade_level: 2)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(premium_contact_user.id)
      end
    end

    context 'when user has no premium contacts' do
      it 'returns an empty array' do
        result = user.premium_user_ids_of_user_contacts

        expect(result).to be_empty
      end
    end

    context 'when premium contacts have multiple grade levels' do
      it 'sorts premium contact user ids by ascending grade level' do
        lower_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        higher_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: lower_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_plan, user: higher_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: lower_grade_user.id, phone: lower_grade_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: higher_grade_user.id, phone: higher_grade_user.phone)
        create(:user_role, user: lower_grade_user, grade_level: 1)
        create(:user_role, user: higher_grade_user, grade_level: 3)

        result = user.premium_user_ids_of_user_contacts

        expect(result.first).to eq(lower_grade_user.id)
        expect(result.second).to eq(higher_grade_user.id)
      end
    end

    context 'when premium contacts do not have grade levels' do
      it 'treats them as having the highest grade level' do
        no_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: no_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: no_grade_user.id, phone: no_grade_user.phone)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(no_grade_user.id)
      end
    end

    context 'when user has both premium and non-premium contacts' do
      it 'returns only premium contact user ids' do
        random_first_digit = rand(6..9)
        premium_user = create(:user, phone: "#{random_first_digit}#{Faker::Number.unique.number(digits: 9)}")
        non_premium_user = create(:user, phone: "#{random_first_digit}#{Faker::Number.unique.number(digits: 9)}")
        create(:user_plan, user: premium_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_user.id, phone: premium_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: non_premium_user.id, phone: non_premium_user.phone)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(premium_user.id)
        expect(result).not_to include(non_premium_user.id)
      end
    end
  end

  describe '#premium_users_list_for_premium_experience' do
    let(:state_circle) { create(:circle, name: 'state_circle', level: :state, circle_type: :location) }
    let(:district_circle) { create(:circle, name: 'district_circle', level: :district, circle_type: :location,
                                   parent_circle: state_circle) }
    let(:mandal_circle) { create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location,
                                 parent_circle: district_circle) }
    let(:village_circle) { create(:circle, name: 'village_circle', level: :village, circle_type: :location,
                                  parent_circle: mandal_circle) }
    let(:user) { create(:user, village_id: village_circle.id, mandal_id: mandal_circle.id,
                        district_id: district_circle.id, state_id: state_circle.id) }

    context 'when premium contacts exceed the limit' do
      it 'limits the result to the first 20 premium contacts' do
        created_users = create_list(:user, 22, :with_premium_subscription)
        created_user_ids = created_users.map(&:id)
        allow(user).to receive(:premium_user_ids_of_user_contacts).and_return(created_user_ids)

        result = user.premium_users_list_for_premium_experience

        expect(result.size).to eq(20)
      end
    end

    context 'when user has premium contacts and district premium users' do
      it 'sort based on user role grade level' do
        created_users = create_list(:user, 5, :with_premium_subscription, :with_user_role)
        created_user_ids = created_users.map(&:id)
        # sort created_users based on user role grade level
        sorted_created_user_ids = created_users.sort_by { |user| user.get_badge_role.get_grade_level }.map(&:id)
        allow(user).to receive(:premium_user_ids_of_user_contacts).and_return(sorted_created_user_ids)
        district_users = create_list(:user, 5, :with_premium_subscription, :with_user_role, village_id: village_circle.id,
                                     mandal_id: mandal_circle.id, district_id: district_circle.id,
                                     state_id: state_circle.id)

        result = user.premium_users_list_for_premium_experience

        expect(result.size).to eq(10)
        expect(result.uniq.size).to eq(result.size)
        # sort order of ids
        result_user_ids = result.pluck(:id)

        sorted_district_user_ids = district_users.sort_by { |user| user.get_badge_role.get_grade_level }.map(&:id)
        expect(result_user_ids).to eq(sorted_created_user_ids + sorted_district_user_ids)
      end
    end

    context 'when user has enough premium contacts' do
      it 'returns premium contacts without needing district premium users' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)

        result = user.premium_users_list_for_premium_experience

        expect(result.first[:id]).to eq(premium_contact_user.id)
      end
    end

    context 'when user does not have enough premium contacts' do
      it 'fills the list with district premium users' do
        district_premium_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: district_premium_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: district_premium_user.id, phone: district_premium_user.phone)

        result = user.premium_users_list_for_premium_experience
        expect(result.first[:id]).to eq(district_premium_user.id)
      end
    end

    context 'when there are no premium users at all' do
      it 'returns an empty list' do
        result = user.premium_users_list_for_premium_experience

        expect(result).to be_empty
      end
    end

    context 'when combining premium contacts and district premium users' do
      it 'ensures no duplicates in the final list' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        district_premium_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month)
        create(:user_plan, user: district_premium_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: district_premium_user.id, phone: district_premium_user.phone)

        result = user.premium_users_list_for_premium_experience

        expect(result.map { |user| user[:id] }).to include(premium_contact_user.id, district_premium_user.id)
        expect(result.uniq.size).to eq(result.size)
      end
    end

    context 'when user has premium contacts and district premium users' do
      it 'sorts based on user role grade level' do
        lower_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        higher_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: lower_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_plan, user: higher_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: lower_grade_user.id, phone: lower_grade_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: higher_grade_user.id, phone: higher_grade_user.phone)
        create(:user_role, user: lower_grade_user, grade_level: 1)
        create(:user_role, user: higher_grade_user, grade_level: 3)

        result = user.premium_users_list_for_premium_experience
        expect(result.first[:id]).to eq(lower_grade_user.id)
        expect(result.second[:id]).to eq(higher_grade_user.id)
      end
    end
  end

  describe '#waiting_list_number' do
    let(:user) { create(:user) }

    context 'when user is on the waiting list' do
      it 'returns the correct waiting list number' do
        create_list(:premium_pitch, 3, status: :wait_list)
        create(:premium_pitch, user: user, status: :wait_list)
        create_list(:premium_pitch, 2, status: :wait_list)
        expect(user.waiting_list_number).to eq(4)
      end
    end

    context 'when user is not on the waiting list' do
      it 'returns default one' do
        create_list(:premium_pitch, 3, status: :wait_list)
        expect(user.waiting_list_number).to eq(1)
      end
    end

    context 'when waiting list is empty' do
      it 'returns default one for any user' do
        expect(user.waiting_list_number).to eq(1)
      end
    end

    context 'when user is first on the waiting list' do
      it 'returns one for the first user' do
        create(:premium_pitch, status: :wait_list)
        create(:premium_pitch, user: user, status: :wait_list)
        expect(user.waiting_list_number).to eq(2)
      end
    end
  end

  describe '#subscription_info_toast' do
    let(:user) { create(:user) }

    context 'when user is subscribed to poster' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(SubscriptionUtils).to receive(:active_poster_premium_end_date).and_return(Time.zone.now + 40.days)
      end

      it 'returns info type for more than 30 days remaining' do
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('info')
        expect(result[:text]).to eq((Time.zone.now + 40.days).strftime('%d/%m/%Y'))
      end

      it 'returns alert type for last day of subscription' do
        allow(SubscriptionUtils).to receive(:active_poster_premium_end_date).and_return(Time.zone.now.end_of_day)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.set_auto_recharge'))
      end

      it 'returns alert type with days remaining for subscription less than 30 days' do
        allow(SubscriptionUtils).to receive(:active_poster_premium_end_date).and_return(Time.zone.now + 5.days)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.set_auto_recharge'))
      end

      it 'returns alert type indicating last subscription charge has failed' do
        allow(SubscriptionUtils).to receive(:active_poster_premium_end_date).and_return(Time.zone.now + 5.days)
        subscription = create(:subscription, user: user)
        allow(user).to receive(:active_subscription).and_return(subscription)
        allow(subscription).to receive(:last_recharge_failed?).and_return(true)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.recharge_stopped'))
      end

      it 'return quiet type indicating subscription is active' do
        allow(SubscriptionUtils).to receive(:active_poster_premium_end_date).and_return(Time.zone.now + 5.days)
        subscription = create(:subscription, user: user)
        allow(user).to receive(:active_subscription).and_return(subscription)
        allow(SubscriptionCharge).to receive(:last_recharge_failed?).and_return(false)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('quiet')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.next_recharge', date: (Time.zone.now + 5.days).strftime('%d/%m/%Y')))
      end
    end

    context 'when user has ever subscribed' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(SubscriptionUtils).to receive(:has_user_ever_subscribed?).and_return(true)
      end

      it 'returns alert type indicating subscription has ended' do
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.set_auto_recharge'))
      end

      it 'returns alert type indicating last subscription charge has failed' do
        subscription = create(:subscription, user: user)
        allow(user).to receive(:active_subscription).and_return(subscription)
        allow(subscription).to receive(:last_recharge_failed?).and_return(true)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.recharge_stopped'))
      end

      it 'returns alert type indicating premium is expired' do
        subscription = create(:subscription, user: user)
        allow(user).to receive(:active_subscription).and_return(subscription)
        allow(SubscriptionCharge).to receive(:last_recharge_failed?).and_return(false)
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('alert')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.premium_subscription_expired'))
      end
    end

    context 'when user is in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(true)
        allow(Metadatum).to receive(:get_user_trail_duration).and_return(15)
      end

      it 'returns info type for trial period' do
        FactoryBot.create(:user_poster_layout, entity: user)
        result = user.subscription_info_toast(user:)
        expect(result[:type]).to eq('info')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.trial_duration', duration: 15))
      end
    end

    # wait list logic is temporarily not there because of self trial
    # context 'when user is in waitlist' do
    #   before do
    #     allow(user).to receive(:is_poster_subscribed).and_return(false)
    #     allow(user).to receive(:is_in_waitlist?).and_return(true)
    #   end
    #
    #   it 'returns quiet type for waitlist request' do
    #     result = user.subscription_info_toast(user: user)
    #     expect(result[:type]).to eq('quiet')
    #     expect(result[:text]).to eq(I18n.t('subscription_info_toast.waitlist_request'))
    #   end
    # end

    context 'when user has not subscribed or not in trial' do
      before do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(false)
        allow(user).to receive(:is_in_waitlist?).and_return(false)
      end

      it 'returns info type encouraging to join waitlist' do
        result = user.subscription_info_toast(user: user)
        expect(result[:type]).to eq('info')
        expect(result[:text]).to eq(I18n.t('subscription_info_toast.join_waitlist'))
      end
    end
  end

  describe '#faqs_feed_item' do
    let(:user) { create(:user) }

    context 'when user is subscribed and has an active subscription' do
      it 'includes cancel button in the last FAQ' do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        subscription = create(:subscription, user: user)
        allow(user).to receive(:cancellable_latest_subscription).and_return(subscription)
        allow(subscription).to receive(:may_cancel?).and_return(true)
        allow(user).to receive(:get_cancellation_deeplink).and_return('/cancel-membership?source=faqs')

        result = user.faqs_feed_item(user: user)

        expect(result[:faqs].last[:button]).not_to be_nil
        expect(result[:faqs].last[:button][:deeplink]).to eq('/cancel-membership?source=faqs')
        expect(result[:faqs].last[:button][:text]).to eq(I18n.t('faqs_feed_item.button_text_10'))
      end
    end

    context 'when user is not subscribed' do
      it 'does not include cancel button in the last FAQ' do
        allow(user).to receive(:is_poster_subscribed).and_return(false)
        allow(user).to receive(:cancellable_latest_subscription).and_return(nil)

        result = user.faqs_feed_item(user: user)

        expect(result[:faqs].last[:button]).to be_nil
      end
    end

    context 'when user is subscribed but subscription is not cancellable' do
      it 'does not include cancel button in the last FAQ' do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        subscription = create(:subscription, user: user)
        allow(user).to receive(:cancellable_latest_subscription).and_return(subscription)
        allow(subscription).to receive(:may_cancel?).and_return(false)

        result = user.faqs_feed_item(user: user)

        expect(result[:faqs].last[:button]).to be_nil
      end
    end

    context 'when user is subscribed but has no cancellable subscription' do
      it 'does not include cancel button in the last FAQ' do
        allow(user).to receive(:is_poster_subscribed).and_return(true)
        allow(user).to receive(:cancellable_latest_subscription).and_return(nil)

        result = user.faqs_feed_item(user: user)

        expect(result[:faqs].last[:button]).to be_nil
      end
    end

    it 'returns correct number of FAQs' do
      result = user.faqs_feed_item(user: user)
      expect(result[:faqs].size).to eq(10)
    end

    it 'returns correct feed type and title' do
      result = user.faqs_feed_item(user: user)
      expect(result[:feed_type]).to eq('faqs')
      expect(result[:feed_item_id]).to eq('faqs')
      expect(result[:title]).to eq(I18n.t('faqs_feed_item.title'))
    end
  end

  describe '#get_cancellation_deeplink' do
    let(:user) { create(:user) }
    let(:subscription) { create(:subscription, user: user) }
    let(:source) { 'test_source' }

    context 'when cancellation_flow_v2 is not supported' do
      before do
        allow(AppVersionSupport).to receive(:cancellation_flow_v2_supported?).and_return(false)
      end

      it 'returns the basic cancellation deeplink with source' do
        result = user.get_cancellation_deeplink(subscription: subscription, source: source)
        expect(result).to eq("/cancel-membership?source=#{source}")
      end
    end

    context 'when cancellation_flow_v2 is supported' do
      context 'when user does not have a premium layout' do
        before do
          allow(AppVersionSupport).to receive(:cancellation_flow_v2_supported?).and_return(true)
          allow(user).to receive(:has_premium_layout?).and_return(false)
        end

        it 'returns the basic cancellation deeplink with source' do
          result = user.get_cancellation_deeplink(subscription: subscription, source: source)
          expect(result).to eq("/cancel-membership?source=#{source}")
        end
      end

      context 'when user has a premium layout' do
        before do
          allow(AppVersionSupport).to receive(:cancellation_flow_v2_supported?).and_return(true)
          allow(user).to receive(:has_premium_layout?).and_return(true)
        end

        context 'when subscription plan is annual' do
          before do
            allow(subscription.plan).to receive(:annual?).and_return(true)
          end

          context 'when user has made successful payments' do
            before do
              allow(subscription.subscription_charges).to receive(:success).and_return([create(:subscription_charge)])
              # Mock the where method to return a double that has present? return false
              allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
                double(where: double(present?: false))
              )
            end

            it 'returns the downgrade sheet deeplink with source' do
              result = user.get_cancellation_deeplink(subscription: subscription, source: source)
              expect(result).to eq("/cancel-flow-downgrade-sheet?source=#{source}")
            end
          end

          context 'when user has made successful payments with amount > 1' do
            before do
              allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
                double(where: double(present?: true))
              )
            end

            it 'returns the basic cancellation deeplink with source' do
              result = user.get_cancellation_deeplink(subscription: subscription, source: source)
              expect(result).to eq("/cancel-membership?source=#{source}")
            end
          end

          context 'when user has made successful payments with amount <= 1' do
            before do
              allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
                double(where: double(present?: false))
              )
            end

            it 'returns the downgrade sheet deeplink with source' do
              result = user.get_cancellation_deeplink(subscription: subscription, source: source)
              expect(result).to eq("/cancel-flow-downgrade-sheet?source=#{source}")
            end
          end

          context 'when user has made successful payments with amount = 1' do
            let(:subscription_charge) { create(:subscription_charge, charge_amount: 1, amount: 1, status: :success) }

            before do
              allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
                double(where: double(present?: false))
              )
              allow(subscription.subscription_charges).to receive(:where).with(status: :success).and_return([subscription_charge])
            end

            it 'returns the downgrade sheet deeplink with source' do
              result = user.get_cancellation_deeplink(subscription: subscription, source: source)
              expect(result).to eq("/cancel-flow-downgrade-sheet?source=#{source}")
            end
          end

          context 'when user has not made any successful payments' do
            before do
              allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
                double(where: double(present?: false))
              )
              allow(subscription.subscription_charges).to receive(:where).with(status: :success).and_return([])
            end

            it 'returns the downgrade sheet deeplink with source' do
              result = user.get_cancellation_deeplink(subscription: subscription, source: source)
              expect(result).to eq("/cancel-flow-downgrade-sheet?source=#{source}")
            end
          end
        end

        context 'when subscription plan is not annual' do
          before do
            allow(subscription.plan).to receive(:annual?).and_return(false)
          end

          it 'returns the premium benefits loss screen deeplink with source' do
            result = user.get_cancellation_deeplink(subscription: subscription, source: source)
            expect(result).to eq("/premium-benefits-loss-screen?source=#{source}")
          end
        end
      end
    end
  end

  describe '#annual_premium_user_with_successful_payment?' do
    let(:user) { create(:user) }
    let(:subscription) { create(:subscription, user: user) }

    context 'when user has no active subscription' do
      before do
        allow(user).to receive(:active_subscription).and_return(nil)
      end

      it 'returns false' do
        expect(user.annual_premium_user_with_successful_payment?).to be false
      end
    end

    context 'when user has an active subscription' do
      before do
        allow(user).to receive(:active_subscription).and_return(subscription)
      end

      context 'when subscription plan is not annual' do
        before do
          allow(subscription.plan).to receive(:annual?).and_return(false)
        end

        it 'returns false' do
          expect(user.annual_premium_user_with_successful_payment?).to be false
        end
      end

      context 'when subscription plan is annual' do
        before do
          allow(subscription.plan).to receive(:annual?).and_return(true)
        end

        context 'when user has made successful payments with amount > 1' do
          before do
            allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
              double(where: double(present?: true))
            )
          end

          it 'returns true' do
            expect(user.annual_premium_user_with_successful_payment?).to be true
          end
        end

        context 'when user has made successful payments with amount <= 1' do
          before do
            allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
              double(where: double(present?: false))
            )
          end

          it 'returns false' do
            expect(user.annual_premium_user_with_successful_payment?).to be false
          end
        end

        context 'when user has made successful payments with amount = 1' do
          let(:subscription_charge) { create(:subscription_charge, charge_amount: 1, amount: 1, status: :success) }

          before do
            allow(subscription.subscription_charges).to receive(:where).with("charge_amount > 1").and_return(
              double(where: double(present?: false))
            )
            allow(subscription.subscription_charges).to receive(:where).with(status: :success).and_return([subscription_charge])
          end

          it 'returns false' do
            expect(user.annual_premium_user_with_successful_payment?).to be false
          end
        end
      end
    end
  end

  describe '#eligible_for_downgrade_in_cancellation_flow?' do
    let(:user) { create(:user) }
    let(:monthly_plan) { create(:plan, duration_in_months: 1, amount: 299) }
    let(:annual_plan) { create(:plan, duration_in_months: 12, amount: 2999) }

    context 'when user has an active annual plan' do
      before do
        user_plan = create(:user_plan, user: user, plan: annual_plan, end_date: Time.now + 1.year)
        allow(user).to receive(:get_active_user_plan).and_return(user_plan)
        allow(user).to receive(:is_poster_subscribed).and_return(true)
      end

      it 'returns true' do
        expect(user.eligible_for_downgrade_in_cancellation_flow?).to be true
      end
    end

    context 'when user has an active monthly plan' do
      before do
        user_plan = create(:user_plan, user: user, plan: monthly_plan, end_date: Time.now + 1.month)
        allow(user).to receive(:get_active_user_plan).and_return(user_plan)
        allow(user).to receive(:is_poster_subscribed).and_return(true)
      end

      it 'returns false' do
        expect(user.eligible_for_downgrade_in_cancellation_flow?).to be false
      end
    end

    context 'when user has no active plan' do
      before do
        allow(user).to receive(:get_active_user_plan).and_return(nil)
        allow(user).to receive(:is_poster_subscribed).and_return(false)
      end

      it 'returns false' do
        expect(user.eligible_for_downgrade_in_cancellation_flow?).to be false
      end
    end

    context 'when user has an annual plan but is not subscribed' do
      before do
        user_plan = create(:user_plan, user: user, plan: annual_plan, end_date: Time.now + 1.year)
        allow(user).to receive(:get_active_user_plan).and_return(user_plan)
        allow(user).to receive(:is_poster_subscribed).and_return(false)
      end

      it 'returns false' do
        expect(user.eligible_for_downgrade_in_cancellation_flow?).to be false
      end
    end
  end

  describe '#viewed_pc_event_ids' do
    let(:user) { create(:user) }
    let(:other_user) { create(:user) }

    before do
      allow($redis).to receive(:smembers).and_return(redis_entries)
    end

    context 'when there are no Redis or database entries' do
      let(:redis_entries) { [] }
      let(:poster_creative_views) { double(order: double(limit: double(pluck: []))) }

      it 'returns an empty array' do
        expect(user.viewed_pc_event_ids).to eq([])
      end
    end

    context 'when there are only Redis entries' do
      let(:redis_entries) do
        [
          { "user_id" => user.id, "poster_creative_id" => 1 }.to_json,
          { "user_id" => user.id, "poster_creative_id" => 2 }.to_json
        ]
      end

      it 'returns the poster creative ids from Redis' do
        expect(user.viewed_pc_event_ids).to match_array([1, 2])
      end
    end

    context 'when there are only database entries' do
      let(:redis_entries) { [] }

      it 'returns the poster creative ids from the database' do
        pcv1 = FactoryBot.create(:poster_creative_view, user: user)
        pcv2 = FactoryBot.create(:poster_creative_view, user: user)
        expect(user.viewed_pc_event_ids).to match_array([pcv1.poster_creative_id, pcv2.poster_creative_id])
      end
    end

    context 'when there are both Redis and database entries' do
      let(:redis_entries) do
        [
          { "user_id" => user.id, "poster_creative_id" => 1 }.to_json,
          { "user_id" => user.id, "poster_creative_id" => 2 }.to_json
        ]
      end

      it 'returns the combined unique poster creative ids from Redis and the database' do
        pcv1 = FactoryBot.create(:poster_creative_view, user: user)
        pcv2 = FactoryBot.create(:poster_creative_view, user: user)
        expect(user.viewed_pc_event_ids).to match_array([1, 2, pcv1.poster_creative_id, pcv2.poster_creative_id])
      end
    end

    context 'when Redis entries contain multiple users' do
      let(:redis_entries) do
        [
          { "user_id" => user.id, "poster_creative_id" => 1 }.to_json,
          { "user_id" => other_user.id, "poster_creative_id" => 2 }.to_json
        ]
      end
      let(:poster_creative_views) { double(order: double(limit: double(pluck: []))) }

      it 'returns only the poster creative ids for the current user' do
        expect(user.viewed_pc_event_ids).to match_array([1])
      end
    end
  end

  describe '#deeplink_url_in_poster_share' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.poster_share_deeplink_redis_key(user.id) }
    let(:current_date) { Time.zone.today.to_s }

    before :each do
      allow(user).to receive(:eligible_for_self_trial?).and_return(true)
      @event = FactoryBot.create(:event, priority: :high)
      image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
      @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                           photo_v2: @admin_medium_2, primary: true)
    end

    context 'when no deeplink has been sent' do
      it 'returns true and adds the current date to Redis' do
        allow(user).to receive(:eligible_for_self_trial?).and_return(true)
        expect($redis.smembers(redis_key)).to eq([])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to eq("/premium-experience?source=whatsapp")
        expect($redis.smembers(redis_key)).to eq([current_date])
      end
    end

    context 'when deeplink has been sent once on a different day' do
      it 'returns true and adds the current date to Redis' do
        allow(user).to receive(:eligible_for_self_trial?).and_return(true)
        $redis.sadd(redis_key, Time.zone.yesterday.to_s)
        expect($redis.smembers(redis_key)).to eq([Time.zone.yesterday.to_s])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to eq("/premium-experience?source=whatsapp")
        expect($redis.smembers(redis_key)).to match_array([current_date, Time.zone.yesterday.to_s])
      end
    end

    context 'when deeplink has been sent twice on different days' do
      it 'returns false and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Date.yesterday.to_s, Date.today.prev_day(2).to_s])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to be nil
      end
    end

    context 'when deeplink has been sent today' do
      it 'returns false and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([current_date])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to be nil
      end
    end
  end

  describe '#upgrade_deeplink_url_in_poster_share(format_type: image)' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.upgrade_deeplink_in_poster_share_redis_key(user.id) }
    let(:current_date) { Time.zone.today.to_s }

    before :each do
      allow(user).to receive(:eligible_for_self_trial?).and_return(false)
      allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(true)
      allow(user).to receive(:common_upgrade_package_conditions_met?).and_return(true)
      @event = FactoryBot.create(:event, priority: :high)
      image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
      @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                           photo_v2: @admin_medium_2, primary: true)
    end

    context 'when no deeplink has been sent' do
      it 'returns true and adds the current date to Redis' do
        expect($redis.smembers(redis_key)).to eq([])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to eq("/upgrade?source=poster-whatsapp")
        expect($redis.smembers(redis_key)).to eq([current_date])
      end
    end

    context 'when deeplink has been sent once on a different day' do
      it 'returns true and adds the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Time.zone.yesterday.to_s])

        expect(user.deeplink_url_in_poster_share('external-share', creative_id: @poster_creative.id)).to eq("/upgrade?source=poster-external-share")
      end
    end

    context 'when deeplink has been sent twice on a different day' do
      it 'returns true and adds the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Time.zone.yesterday.to_s, Date.today.prev_day(2).to_s])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to eq("/upgrade?source=poster-whatsapp")
      end
    end

    context 'when deeplink has been sent thrice on different days' do
      it 'returns false and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Date.yesterday.to_s,
                                                                        Date.today.prev_day(2).to_s,
                                                                        Date.today.prev_day(3).to_s])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to be nil
      end
    end

    context 'when deeplink has been sent today' do
      it 'returns false and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([current_date])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @poster_creative.id)).to be nil
      end
    end
  end

  describe '#upgrade_deeplink_url_in_poster_share(format_type: video)' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.video_poster_share_deeplink_redis_key(user.id) }
    let(:current_date) { Time.zone.today.to_s }
    let(:video) { FactoryBot.create(:video, user: user) }

    before :each do
      allow(user).to receive(:eligible_for_self_trial?).and_return(false)
      allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(true)
      allow(user).to receive(:common_upgrade_package_conditions_met?).and_return(true)
      allow(user).to receive(:is_test_user_for_floww?).and_return(true)

      @event = FactoryBot.create(:event, priority: :high)
      @video_creative = FactoryBot.create(:video_creative, event: @event, video: video)
    end

    context 'when no deeplink has been sent' do
      it 'returns true and adds the current date to Redis' do
        expect($redis.smembers(redis_key)).to eq([])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to eq("/premium-experience")
        expect($redis.smembers(redis_key)).to eq([current_date])
      end
    end

    context 'when deeplink has been sent once on a different day' do
      it 'returns true and adds the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Time.zone.yesterday.to_s])

        expect(user.deeplink_url_in_poster_share('external-share', creative_id: @video_creative.id, format_type: 'video')).to eq("/premium-experience")
      end
    end

    context 'when deeplink has been sent twice on different days' do
      it 'returns true and adds the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([Time.zone.yesterday.to_s, Date.today.prev_day(2).to_s])

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to eq("/premium-experience")
      end
    end

    context 'when deeplink has been sent thrice on different days' do
      it 'returns nil and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([
                                                                         Date.yesterday.to_s,
                                                                         Date.today.prev_day(2).to_s,
                                                                         Date.today.prev_day(3).to_s
                                                                       ])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to be_nil
      end
    end

    context 'when deeplink has already been sent today' do
      it 'returns nil and does not add the current date to Redis' do
        allow($redis).to receive(:smembers).with(redis_key).and_return([current_date])
        expect($redis).not_to receive(:sadd)
        expect($redis).not_to receive(:expire)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to be_nil
      end
    end

    context 'when user is not a test user for floww' do
      it 'returns nil and does not proceed' do
        allow(user).to receive(:is_test_user_for_floww?).and_return(false)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to be_nil
      end
    end

    context 'when event is not high priority' do
      it 'returns nil and does not proceed' do
        @event.update(priority: :low)

        expect(user.deeplink_url_in_poster_share('whatsapp', creative_id: @video_creative.id, format_type: 'video')).to be_nil
      end
    end
  end


  describe '#signed_up_today?' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.signed_up_today_key(user.id) }

    context 'when the user signed up today' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return('true')
      end

      it 'returns true' do
        expect(user.signed_up_today?).to be true
      end
    end

    context 'when the user did not sign up today' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return(nil)
      end

      it 'returns false' do
        expect(user.signed_up_today?).to be false
      end
    end
  end

  describe '#set_signed_up_today' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.signed_up_today_key(user.id) }

    context 'when setting signed up today' do
      it 'sets the key in Redis with a value of true' do
        user.set_signed_up_today
        expect($redis.get(redis_key)).to eq('true')
      end

      it 'sets the key to expire in 1 day' do
        user.set_signed_up_today
        expect($redis.ttl(redis_key)).to be_within(5).of(1.day.to_i)
      end
    end
  end

  describe '#self_trial_user?' do
    let(:user) { create(:user) }

    context 'when the user has a successful auth charge and layout created after the charge' do
      it 'returns true' do
        first_successful_recharge = create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success,
                                           created_at: 1.day.ago, success_at: 1.day.ago)
        layout = create(:user_poster_layout, entity: user, created_at: Time.zone.now)
        allow(user).to receive(:get_user_poster_layout).and_return(layout)

        expect(user.self_trial_user?).to be true
      end
    end

    context 'when the user has no successful auth charge' do
      it 'returns false' do
        allow(user).to receive(:get_user_poster_layout).and_return(nil)

        expect(user.self_trial_user?).to be false
      end
    end

    context 'when the user has a successful auth charge but no layout' do
      it 'returns true' do
        create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success, created_at: 1.day.ago, success_at: 1.day.ago)
        allow(user).to receive(:get_user_poster_layout).and_return(nil)

        expect(user.self_trial_user?).to be true
      end
    end

    context 'when the user has a successful auth charge but layout created before the charge' do
      it 'returns false' do
        first_successful_recharge = create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success,
                                           created_at: Time.zone.now, success_at: Time.zone.now)
        layout = create(:user_poster_layout, entity: user, created_at: 1.day.ago)
        allow(user).to receive(:get_user_poster_layout).and_return(layout)

        expect(user.self_trial_user?).to be false
      end
    end
  end

  describe '#calculate_lead_score' do
    let(:user) { create(:user) }

    context 'when user is a leader by profession' do
      before do
        allow(user).to receive(:leader_profession?).and_return(true)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(13)
      end
    end

    context 'when user is a self trial user' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(true)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'when user took monthly package and trial start date not present' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(false)
        expect(user.calculate_lead_score).to eq(10)
      end

      it 'when user took yearly package and trial start date not present' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(true)
        expect(user.calculate_lead_score).to eq(10)
      end

      it 'when user took monthly package and trial start date present and start date is today' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(false)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today.to_s)
        expect(user.calculate_lead_score).to eq(30)
      end

      it 'when user took monthly package and trial start date present and start date is yesterday' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(false)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.yesterday.to_s)
        expect(user.calculate_lead_score).to eq(29)
      end

      it 'when user took monthly package and trial start date present and start date is 7 days ago' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(false)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: 7.days.ago.to_date.to_s)
        expect(user.calculate_lead_score).to eq(23)
      end

      it 'when user took monthly package and trial start date present and start date is 16 days ago' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(false)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: 16.days.ago.to_date.to_s)
        expect(user.calculate_lead_score).to eq(15)
      end

      it 'when user took yearly package and trial start date present and start date is today' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(true)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.today.to_s)
        expect(user.calculate_lead_score).to eq(35)
      end

      it 'when user took yearly package and trial start date present and start date is yesterday' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(true)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: Time.zone.yesterday.to_s)
        expect(user.calculate_lead_score).to eq(34)
      end

      it 'when user took yearly package and trial start date present and start date is 7 days ago' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(true)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: 7.days.ago.to_date.to_s)
        expect(user.calculate_lead_score).to eq(28)
      end

      it 'when user took yearly package and trial start date present and start date is 16 days ago' do
        allow(user).to receive(:is_yearly_self_trial_user?).and_return(true)
        FactoryBot.create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key,
                          value: 16.days.ago.to_date.to_s)
        expect(user.calculate_lead_score).to eq(15)
      end
    end

    context 'when user has a badge role' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(true)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(13)
      end
    end

    context 'when user is affiliated with a party circle' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(1)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(11)
      end
    end

    context 'when user has shared more than 20 posters' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 21)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(13)
      end
    end

    context 'when user has shared between 6 and 20 posters' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 10)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(11)
      end
    end

    context 'when user has shared 5 or fewer posters' do
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 5)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(10)
      end
    end

    context 'when user last session time is less than 7 days' do
      let(:user) { create(:user) }
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now - 3.days)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(9)
      end
    end

    context 'when user last session time is more than 7 days' do
      let(:user) { create(:user) }
      before do
        allow(user).to receive(:leader_profession?).and_return(false)
        allow(user).to receive(:self_trial_user?).and_return(false)
        allow(user).to receive(:has_badge_role?).and_return(false)
        allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
        allow(user).to receive(:user_last_session_time).and_return(Time.zone.now - 10.days)
        allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      end

      it 'returns the correct lead score' do
        expect(user.calculate_lead_score).to eq(8)
      end
    end
  end

  context 'when user last trial setup attempt time is less than 1 hour' do
    let(:user) { create(:user) }
    before do
      allow(user).to receive(:leader_profession?).and_return(false)
      allow(user).to receive(:self_trial_user?).and_return(false)
      allow(user).to receive(:has_badge_role?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
      allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
      allow(user).to receive(:user_last_trial_setup_attempt_time).and_return(Time.zone.now - 30.minutes)
      allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
    end

    it 'returns the correct lead score' do
      expect(user.calculate_lead_score).to eq(14)
    end
  end

  context 'when user last trial setup attempt time is less than 7 days' do
    let(:user) { create(:user) }
    before do
      allow(user).to receive(:leader_profession?).and_return(false)
      allow(user).to receive(:self_trial_user?).and_return(false)
      allow(user).to receive(:has_badge_role?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
      allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
      allow(user).to receive(:user_last_trial_setup_attempt_time).and_return(Time.zone.now - 3.days)
      allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
    end

    it 'returns the correct lead score' do
      expect(user.calculate_lead_score).to eq(13)
    end
  end

  context 'when user last trial setup attempt time is more than 7 days' do
    let(:user) { create(:user) }
    before do
      allow(user).to receive(:leader_profession?).and_return(false)
      allow(user).to receive(:self_trial_user?).and_return(false)
      allow(user).to receive(:has_badge_role?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
      allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
      allow(user).to receive(:user_last_trial_setup_attempt_time).and_return(Time.zone.now - 10.days)
      allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
    end

    it 'returns the correct lead score' do
      expect(user.calculate_lead_score).to eq(12)
    end
  end

  context 'when badge network density is more than 5' do
    let(:user) { create(:user) }
    before do
      allow(user).to receive(:leader_profession?).and_return(false)
      allow(user).to receive(:self_trial_user?).and_return(false)
      allow(user).to receive(:has_badge_role?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
      allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
      allow(user).to receive(:user_last_trial_setup_attempt_time).and_return(nil)
      allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      allow(UserContactSuggestion).to receive(:where).and_return(double(joins: double(count: 6)))
    end

    it 'returns the correct lead score' do
      expect(user.calculate_lead_score).to eq(11)
    end
  end

  context 'when badge network density is 5 or less' do
    let(:user) { create(:user) }
    before do
      allow(user).to receive(:leader_profession?).and_return(false)
      allow(user).to receive(:self_trial_user?).and_return(false)
      allow(user).to receive(:has_badge_role?).and_return(false)
      allow(user).to receive(:affiliated_party_circle_id).and_return(nil)
      allow(user).to receive(:user_last_session_time).and_return(Time.zone.now)
      allow(user).to receive(:user_last_trial_setup_attempt_time).and_return(nil)
      allow(PosterShare).to receive(:where).and_return(double(limit: double(count: 0)))
      allow(UserContactSuggestion).to receive(:where).and_return(double(joins: double(count: 5)))
    end

    it 'returns the correct lead score' do
      expect(user.calculate_lead_score).to eq(10)
    end
  end

  describe '#premium_user_ids_of_user_contacts' do
    let(:user) { create(:user) }

    context 'when user has premium contacts with 12-month plans' do
      it 'returns premium contact user ids with 12-month plans' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month, plan: create(:plan, duration_in_months: 12))
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)

        result = user.premium_user_ids_of_user_contacts(duration_in_months: 12)

        expect(result).to include(premium_contact_user.id)
      end
    end

    context 'when user has premium contacts with non-12-month plans' do
      it 'does not return premium contact user ids with non-12-month plans' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month, plan: create(:plan, duration_in_months: 6))
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)

        result = user.premium_user_ids_of_user_contacts(duration_in_months: 12)

        expect(result).not_to include(premium_contact_user.id)
      end
    end

    context 'when user has no premium contacts' do
      it 'returns an empty array' do
        result = user.premium_user_ids_of_user_contacts(duration_in_months: 12)

        expect(result).to be_empty
      end
    end

    context 'when duration_in_months is not specified' do
      it 'returns all premium contact user ids' do
        premium_contact_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: premium_contact_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_contact_user.id, phone: premium_contact_user.phone)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(premium_contact_user.id)
      end
    end

    context 'when premium contacts have multiple grade levels' do
      it 'sorts premium contact user ids by ascending grade level' do
        lower_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        higher_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: lower_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_plan, user: higher_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: lower_grade_user.id, phone: lower_grade_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: higher_grade_user.id, phone: higher_grade_user.phone)
        create(:user_role, user: lower_grade_user, grade_level: 1)
        create(:user_role, user: higher_grade_user, grade_level: 3)

        result = user.premium_user_ids_of_user_contacts

        expect(result.first).to eq(lower_grade_user.id)
        expect(result.second).to eq(higher_grade_user.id)
      end
    end

    context 'when premium contacts do not have grade levels' do
      it 'treats them as having the highest grade level' do
        no_grade_user = create(:user, phone: "2#{Faker::Number.unique.number(digits: 9)}".to_i)
        create(:user_plan, user: no_grade_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: no_grade_user.id, phone: no_grade_user.phone)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(no_grade_user.id)
      end
    end

    context 'when user has both premium and non-premium contacts' do
      it 'returns only premium contact user ids' do
        random_first_digit = rand(6..9)
        premium_user = create(:user, phone: "#{random_first_digit}#{Faker::Number.unique.number(digits: 9)}")
        non_premium_user = create(:user, phone: "#{random_first_digit}#{Faker::Number.unique.number(digits: 9)}")
        create(:user_plan, user: premium_user, end_date: Time.zone.now + 1.month)
        create(:user_contact_suggestion, user: user, phone_user_id: premium_user.id, phone: premium_user.phone)
        create(:user_contact_suggestion, user: user, phone_user_id: non_premium_user.id, phone: non_premium_user.phone)

        result = user.premium_user_ids_of_user_contacts

        expect(result).to include(premium_user.id)
        expect(result).not_to include(non_premium_user.id)
      end
    end
  end

  context 'user consent check for subscription plan downgrade check' do
    let(:user) { create(:user) }
    before do
      allow(AppVersionSupport).to receive(:supports_downgrade_sheet?).and_return(true)
    end
    it 'returns false as user does not have any active or on hold subscription' do
      expect(user.show_downgrade_consent_sheet?).to be false
    end

    it 'returns true as user has active subscription but has given any consent to downgrade' do
      subscription = create(:subscription, user: user, status: :active)
      subscription.mark_subscription_to_take_consent_for_downgrade
      expect(user.show_downgrade_consent_sheet?).to be true
    end

    it 'returns false as user has active subscription but has given consent to downgrade' do
      subscription = create(:subscription, user: user, status: :active)
      subscription.mark_subscription_to_take_consent_for_downgrade
      subscription.mark_subscription_plan_downgrade_consent_as_yes
      expect(user.show_downgrade_consent_sheet?).to be false
    end
  end

  describe '#should_pitch_yearly_package?' do
    let(:y_user) { create(:user) }
    let(:m_user) { create(:user) }
    let(:premium_device) { create(:device, make: "make", model: 'model', price: 50000, launch_date: 2.years.ago.to_date) }
    let(:not_premium_device) { create(:device, make: "make", model: 'model', price: 20000, launch_date: 1.month.ago.to_date) }

    context "when user's profession is eligible for yearly premium" do
      let(:y_premium_pitch) { create(:premium_pitch, user: y_user, source: :LEADER_PROFESSION) }

      it 'and is a premium device, returns true' do
        allow(y_user).to receive(:latest_device).and_return(premium_device)
        allow(y_user).to receive(:premium_pitch).and_return(y_premium_pitch)
        expect(y_user.should_pitch_yearly_package?).to be true
      end

      it 'and is not a premium device, returns false' do
        allow(y_user).to receive(:latest_device).and_return(not_premium_device)
        allow(y_user).to receive(:premium_pitch).and_return(y_premium_pitch)
        expect(y_user.should_pitch_yearly_package?).to be false
      end
    end

    context "when user's profession is not eligible for yearly premium" do
      let(:m_premium_pitch) { create(:premium_pitch, user: m_user, source: :HIGH_END_DEVICE_NON_LEADER) }

      it 'and is a premium device, returns false' do
        allow(m_user).to receive(:latest_device).and_return(premium_device)
        allow(m_user).to receive(:premium_pitch).and_return(m_premium_pitch)
        expect(m_user.should_pitch_yearly_package?).to be false
      end

      it 'and is not a premium device, returns false' do
        allow(m_user).to receive(:latest_device).and_return(not_premium_device)
        allow(m_user).to receive(:premium_pitch).and_return(m_premium_pitch)
        expect(m_user.should_pitch_yearly_package?).to be false
      end
    end
  end

  describe '#show_offer_amount_upgrade_sheet?' do
    let(:user) { create(:user) }

    context 'when app version does not support upgrade package sheet' do
      before do
        allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(false)
      end

      it 'returns false' do
        expect(user.show_offer_amount_upgrade_sheet?).to be false
      end
    end

    context 'when user is not eligible for 1 year campaign' do
      before do
        allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(true)
        allow(user).to receive(:user_eligible_1_year_campaign).and_return(nil)
      end

      it 'returns false' do
        expect(user.show_offer_amount_upgrade_sheet?).to be false
      end
    end

    context 'when user has already seen the offer campaign' do
      let(:campaign) { create(:campaign) }

      before do
        allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(true)
        allow(user).to receive(:user_eligible_1_year_campaign).and_return(campaign)
        allow(user).to receive(:has_seen_offer_campaign_already?).with(campaign_id: campaign.id).and_return(true)
      end

      it 'returns false' do
        expect(user.show_offer_amount_upgrade_sheet?).to be false
      end
    end

    context 'when user is eligible and has not seen the offer campaign' do
      let(:campaign) { create(:campaign) }

      before do
        allow(user).to receive(:app_version_supports_upgrade_package_sheet?).and_return(true)
        allow(user).to receive(:user_eligible_1_year_campaign).and_return(campaign)
        allow(user).to receive(:has_seen_offer_campaign_already?).with(campaign_id: campaign.id).and_return(false)
      end

      it 'returns true' do
        expect(user.show_offer_amount_upgrade_sheet?).to be true
      end
    end
  end

  describe '#is_yearly_self_trial_user?' do
    let(:user) { create(:user) }

    context 'when user has a successful recharge and layout created after the recharge' do
      before do
        plan = FactoryBot.create(:plan, duration_in_months: 12)
        subscription = FactoryBot.create(:subscription, user: user, status: :active, plan: plan)
        FactoryBot.create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success,
                          created_at: 1.day.ago, subscription: subscription,
                          updated_at: 1.day.ago, success_at: 1.day.ago)
        layout = create(:user_poster_layout, entity: user, created_at: Time.zone.now)
        allow(user).to receive(:get_user_poster_layout).and_return(layout)
      end

      it 'returns true' do
        expect(user.is_yearly_self_trial_user?).to be true
      end
    end

    context 'when user has a successful recharge and layout created after the recharge and subscription is monthly' do
      before do
        plan = FactoryBot.create(:plan, duration_in_months: 1)
        subscription = FactoryBot.create(:subscription, user: user, status: :active, plan: plan)
        FactoryBot.create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success,
                          created_at: 1.day.ago, subscription: subscription,
                          updated_at: 1.day.ago, success_at: 1.day.ago)
        layout = create(:user_poster_layout, entity: user, created_at: Time.zone.now)
        allow(user).to receive(:get_user_poster_layout).and_return(layout)
      end

      it 'returns false' do
        expect(user.is_yearly_self_trial_user?).to be false
      end
    end

    context 'when user has no successful recharge' do
      before do
        allow(user).to receive(:get_first_successful_recharge).and_return(nil)
      end

      it 'returns false' do
        expect(user.is_yearly_self_trial_user?).to be false
      end
    end

    context 'when user has a successful recharge but layout created before the recharge' do
      before do
        FactoryBot.create(:subscription_charge, user: user, charge_amount: 1, amount: 1, status: :success,
                          created_at: 1.day.ago, success_at: 1.day.ago)
        layout = FactoryBot.create(:user_poster_layout, entity: user, created_at: Time.zone.now)
        allow(user).to receive(:get_user_poster_layout).and_return(layout)
      end

      it 'returns false' do
        expect(user.is_yearly_self_trial_user?).to be false
      end
    end

    context 'when user has a successful recharge but no layout' do
      before do
        @user = create(:user)
        plan = create(:plan, duration_in_months: 12)
        subscription = create(:subscription, user: @user, status: :active, plan: plan)
        FactoryBot.create(:subscription_charge, user: @user, charge_amount: 1, amount: 1, status: :success,
                          subscription: subscription)
      end

      it 'returns true' do
        expect(@user.is_yearly_self_trial_user?).to be true
      end
    end
  end

  describe '#update_user_stage_in_premium_pitch' do
    let(:user) { create(:user) }

    context 'when premium pitch exists' do
      it 'updates the stage of the premium pitch' do
        premium_pitch = FactoryBot.create(:premium_pitch, user: user)
        user.update_user_stage_in_premium_pitch(stage: :LEADER_POSTER_SHARE)
        expect(premium_pitch.reload.crm_stage.to_sym).to eq(:LEADER_POSTER_SHARE)
      end
    end

    context 'when premium pitch does not exist' do
      it 'expect honey badger to be notified' do
        expect(Honeybadger).to receive(:notify).with("Premium pitch not found for user", context: { user_id: user.id })
        user.update_user_stage_in_premium_pitch(stage: :LEADER_POSTER_SHARE)
      end
    end
  end

  describe '#is_ivr_experiment_user?' do
    let(:user) { create(:user) }
    context 'when the user is IVR experiment user' do
      before do
        allow($redis).to receive(:sismember).with(Constants.charge_ivr_experiment_redis_key, user.id).and_return(true)
      end
      it 'returns true' do
        expect(user.is_ivr_experiment_user?).to be(true)
      end
    end

    context 'when the user is not a IVR experiment user' do
      before do
        allow($redis).to receive(:sismember).with(Constants.charge_ivr_experiment_redis_key, user.id).and_return(false)
      end
      it 'returns false' do
        expect(user.is_ivr_experiment_user?).to be(false)
      end
    end
  end

  describe "#get_user_creative_data" do
    let(:user) { create(:user) }
    let(:campaign_name) { "fake_campaign" }

    context "when there is no high priority event" do
      before do
        allow(Event).to receive(:upcoming_events_including_current).with(user: user, only_high_priority: true)
                                                                   .and_return([])
      end
      it "should return nil" do
        expect(user.get_user_creative_data(campaign_name)).to be_nil
      end
    end

    context "when there is a high priority event but no creative" do
      let(:event) { instance_double(Event) }
      before do
        allow(Event).to receive(:upcoming_events_including_current).with(user: user, only_high_priority: true)
                                                                   .and_return([event])
        allow(event).to receive(:get_creative_for_wati_campaign).and_return(nil)
      end
      it "should return nil" do
        expect(user.get_user_creative_data(campaign_name)).to be_nil
      end
    end

    context "when there is a high priority event with creative" do
      let(:event) { instance_double(Event) }
      let(:poster_creative) { instance_double(PosterCreative, id: 3425) }
      before do
        create(:user_poster_layout, entity_id: user.id, entity_type: "User", created_at: 2.days.ago,
               active: true, first_activated_at: Time.zone.now)
        allow(Event).to receive(:upcoming_events_including_current)
                          .with(user: user, only_high_priority: false, order_by_priority: true)
                          .and_return([event])
        allow(event).to receive(:get_creative_for_wati_campaign).and_return(poster_creative)
      end
      it "should return a object for user_metadata of a user" do
        data = user.get_user_creative_data(campaign_name)
        expect(data).to be_a(UserMetadatum)
        expect(data.user_id).to eq(user.id)
        expect(data.key).to eq("#{campaign_name}_creative_id")
        expect(data.value).to eq(poster_creative.id.to_s)
      end
    end
  end
end
