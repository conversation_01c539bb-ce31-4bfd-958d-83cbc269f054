require 'sidekiq/web'
require 'sidekiq-scheduler/web'
require 'sidekiq_unique_jobs/web'
require "sidekiq/throttled/web"

Rails.application.routes.draw do
  devise_config = ActiveAdmin::Devise.config
  devise_config[:controllers][:omniauth_callbacks] = 'admin/omniauth_callbacks'
  devise_for :admin_users, devise_config
  ActiveAdmin.routes(self)

  namespace :admin do
    get '/layout-creation-info', to: 'rm_flow#get_layout_creation_info'
    get '/party-circles', to: 'rm_flow#party_circles'
    get '/default-leader-circles-of-a-party', to: 'rm_flow#default_leader_circles_of_a_party'
    get '/protocol-xy-positions', to: 'rm_flow#protocol_xy_positions'
    get '/search-circles-for-protocol', to: 'rm_flow#search_circles_for_protocol'
    post '/save-layout-as-draft', to: 'rm_flow#save_layout_as_draft'
    get '/external-redirect', to: 'admin#external_redirect'
    get '/validate-admin-session', to: 'admin#validate_admin_session'
    get '/dashboard-link', to: 'admin#get_dashboard_link'

    # Routes for OE Work Flow with user ID in the path
    get '/users/:user_id/oe_work_flow', to: 'oe_work_flow#index', as: 'users_oe_work_flow'

    # Route for BOE Work Flow with user ID in the path
    get '/users/:user_id/boe_work_flow', to: 'boe_work_flow#index', as: 'users_boe_work_flow'
  end

  get 'main/assetlinks'
  get '/', to: 'api#index'
  get '/status', to: 'api#index'

  # Sidekiq::Throttled::Web.enhance_queues_tab!
  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    # Protect against timing attacks:
    # - See https://codahale.com/a-lesson-in-timing-attacks/
    # - See https://thisdata.com/blog/timing-attacks-against-string-comparison/
    # - Use & (do not use &&) so that it doesn't short circuit.
    # - Use digests to stop length information leaking (see also ActiveSupport::SecurityUtils.variable_size_secure_compare)
    ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(username), ::Digest::SHA256.hexdigest(Rails.application.credentials[:sidekiq_username])) &
      ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(password), ::Digest::SHA256.hexdigest(Rails.application.credentials[:sidekiq_password]))
  end if Rails.env.production?
  mount Sidekiq::Web => '/sidekiq'

  # API that simply refreshes the access token, and sends it in the response header
  get '/refresh-token', to: 'api#refresh_token'

  get '/test', to: 'api#test'
  get '/test-2', to: 'api#test_2'

  get '/download' => redirect('http://onelink.to/praja-buzz')
  get '/privacy-policy' => redirect('https://m.praja.buzz/privacy-policy')
  get '/terms-of-use' => redirect('https://m.praja.buzz/terms-of-use')

  get '/get-initial-data', to: 'api#get_initial_data'
  post '/get-initial-data-v2', to: 'api#get_initial_data'

  get '/get-refer-text', to: 'api#get_refer_text'
  get '/referrals-data', to: 'api#get_referrals_data'
  post '/fcm-token', to: 'api#save_fcm_token'

  get '/users/search-tags', to: 'users#search_tags'
  post '/users/search-contacts', to: 'users#search_contacts'
  post '/users/invite-contacts', to: 'users#invite_contacts'
  post '/users/follow-contacts', to: 'users#follow_contacts'
  post '/users/upload-contacts', to: 'users#upload_contacts'
  get '/users/get-verification-data', to: 'users#get_verification_data'
  post '/users/save-verification-designation', to: 'users#save_verification_designation'

  get 'users/get-contacts-for-follow', to: 'users#get_contacts_for_follow'
  post 'users/follow-contacts-v1', to: 'users#follow_contacts_v1'
  get 'users/get-suggested-users-lists', to: 'users#get_suggested_users_lists'
  post 'users/follow-all-suggested-users', to: 'users#follow_all_suggested_users'
  get '/get-fan-poster-requests-prompt-data', to: 'users#get_fan_poster_requests_prompt_data'
  post '/request-fan-poster', to: 'users#request_fan_poster'
  post '/request-circle-premium', to: 'users#request_circle_premium'
  get '/get-fan-poster-requests-data', to: 'users#get_fan_poster_requests_data'

  resources :posters do
    put '/mark-as-seen', to: 'posters#mark_as_seen_poster_card'
    get '/get-share-text', to: 'posters#get_share_text_with_short_link'
  end
  post 'posters/help', to: 'posters#help'
  post '/posters/share', to: 'posters#share'
  post '/posters/mark-as-seen-creatives', to: 'posters#poster_creatives_mark_as_seen_bulk'
  post '/posters/mark-as-seen-frames', to: 'posters#frames_mark_as_seen_bulk'
  get '/subscription-details', to: 'posters#subscription_details'

  put '/saw-trend-tutorial', to: 'users#saw_trend_tutorial'
  put '/users/suggested-list-seen', to: 'users#suggested_list_seen'
  post '/get-users', to: 'users#get_users_with_ids'

  # TODO: Remove this route after upgrading all users to dm enabled version
  get '/get-dm-recommended-users', to: 'users#get_dm_recommended_users_for_new_conversation'
  get '/dm-recommended-users', to: 'users#get_dm_recommended_users_for_new_conversation'
  get '/dm-recommended-users-for-share', to: 'users#get_dm_recommended_users_for_share'

  # apis with pagination
  post '/dm-recommended-users-v1', to: 'users#get_dm_recommended_users_for_new_conversation_v1'
  post '/dm-recommended-users-for-share-v1', to: 'users#get_dm_recommended_users_for_share_v1'

  post '/get-user-dm-circles', to: 'users#get_user_dm_circles'
  get '/get-circles', to: 'users#get_circles'

  get '/users/search-v2', to: 'users#get_users_search_results'

  resources :users, except: [:index] do
    put '/suggested-seen', to: 'users#suggested_seen'
    get '/posts', to: 'users#get_posts'
    get '/liked-posts', to: 'users#get_liked_posts'
    put '/follow', to: 'users#follow'
    put '/unfollow', to: 'users#unfollow'
    get '/followers', to: 'users#followers'
    get '/following', to: 'users#following'
    put '/report', to: 'users#report'
    put '/block', to: 'users#block'
    put '/unblock', to: 'users#unblock'
    get '/invite-link', to: 'users#invite_link'
    get '/fb-invite-link', to: 'users#fb_invite_link'
    post '/charge-ivr-menu-selection', to: 'users#menu_selection'
    post '/charge-ivr-call-status', to: 'users#call_status'
  end

  resources :professions do
    post '/submit', to: 'professions#submit'
  end
  post '/professions/skip', to: 'professions#skip'

  resources :toasts do
    put '/close', to: 'toasts#close'
  end

  put '/notifications/mark-all-as-read', to: 'notifications#mark_all_as_read'
  resources :notifications do
    put '/read', to: 'notifications#mark_as_read'
  end

  get '/all-districts', to: 'circles#all_districts'
  get '/district-mandals', to: 'circles#district_mandals'
  get '/mandal-villages', to: 'circles#mandal_villages'
  get '/political-parties', to: 'circles#get_political_parties'
  post '/circles/create-private', to: 'circles#create_private'

  get '/get-logged-in-user', to: 'users#get_logged_in_user'
  get '/my-profile', to: 'users#profile'
  get '/badge-notify', to: 'users#get_badge_notification'
  post '/get-otp', to: 'users#get_otp'
  get '/get-voice-otp', to: 'users#get_voice_otp'
  get '/get-exotel-otp-text', to: 'users#get_exotel_otp_text'
  post '/login-with-truecaller', to: 'users#login_with_truecaller'
  post '/login', to: 'users#login'
  put '/logout', to: 'users#logout'
  post '/signup', to: 'users#create'
  post '/save-profile', to: 'users#update'
  put '/update-profile-pic', to: 'users#update_profile_pic'
  delete '/delete-profile-pic', to: 'users#delete_profile_pic'
  put '/update-poster-photo', to: 'users#update_poster_photo'
  get '/poster-photos', to: 'users#poster_photos'

  get '/my-circles', to: 'users#get_posting_circles'
  get '/my-posting-circles', to: 'users#get_posting_circles'
  get '/my-create-post-data', to: 'users#get_create_post_data'
  get '/my-viewing-circles', to: 'users#get_viewing_circles'
  get '/my-viewing-circles-v2', to: 'users#get_viewing_circles_v2'
  get '/my-viewing-circles-v3', to: 'users#get_viewing_circles_v3'
  get '/my-suggested-circles-v3', to: 'users#get_suggested_circles_v3'
  get '/following-circles-for-tag', to: 'users#get_following_circles_for_tag'
  get '/suggested-circles-for-tag', to: 'users#get_suggested_circles_for_tag'
  get '/search-circles-for-tag', to: 'users#search_circles_for_tag'
  put '/info-pop-up-accept', to: 'users#info_pop_up_accept'
  put '/alert-pop-up-accept', to: 'users#alert_pop_up_accept'
  post '/save-premium-lead', to: 'users#save_premium_lead'

  get '/my-photos', to: 'user_photos#index'

  get '/my-posts', to: 'user_posts#my_feed' # OLD app is on GET
  post '/feed', to: 'user_posts#feed'
  post '/my-posts', to: 'user_posts#my_feed'
  post '/my-feed', to: 'user_posts#my_feed'
  post '/trending-feed', to: 'user_posts#trending_feed'
  post '/feeds/election-feed', to: 'user_posts#election_feed'

  post '/mla-constituency/submit', to: 'users#save_mla_constituency'

  post '/create-post', to: 'posts#create'
  post '/create-post-v2', to: 'posts#create_v2'

  get '/my-notifications', to: 'notifications#index'
  get '/my-notifications-count', to: 'notifications#notifications_count'

  get '/search', to: 'users#search'
  post '/upload-photo', to: 'photos#upload'
  post '/upload-video', to: 'videos#upload'
  post '/update-video-process', to: 'videos#processing_update'
  put '/mark-as-seen-bulk', to: 'posts#mark_as_seen_bulk'
  get '/dm-search', to: 'users#dm_search'

  get '/election_tickers/link_preview', to: 'election_tickers#ticker_link_preview'
  get '/election_tickers/constituency_status_link_preview', to: 'election_tickers#constituency_status_link_preview'
  get '/election_tickers/live_ticker', to: 'election_tickers#live_ticker'
  get '/election_tickers/live_location_constituency_status', to: 'election_tickers#location_constituency_status_carousel'
  get '/election_tickers/live_user_constituency_status', to: 'election_tickers#live_user_constituency_status_carousel'
  get '/election_tickers/live_leader_constituency_status', to: 'election_tickers#live_leader_constituency_status_carousel'

  resources :posts do
    get '/share', to: 'posts#share'
    get '/get-post-preview', to: 'posts#get_post_preview'
    put '/delete', to: 'posts#delete'
    put '/report', to: 'posts#report'
    get '/likes', to: 'posts#liked_users'
    put '/like', to: 'post_likes#like'
    put '/share-to-whatsapp', to: 'posts#share_to_whatsapp'
    put '/mark-as-seen', to: 'posts#mark_as_seen'
    put '/unlike', to: 'post_likes#unlike'
    post '/comment', to: 'post_comments#create'
    put '/update-comments-config', to: 'posts#update_comments_config'

    get '/get-send-to-circles', to: 'posts#get_send_to_circles'
    get '/opinions', to: 'posts#get_post_opinions'
    get '/get-share-text', to: 'posts#get_share_text'
    post '/remove-tag', to: 'posts#remove_circle_tag'
    get '/feed', to: 'posts#notification_post_feed'
    get '/feed-v2', to: 'posts#notification_post_feed_v2'

    resources :post_photos, path: :photos
    resources :post_comments, path: :comments do
      put '/report', to: 'post_comments#report'
      put '/delete', to: 'post_comments#delete'
    end
  end

  get '/hashtags/search', to: 'hashtags#search'
  resources :hashtags do
    match '/posts', to: 'hashtags#get_posts', via: [:get, :post]
    get '/likes', to: 'hashtags#liked_users'
    put '/like', to: 'hashtags#like'
    put '/share-to-whatsapp', to: 'hashtags#share_to_whatsapp'
    put '/unlike', to: 'hashtags#unlike'
    get '/get-share-text', to: 'hashtags#get_share_text'
  end
  get '/hashtags/public/:hashid', to: 'hashtags#get_public_hashtag', param: :hashid
  get '/posts/public/:hashid', to: 'posts#get_public_post', param: :hashid

  get '/circles/search-v2', to: 'circles#get_circles_search_results'
  post '/conversation-members', to: 'circles#get_conversation_members'

  resources :circles do
    put '/suggested-seen', to: 'circles#suggested_seen'
    get '/users', to: 'circles#get_users'
    match '/feed', to: 'circles#get_feed', via: [:get, :post]
    post '/feed-v1', to: 'circles#get_feed_v1'
    post '/feed-v2', to: 'circles#get_feed_v2'
    post '/trending-feed', to: 'circles#trending_feed'
    post '/trending-feed-v1', to: 'circles#trending_feed_v1'
    post '/upload-creative', to: 'circles#upload_creative'
    get '/circle-v1', to: 'circles#show_v1'
    get '/circle-v2', to: 'circles#show_v2'
    get '/posts', to: 'circles#get_posts'
    get '/info', to: 'circles#get_info'
    put '/join', to: 'circles#add_user'
    put '/unjoin', to: 'circles#remove_user'
    get '/get-share-text', to: 'circles#get_share_text'
    get '/view-all-positions', to: 'circles#get_all_leader_circle_owner_positions'
    get '/get-suggested-users-lists', to: 'circles#get_suggested_users_lists'
    get '/share-channel', to: 'circles#share_channel'
    get '/preview', to: 'circles#get_preview_html'
    get '/channel-preview', to: 'circles#get_channel_preview_html'
    put '/leave-channel', to: 'circles#leave_channel'
    put '/leave-group', to: 'circles#leave_private_group'
    put '/add-as-admin', to: 'circles#add_as_admin'
    put '/remove-as-admin', to: 'circles#remove_as_admin'
    put '/make-as-sender', to: 'circles#add_user_as_sender_to_private_group'
    put '/remove-as-sender', to: 'circles#remove_user_as_sender_from_private_group'
  end

  scope 'singular', controller: :singular, :defaults => { :format => 'json' } do
    post '/postback', action: :postback
  end

  scope 'media-service', controller: :media_service do
    get :index
    post '/update-video-process', action: :update_video_process
    post '/poster-page', action: :my_poster_page
    post '/protocol-page', action: :protocol_page
  end

  scope 'dm-service', controller: :dm_service do
    get :index
    post '/authorize-one-to-one', action: :authorize_user_to_create_conversation
    post '/send-one-to-one-notification', action: :send_dm_one_to_one_notification
    post '/send-private-group-notification', action: :send_dm_private_group_notification
    post '/send-channel-notification', action: :send_dm_channel_notification
    get '/get-user-dm-circles-info', action: :get_user_dm_circles_info
    get '/get-user-dm-circles-info-v2', action: :get_user_dm_circles_info_v2
    get '/get-user-dm-circle-info', action: :get_user_dm_circle_info
    post '/filter-circle-users', action: :filter_circle_users
  end

  scope 'garuda-service', controller: :garuda_service do
    post '/deactivate-tokens', action: :deactivate_tokens
    put '/mark-notification-as-sent', action: :mark_notification_as_sent
  end

  scope 'mixpanel', controller: :mixpanel do
    post :cohort_sync
  end

  scope 'floww', controller: :floww do
    post :send_to_mixpanel
    post :rm_assigned
    post :stage_updated
    post :layout_setup_callback
  end

  scope 'lead_squared', controller: :lead_squared do
    post :lead_stage_update
    match "/lead_stage_update", to: "external_service#head_route", via: :head
  end

  scope 'juspay', controller: :juspay do
    post :webhook
  end

  get 'my-poster-sections', to: 'poster_sections#my_poster_sections'
  get 'my-poster-sections-v2', to: 'poster_sections#my_poster_sections_v2'
  get '/creative-categories/layouts', to: 'poster_creatives#get_creatives_of_category'
  get '/poster/preview', to: 'poster_creatives#dm_channel_poster_attachment_preview'
  get '/creatives', to: 'poster_creatives#get_creatives'
  post '/posters-feed', to: 'poster_creatives#get_posters_feed'
  post '/posters-feed-filters', to: 'users#get_posters_feed_filters'
  resources :poster_creatives do
    get '/preview', to: 'poster_creatives#preview_html'
  end

  post '/video-posters/create', to: 'user_video_posters#create'
  get '/video-posters/:id', to: 'user_video_posters#show'
  put '/video-posters/generation-failed', to: 'user_video_posters#video_poster_generation_failed'
  put '/video-posters/generation-completed', to: 'user_video_posters#video_poster_generation_completed'
  post '/video-posters/share', to: 'user_video_posters#share'

  resources :orders do
    post '/checkout', action: :get_checkout_url
  end

  resources :plans do
    post '/subscribe', to: 'plans#subscribe'
    post '/juspay-subscribe', to: 'plans#juspay_subscribe'
    post '/intent-checkout-subscribe', to: 'plans#intent_checkout_subscribe'
  end

  scope 'orders', controller: :orders do
    get '/get-poster-order', action: :get_poster_order
  end

  scope 'phone_pe', controller: :phone_pe do
    post '/callback', action: :callback
    post '/redirect', action: :redirect
  end

  scope 'cashfree', controller: :cashfree do
    post '/callback', action: :callback
    post '/subscription-callback', action: :subscription_callback
  end

  namespace :web_app, path: 'web-app' do
    post '/get-otp', to: 'auth#get_email_otp'
    post '/login', to: 'auth#login'
    put '/logout', to: 'auth#logout'
    put '/update-profile', to: 'users#update_profile'
    post '/create-post', to: 'posts#create_post'
    get '/get-create-post-data', to: 'posts#create_post_data'
    post '/get-poster-web-tool-frames', to: 'posters#get_poster_web_tool_frames'
    get '/get-metadata-for-poster-web-tool', to: 'posters#get_metadata_for_poster_web_tool'
    namespace :admin do
      get '/overview', to: 'circles#overview'
      get '/get-initial-data', to: 'web_app_admin#get_initial_data'
      put '/update-circle', to: 'circles#update_circle'
      put '/update-owner-profile', to: 'web_app_admin#update_owner_profile'
      get '/posts', to: 'posts#index'
    end
    resources :posts do
      put '/delete', to: 'posts#delete'
    end
    resources :orders do
      get '/get-cashfree-payment-session-id', to: 'orders#get_cashfree_payment_session_id'
    end
    resources :plans do
      get '/get-juspay-link', to: 'plans#get_juspay_link'
    end
  end

  # to fetch subscription
  resources :subscriptions do
    post '/failure-callback', to: 'subscriptions#failure_callback'
  end
  post '/cancel-subscription', to: 'subscriptions#cancel_subscription'

  post '/wait-list', to: 'users#wait_list'
  post '/profile-views', to: 'users#user_profile_views'
  get '/premium-experience', to: 'users#premium_experience'
  get '/premium-bottom-sheet', to: 'plans#premium_bottom_sheet'
  get '/premium-success', to: 'users#premium_success'
  get '/premium-success-v2', to: 'users#premium_success_v2'
  get '/premium-benefits-loss-screen', to: 'users#premium_benefits_loss_screen'
  get '/start-trial-popup', to: 'users#start_trial_popup'
  # post '/start-trial', to: 'users#start_trial'
  get '/recharge-paywall', to: 'users#recharge_paywall'
  get '/recharge-paywall-v2', to: 'users#recharge_paywall_v2'
  get '/annual-recharge-paywall', to: 'users#annual_recharge_paywall'
  get '/get-cancel-data', to: 'users#get_subscription_cancel_data'
  get '/get-cancel-data-v2', to: 'users#get_subscription_cancel_data_v2'
  get '/cancellation-confirmation-sheet', to: 'users#cancellation_confirmation_sheet'
  get '/support-sheet', to: 'users#support_sheet'
  post '/request-support-callback', to: 'users#support_callback_request'
  get '/downgrade-sheet', to: 'users#downgrade_bottom_sheet'
  post '/downgrade', to: 'users#downgrade_consent'
  get '/cancel-flow-downgrade-sheet', to: 'users#cancel_flow_downgrade_to_monthly_details'
  post '/downgrade-plan', to: 'users#downgrade_plan'

  get '/upgrade-plan', to: 'users#plan_upgrade_details'
  get '/offer-reveal-sheet', to: 'users#offer_reveal_sheet'
  post '/offer-reveal-status', to: 'users#offer_reveal_status'

  post '/layout-feedback-review-status', to: 'users#layout_feedback_review_status'
  # For details on the DSL available within this file, see http://guides.rubyonrails.org/routing.html

  post '/extend-subscription', to: 'users#extend_user_plan'
end
