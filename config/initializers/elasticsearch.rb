require 'faraday_middleware/aws_sigv4'

config = {
  transport_options: { request: { timeout: 5 } }
}

if File.exist?('config/elasticsearch.yml')
  template = ERB.new(File.new('config/elasticsearch.yml').read)
  processed = YAML.safe_load(template.result(binding))
  config.merge!(processed[Rails.env].symbolize_keys)
end

if Rails.env == 'production'
  client_options = {
    request: { timeout: 5 },
  }
  if config.key?(:ca_file)
    client_options[:ssl] = { ca_file: config[:ca_file] }
  end
  Searchkick.client_options[:transport_options] = client_options
end

if Rails.env == 'test'
  Searchkick.aws_credentials = {
    access_key_id: Rails.application.credentials[:aws_access_key_id],
    secret_access_key: Rails.application.credentials[:aws_secret_access_key],
    region: 'ap-south-1'
  }
end

ENV["OPENSEARCH_URL"] = "#{config[:scheme]}://#{config[:host]}:#{config[:port]}"

ES_CLIENT = Searchkick.client

class Searchkick::BulkReindexJob
  concurrency 4
end
