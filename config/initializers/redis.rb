# frozen_string_literal: true

rails_root = Rails.root || "#{File.dirname(__FILE__)}/../.."
rails_env = Rails.env || 'development'
redis_config = YAML.load(ERB.new(File.read("#{rails_root}/config/redis.yml")).result)
redis_config.merge! redis_config.fetch(rails_env, {})
redis_config.symbolize_keys!

if redis_config.key?(:sentinels)
  $redis = ConnectionPool::Wrapper.new do
    Redis.new(driver: :hiredis,
              url: 'redis://mymaster',
              sentinels: redis_config[:sentinels],
              role: :master,
              db: redis_config[:general_db])
  end
else
  $redis = ConnectionPool::Wrapper.new do
    Redis.new(driver: :hiredis,
              host: redis_config[:host],
              port: redis_config[:port],
              db: redis_config[:general_db])
  end
end

# Can only assign when created, ordering cannot be guaranteed outside this file
Searchkick.redis = $redis
ActiveJob::TrafficControl.client = $redis
