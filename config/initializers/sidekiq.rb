require 'sidekiq/web'
require 'sidekiq-unique-jobs'
require "sidekiq/throttled"

# require 'rack/protection'

rails_root = Rails.root || File.dirname(__FILE__) + '/../..'
rails_env = Rails.env || 'development'
redis_config = YAML.load(ERB.new(File.read("#{rails_root}/config/redis.yml")).result)
redis_config.merge! redis_config.fetch(rails_env, {})
redis_config.symbolize_keys!

Sidekiq.configure_server do |config|

  if redis_config.key?(:sentinels)
    config.redis = {
      url: "redis://mymaster",
      sentinels: redis_config[:sentinels],
      role: :master,
      db: redis_config[:sidekiq_db],
      namespace: "sidekiq_#{rails_env}"
    }
  else
    config.redis = { url: "redis://#{redis_config[:host]}:#{redis_config[:port]}/#{redis_config[:sidekiq_db]}", namespace: "sidekiq_#{rails_env}" }
  end

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end

  config.server_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Server
  end

  SidekiqUniqueJobs::Server.configure(config)

  # if env is preprod disable cron jobs
  unless ENV['DEPLOYMENT'] == 'preprod'
    config.on(:startup) do
      Sidekiq.schedule = YAML.load_file(rails_root.to_s + '/config/sidekiq_scheduler.yml')
      SidekiqScheduler::Scheduler.instance.reload_schedule!
    end
  end
end

Sidekiq.configure_client do |config|
  if redis_config.key?(:sentinels)
    config.redis = {
      url: "redis://mymaster",
      sentinels: redis_config[:sentinels],
      role: :master,
      db: redis_config[:sidekiq_db],
      namespace: "sidekiq_#{rails_env}"
    }
  else
    config.redis = { url: "redis://#{redis_config[:host]}:#{redis_config[:port]}/#{redis_config[:sidekiq_db]}", namespace: "sidekiq_#{rails_env}" }
  end
  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end
end

# prevent dying jobs from littering uniqueness keys
Sidekiq.configure_server do |config|
  config.death_handlers << ->(job, _ex) do
    digest = job['lock_digest']
    SidekiqUniqueJobs::Digests.new.delete_by_digest(digest) if digest
  end
end

# Sidekiq::Web.set :session_secret, Rails.application.credentials[:secret_key_base]
# Sidekiq::Web.set :sessions, Rails.application.config.session_options

# Sidekiq::Extensions.enable_delay!
# Sidekiq::Throttled.setup!
SidekiqUniqueJobs.configure do |config|
  config.lock_info = true
  config.lock_ttl = 1.hour.to_i
  config.lock_prefix = "praja_unique_jobs"
end
