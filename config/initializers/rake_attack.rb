# frozen_string_literal: true

Rack::Attack.throttle("search by ip", limit: 50, period: 60) do |request|
  request.ip if request.path == '/search' || request.path == '/search-circles-for-tag'
end

Rack::Attack.throttled_response_retry_after_header = true
Rack::Attack.throttled_responder = lambda do |request|
  match_data = request.env['rack.attack.match_data']
  now = match_data[:epoch_time]

  headers = {
    'RateLimit-Limit' => match_data[:limit].to_s,
    'RateLimit-Remaining' => '0',
    'RateLimit-Reset' => (now + (match_data[:period] - now % match_data[:period])).to_s,
    'Content-Type' => 'application/json'
  }

  [ 429, headers, [{ success: false, message: 'దయచేసి కాసేపాగి ప్రయత్నించండి!' }.to_json]]
end
