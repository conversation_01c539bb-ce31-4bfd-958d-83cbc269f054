# Get the list of locale files in the config/locales directory
locale_files = Dir[Rails.root.join('config', 'locales', '*.yml')]

# Filter out specific files like devise.en.yml
locale_files.reject! { |file| file.include?('devise.') }

# Extract the locale names from the file names
locales = locale_files.map do |file|
  File.basename(file, '.yml').to_sym
end

# Set the available locales
I18n.available_locales = locales

# Optionally, set the default locale (e.g., to :en)
I18n.default_locale = :en
