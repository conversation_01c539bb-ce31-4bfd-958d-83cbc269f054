# CarrierWave.configure do |config|
#   config.ignore_integrity_errors = false
#   config.ignore_processing_errors = false
#   config.ignore_download_errors = false
#   config.permissions = 0666
#   config.directory_permissions = 0777
#   config.storage = :fog
#   config.asset_host = "https://cdn.thecircleapp.in"
#   config.fog_credentials = {
#       provider:              'AWS',                        # required
#       aws_access_key_id:     Rails.application.credentials[:aws_access_key_id], # required unless using use_iam_profile
#       aws_secret_access_key: Rails.application.credentials[:aws_secret_access_key], # required unless using use_iam_profile
#       # use_iam_profile:       true,                         # optional, defaults to false
#       region:                'ap-south-1',                 # optional, defaults to 'us-east-1'
#       # host:                  'cdn.thecircleapp.in',        # optional, defaults to nil
#       # endpoint:              'https://cdn.thecircleapp.in' # optional, defaults to nil
#   }
#   config.fog_directory  = Rails.application.credentials[:aws_s3_bucket_name]    # required
#   config.fog_public     = true                                                 # optional, defaults to true
#   # config.fog_attributes = { cache_control: "public, max-age=#{365.days.to_i}" } # optional, defaults to {}
#   config.fog_use_ssl_for_aws = true
# end
