FirebaseDynamicLink.configure do |config|
  # the adapter should be supported by Faraday
  # more info look at https://github.com/lostisland/faraday/tree/master/test/adapters
  # Faraday.default_adapter is the default adapter
  config.adapter = :httpclient

  # required
  config.api_key = Rails.application.credentials[:firebase_api_key]

  # default 'UNGUESSABLE'
  config.suffix_option = 'SHORT' # or 'UNGUESSABLE'

  # required
  config.dynamic_link_domain = 'https://praja.page.link'

  # default 3 seconds
  config.timeout = 3

  # default 3 seconds
  config.open_timeout = 3
end

$firebase_dl_client = FirebaseDynamicLink::Client.new