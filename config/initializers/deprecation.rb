# Invoke `CircleApiDeprecation.warn("Use the other method instead")
# to let the user's of deprecated methods know what to use instead
#
# Deprecation object since ActiveSupport::Deprecation.warn is deprecated and rails suggest to use
# explicit of deprecation objects with removal version, app_name/library_name to warn about deprecation
#
# Since our application doesn't have any notion of version, we use a nominal - `next` as removal version
CircleApiDeprecation = ActiveSupport::Deprecation.new('next', 'CircleApi')
