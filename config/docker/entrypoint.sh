#! /bin/sh

# Wait for DB services
sh ./config/docker/wait-for-services.sh

# Prepare DB (Migrate - If not? Create db & Migrate)
sh ./config/docker/prepare-db.sh

# Remove a potentially pre-existing server.pid for Rails.
rm -f /app/tmp/pids/server.pid

if [ $SERVICE = "api" ]; then
  # Then exec the container's main process (what's set as CMD in the Dockerfile).
  bundle exec puma -C config/puma.rb
else
  bundle exec sidekiq -C config/sidekiq.yml
fi
