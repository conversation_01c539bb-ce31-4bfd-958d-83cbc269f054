#! /bin/sh

if [ $SERVICE = "api" ] && [ $RAILS_ENV != "production" ]; then
  # If the database exists, migrate. Otherwise setup (create and migrate)
  bundle exec rake db:migrate 2>/dev/null || bundle exec rake db:create db:migrate
  bundle exec rake db:seed
  # TODO: Test schema caching & remove this line to enable schema dump caching.
  # bundle exec rake db:schema:cache:dump
  echo "Database Migrations Done!"
fi
