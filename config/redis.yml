development:
  host: 'praja-redis'
  port: '6379'
  cache_db: 0
  sidekiq_db: 1
  general_db: 15
test:
  host: <%= ENV['REDIS_HOST'] || 'localhost' %>
  port: '6379'
  cache_db: 0
  sidekiq_db: 1
  general_db: 15
production:
  host: 'praja-redis.oofnk6.ng.0001.aps1.cache.amazonaws.com'
  port: '6379'
  cache_db: <%= ENV['DEPLOYMENT'] == 'preprod' ? 3 : 0 %>
  sidekiq_db: <%= ENV['DEPLOYMENT'] == 'preprod' ? 13 : 12 %>
  general_db: 1
  sentinels:
    - host: 'ror-redis-node-0.ror-redis-headless.ror.svc.cluster.local'
      port: '26379'
    - host: 'ror-redis-node-1.ror-redis-headless.ror.svc.cluster.local'
      port: '26379'
    - host: 'ror-redis-node-2.ror-redis-headless.ror.svc.cluster.local'
      port: '26379'
