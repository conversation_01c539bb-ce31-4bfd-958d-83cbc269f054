default: &default
  adapter: mysql2
  encoding: utf8mb4
  collation: utf8mb4_0900_ai_ci
  pool: <%= ENV.fetch("WEB_CONCURRENCY") { 1 }.to_i * ENV.fetch("RAILS_MAX_THREADS") { 5 }.to_i %>
  host: <%= ENV['DB_HOST'] || '127.0.0.1' %>
  username: <%= ENV['DB_USERNAME'] || 'root' %>
  password: <%= ENV['DB_PASSWORD'] || '' %>
  socket: /tmp/mysql.sock

development:
  primary:
    <<: *default
    database: circle_api_development
    host: praja-mysql
    username: admin
    password: toor
  primary_replica:
    <<: *default
    database: circle_api_development
    host: praja-mysql
    username: admin
    password: toor
    replica: true

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  primary:
    <<: *default
    database: circle_api_test
    username: root
    password: toor
  primary_replica:
    <<: *default
    database: circle_api_test
    username: root
    password: toor
    replica: true

# As with config/secrets.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password as a unix environment variable when you boot
# the app. Read http://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full rundown on how to provide these environment variables in a
# production deployment.
#
# On Heroku and other platform providers, you may have a full connection URL
# available as an environment variable. For example:
#
#   DATABASE_URL="mysql2://myuser:mypass@localhost/somedatabase"
#
# You can use this database configuration with:
#
#   production:
#     url: <%= ENV['DATABASE_URL'] %>
#
production:
  primary:
    <<: *default
    pool: <%= (ENV['DB_CONN_POOL'] || 50).to_i %>
    host: <%= ENV['DB_HOST'] || 'praja-database.czvywyph3uw6.ap-south-1.rds.amazonaws.com' %>
    port: 3306
    database: circle_api_production
    username: <%= ENV['DATABASE_USERNAME'] || 'circle_admin' %>
    password: <%= ENV['DATABASE_PASSWORD'] %>
  primary_replica:
    <<: *default
    pool: <%= (ENV['DB_CONN_POOL'] || 50).to_i %>
#    host: <%#= ENV['DB_REPLICA_HOST'] || 'praja-redash-replica.czvywyph3uw6.ap-south-1.rds.amazonaws.com' %>
    host: <%= ENV['DB_HOST'] || 'praja-database.czvywyph3uw6.ap-south-1.rds.amazonaws.com' %>
    port: 3306
    database: circle_api_production
    username: <%= ENV['DATABASE_USERNAME'] || 'circle_admin' %>
    password: <%= ENV['DATABASE_PASSWORD'] %>
    replica: true
