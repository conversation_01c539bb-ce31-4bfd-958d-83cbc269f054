#
# This file configures the New Relic Agent.  New Relic monitors Ruby, Java,
# .NET, PHP, Python, Node, and Go applications with deep visibility and low
# overhead.  For more information, visit www.newrelic.com.
#
# Generated March 4, 2022
#
# This configuration file is custom generated for NewRelic Administration
#
# For full documentation of agent configuration options, please refer to
# https://docs.newrelic.com/docs/agents/ruby-agent/installation-configuration/ruby-agent-configuration

common: &default_settings
  # Required license key associated with your New Relic account.
  license_key: 799dfc6cf064d984a264933325953e3bb148NRAL

  # Your application name. Renaming here affects where data displays in New
  # Relic.  For more details, see https://docs.newrelic.com/docs/apm/new-relic-apm/maintenance/renaming-applications
  app_name: Praja API

  distributed_tracing:
    enabled: true

  # To disable the agent regardless of other settings, uncomment the following:
  # agent_enabled: false

  # Logging level for log/newrelic_agent.log
  log_level: warn

  transaction_tracer.record_redis_arguments: true
  attributes.include: ["*"]
  attributes.exclude: ["request.parameters.*"]
  custom_insights_events.enabled: true
  browser_monitoring:
    auto_instrument: false

# Environment-specific settings are in this section.
# RAILS_ENV or RACK_ENV (as appropriate) is used to determine the environment.
# If your application has other named environments, configure them here.
development:
  <<: *default_settings
  app_name: Praja API (Development)

  monitor_mode: false

test:
  <<: *default_settings
  app_name: Praja API (Test)
  # It doesn't make sense to report to New Relic from automated test runs.
  monitor_mode: false

production:
  <<: *default_settings
