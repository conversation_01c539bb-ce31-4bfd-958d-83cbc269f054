development:
  scheme: "http"
  host: "praja-opensearch"
  port: 9200
  log: true
  retry_on_failure: false

test:
  scheme: <%= ENV['ES_SCHEME'] || "https" %>
  host: <%= ENV['ES_HOST'] || "search-praja-dev-zlzuu57kd6dacg5w3qmdhc6cba.ap-south-1.es.amazonaws.com" %>
  port: <%= ENV['ES_PORT'] || 443 %>
  log: true
  retry_on_failure: false

production:
  scheme: "https"
  host: "admin:<EMAIL>"
  port: 9200
  retry_on_failure: false
  ca_file: "/etc/ssl/certs/ror-opensearch-cluster-ca.crt"
