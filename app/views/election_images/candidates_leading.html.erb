<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&family=Poppins:wght@700&display=swap");

  :root {
    --outer-container-dimensions: 1024px;
    --praja-logo-width: 120px;

    <% if party_id == 31403 %>
    --party-gradient: linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%);
    --party-footer-bg-gradient: linear-gradient(180deg, #087748 22.06%, rgba(8, 119, 72, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31402 %>
    --party-gradient: linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%);
    --party-footer-bg-gradient: linear-gradient(180deg, #C90807 22.06%, rgba(219, 39, 38, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31406 %>
    --party-gradient: linear-gradient(308.32deg, #cc0000 43.08%, #ffb0b0 105.21%);
    --party-footer-bg-gradient: linear-gradient(180deg, #820707 22.06%, rgba(130, 7, 7, 0) 97.79%);
    --party-contrast-text-color: #ffed91;
    <% elsif party_id == 31401 || party_id == 37967 %>
    --party-gradient: linear-gradient(
      149.88deg,
      #f37022 4.18%,
      #e5fff7 52.37%,
      #0f823f 98.51%
    );
    --party-footer-bg-gradient: linear-gradient(
      180deg,
      #045c2b 24.48%,
      rgba(4, 92, 43, 0) 98.69%
    );
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31398 || party_id == 37788 %>
    --party-gradient: rgba(243, 114, 22, 1);
    --party-footer-bg-gradient: linear-gradient(180deg, #AA2D05 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31405 %>
    --party-gradient: linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%);
    --party-footer-bg-gradient: linear-gradient(180deg, #AD0969 22.06%, rgba(173, 9, 105, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% else %>
    --party-gradient: linear-gradient(45deg, #0061FF, #A1DDFF);
    --party-footer-bg-gradient: linear-gradient(180deg, #0061FF 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% end %>
  }

  #outer-container {
    position: relative;
    height: var(--outer-container-dimensions);
    width: var(--outer-container-dimensions);
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/b6a6f92f-85ed-4f59-a2d3-33f5bd168c92.png");
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* Header Styling */
  #header-container {
    display: flex;
    flex-direction: column;
    width: 70%;
    align-items: start;
    margin-top: 30px;
    gap: 16px;
  }

  #title {
    font-size: 48px;
    color: white;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
    padding-left: 20px;
  }

  #subtitle {
    background-color: rgba(148, 4, 4, 0.8);
    width: fit-content;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-size: 48px;
    font-weight: 600;
    padding: 10px 20px 0 20px;
  }

  #constituency-name {
    border: 1px solid white;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-size: 38px;
    font-weight: 600;
    padding: 10px 20px 0 20px;
    border-radius: 8px;
    display: flex;
    gap: 4px;
  }

  #constituency-name svg {
    width: 40px;
    height: 40px;
    fill: white;
  }

  #time {
    position: absolute;
    top: 32px;
    right: 32px;
    color: white;
    font-family: "Poppins", sans-serif;
    font-size: 40px;
    font-weight: 700;
  }

  #leaders-container {
    display: flex;
    width: 84%;
    margin: auto 8%;
    flex-direction: column;
  }

  #main-leader-container {
    flex: 1;
    max-height: 320px;
    margin-top: 24px;
    display: flex;
    gap: 24px;
  }

  #main-leader-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 32px;
  }

  #main-leader-details #main-leader-status {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }

  #status {
    <% if is_won %>
      background: #078907;
      color: white;
    <% else %>
      background: white;
      color: #078907;
    <% end %>
    display: inline-block;
    border-radius: 12px;
    padding: 14px 24px 10px;
    font-size: 48px;
    font-family: "Poppins", sans-serif;
    font-weight: 700;
  }

  #status.small {
    font-size: 40px;
  }

  #main-leader-details #main-leader-name {
    font-size: 64px;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-weight: 700;
  }

  #main-leader-details #main-leader-name.small {
    font-size: 48px;
  }

  #main-leader-details .party-icon {
    width: 84px;
    height: 84px;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    border: 6px solid white;
  }

  .party-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top center;
  }

  .leader-image {
    height: 100%;
    aspect-ratio: 1.0;
    overflow: hidden;
    border-radius: 0 36% 0 36%;
    background: var(--party-gradient);
  }

  .leader-image img {
    margin-left: 10%;
    width: 90%;
    margin-bottom: 10%;
    height: 90%;
    border-radius: 0 36% 0 36%;
    object-fit: cover;
  }

  #trailing {
    display: inline-block;
    font-size: 32px;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-weight: 400;
    margin-top: 0;
    margin-bottom: 4px;
  }

  #other-candidates-container {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-top: 64px;
    margin-right: 15%;
    gap: 16px;
    background: rgba(0, 0, 0, 0.5);
    padding: 16px;
    border-radius: 8px;
  }

  .other-candidate {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }

  .other-candidate .party-name {
    width: 184px;
    font-size: 32px;
    font-family: "Poppins", sans-serif;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
  }

  .other-candidate .name {
    font-size: 32px;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-weight: 400;
  }

  .other-candidate .party-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    margin-bottom: 10px;
  }

  #praja-logo {
    position: absolute;
    display: flex;
    flex-direction: row;
    align-items: center;
    right: 32px;
    bottom: 32px;
    color: white;
    font-family: "Poppins", sans-serif;
    font-weight: 700;
    font-size: 42px;
    gap: 16px;
  }

  #praja-logo img {
    height: 70px;
    width: 70px;
    border-radius: 10px;
    box-shadow: 4px 4px 4px 0 #00000026;
  }

  #red-text {
    color: red;
  }

</style>

<div id="outer-container">
  <div id="header-container">
    <div id="constituency-name">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960">
        <path
          d="m438-540-28-28q-12-12-28-12t-28 12q-12 12-12 28.5t12 28.5l56 57q12 12 28 12t28-12l142-142q12-12 12-28.5T608-653q-12-12-28.5-12T551-653L438-540Zm362-12q0 45-17.5 94.5t-51 103Q698-301 648-244T533-127q-11 10-25 15t-28 5q-14 0-28-5t-25-15q-65-60-115-117t-83.5-110.5q-33.5-53.5-51-103T160-552q0-150 96.5-239T480-880q127 0 223.5 89T800-552Z"
          />
      </svg>
      <div><%= constituency_name %></div> 
    </div>
    <div id="subtitle"><%= sub_title %></div>
  </div>
  <div id="leaders-container">
    <div id="main-leader-container">
      <div class="leader-image">
        <img src="<%= leader_image_url %>" />
      </div>
      <div id="main-leader-details">
        <div id="main-leader-status">
          <div class="party-icon">
            <img src="<%= party_icon_url %>" />
          </div>
          <% if Unicode::DisplayWidth.of(lead_text) <= 12 %>
            <div id="status"><%= lead_text %></div>
          <% else %>
            <div id="status" class="small"><%= lead_text %></div>
          <% end %>
        </div>
        <% if Unicode::DisplayWidth.of(leader_name) <= 10 %>
          <div id="main-leader-name"><%= leader_name %></div>
        <% else %>
          <div id="main-leader-name" class="small"><%= leader_name %></div>
        <% end %>
      </div>
    </div>
    <div id="other-candidates-container">
      <div id="trailing-divider">
        <p id="trailing">ఇతర అభ్యర్థులు</p>
      </div>
      <% other_candidates.each do |candidate| %>
        <div class="other-candidate">
          <div class="party-icon">
            <img src="<%= candidate[:party_icon_url] %>" />
          </div>
          <div class="party-name"><%= candidate[:party_name] %></div>
          <div class="name"><%= candidate[:name] %></div>
        </div>
      <% end %>
    </div>
  </div>
  <div id="time">
    <%= time %>
  </div>
  <div id="praja-logo">
      <img
        src="https://az-cdn.thecircleapp.in/production/admin-media/40/e6b78930-0d82-450d-8530-f09318771812.png"
        alt="Praja Logo"
        />
  </div>
</div>
