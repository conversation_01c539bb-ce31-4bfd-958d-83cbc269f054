<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&family=Poppins:wght@700&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@100..800&display=swap");

  :root {
    --outer-container-dimensions: 1024px;
    --praja-logo-width: 120px;
    <% if leads.blank? %>
    --leads-text-width: 0;
    <% else %>
    --leads-text-width: 108px;
    <% end %>
  }

  /* Header Styling */

  #outer-container {
    position: relative;
    height: var(--outer-container-dimensions);
    width: var(--outer-container-dimensions);
    background-image: url("https://az-cdn.thecircleapp.in/fit-in/1024x1024/production/admin-media/40/d393a3f6-a438-4ecc-acf3-5b9087a2cc30.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 0;
    margin: auto;
  }

  #header-container {
    height: 22.3%;
    width: 100%;
    display: flex;
    flex-direction: row;
  }

  #text-container {
    width: 65%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 64px;
    margin-left: 20px;
    gap: 4px;
  }

  #title-container {
    color: white;
    font-size: 48px;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
    padding-left: 20px;
  }

  #subtitle-container {
    background-color: rgba(148, 4, 4, 0.8);
    width: fit-content;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-size: 48px;
    font-weight: 600;
    padding: 10px 20px 0 20px;
  }

  #time-container {
    flex-grow: 1;
    display: flex;
    justify-content: end;
    align-items: flex-start;
  }

  #time-text-container {
    font-size: 48px;
    font-weight: bold;
    width: fit-content;
    font-family: "Poppins", sans-serif;
    padding: 4px 20px;
    margin-top: 64px;
    margin-right: 20px;
    color: white;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.4);
  }

  /* Ticker Styling */

  #ticker-parent-container {
    height: calc(60% - 44px);
    width: calc(90% - 20px);
    margin: auto;
    background: rgb(0, 0, 0, 0.5);
    padding: 40px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  #ticker-container {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    gap: 20px;
    justify-content: space-around;
    margin-left: var(--leads-text-width);
  }

  /* Ticker is added in Script */
  .ticker {
    height: 100%;
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  .winner-badge {
    position: absolute;
    bottom: 16px;
    width: 84%;
    margin: 0 8%;
    object-fit: contain;
  }

  #count-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  #leads-container {
    display: flex;
    flex-direction: row;
    color: white;
    <% if leads.present? %>
    border-bottom: 1px solid white;
    <% end %>
    justify-content: start;
  }

  #wons-container {
    display: flex;
    flex-direction: row;
    color: white;
    <% if leads.present? %>
    border-bottom: 1px solid white;
    <% end %>
    justify-content: start;
  }

  #leads-ui {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    color: white;
    <% if leads.present? && wons.blank? %>
    font-size: 72px;
    font-weight: 800;
    <% else %>
    font-size: 48px;
    font-weight: 600;
    <% end %>
    font-family: "Anek Telugu", sans-serif;
    justify-content: space-around;
    text-align: center;
  }

  #wons-ui {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    color: white;
    <% if leads.blank? && wons.present? %>
    font-size: 72px;
    font-weight: 800;
    justify-content: space-evenly;
    <% else %>
    font-size: 48px;
    font-weight: 600;
    justify-content: space-around;
    <% end %>
    font-family: "Anek Telugu", sans-serif;
  }

  .count {
    text-align: center;
    flex: 1;
  }

  #leads-text {
    font-family: "Anek Telugu", sans-serif;
    font-size: 24px;
    align-content: end;
    padding-bottom: 18px;
    width: var(--leads-text-width);
    <% if leads.blank? && wons.present? %>
    display: none;
    <% end %>
  }

  #party-icon {
    width: 100%;
    height: 92px;
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
  }

  #party-icon img {
    height: 100%;
    margin: auto;
    display: flex;
    object-fit: contain;
  }

  .leader-image {
    position: absolute;
    top: 96px;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    object-fit: cover;
    mix-blend-mode: multiply;
  }

  .leader-image-plus-darker {
    position: absolute;
    top: 96px;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
    object-fit: cover;
  }

  .leader-image-overlay {
    position: absolute;
    top: 96px;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    flex-grow: 1;
    opacity: 0.7;
    object-fit: cover;
    mix-blend-mode: overlay;
  }

  #announcement-text-container {
    color: white;
    font-size: 48px;
    font-family: "Anek Telugu", sans-serif;
    font-weight: 600;
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }

  /* Footer Styling */

  #footer-container {
    position: relative;
    flex-grow: 1;
    background: white;
    display: flex;
    flex-direction: row;
    margin-top: 2%;
  }

  #footer-container img {
    position: absolute;
    top: -20px;
    left: 80px;
    height: 120px;
    width: 120px;
    border-radius: 8px;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3), 0 6px 6px rgba(0, 0, 0, 0.23);
  }

  #footer-text {
    margin-left: calc(var(--praja-logo-width) + 80px);
    height: 100%;
    width: 100%;
    align-content: center;
    font-size: 48px;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
    justify-content: center;
    align-items: center;
    display: flex;
  }

  #red-text {
    color: red;
  }

  </style>


<div id="outer-container">
  <div id="header-container">
    <div id="text-container">
      <div id="title-container"><%= title %></div>  <!-- Title: ఆంధ్రప్రదేశ్ -->
      <div id="subtitle-container"><%= sub_title %></div> <!-- Subtitle: అసెంబ్లీ ఎన్నికలు 2024 -->
    </div>
    <% if time.present? %>
      <div id="time-container">
        <div id="time-text-container"><%= time %></div> <!-- Time: 10:00 AM -->
      </div>
    <% end %>
  </div>
  <div id="ticker-parent-container">
    <div id="ticker-container">
      <!-- loop through ticker_list -->
      <% ticker_list.each do |detail| %>
        <div class="ticker" style="background: <%= detail[:css_background] %>">
            <div id="party-icon"><img src="<%= detail[:logo_url] %>" alt="Party Icon" ></div>
            <img src="<%= detail[:leaders_image_url] %>" alt="Ticker Image" class="leader-image">
            <img src="<%= detail[:leaders_image_url] %>" alt="Ticker Image" class="leader-image-plus-darker">
            <img src="<%= detail[:leaders_image_url] %>" alt="Ticker Image" class="leader-image-overlay">
            <% if detail[:winner] %>
              <img class="winner-badge" src="https://cdn.thecircleapp.in/production/admin-media/32/fcd7d2e2daef06d2c4e4d305a4689df7.png" />
            <% end %>
        </div>
      <% end %>
    </div>
    <div id="count-container">
      <% if leads.present? %>
        <div id="leads-container">
          <div id="leads-text"><%= leads_text %></div>  <!-- ముందంజ -->
          <div id="leads-ui">
            <% leads.each do |lead| %>
              <div class="count"><%= lead %></div>
            <% end %>
          </div>
        </div>
      <% end %>
      <% if wons.present? %>
        <div id="wons-container">
          <% if leads.present? %>
            <div id="leads-text"><%= wons_text %></div>  <!-- విజేతలు -->
          <% end %>
          <div id="wons-ui">
            <% wons.each do |won| %>
              <div class="count"><%= won %></div>
            <% end %>
          </div>
        </div>
      <% end %>
      <% if announcement_text.present? %>
        <div id="announcement-text-container"><%= announcement_text %></div>  <!-- 10 రోజుల్లో చూపబడుతుంది -->
      <% end %>
    </div>
  </div>
  <div id="footer-container">
    <img
      src="https://az-cdn.thecircleapp.in/fit-in/120x120/production/admin-media/40/e6b78930-0d82-450d-8530-f09318771812.png"
      alt="praja logo"
      />
    <div id="footer-text">
      <span>ప్రజా యాప్‌లో &nbsp;</span>
      <span id="red-text"> లైవ్ అప్‌డేట్స్</span>
    </div>
  </div>
</div>
