<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Suravaram&display=swap");

  @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@100..900&display=swap');

  :root {
    --outer-container-width: 1000px;
    --outer-container-height: 1250px;
    --user-bottom: 125px;
    --protocol-height: 300px;
    --user-profile-height: 373px;

    <% if party_id == 31403 %>
    --party-gradient: linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%);
    --party-footer-bg-gradient: linear-gradient(180deg, #087748 22.06%, rgba(8, 119, 72, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31402 %>
    --party-gradient: linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%);
    --party-footer-bg-gradient: linear-gradient(180deg, #C90807 22.06%, rgba(219, 39, 38, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31406 %>
    --party-gradient: linear-gradient(308.32deg, #cc0000 43.08%, #ffb0b0 105.21%);
    --party-footer-bg-gradient: linear-gradient(180deg, #820707 22.06%, rgba(130, 7, 7, 0) 97.79%);
    --party-contrast-text-color: #ffed91;
    <% elsif party_id == 31401 || party_id == 37967 %>
    --party-gradient: linear-gradient(
      149.88deg,
      #f37022 4.18%,
      #e5fff7 52.37%,
      #0f823f 98.51%
    );
    --party-footer-bg-gradient: linear-gradient(
      180deg,
      #045c2b 24.48%,
      rgba(4, 92, 43, 0) 98.69%
    );
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31398 || party_id == 37788 %>
    --party-gradient: rgba(243, 114, 22, 1);
    --party-footer-bg-gradient: linear-gradient(180deg, #AA2D05 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31405 %>
    --party-gradient: linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%);
    --party-footer-bg-gradient: linear-gradient(180deg, #AD0969 22.06%, rgba(173, 9, 105, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% else %>
    --party-gradient: linear-gradient(45deg, #0061FF, #A1DDFF);
    --party-footer-bg-gradient: linear-gradient(180deg, #0061FF 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% end %>

  }

  * {
    box-sizing: border-box;
    font-family: "Anek Telugu", sans-serif;
    color: white;
  }

  #outer-container {
    height: var(--outer-container-height);
    width: var(--outer-container-width);
    position: relative;
    overflow: hidden;
  }

  #bg-image {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/32/045e8798-f8ca-485a-a9b5-b7d628f02576.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 100%;
    width: 100%;
    mix-blend-mode: overlay;
  }

  #bg-image-2 {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/32/045e8798-f8ca-485a-a9b5-b7d628f02576.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 100%;
    width: 100%;
    mask-image: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.6),
      rgba(0, 0, 0, 0.05),
      rgba(0, 0, 0, 0.2)
    );
  }

  #bg-gradient {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--party-gradient);
  }

  #profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    top: 324px;
    left: 32px;
    width: 444px;
    gap: 12px;
    z-index: 10;
  }

  #profile-pic-container {
    width: 396px;
    height: 396px;
    border-radius: 198px;
    box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.7);
    background: var(--party-gradient);
    padding: 12px 2px 12px 12px;
  }

  #profile-pic-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: white;
    object-fit: cover;
  }

  #my-name {
    font-family: "Suravaram", serif;
    font-weight: 400;
    font-style: normal;
    font-size: 56px;
    width: 100%;
    line-height: 56px;
    text-align: center;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
  }

  #ane-nenu {
    width: 360px;
  }

  #congrats-section {
    position: absolute;
    top: 300px;
    right: 0;
    left: 440px;
    bottom: 497px;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: flex-start;
    overflow: hidden;
  }

  #message {
    width: 460px;
    margin: 8px auto 8px auto;
    font-family: "Noto Sans Telugu", serif;
    font-weight: 700;
    font-style: normal;
    font-size: 36px;
    line-height: 48px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
    text-align: center;
  }

  #name-section {
    width: 100%;
    position: relative;
    padding: 24px 0;
  }

  #paper-top {
    width: 100%;
    transform: scaleX(1.2);
    z-index: 2;
    position: absolute;
    top:-24px;
  }

  #name {
    background: white;
    color: black;
    font-size: 32px;
    text-align: center;
    width: 100%;
    position: relative;
    z-index: 5;
    font-weight: 600
  }

  #name.small {
    font-size: 24px;
  }

  .bigger {
    font-size: 1.5em;
    font-weight: 600;
    color: black;
  }

  #paper-bottom {
    width: 100%;
    transform: scaleX(1.2);
    z-index: 2;
    position: absolute;
    bottom: -2px;
  }

  #congrats {
    width: 78%;
    margin: 0 auto;
  }

  #confetti {
    position: absolute;
    top: 220px;
    width: 100%;
  }

  #fireworks {
    position: absolute;
    top: 600px;
    left: 320px;
    width: 240px;
    opacity: 0.9;
  }

  #fake-user,
  #fake-footer,
  #fake-header {
    position: absolute;
    opacity: 0.0;
    background: black;
  }

  #fake-user {
    bottom: 125px;
    right: 0;
    width: 331.25px;
    height: 372.5px;
    z-index: 2;
  }

  #fake-footer {
    width: 100%;
    height: 225px;
    bottom: 0;
    z-index: 2;
  }

  #fake-header {
    width: 100%;
    height: 300px;
    top: 0;
    z-index: 2;
  }

</style>

<div id="outer-container">
  <div id="bg-gradient"></div>
  <div id="bg-image"></div>
  <div id="bg-image-2"></div>
  
  <div id="profile-section">
    <div id="profile-pic-container"><img src="<%= leader_image_url %>" alt="" /></div>
    <div id="my-name"><%= leader_name %></div>
    <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/98162d33-f0b6-411c-8108-b17405c87b0c.png" alt="" id="ane-nenu" />
  </div>
  
  <div id="congrats-section">
    <div id="message"><%= region %><br><%= role %><br>ప్రమాణ స్వీకారం చేసిన</div>
    <div id="name-section">
      <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/0669234f-8fb8-4ca4-80dd-a1869c885bd5.png" alt="" id="paper-top">
      <% if Unicode::DisplayWidth.of(leader_name) <= 12 %>
        <div id="name">శ్రీ<span class="
                 bigger"> <%= leader_name %> </span>గారికి</div>
      <% else %>
        <div id="name" class="small">శ్రీ<span class="
                 bigger"> <%= leader_name %> </span>గారికి</div>
      <% end %>
      <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/b03b610d-406a-4c39-bc6e-7d9c08427feb.png" alt="" id="paper-bottom">
    </div>
    <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/76b0e882-8c9f-426e-801f-39756fa60374.png" alt="" id="congrats">
  </div>
 
  <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/04b12e1d-6741-4700-abf5-ab857c80d669.png" alt="" id="confetti">
  
  <img src="https://az-cdn.thecircleapp.in/production/admin-media/32/63ffd4e5-b2b9-47b2-8a33-4b5a329f39f5.png" alt="" id="fireworks">
  
  <div id="fake-user"></div>
  <div id="fake-footer"></div>
  <div id="fake-header"></div>
</div>
