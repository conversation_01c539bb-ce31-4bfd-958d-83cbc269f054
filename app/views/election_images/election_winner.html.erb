<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&family=Poppins:wght@700&display=swap");

  :root {
    --outer-container-width: 1000px;
    --outer-container-height: 1250px;
    --user-bottom: 125px;
    --protocol-height: 300px;
    --user-profile-height: 373px;

    <% if party_id == 31403 %>
    --party-gradient: linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%);
    --party-footer-bg-gradient: linear-gradient(180deg, #087748 22.06%, rgba(8, 119, 72, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31402 %>
    --party-gradient: linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%);
    --party-footer-bg-gradient: linear-gradient(180deg, #C90807 22.06%, rgba(219, 39, 38, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31406 %>
    --party-gradient: linear-gradient(308.32deg, #cc0000 43.08%, #ffb0b0 105.21%);
    --party-footer-bg-gradient: linear-gradient(180deg, #820707 22.06%, rgba(130, 7, 7, 0) 97.79%);
    --party-contrast-text-color: #ffed91;
    <% elsif party_id == 31401 || party_id == 37967 %>
    --party-gradient: linear-gradient(
      149.88deg,
      #f37022 4.18%,
      #e5fff7 52.37%,
      #0f823f 98.51%
    );
    --party-footer-bg-gradient: linear-gradient(
      180deg,
      #045c2b 24.48%,
      rgba(4, 92, 43, 0) 98.69%
    );
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31398 || party_id == 37788 %>
    --party-gradient: rgba(243, 114, 22, 1);
    --party-footer-bg-gradient: linear-gradient(180deg, #AA2D05 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31405 %>
    --party-gradient: linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%);
    --party-footer-bg-gradient: linear-gradient(180deg, #AD0969 22.06%, rgba(173, 9, 105, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% else %>
    --party-gradient: linear-gradient(45deg, #0061FF, #A1DDFF);
    --party-footer-bg-gradient: linear-gradient(180deg, #0061FF 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% end %>

  }

  * {
    box-sizing: border-box;
    font-family: "Anek Telugu", sans-serif;
  }

  #outer-container {
    height: var(--outer-container-height);
    width: var(--outer-container-width);
    background: #e2dacd;
    position: relative;
    overflow: hidden;
  }

  #bg-image {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/32/eec43215-15ad-41e8-b015-131ce0c17f73.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 100%;
    width: 100%;
    mix-blend-mode: overlay;
  }

  #bg-image-2 {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/32/eec43215-15ad-41e8-b015-131ce0c17f73.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 100%;
    width: 100%;
    mask-image: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0, 0.05), rgba(0,0,0,0.2));
    mix-blend-mode: overla;
  }

  #bg-gradient {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--party-gradient);
  }

  #outer-container-content {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  /* Lead Candidate Styling */
  #lead-contestant-container {
    width: 100%;
    margin-top: var(--protocol-height);
    position: relative;
  }

  #profile-picture-container {
    position: absolute;
    aspect-ratio: 1;
    width: 450px;
    left: 30px;
    top: 0;
    z-index: 30;
  }

  #profile-bg {
    aspect-ratio: 1;
    width: 100%;
    border-radius: 50%;
    position: relative;
    padding-top: 20px;
    padding-left: 20px;
    box-shadow: 4px 4px 4px 0 #00000040;
    background: var(--party-gradient);
  }

  #profile-bg img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  #party-icon {
    position: absolute;
    height: 150px;
    width: 150px;
    overflow: hidden;
    bottom: 0;
    right: -35px;
    border: 4px solid white;
    border-radius: 50%;
    box-shadow: 4px 4px 4px 0 #00000026;
  }

  #party-icon img {
    object-fit: cover;
    object-position: top center;
    width: 100%;
    height: 100%;
  }

  /* Header Content styling */

  #header-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: start;
    margin-top: 32px;
    font-size: 32px;
    margin-left: 144px;
    font-weight: 600;
  }

  #title {
    padding-left: 10px;
  }

  #subtitle {
    background-color: #af1919;
    padding: 8px 10px 0 10px;
    width: fit-content;
    color: white;
  }

  #name-container {
    position: absolute;
    width: 55%;
    height: 100%;
    right: 0;
    top: 0;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 20;
  }

  #name {
    background-color: rgba(0, 0, 0, 0.3);
    width: 100%;
    text-align: left;
    font-weight: bold;
    font-size: 48px;
    padding: 20px 20px 20px 80px;
  }

  #name .sir {
    font-weight: normal;
    font-size: 24px;
  }

  #congratulations-img {
    margin-top: -32px;
    width: 396px;
    z-index: 20;
  }

  #fire-crackers-2 {
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/2d3a790e-d0a7-45bd-86c3-7974152fa36d.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: calc(var(--protocol-height) - 96px);
    left: -24px;
    height: 300px;
    width: 300px;
    transform: scale(1.5);
    opacity: 0.25;
    z-index: 1;
  }

  #fire-crackers-1 {
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/2d3a790e-d0a7-45bd-86c3-7974152fa36d.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: calc(var(--protocol-height) - 64px);
    right: 0;
    height: 300px;
    width: 300px;
    transform: scale(1.5);
    opacity: 0.25;
    z-index: 1;
    mix-blend-mode: screen;
  }

  #fire-crackers-3 {
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/2d3a790e-d0a7-45bd-86c3-7974152fa36d.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 360px;
    height: 300px;
    width: 300px;
    transform: scale(1.8);
    opacity: 0.1;
    z-index: 1;
    mix-blend-mode: screen;
  }

  #ribbons {
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/09d50b53-9667-4695-972f-b1a040c73f24.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: translateY(-60px);
    opacity: 1;
    z-index: 4;
  }

  #ribbons-role {
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/09d50b53-9667-4695-972f-b1a040c73f24.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: translateY(200px) rotateY(180deg);
    opacity: 1;
    z-index: 11;
  }

  #role-container {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    top: calc(var(--protocol-height) + 450px + 32px);
    left: 30px;
    color: white;
    width: 420px;
    z-index: 20;
    gap: 8px;
  }

  #role-parent {
    position: relative;
    height: 100px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #back-layer,
  #front-layer {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
  }

  #role {
    margin: auto;
    width: 100%;
    text-align: center;
    font-size: 96px;
    font-weight: bold;
    letter-spacing: 8px;
    background: linear-gradient(
      254.45deg,
      #a68320 9.17%,
      #e7b42d 20.74%,
      #ffea8e 49.6%,
      #e7b42d 66.63%,
      #a68320 89.12%
      );
    background-clip: text;
    --webkit-background-clip: text;
    color: transparent;
  }

  #role-shadow {
    margin: auto;
    width: 100%;
    text-align: center;
    font-size: 96px;
    font-weight: bold;
    letter-spacing: 8px;
    color: white;
    text-shadow: 4px 4px black;
  }

  #constituency-container {
    margin: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    font-weight: 600;
  }

  #constituency-container svg {
    width: 40px;
    height: 40px;
    fill: white;
    transform: translateY(-6px);
  }

  #constituency-container span {
    font-size: 38px;
    font-weight: 500;
    text-shadow: 2px 2px 6px black;
  }

  #fake-user,
  #fake-footer,
  #fake-header {
    position: absolute;
    opacity: 0.0;
    background: black;
  }

  #fake-user {
    bottom: 125px;
    right: 0;
    width: 331.25px;
    height: 372.5px;
    z-index: 2;
  }

  #fake-footer {
    width: 100%;
    height: 225px;
    bottom: 0;
    z-index: 2;
  }

  #fake-header {
    width: 100%;
    height: 300px;
    top: 0;
    z-index: 2;
  }

</style>

<div id="outer-container">
  <div
    id="bg-gradient"
  ></div>
  <div id="bg-image"></div>
  <div id="bg-image-2"></div>

  <div id="outer-container-content">
    <div id="lead-contestant-container">
      <div id="profile-picture-container">
        <div
          id="profile-bg"
        >
          <img
            src="<%= leader_image_url %>"
             alt="Leader Image"/>
          <% if party_icon_url.present? %>
            <div id="party-icon">
              <img
                src="<%= party_icon_url %>"
                 alt="Party Icon"/>
            </div>
          <% end %>
        </div>
      </div>
      <div id="name-container">
        <div id="header-content">
          <div id="title"><%= title %></div>
          <div id="subtitle"><%= sub_title %></div>
        </div>
        <div id="name"><%= name %> <span class="sir">గారికి</span></div>
        <img
          id="congratulations-img"
          src="https://az-cdn.thecircleapp.in/production/admin-media/32/9048b8d9-f615-4908-b97a-f624f4002f31.png"
          alt="Congratulations" />
      </div>
    </div>
    <div id="role-container">
      <div id="role-parent">
        <div id="back-layer">
          <div id="role-shadow"><%= role %></div>
        </div>
        <div id="front-layer">
          <div id="role"><%= role %></div>
        </div>
      </div>
      <div id="constituency-container">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960">
          <path
            d="m438-540-28-28q-12-12-28-12t-28 12q-12 12-12 28.5t12 28.5l56 57q12 12 28 12t28-12l142-142q12-12 12-28.5T608-653q-12-12-28.5-12T551-653L438-540Zm362-12q0 45-17.5 94.5t-51 103Q698-301 648-244T533-127q-11 10-25 15t-28 5q-14 0-28-5t-25-15q-65-60-115-117t-83.5-110.5q-33.5-53.5-51-103T160-552q0-150 96.5-239T480-880q127 0 223.5 89T800-552Z"
            />
        </svg>
        <span><%= constituency %></span>
      </div>
    </div>
    <div id="ribbons"></div>
    <div id="ribbons-role"></div>
    <div id="fire-crackers-1"></div>
    <div id="fire-crackers-2"></div>
    <div id="fake-user"></div>
    <div id="fake-footer"></div>
    <div id="fake-header"></div>
  </div>
</div>
