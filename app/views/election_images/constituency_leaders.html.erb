<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&family=Poppins:wght@700&display=swap");

  :root {
    --outer-container-dimensions: 1024px;
    --praja-logo-width: 120px;
  }

  #outer-container {
    height: var(--outer-container-dimensions);
    width: var(--outer-container-dimensions);
    background-image: url("https://az-cdn.thecircleapp.in/production/admin-media/40/b6a6f92f-85ed-4f59-a2d3-33f5bd168c92.png");
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* Header Styling */
  #header-container {
    display: flex;
    flex-direction: column;
    width: 70%;
    align-items: start;
    margin-top: 30px;
  }

  #title {
    font-size: 48px;
    color: white;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
    padding-left: 20px;
  }

  #subtitle {
    background-color: rgba(148, 4, 4, 0.8);
    width: fit-content;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-size: 48px;
    font-weight: 600;
    padding: 10px 20px 0 20px;
  }

  #constituency-name {
    margin-top: 20px;
    border: 1px solid white;
    color: white;
    font-family: "Anek Telugu", sans-serif;
    font-size: 38px;
    font-weight: 500;
    padding: 10px 20px 0 20px;
    border-radius: 8px;
    margin-left: 10px;
  }

  /* Leader Images Styling */

  #leaders-container {
    flex-grow: 1;
    width: 90%;
    margin: auto;
    margin-top: 36px;
    display: flex;
    flex-direction: row;
    gap: 24px;
    overflow: hidden;
    justify-content: space-evenly;
  }

  #leader-ui {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    max-width: 220px;
  }

  #party-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 4px solid white;
  }

  #party-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top center;
  }

  #leader-image {
    max-width: 220px;
    height: 220px;
    margin-top: 20px;
    overflow: hidden;
    border-radius: 0 72px 0 72px;
  }

  #leader-image img {
    max-width: 200px;
    height: 200px;
    object-fit: cover;
    margin-left: 20px;
    margin-bottom: 20px;
    border-radius: 0 72px 0 72px;
  }

  #leader-name {
    margin-top: 14px;
    max-width: 220px;
    font-size: 26px;
    color: white;
    text-align: center;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
  }

  #leader-name-small {
    margin-top: 14px;
    max-width: 220px;
    font-size: 20px;
    color: white;
    text-align: center;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
  }

  /* Duration Styling */
  #duration-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    width: 90%;
    margin: auto;
    padding: 20px 20px;
    background: #834318;
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 40px;
  }

  #duration-text {
    color: white;
    font-size: 38px;
    font-family: "Anek Telugu", sans-serif;
    font-weight: 600;
    margin-left: 20px;
    margin-right: 20px;
  }

  #divider {
    flex: 1;
    height: 1px;
    background-color: white;
  }

  /* Footer Styling */
  #footer-container {
    position: relative;
    width: 100%;
    padding: 20px 0;
    background: white;
    display: flex;
    justify-content: flex-end;
  }

  #footer-container img {
    position: absolute;
    top: -20px;
    left: 80px;
    height: 120px;
    width: 120px;
    border-radius: 8px;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3), 0 6px 6px rgba(0, 0, 0, 0.23);
  }

  #footer-text {
    height: 100%;
    width: calc(100% - var(--praja-logo-width) - 80px);
    font-size: 48px;
    font-weight: 600;
    font-family: "Anek Telugu", sans-serif;
    text-align: center;
    align-content: center;
  }

  #red-text {
    color: red;
  }

</style>

<div id="outer-container">
  <div id="header-container">
    <div id="title"><%= title %></div>
    <div id="subtitle"><%= sub_title %></div>
    <div id="constituency-name"><%= constituency_name %> </div>
  </div>
  <div id="leaders-container">
    <% leaders.each do |leader| %>
      <div id="leader-ui">
        <div id="party-icon">
          <img src="<%= leader[:party_icon_url] %>" alt="Party icon" />
        </div>
        <div
          id="leader-image"
          style="background: <%= leader[:css_background] %>"
        >
          <img src="<%= leader[:photo_url] %>" alt="Leader image" />
        </div>
        <% if Unicode::DisplayWidth.of(leader[:name]) < 15 %>
          <div id="leader-name"><%= leader[:name] %></div>
        <% else %>
          <div id="leader-name-small"><%= leader[:name] %></div>
        <% end %>
      </div>
    <% end %>
  </div>
  <div id="duration-container">
    <div id="divider"></div>
    <div id="duration-text"><%= announcement_text %></div>
    <div id="divider"></div>
  </div>
  <div id="footer-container">
    <img
      src="https://az-cdn.thecircleapp.in/production/admin-media/40/e6b78930-0d82-450d-8530-f09318771812.png"
      alt="praja logo"
      />
    <div id="footer-text">
      <span>ప్రజా యాప్‌లో &nbsp;</span>
      <span id="red-text"> లైవ్ అప్‌డేట్స్</span>
    </div>
  </div>
</div>
