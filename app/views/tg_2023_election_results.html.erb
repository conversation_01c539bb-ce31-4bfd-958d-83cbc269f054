<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@600&family=Ramabhadra&display=swap" rel="stylesheet">

<style>
#outer-container {
  width: 1200px;
  height: 700px;
  margin: auto;
  background: url("https://cdn.thecircleapp.in/production/admin-media/32/39b9905e3cdf4121b65d027a89c99503.jpg");
  color: white;
  position: relative;
}

#result-list {
  position: absolute;
  bottom: 24px;
  right: 24px;
  left: 24px;
  display: flex;
  gap: 24px;
}

.result {
  flex: 1;
  height: 380px;
  background: #252525;
  border-radius: 8px;
  inset: 24px;
  text-align: center;
}

.result h2 {
  font-size: 36px;
  margin: 18px 0 12px 0;
  font-family: 'Noto Sans Telugu', sans-serif;
}

.result img {
  width: 144px;
  height: 144px;
  border-radius: 72px;
  box-shadow: 3px 3px 12px rgba(0,0,0, 0.48); 
  margin: 0 auto;
}

.result.with-win h2 {
  font-size: 20px;
}

.result.with-win img {
  width: 102px;
  height: 102px;
  margin: -8px auto 0 auto;
}

.result.winner-declared.with-win img.winner-badge {
  width: 144px;
  height: 28px;
  box-shadow: none;
  margin-top: 8px;
  border-radius: 0;
  opacity: 0;
}

.result.winner-declared.with-win.winner img.winner-badge {
  opacity: 1;
}

.result.winner-declared.with-win img {
  width: 144px;
  height: 144px;
}

.result.winner-declared.with-win h1 {
  font-size: 80px;
  margin-top: -24px;
}

.result h1 {
  font-size: 72px;
  margin: -12px 0 0 0;
  font-family: 'Ramabhadra', sans-serif;
}

.result h3 {
  font-size: 28px;
  margin: -40px 0 0 0;
  font-family: 'Ramabhadra', sans-serif;
}

.result.with-win .lead h1 {
  font-size: 48px;
  margin: -12px 0 0 0;
}

.result.with-win .lead h3 {
  font-size: 20px;
  margin: -32px 0 0 0;
}

.result.with-win h1 {
  margin: -12px 0 0 0;
}
.result.with-win h3 {
  margin: -44px 0 0 0;
}

#time-container {
  position: absolute;
  top: 112px;
  right: 120px;
  color: white;
  font-family: 'Ramabhadra', sans-serif;
  text-align: center;
}

#time-container h2,h4 {
  margin: 0;
}

#time-container h2 {
  line-height: 1.0;
  font-size: 26px;
}

#time-container h4 {
  font-size: 17px;
}
</style>

<div id="outer-container">
  <div id="result-list">
    <% data_hash.each do |tg_party| %>
      <div class="result <%= show_win_count ? 'with-win ' : '' %><%= winner_declared ? 'winner-declared ' : '' %><%= tg_party['winner'] ? 'winner' : '' %>" style="background: linear-gradient(to top, <%= tg_party['color'].to_s %>, #192044);">
        <h2><%= tg_party['display_name'].to_s %></h2>
        <img src="<%= tg_party['icon'].to_s %>" />

        <% if !show_win_count %>
          <h1><%= tg_party['seat_count'].to_s %></h1>
          <h3>LEAD</h3>
        <% end %>

        <% if show_win_count && !winner_declared %>
          <div class="lead">
            <h1><%= tg_party['seat_count'].to_s %></h1>
            <h3>LEAD</h3>
          </div>
          <h1><%= tg_party['win_count'].to_s %></h1>
          <h3>WINS</h3>
        <% end %>

        <% if winner_declared %>
          <img class="winner-badge" src="https://cdn.thecircleapp.in/production/admin-media/32/fcd7d2e2daef06d2c4e4d305a4689df7.png" />
          <h1><%= tg_party['win_count'].to_s %></h1>
          <h3>WINS</h3>
        <% end %>
      </div>
    <% end %>
  </div>

  <% if updated_at_time.present? && updated_at_date.present? %>
  <div id="time-container">
    <h2><%= updated_at_time %></h2>
    <h4>డిసెంబర్ <%= updated_at_date %>, 2023</h4>
  </div>
  <% end %>
</div>