<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@500&display=swap" rel="stylesheet">

<div class="invite-card">
  <div class="invite-card-content">
    <div class="invite-card-user-avatar">
      <div class="invite-card-user-ring <%= user[:badge].present? ? user[:badge][:badge_ring].downcase.dasherize : '' %>"></div>
      <div class="invite-card-user-picture" style="<%= user[:photo_url].present? ? "background: #fff url(#{user[:photo_url]}) center center; background-size: cover;" : '' %>">
        <% if user[:badge].present? && user[:badge][:icon_url].present? %>
        <div
          class="invite-card-user-badge-icon"
          style="background: transparent url('<%= user[:badge][:icon_url] %>') no-repeat center center; background-size: cover;">
        </div>
        <% end %>
      </div>
    </div>
    <% if user[:badge].present? %>
    <div class="invite-card-user-badge <%= user[:badge][:badge_banner].present? ? user[:badge][:badge_banner].downcase.dasherize : "no" %>-badge">
      <div class="badge-left"></div>
      <div class="badge-center">
        <div class="badge-description"><%= user[:badge][:description] %></div>
      </div>
      <div class="badge-right"></div>
    </div>
    <% end %>
    <div class="invite-card-user-name">
      <span><%= user[:name] %></span>
    </div>
    <br />
    <br />
    <div class="invite-card-footer">
      <div class="footer-logo"></div>
<!--      <div class="footer-text">&#3122;&#3147; &#3112;&#3112;&#3149;&#3112;&#3137; &#3115;&#3134;&#3122;&#3147; &#3077;&#3125;&#3149;&#3125;&#3074;&#3105;&#3135;</div>-->
      <div class="footer-text">లో నన్ను ఫాలో అవ్వండి</div>
    </div>
  </div>
</div>

<style>
  .invite-card {
    width: 1000px;
    height: 1000px;
    background: url('https://az-cdn.thecircleapp.in/assets/invite-card.png') center center;
    background-size: cover;
    font-family: 'Noto Sans Telugu', sans-serif;
    position: relative;
  }

  .invite-card-content {
    width: 700px;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .invite-card-user-avatar {
    position: relative;
    z-index: 999;
    width: 360px;
    height: 360px;
    border-radius: 100%;
    margin: auto;
    top: 100px;
  }

  .invite-card-user-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 360px;
    height: 360px;
    border-radius: 100%;
  }

  /* Silver Ring */
  .invite-card-user-ring.silver-ring {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/s_strip.svg') center center;
    background-size: cover;
  }

  /* Gold Ring */
  .invite-card-user-ring.gold-ring {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/g_strip.svg') center center;
    background-size: cover;
  }

  .invite-card-user-picture {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 340px;
    height: 340px;
    border-radius: 100%;
    background: #fff url('https://az-cdn.thecircleapp.in/assets/logo.png') center center;
    background-size: cover;
  }

  .invite-card-user-badge-icon {
    position: absolute;
    bottom: 30px;
    right: -35px;
    width: 84px;
    height: 108px;
  }

  .invite-card-user-badge {
    position: relative;
    height: 50px;
    width: fit-content;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    top: 75px;
    max-width: 600px;
  }
  .p-relative {
    position: relative;
  }
  .invite-card-user-badge .badge-left, .invite-card-user-badge .badge-right {
    position: absolute;
    width: 30px;
    height: 50px;
    top: 12px;
  }
  .invite-card-user-badge .badge-left {
    left: -19px;
  }
  .invite-card-user-badge .badge-right {
    right: -19px;
  }
  .invite-card-user-badge .badge-center {
    position: relative;
    height: 50px;
    z-index: 100;
    margin: auto;
  }
  .invite-card-user-badge .badge-center .badge-description {
    position: relative;
    top: 10px;
    text-align: center;
    font-size: 1.5rem;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 0 10px;
    box-sizing: border-box;
  }

  /* Silver badge */
  .invite-card-user-badge.silver-badge .badge-left {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/s_left.png') right center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.silver-badge .badge-right {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/s_right.png') left center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.silver-badge .badge-center {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/s_center.png') center center;
    background-size: 100% 100%;
  }

  /* Gold badge */
  .invite-card-user-badge.gold-badge .badge-left {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/g_left.png') right center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.gold-badge .badge-right {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/g_right.png') left center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.gold-badge .badge-center {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/g_center.png') center center;
    background-size: 100% 100%;
  }

  /* White badge */
  .invite-card-user-badge.white-badge .badge-left {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/w_left.png') right center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.white-badge .badge-right {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/w_right.png') left center;
    background-size: 100% 100%;
  }
  .invite-card-user-badge.white-badge .badge-center {
    background: url('https://az-cdn.thecircleapp.in/assets/badges/w_center.png') center center;
    background-size: 100% 100%;
  }

  .invite-card-user-name {
    height: 200px;
    background-color: #213195;
    border-radius: 20px;
    position: relative;
    text-align: center;
  }
  .invite-card-user-name span {
    display: -webkit-box;
    max-width: 600px;
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    top: 100px;
    color: white;
    font-size: 2.2rem;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .invite-card-footer {
    margin: auto;
  }
  .footer-logo {
    background: url('https://az-cdn.thecircleapp.in/assets/praja-full-logo.png') no-repeat center center;
    background-size: contain;
    height: 60px;
  }
  .footer-text {
    font-size: 1.6rem;
    color: white;
    text-align: center;
    margin-top: 10px;
  }
</style>
