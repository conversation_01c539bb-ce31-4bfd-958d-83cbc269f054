<style>
  /* latin-ext */
  @font-face {
    font-family: 'DM Serif Display';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/dmserifdisplay/v15/-nFnOHM81r4j6k0gjAW3mujVU2B2G_5x0vrx52jJ3Q.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'DM Serif Display';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/dmserifdisplay/v15/-nFnOHM81r4j6k0gjAW3mujVU2B2G_Bx0vrx52g.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* telugu */
  @font-face {
    font-family: 'Dhurjati';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/dhurjati/v24/_6_8ED3gSeatXfFiFU3pQqUPuiA3cBc.woff2) format('woff2');
    unicode-range: U+0951-0952, U+0964-0965, U+0C00-0C7F, U+1CDA, U+200C-200D, U+25CC;
  }

  /* telugu */
  @font-face {
    font-family: 'Ramabhadra';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/ramabhadra/v15/EYq2maBOwqRW9P1SQ83LSghMXrmV03t9Qw.woff2) format('woff2');
    unicode-range: U+0951-0952, U+0964-0965, U+0C00-0C7F, U+1CDA, U+200C-200D, U+25CC;
  }

  #outer-container {
    width: 1200px;
    height: 700px;
    margin: auto;
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/32/cc4266b1700f1e83c3bb66e17f5ff21f.jpg");*/
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/3f8f46678f38c3d0f71398deee17504f.jpg");*/
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/46686bb48373a62d413aa54f784cf118.jpg");*/
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/9f1d3864d44c0088d92a25951645a762.jpg");*/
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/ac18faaffab89f97dee088eadeff063b.jpg");*/
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/3e90cff428bbc7b15bd2a018ab52a818.jpg");*/
    background: url("https://az-cdn.thecircleapp.in/production/admin-media/10/be8ce47eaa0bd29034e2b052ae782536.jpg");
    /*background: url("https://cdn.thecircleapp.in/production/admin-media/10/17b56e6bc324f41ed2a21a4c40e17aba.jpg");*/
    position: relative;
  }

  #user-outer-container {
    position: absolute;
    left: 88px;
    width: 360px;
    height: 360px;
    bottom: 102px;
  }

  #user-inner-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  #party-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    z-index: 0;
  }

  #leader-photo {
    left: 50%;
    top: 50%;
    position: absolute;
    width: 85%;
    height: 85%;
    border-radius: 50%;
    object-fit:cover;
    z-index: 1;
    transform: translate(-50%, -50%);
  }

  #badge {
    position: absolute;
    right: -8%;
    bottom: 0;
    width: 108px;
    z-index: 3;
  }

  h1 {
    font-family: 'Dhurjati', 'DM Serif Display', serif;
    color: #fff799;
    text-align: center;
    font-size: 64px;
    text-shadow: 3px 3px 10px rgba(0, 0, 0,0.48), 3px 3px 20px rgba(0, 0, 0,0.16);
    line-height: 1.1;
    margin: 0;
    font-weight: 400;
  }

  /* Need to assign class to h1 tag based on whether name is telugu or english*/
  h1.telugu {
    font-size: 80px; /*telugu font size*/
    letter-spacing: 2px;
    font-weight: 400;
  }

  h1 .small {
    font-size: 48px;
  }

  h3 {
    font-family: 'Ramabhadra', sans-serif;
    color: white;
    text-align: center;
    font-size: 46px;
    line-height: 1.2;
    margin: 0;
    font-weight: normal;
  }

  #info-container {
    position: absolute;
    left: 480px;
    right: 32px;
    bottom: 218px;
    top: 192px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
  }

  #constituency_name {
    font-size: 60px;
  }
</style>

<div id="outer-container">
  <div id="user-outer-container">
    <div id="user-inner-container">
      <% puts "data -- #{data}" %>
      <img src="<%= data[:party_logo_url] %>" id="party-photo"  alt="party logo"/>
      <img src="<%= data[:candidate_image_url] %>" id="leader-photo"  alt="mla photo"/>
      <% if data[:party_badge_url].present? %><img src="<%= data[:party_badge_url] %>" alt="party badge" id="badge"><% end %>
    </div>
  </div>
  <div id="info-container">
    <h3><span id="constituency_name"><%= data[:constituency_name] %></span><br>నియోజకవర్గం నుండి గెలుపొందిన</h3>
    <h1 class="telugu"><%= data[:candidate_name] %> <span class="small">గారికి</span></h1>
  </div>
</div>
