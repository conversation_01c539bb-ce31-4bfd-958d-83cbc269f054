<style>
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Noto+Sans+Telugu:wght@400;500;700&display=swap');

#outer-container {
  width: 1200px;
  height: 630px;
  position: relative;
  margin: auto;
  background: white;
  overflow: hidden;
}

#poster-blur-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  
  background-image: linear-gradient(rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.55)), url("<%= creative_image_url %>");
  background-size: cover;
  background-position: top;
  z-index: 0;
}

#poster-blur-bg::before {
  content: "";
  backdrop-filter: blur(16px);
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

#inner-container {
  z-index: 2;
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  gap: 32px;
}

#content {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  gap: 72px;
}

#content p {
  width: 454px;
  font-size: 48px;
  color: white;
  font-weight: 400;
  line-height: 112px;
  margin: 0;
}

.highlight {
  font-size: 80px;
  font-weight: 500;
  color: white;
}

#frame {
  flex: 1;
  height: calc(100% - 48px);
  margin: 24px;
  position: relative;
}

#frame img {
  width: 100%;
  height: 100%;
  border: 8px solid white;
  box-shadow: 0 0 24px #333;
  object-fit: cover;
}

#frame svg {
  width: 96px;
  height: 96px;
  position: absolute;
  bottom: 16px;
  right: 24px;
  fill: white;
}

#bottom-bar {
  justify-content: center;
  display: flex;
  align-items: center;
  color: white;
}

#app {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 16px;
}

#app img.icon {
  width: 96px;
  height: 96px;
  border-radius: 24px;
  border: 2px solid #cacaca;
}

#app img.logo {
  height: 64px;
}

body {
  background: white;
  font-family: 'Noto Sans Telugu','Lato',  sans-serif;
}

* {
  box-sizing: border-box;
}
</style>
<div id="outer-container">
  <div id="poster-blur-bg"></div>
  <div id="inner-container">
    <div id="frame"><img src="<%= creative_image_url %>" alt="">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 40" x="0px" y="0px">
        <path d="M6.11,27.24c.03,.04,.06,.06,.09,.09s.06,.05,.09,.07c.03,.03,.06,.05,.09,.07,2.61,2.2,5.95,3.52,9.62,3.52s7.03-1.32,9.63-3.53c.04-.02,.08-.06,.12-.09,.02-.01,.04-.03,.06-.04,3.17-2.76,5.18-6.82,5.18-11.34,0-8.26-6.72-14.99-14.99-14.99S1.01,7.73,1.01,15.99c0,4.49,1.97,8.51,5.1,11.25ZM16,8.04c2.64,0,4.78,2.14,4.78,4.78s-2.14,4.78-4.78,4.78-4.78-2.15-4.78-4.78,2.14-4.78,4.78-4.78Zm-3.21,11.77c1.06-.14,2.13-.21,3.21-.21,1.07,0,2.14,.07,3.21,.21,3.11,.41,5.29,3.16,4.94,6.27v.03c-2.23,1.8-5.07,2.88-8.15,2.88s-5.93-1.09-8.16-2.89v-.04c-.34-3.09,1.84-5.84,4.95-6.25Z" />
      </svg>
    </div>
    <div id="content">

      <p>మీ <span class="highlight">పేరు</span> &<br><span class="highlight">ఫోటో</span>తో<br><span class="highlight">పోస్టర్</span> పొందండి</p>
      <div id="bottom-bar">
        <div id="app">
          <img class="icon" src="https://az-cdn.thecircleapp.in/120x120/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg" />
          <img src="https://az-cdn.thecircleapp.in/assets/praja-text-logo-white.png" alt="" class="logo">
        </div>
      </div>
    </div>

  </div>

</div>
