<style>
  @import url("https://fonts.googleapis.com/css2?family=Anek+Telugu:wght@100..800&family=Poppins:wght@700&display=swap");

  :root {
    <% if party_id == 31403 %>
    --party-gradient: linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%);
    --party-footer-bg-gradient: linear-gradient(0deg, #087748 22.06%, rgba(8, 119, 72, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31402 %>
    --party-gradient: linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%);
    --party-footer-bg-gradient: linear-gradient(0deg, #C90807 22.06%, rgba(219, 39, 38, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31406 %>
    --party-gradient: linear-gradient(308.32deg, #cc0000 43.08%, #ffb0b0 105.21%);
    --party-footer-bg-gradient: linear-gradient(0deg, #820707 22.06%, rgba(130, 7, 7, 0) 97.79%);
    --party-contrast-text-color: #ffed91;
    <% elsif party_id == 31401 || party_id == 37967 %>
    --party-gradient: linear-gradient(
      149.88deg,
      #f37022 4.18%,
      #e5fff7 52.37%,
      #0f823f 98.51%
    );
    --party-footer-bg-gradient: linear-gradient(
      0deg,
      #045c2b 24.48%,
      rgba(4, 92, 43, 0) 98.69%
    );
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31398 || party_id == 37788 %>
    --party-gradient: rgba(243, 114, 22, 1);
    --party-footer-bg-gradient: linear-gradient(0deg, #AA2D05 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% elsif party_id == 31405 %>
    --party-gradient: linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%);
    --party-footer-bg-gradient: linear-gradient(0deg, #AD0969 22.06%, rgba(173, 9, 105, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% else %>
    --party-gradient: linear-gradient(45deg, #0061FF, #A1DDFF);
    --party-footer-bg-gradient: linear-gradient(0deg, #0061FF 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
    --party-contrast-text-color: #121212;
    <% end %>

  }

  * {
    box-sizing: border-box;
  }

  #outer-container {
    position: relative;
    height: 1000px;
    width: 800px;
    overflow: hidden;
    background: var(--party-gradient);
  }
  #content-container {
    position: relative;
    margin-top: 304px;
  }

  #leader-container {
    display: flex;
    height: 100%;
    width: calc(100% - 320px);
    flex-direction: column;
    justify-content: start;
    align-items: center;
    box-sizing: border-box;
    text-align: center;
  }
  #leader-container > * {
    z-index: 4;
  }

  #avatar-container {
    position: relative;
  }

  #avatar {
    height: 320px;
    width: 320px;
    border-radius: 50%;
    box-sizing: border-box;
    border: 4px solid white;
    object-fit: cover;
  }

  #badge-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 96px;
    width: 96px;
    object-fit: contain;
  }

  #name h1 {
    color: #fff;
    font-size: 48px;
    margin: 20px 0px 0px 0px;
    font-family: "Anek Telugu", sans-serif;
    line-height: 1.25;
    box-sizing: border-box;
    padding: 0 16px;
  }

  #name h2 {
    color: #fff;
    font-size: 40px;
    margin: 12px 0px 0px 0px;
    font-family: "Anek Telugu", sans-serif;
    line-height: 1.5;
    box-sizing: border-box;
    padding: 0 16px;
  }

  #position {
    font-size: 34px;
    font-weight: 500;
    width: 100%;
    color: #fff;
    font-family: "Anek Telugu", sans-serif;
    margin-top: 8px;
  }

  #constituency {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 365px;
    position: absolute;
    top: 56px;
    right: 16px;
    color: var(--party-contrast-text-color);
    text-align: center;
    font-family: "Anek Telugu", sans-serif;
  }

  #constituency > * {
    z-index: 4;
  }

  #constituency svg {
    fill: var(--party-contrast-text-color);
    width: 64px;
    height: 64px;
  }

  #constituency h1 {
    margin: 12px 0 0 0;
    font-size: 36px;
    font-weight: bold;
    border: 2px var(--party-contrast-text-color) dashed;
    border-radius: 60px;
    padding: 12px 24px 4px 24px;
  }
  #constituency h3 {
    font-size: 28px;
    margin: 16px 0 0 0;
  }

  #sunrise-bg {
    position: absolute;
    top: 64px;
    left: -192px;
    z-index: 2;
  }

  #sunrise-bg img {
    width: 800px;
    height: 800px;
    object-fit: cover;
    opacity: 0.4;
    transform: scale(1.4);
  }

  #bottom-container {
    position: absolute;
    height: 550px;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 3;
    background: var(--party-footer-bg-gradient);
  }

  #vote {
    top: 232px;
    left: 72px;
    position: absolute;
    color: white;
    z-index: 0;
    opacity: 0.8;
    width: 320px;
    text-align: center;
  }

  #vote h1 {
    margin: 0 0 0 16px;
    font-family: "Poppins", sans-serif;
    font-weight: bold;
    font-size: 56px;
    letter-spacing: 4px;
  }
</style>


<script src="https://cdn.jsdelivr.net/gh/peterhry/CircleType@2.3.1/dist/circletype.min.js"></script>
<div id="outer-container">
  <div id="content-container">
    <div id="leader-container">
      <div id="avatar-container">
        <% if avatar_url.present? %>
          <img src="<%= avatar_url %>" alt="avatar" id="avatar" />
        <% end %>
        <% if badge_icon_url.present? %>
          <img src="<%= badge_icon_url %>" alt="badge-icon" id="badge-icon" />
        <% end %>
      </div>
      <div id="name">
        <% if Unicode::DisplayWidth.of(name) < 15 %>
          <h1><%= name %></h1>
        <% else %>
          <h2><%= name %></h2>
        <% end %>
      </div>
      <div id="position"><%= position %> అభ్యర్థి</div>
    </div>
    <div id="constituency">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="24"
        viewBox="0 -960 960 960"
        width="24"
      >
        <path
          d="m438-540-28-28q-12-12-28-12t-28 12q-12 12-12 28.5t12 28.5l56 57q12 12 28 12t28-12l142-142q12-12 12-28.5T608-653q-12-12-28.5-12T551-653L438-540Zm362-12q0 45-17.5 94.5t-51 103Q698-301 648-244T533-127q-11 10-25 15t-28 5q-14 0-28-5t-25-15q-65-60-115-117t-83.5-110.5q-33.5-53.5-51-103T160-552q0-150 96.5-239T480-880q127 0 223.5 89T800-552Z"
          />
      </svg>
      <h1><%= constituency %></h1>
      <h3>నియోజకవర్గం</h3>
    </div>
  </div>
  <div id="bottom-container"></div>
  <div id="vote"><h1>VOTE FOR</h1></div>
  <div id="sunrise-bg">
    <img
      src="https://az-cdn.thecircleapp.in/production/admin-media/32/605fea86-ddee-4bb3-b6e5-8660e7152d52.png"
      />
  </div>
</div>

<script>
  new CircleType(document.querySelector("#vote h1")).radius(240);
</script>

