<%= form_for(@user_poster_layout, url: create_duplicate_admin_user_poster_layout_path(@user_poster_layout), method: :post, html: { multipart: true }) do |f| %>
  <fieldset class="inputs">
    <ol>
      <li>
        <%= f.label :entity_type %>
        <%= f.select :entity_type, ['User', 'Circle'], { selected: "User" }, { disabled: true } %>
      </li>
      <li>
        <%= f.label :entity_id, "Entity Id" %>
        <%= f.number_field :entity_id %>
      </li>
      <li>
        <%= f.label :user_poster_photo_without_background %>
        <%= f.file_field :user_poster_photo %>
      </li>
      <li>
        <%= f.label :user_poster_photo_with_background %>
        <%= f.file_field :user_poster_photo_with_background %>
      </li>
      <li>
        <%= f.label :poster_affiliated_party_id %>
        <%= f.select :poster_affiliated_party_id, options_from_collection_for_select(Circle.where(level: :political_party), "id", "name"), include_blank: true %>
      </li>
      <li>
        <%= f.label :se_user_id %>
        <%= f.select :se_user_id, options_from_collection_for_select(AdminUser.all, "id", "email"), include_blank: true %>
      </li>
    </ol>
  </fieldset>
  <fieldset class="actions">
    <ol>
      <li><%= f.submit 'Activate for User', class: 'activate-button' %></li>
    </ol>
  </fieldset>
<% end %>
