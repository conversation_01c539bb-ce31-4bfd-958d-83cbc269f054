<%= form_tag update_quality_score_admin_user_poster_layout_path, method: :post do %>
  <div>
    <label> Enter Quality Score (1-10): </label>
    <%= number_field_tag :quality_score, nil, in: 1..10, id: "quality_score" %>
  </div>
  <br>
  <div id="reason_div" style="display: none;">
    <label> Select reason for less quality score: </label>
    <%= select_tag :quality_score_reason,
                   options_for_select(["Hero photo","Party name","User photo","Circle name","Circle level","Photo clarity",
                                       "Wrong circle","Family photo","Frames mistake","Repeated frames","Name cut in frames",
                                       "Frames not selected","Protocall photo in frames","Protocall circle photo clarity", "Other"]),
                   id: "quality_score_reason", multiple: true, class: "quality-score-reason-multi-select" %>
  </div>
  <br>
  <div id="other_reason_div" style="display: none;">
    <label> Specify Reason: </label>
    <%= text_field_tag :other_reason, nil, id: "other_reason" %>
  </div>
  <br>
  <div>
    <%= submit_tag "Submit" %>
  </div>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      $(".quality-score-reason-multi-select").select2({
        placeholder: "Select reasons",
        allowClear: true,
        width: "30%"
      });
      const qualityScoreInput = document.getElementById("quality_score");
      const reasonDiv = document.getElementById("reason_div");
      const qualityReasonSelect = document.getElementById("quality_score_reason");
      const otherReasonDiv = document.getElementById("other_reason_div");
      qualityScoreInput.addEventListener("input", function() {
        const score = parseInt(qualityScoreInput.value);
        if (score < 10) {
          reasonDiv.style.display = "block";
        } else {
          reasonDiv.style.display = "none";
          otherReasonDiv.style.display = "none";
        }
      });
      $('#quality_score_reason').on('change', function() {
        const selectedValues = $(this).val() || [];
        if (selectedValues.includes("Other")) {
          otherReasonDiv.style.display = "block";
        } else {
          otherReasonDiv.style.display = "none";
        }
      });
    });
  </script>
<% end %>
