<!--<h1>Nearest Paid Users</h1>-->
<style>
  .table-header {
    color: #8B008B;
    text-align: center;
    font-size: 13px;
  }

  .center-text {
    text-align: center;
  }

  .status-container {
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: bold;
  }
</style>

<table>
  <thead>
  <tr>
    <th class="table-header">User ID</th>
    <th class="table-header">Name</th>
    <th class="table-header">Phone</th>
    <th class="table-header">Badge</th>
    <th class="table-header">Party Name</th>
    <th class="table-header">Village Name</th>
    <th class="table-header">Mandal Name</th>
    <th class="table-header">Subscription End Date</th>
    <th class="table-header">Amount</th>
    <th class="table-header">Premium Status</th>
  </tr>
  </thead>
  <tbody>
  <% @nearest_users.each do |u| %>
    <tr>
      <td>
        <a href="<%= admin_user_path(u.id) %>">
          <%= u.id %>
        </a>
      </td>
      <td><%= u.name %></td>
      <td><%= u.phone.presence %></td>
      <td><%= u.get_badge %></td>
      <td><%= u.affiliated_party_circle&.name_en %></td>
      <td><%= u.village.name_en %></td>
      <td><%= u.mandal.name_en %></td>
      <td><%= u.user_plan.end_date.strftime("%d-%m-%Y") %></td>
      <td><%= u.user_plan.amount %></td>
      <td class="center-text">
          <span class="status-container" style="color: <%= u.user_plan.end_date > Time.zone.now ? '#008000' : '#FF0000' %>;">
            <%= u.user_plan.end_date > Time.zone.now ? 'RUNNING' : 'STOPPED' %>
          </span>
      </td>
    </tr>
  <% end %>
  </tbody>
</table>
