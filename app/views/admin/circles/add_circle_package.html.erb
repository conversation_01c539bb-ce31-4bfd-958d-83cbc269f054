<%= form_for @circle, url: add_circle_package_on_circle_admin_circle_path(@circle), method: :post, html: { class: 'formtastic package' } do |f| %>
  <fieldset class="inputs">
    <ol>
      <li><%= f.label :name, class: 'label' %><%= f.text_field :name, disabled: true %></li>
      <li>
        <%= label_tag :circle_package_mapping_id, "Circle Package"%>
        <%= select_tag "circle[circle_package_id]", options_from_collection_for_select(CirclePackage.all, :id, :name), include_blank: true%>
      </li>

      <li>
        <%= f.label :start_date%><%= f.date_field :start_date %>
      </li>

      <li><%= f.label :end_date%><%= f.date_field :end_date %></li>

      <li>
        <%= label_tag :relationship_manager_id, "Relationship Manager"%>
        <%= select_tag "circle[relationship_manager_id]", options_from_collection_for_select(AdminUser.all, :id, :email, @circle&.relationship_manager_id), include_blank: true%>
      </li>
    </ol>
  </fieldset>
  <fieldset class="actions">
    <ol><li><%= f.submit 'Add Circle Package', class: 'button' %></li></ol>
  </fieldset>
<% end %>
