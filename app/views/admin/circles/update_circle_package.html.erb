<%= form_for @circle, url: update_circle_package_on_circle_admin_circle_path(@circle), method: :post, html: { class: 'formtastic package' } do |f| %>
  <%= f.hidden_field :id %>
  <%= f.hidden_field :circle_package_mapping_id, value: @circle_package_mapping.id %>
  <fieldset class="inputs">
    <ol>
      <li>
        <%= f.label :name, "Name" %>
        <%= f.text_field :name, disabled: true%>
      </li>

      <li >
        <%= f.label :circle_package_mapping_id, "Package" %>
        <%= select_tag "circle[circle_package_id]", options_from_collection_for_select(CirclePackage.all, :id, :name, @circle_package_mapping.circle_package_id) %>
      </li>

      <li>
        <%= f.label :start_date, "Start Date" %>
        <%= f.date_field :start_date, value: @circle_package_mapping.start_date.try(:strftime, "%Y-%m-%d")%>
      </li>

      <li>
        <%= f.label :end_date, "End Date" %>
        <%= f.date_field :end_date, value: @circle_package_mapping.end_date.try(:strftime, "%Y-%m-%d") %>
      </li>

      <li>
        <%= label :relationship_manager_id, "Relationship Manager"%>
        <%= select_tag "circle[relationship_manager_id]", options_from_collection_for_select(AdminUser.all, :id, :email, @circle.relationship_manager_id), include_blank: true%>
      </li>
    </ol>
  </fieldset>
  <fieldset class="actions">
    <ol><li><%= f.submit 'Update Circle Package', class: 'button' %></li></ol>
  </fieldset>
<% end %>
