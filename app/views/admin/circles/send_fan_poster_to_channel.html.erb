<%= form_for :fan_poster, url: create_fan_poster_admin_circle_path(@circle), method: :post, html: { multipart: true, class: 'formtastic fan_poster' } do |f| %>
  <fieldset class="inputs">
    <ol>
<!--      <li>-->
        <%# if @circle.circle_monthly_usage.present? && (@circle.circle_monthly_usage.fan_posters_limit != 0 && !@circle.circle_monthly_usage.fan_posters_limit.blank?) %>
<!--          <p class="inline-hints">You have <%#= @circle.circle_monthly_usage.fan_posters_limit - @circle.circle_monthly_usage.fan_posters_usage %> messages usage left</p>-->
        <%# end %>
<!--      </li>-->
      <li class="input string required" id="fan_poster_name_input">
        <%= f.label :circle, class: 'label' %>
        <%= f.text_field :circle, value: @circle.name, disabled: true %>
      </li>

      <li class="input select optional" id="fan_poster_creative_kind_input">
        <%= f.label :creative_kind, class: 'label' %>
        <%= f.select :creative_kind, PosterCreative.creative_kinds.keys, { include_blank: true } %>
      </li>

      <li class="input file optional" id="fan_poster_photo_v3_input">
        <%= f.label :photo_v3, class: 'label' %>
        <%= f.file_field :photo_v3 %>
        <p class="inline-hints">Aspect ratio - 800x1000</p>
      </li>

      <li class="input string optional" id="fan_poster_start_time_input">
        <%= f.label :start_time, 'Poster Start Date', class: 'label' %>
        <%= f.datetime_field :start_time %>
      </li>

      <li class="input string optional" id="fan_poster_end_time_input">
        <%= f.label :end_time, 'Poster End Date', class: 'label' %>
        <%= f.datetime_field :end_time %>
      </li>

      <li class="input boolean optional" id="fan_poster_send_dm_message_notification_input">
        <%# if @circle.circle_monthly_usage.channel_notification_limit.blank? || @circle.circle_monthly_usage.channel_notification_usage < @circle.circle_monthly_usage.channel_notification_limit %>
          <%#= f.label :send_dm_message_notification, class: "" do %>
            <%#= f.check_box :send_dm_message_notification, id: "fan_poster_send_dm_message_notification_input" %>
<!--            Send DM Message Notification-->
          <%# end %>
          <%# if @circle.circle_monthly_usage.channel_notification_limit.present? %>
<!--            <p class="inline-hints">You have <%#= @circle.circle_monthly_usage.channel_notification_limit - @circle.circle_monthly_usage.channel_notification_usage %> notifications usage left</p>-->
          <%# end %>
        <%# else %>
          <%#= f.label :send_dm_message_notification, class: "" do %>
            <%#= f.check_box :send_dm_message_notification, disabled: true, id: "fan_poster_send_dm_message_notification_input" %>
<!--            Send DM Message Notification-->
          <%# end %>
<!--          <p class="inline-hints">You have reached the limit of sending DM Message Notifications</p>-->
        <%# end %>

        <%= f.label :send_dm_message_notification, class: "" do %>
          <%= f.check_box :send_dm_message_notification, id: "fan_poster_send_dm_message_notification_input" %>
          Send DM Message Notification
        <% end %>
      </li>

      <li class="input text optional" id="fan_poster_dm_text_message_input">
        <%= f.label :dm_text_message, class: 'label' %>
        <%= f.text_field :dm_text_message %>
      </li>
    </ol>
  </fieldset>

  <fieldset class="actions">
    <ol>
      <li class="action input_action" id="fan_poster_submit_action">
        <%= f.submit "Send Fan Poster to Channel" %>
      </li>
    </ol>
  </fieldset>
<% end %>
