<%= form_for @post, url: send_post_to_channel_admin_post_path(@post), method: :post, html: { multipart: true, class: 'formtastic post_message' } do |f| %>
  <fieldset class="inputs">
    <ol>
      <li>
        <%= f.label :id, 'Post Id' %>
        <%= f.text_field :id, value: @post.id, disabled: true %>
      </li>
      <li>
        <%= f.label :circle_name, 'Circle' %>
        <%= f.text_field :circle_name, value: params[:circle_name], disabled: true %>
        <%= f.hidden_field :circle_id, value: params[:circle_id] %>
      </li>
      <li class="input boolean optional">
        <%= f.label :send_dm_message_notification do %>
          <%= f.check_box :send_dm_message_notification %>
          Send DM Message Notification
        <% end %>
      </li>
      <li>
        <%= f.label :dm_text_message %>
        <%= f.text_field :dm_text_message %>
      </li>
    </ol>
  </fieldset>
  <fieldset class="actions">
    <ol><li><%= f.submit "Send Post Message" %></li></ol>
  </fieldset>
<% end %>
