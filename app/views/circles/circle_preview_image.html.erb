<style>
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Noto+Sans+Telugu:wght@400;500;700&display=swap');

#outer-container {
  width: 600px;
  height: 315px;
  position: relative;
  margin: auto;
  background: white;
}

#details-container {
  top: 24px;
  left: 24px;
  position: absolute;
  display: flex;
  align-items: top;
  gap: 16px;
  width: calc(100% - 64px);
}

#details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#details h1 {
  margin: 0 0 2px 4px;
  font-size: 34px;
}

#details h2 {
  margin: 0 0 2px 4px;
  font-size: 26px;
}

img.dp {
  width: 188px;
  height: 188px;
  border-radius: 51px;
  object-fit: cover;
  box-shadow: 0 0 8px #efefef;
}

#app {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 16px;
  margin-right: 12px;
  gap: 8px;
}

#app img.icon {
  width: 52px;
  height: 52px;
  border-radius: 13px;
  border: 1px solid #cacaca;
}

#app img.logo {
  height: 36px;
}

#badge {
  position: relative;
  height: 64px;
  display: flex;
}

#badge img {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
}

#badge #badge-bg {
  font-size: 22px;
  font-weight: 500;
  height: 40px;
  color: #222222;
  border-radius: 18px;
  background: #DCF2FF;
  display: flex;
  margin-top: 10px;
  margin-left: 32px;
  box-shadow: 0.5px 0.5px 4px 0px rgba(0, 0, 0, 0.25);
}

#badge #badge-bg p {
  margin: 0;
  padding: 8px 20px 4px 40px;
}

#badge #badge-bg svg {
  transform: rotate(-15deg);
  height: 18px;
  width: 18px;
  display: inline;
  margin-top: 2px;
  margin-right: 6px;
}

#type {
  display: flex;
  align-items: center;
  height: 40px;
  font-size: 18px;
  font-weight: 500;
  color: #3f3f3f;
  gap: 6px;
  margin-left: 6px;
}

#type svg {
  width: 28px;
  height: 28px;
  fill: #000;
}

#bottom-bar {
  width: 100%;
  position: absolute;
  bottom: 0;
  justify-content: center;
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #dadada;
}

#bottom-bar p {
  margin: 6px 16px 0 0;
  font-size: 26px;
  font-weight: 400;
}

#dot {
  width: 8px;
  height: 8px;
  background: black;
  border-radius: 4px;
  margin: 0 12px 0 0;
}

body {
  background: #555555;
  font-family: 'Noto Sans Telugu','Lato',  sans-serif;
}
</style>

<div id="outer-container">
  <div id="details-container">
    <img class="dp" src="<%= photo_url %>" />
    <div id="details">
      <% if Unicode::DisplayWidth.of(name) < 15 %>
        <h1><%= name %></h1>
      <% else %>
        <h2><%= name %></h2>
      <% end %>

      <% if is_official %>
        <div id="badge"><img src="https://az-cdn.thecircleapp.in/production/admin-media/32/ffa06092-5ef4-4ae2-969c-357e7ad070b0.png" alt="">
            <div id="badge-bg">
              <p>అధికారిక సర్కిల్ </p>
            </div>
        </div>
      <% end %>

      <% if !is_official %>
        <div id="type">
          <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24"><rect fill="none" height="24" width="24"/><g><path d="M12,12.75c1.63,0,3.07,0.39,4.24,0.9c1.08,0.48,1.76,1.56,1.76,2.73L18,18H6l0-1.61c0-1.18,0.68-2.26,1.76-2.73 C8.93,13.14,10.37,12.75,12,12.75z M4,13c1.1,0,2-0.9,2-2c0-1.1-0.9-2-2-2s-2,0.9-2,2C2,12.1,2.9,13,4,13z M5.13,14.1 C4.76,14.04,4.39,14,4,14c-0.99,0-1.93,0.21-2.78,0.58C0.48,14.9,0,15.62,0,16.43V18l4.5,0v-1.61C4.5,15.56,4.73,14.78,5.13,14.1z M20,13c1.1,0,2-0.9,2-2c0-1.1-0.9-2-2-2s-2,0.9-2,2C18,12.1,18.9,13,20,13z M24,16.43c0-0.81-0.48-1.53-1.22-1.85 C21.93,14.21,20.99,14,20,14c-0.39,0-0.76,0.04-1.13,0.1c0.4,0.68,0.63,1.46,0.63,2.29V18l4.5,0V16.43z M12,6c1.66,0,3,1.34,3,3 c0,1.66-1.34,3-3,3s-3-1.34-3-3C9,7.34,10.34,6,12,6z"/></g></svg>
          <p>సర్కిల్</p>
        </div>
      <% end %>
    </div>

  </div>
  <div id="bottom-bar">
    <div id="app">
      <img class="icon" src="https://az-cdn.thecircleapp.in/120x120/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg" />
      <img src="https://az-cdn.thecircleapp.in/assets/praja-text-logo-black.png" alt="" class="logo">
    </div>
    <div id="dot"></div>
    <p>తెలుగు సోషల్ మీడియా</p>
  </div>
</div>
