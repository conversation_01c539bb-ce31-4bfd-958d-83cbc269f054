<style>
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Noto+Sans+Telugu:wght@400;500;700&display=swap');

#outer-container {
  width: 600px;
  height: 315px;
  position: relative;
  margin: auto;
  background: white;
}

#details-container {
  top: 24px;
  left: 24px;
  position: absolute;
  display: flex;
  align-items: top;
  gap: 16px;
  width: calc(100% - 64px);
}

#details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#details h1 {
  margin: 0 0 2px 4px;
  font-size: 34px;
}

#details h2 {
  margin: 0 0 2px 4px;
  font-size: 26px;
}

img.dp {
  width: 188px;
  height: 188px;
  border-radius: 51px;
  object-fit: cover;
  box-shadow: 0 0 8px #efefef;
}

#app {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 16px;
  margin-right: 12px;
  gap: 8px;
}

#app img.icon {
  width: 52px;
  height: 52px;
  border-radius: 13px;
  border: 1px solid #cacaca;
}

#app img.logo {
  height: 36px;
}

#badge {
  position: relative;
  height: 64px;
  display: flex;
}

#badge img {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
}

#badge #badge-bg {
  font-size: 22px;
  font-weight: 500;
  height: 40px;
  color: #222222;
  border-radius: 18px;
  background: #DCF2FF;
  display: flex;
  margin-top: 10px;
  margin-left: 32px;
  box-shadow: 0.5px 0.5px 4px 0px rgba(0, 0, 0, 0.25);
}

#badge #badge-bg p {
  margin: 0;
  padding: 8px 20px 4px 40px;
}

#badge #badge-bg svg {
  transform: rotate(-15deg);
  height: 18px;
  width: 18px;
  display: inline;
  margin-top: 2px;
  margin-right: 6px;
}

#type {
  display: flex;
  align-items: center;
  height: 40px;
  font-size: 18px;
  font-weight: 500;
  color: #3f3f3f;
  gap: 6px;
  margin-left: 6px;
}

#type svg {
  width: 28px;
  height: 28px;
  fill: #000;
  transform: rotate(-15deg);
}

#bottom-bar {
  width: 100%;
  position: absolute;
  bottom: 0;
  justify-content: center;
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #dadada;
}

#bottom-bar p {
  margin: 6px 16px 0 0;
  font-size: 26px;
  font-weight: 400;
}

#dot {
  width: 8px;
  height: 8px;
  background: black;
  border-radius: 4px;
  margin: 0 12px 0 0;
}

body {
  background: #555555;
  font-family: 'Noto Sans Telugu','Lato',  sans-serif;
}
</style>

<div id="outer-container">
  <div id="details-container">
    <img class="dp" src="<%= photo_url %>" />
    <div id="details">
      <% if Unicode::DisplayWidth.of(name) < 15 %>
        <h1><%= name %></h1>
      <% else %>
        <h2><%= name %></h2>
      <% end %>

      <% if is_official %>
        <div id="badge"><img src="https://az-cdn.thecircleapp.in/production/admin-media/32/ffa06092-5ef4-4ae2-969c-357e7ad070b0.png" alt="">
            <div id="badge-bg">
              <p>అధికారిక ఛానల్</p>
            </div>
        </div>
      <% end %>

      <% if !is_official %>
        <div id="type">
          <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M18 11v2h4v-2h-4zm-2 6.61c.96.71 2.21 1.65 3.2 2.39.4-.53.8-1.07 1.2-1.6-.99-.74-2.24-1.68-3.2-2.4-.4.54-.8 1.08-1.2 1.61zM20.4 5.6c-.4-.53-.8-1.07-1.2-1.6-.99.74-2.24 1.68-3.2 2.4.4.53.8 1.07 1.2 1.6.96-.72 2.21-1.65 3.2-2.4zM4 9c-1.1 0-2 .9-2 2v2c0 1.1.9 2 2 2h1v4h2v-4h1l5 3V6L8 9H4zm11.5 3c0-1.33-.58-2.53-1.5-3.35v6.69c.92-.81 1.5-2.01 1.5-3.34z"/></svg>
          <p>ఛానల్</p>
        </div>
      <% end %>
    </div>

  </div>
  <div id="bottom-bar">
    <div id="app">
      <img class="icon" src="https://az-cdn.thecircleapp.in/120x120/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg" />
      <img src="https://az-cdn.thecircleapp.in/assets/praja-text-logo-black.png" alt="" class="logo">
    </div>
    <div id="dot"></div>
    <p>తెలుగు సోషల్ మీడియా</p>
  </div>
</div>
