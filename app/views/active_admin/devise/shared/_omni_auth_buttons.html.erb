<%- if devise_mapping.omniauthable? %>
  <%- resource_class.omniauth_providers.each do |provider| %>
    <%
      # Extract redirect_to parameter from URL if present
      redirect_param = params[:redirect_url].present? ? { redirect_url: params[:redirect_url] } : {}
      auth_path = omniauth_authorize_path(resource_name, provider, redirect_param)
    %>
    <% if provider == :google_oauth2 %>
      <%= form_tag(auth_path, method: :post, style: "display: inline;") do %>
        <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
        <% if params[:redirect_url].present? %>
          <%= hidden_field_tag :redirect_url, params[:redirect_url] %>
        <% end %>
        <%= button_tag type: "submit", class: "oauth-button", style: "background: none; border: none; padding: 0; cursor: pointer;" do %>
          <%= image_tag('google_signin.png', alt: t('active_admin.sign_in_with_google'), height: '50px', align: 'center') %>
        <% end %>
      <% end %>
    <% else %>
      <%= link_to t('active_admin.sign_in_with_omniauth_provider', provider: provider.to_s.titleize),
                  omniauth_authorize_path(resource_name, provider), method: :post %>
    <% end -%>
    <br>
  <% end -%>
<% end -%>
