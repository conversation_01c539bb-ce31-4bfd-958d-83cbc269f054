// Styles for BOE Work Flow page
.badge-actions {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.already-role-button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
  cursor: pointer;
  border: none;
}

.already-role-button:hover {
  background-color: #45a049;
}

.admin_boe_work_flow .badge-container {
  display: flex;
  flex-direction: column;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.admin_boe_work_flow .badge-header {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  margin-bottom: 15px;
}

.admin_boe_work_flow .badge-text {
  padding: 15px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 2em;
  margin-right: 20px;
  min-height: 50px;
}

// Success message styles
.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  padding: 12px 20px;
  background-color: #dff0d8;
  border: 2px solid #4CAF50;
  border-radius: 6px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  animation: highlight-success 1s ease-in-out;
}

.success-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #4CAF50;
  font-weight: bold;
}

.success-text {
  font-size: 18px;
  font-weight: bold;
  color: #3c763d;
}

@keyframes highlight-success {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Modal styles for user role selection
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  backdrop-filter: blur(10px);
}

.modal-content {
  background-color: #232729;
  backdrop-filter: blur(10px);
  margin: 10% auto;
  width: 50%;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}

.modal-header {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2), 0 1px 0 0 rgba(255, 255, 255, 0.07) inset;
  padding: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: #424242;
  border-bottom: 1px solid #424242;
}

.modal-header h3 {
  margin: 0;
  padding: 0;
  //color: #333;
}

.close h3 {
  color: #aaa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}


.close h3:hover,
.close h3:focus {
  color: #fff;
  text-decoration: none;
}

.modal-footer {
  padding-top: 12px;
  padding-bottom: 12px;
  border-top: 1px solid #232729;
  text-align: right;
}

.user-role-item {
  padding: 12px;
  border-radius: 4px;
  transition: background-color 0.3s, border-color 0.3s;
}

.user-role-item:hover {
  border-color: #ccc;
}

.user-role-item input[type="radio"] {
  margin-right: 10px;
}

.role-status {
  color: #aaa;
  font-style: italic;
}

.primary-button {
  background-color: #4CAF50;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.primary-button:hover {
  background-color: #45a049;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* Alternating background colors for user roles list */
#user-roles-list .user-role-item:nth-child(even) {
  background-color: #232729;
}

#user-roles-list .user-role-item:nth-child(odd) {
  background-color: #272b2d;
}

/* Update text color for better contrast */
.user-role-item {
  color: #fff;
}

.user-role-item:hover {
  background-color: #2c3032 !important;
}
