module LayoutsForDemo
  extend ActiveSupport::Concern

  def demo_layouts_with_user_data(user_role: nil)
    is_user_position_back = false
    name_type = self.name_type
    badge_text_type = self.badge_text_type unless name_type == "long"
    is_user_position_back = true if name_type == "long" || badge_text_type == "long"
    affiliated_circle = Circle.find_by(id: affiliated_party_circle_id.to_i)

    circle = Circle.find_by(id: affiliated_party_circle_id.to_i)
    user_poster_layout = self.get_user_poster_layout
    user_layout_variation, common_hash_for_layout = self.get_common_hash_for_layout_and_layout_variation(user_poster_layout.id, entity: self)
    header_1_photos = common_hash_for_layout[:header_1_photos]
    header_2_photos = common_hash_for_layout[:header_2_photos]

    fonts_config = default_font_config(is_user_position_back)

    # identity_types = %w[trapezoidal_identity polygonal_profile_identity]
    demo_layouts = {
      1 => { identity_type: 'semi_circular_identity', frame_type: 'premium',
             dummy_photo_id: Constants.user_dummy_cutout_id },
      2 => { identity_type: 'polygonal_profile_identity', frame_type: 'premium',
             dummy_photo_id: Constants.user_dummy_cutout_id },
      3 => { identity_type: 'gold_lettered_user', frame_type: 'family_frame_premium',
             dummy_photo_id: Constants.family_dummy_cutout_id },
      4 => { identity_type: 'top_trapezoidal_identity', frame_type: 'hero_frame_premium',
             dummy_photo_id: Constants.hero_dummy_cutout_id }
    }

    # build above data for 3 different layouts
    layout_jsons = []

    demo_layouts.each do |layout_id, layout_req_data|
      # add type in identity
      identity_type = layout_req_data[:identity_type]
      layout_type = layout_req_data[:frame_type]
      gradients = affiliated_circle.get_gradients_for_posters_tab(self, false, identity_type)
      user = user_json_for_layout(user_role, 'premium', original_layout_type: layout_type)
      user_dummy_photo = AdminMedium.find_by(id: layout_req_data[:dummy_photo_id])

      identity = {
        user: user,
        type: identity_type,
        is_user_position_back: is_user_position_back,
        show_badge_ribbon: false,
        party_highlight_color_primary: affiliated_circle.get_primary_highlight_color(identity_type),
        party_highlight_color_secondary: affiliated_circle.get_secondary_highlight_color(identity_type),
        party_icon_url: layout_type == 'family_frame_premium' ? nil : get_affiliated_party_icon(affiliated_party_circle_id)
      }
      if user[:photo_url].blank?
        user[:photo_url] = user_dummy_photo.compressed_url(size: 200) if user_dummy_photo.present?
      end
      text_color = get_text_color(circle, false, identity_type)
      layout_hash = layout_req_data.merge({
                                            layout_type: layout_type,
                                            header_1_photos: layout_type == 'family_frame_premium' ? [] : header_1_photos,
                                            header_2_photos: layout_type == 'family_frame_premium' ? [] : header_2_photos,
                                            gradients: gradients,
                                            fonts_config: fonts_config,
                                            text_color: text_color,
                                            identity: identity
                                          })
      layout_jsons << build_layout_json(layout_hash: layout_hash)
    end

    layout_jsons
  end

  def demo_layouts_for_premium_experience(user_role: nil)
    is_user_position_back = false
    name_type = self.name_type
    badge_text_type = self.badge_text_type unless name_type == "long"
    is_user_position_back = true if name_type == "long" || badge_text_type == "long"
    affiliated_circle = Circle.find_by(id: affiliated_party_circle_id.to_i)

    # identity_type = 'trapezoidal_identity'
    # layout_type = 'premium'

    # populate the header_1 and header_2 dummy cutout images
    header_1_photos = []
    header_2_photos = []
    leader_photos = []
    h1_photos = []
    h2_photos = []
    h1_photos_count = 2
    h2_photos_count = 4
    circle = Circle.find_by(id: affiliated_party_circle_id.to_i)
    user_poster_layout = self.get_user_poster_layout
    # define the layout, frame and identity
    layout = 'layout_2_4'
    layout_details = UserPosterLayout::LAYOUTS[layout.to_sym]

    dummy_h1_cutout_photo = AdminMedium.find_by(id: Constants.h1_dummy_photo_id)
    dummy_h2_cutout_photo = AdminMedium.find_by(id: Constants.h2_dummy_photo_id)
    h1_photos += [dummy_h1_cutout_photo] * h1_photos_count if h1_photos_count > 0 && dummy_h1_cutout_photo.present?
    h2_photos = [dummy_h2_cutout_photo] * h2_photos_count if dummy_h2_cutout_photo.present?
    leader_photos += h1_photos
    leader_photos += h2_photos

    leader_photos.each_with_index do |photo, index|
      if index < 2
        header_1_photos << {
          radius: layout_details[:photos][index][:radius],
          position_x: layout_details[:photos][index][:position_x],
          position_y: layout_details[:photos][index][:position_y],
          photo_url: photo.compressed_url(size: 200)
        }
      else
        header_2_photos << {
          radius: layout_details[:photos][index][:radius],
          position_x: layout_details[:photos][index][:position_x],
          position_y: layout_details[:photos][index][:position_y],
          photo_url: photo.compressed_url(size: 200)
        }
      end
    end

    fonts_config = default_font_config(is_user_position_back)
    # user_dummy_photo = AdminMedium.find_by(id: Constants.user_dummy_cutout_id)

    # identity_types = %w[trapezoidal_identity polygonal_profile_identity]
    demo_layouts = {
      1 => { identity_type: 'semi_circular_identity', frame_type: 'premium',
             dummy_photo_id: Constants.user_dummy_cutout_id },
      2 => { identity_type: 'polygonal_profile_identity', frame_type: 'premium',
             dummy_photo_id: Constants.user_dummy_cutout_id },
      3 => { identity_type: 'gold_lettered_user', frame_type: 'family_frame_premium',
             dummy_photo_id: Constants.family_dummy_cutout_id },
      4 => { identity_type: 'top_trapezoidal_identity', frame_type: 'hero_frame_premium',
             dummy_photo_id: Constants.hero_dummy_cutout_id }
    }

    # build above data for 3 different layouts
    layout_jsons = []

    demo_layouts.each do |layout_id, layout_req_data|
      # add type in identity
      identity_type = layout_req_data[:identity_type]
      layout_type = layout_req_data[:frame_type]
      gradients = affiliated_circle.get_gradients_for_posters_tab(self, false, identity_type)
      user = user_json_for_layout(user_role, 'premium', original_layout_type: layout_type)
      user_dummy_photo = AdminMedium.find_by(id: layout_req_data[:dummy_photo_id])
      # add a dummy badge if user doesn't have a badge
      user[:badge] = {
        "id": 1,
        "active": true,
        "badge_text": 'మీ హోదా',
        "description": 'మీ హోదా',
        "badge_banner": 'SILVER'
      } if layout_type != 'family_frame_premium'

      identity = {
        user: user,
        type: identity_type,
        is_user_position_back: is_user_position_back,
        show_badge_ribbon: false,
        party_highlight_color_primary: affiliated_circle.get_primary_highlight_color(identity_type),
        party_highlight_color_secondary: affiliated_circle.get_secondary_highlight_color(identity_type),
        party_icon_url: layout_type == 'family_frame_premium' ? nil : get_affiliated_party_icon(affiliated_party_circle_id)
      }

      user[:photo_url] = user_dummy_photo.compressed_url(size: 200) if user_dummy_photo.present?
      text_color = get_text_color(circle, false, identity_type)
      layout_hash = layout_req_data.merge({
                                            layout_type: layout_type,
                                            header_1_photos: layout_type == 'family_frame_premium' ? [] : header_1_photos,
                                            header_2_photos: layout_type == 'family_frame_premium' ? [] : header_2_photos,
                                            gradients: gradients,
                                            fonts_config: fonts_config,
                                            text_color: text_color,
                                            identity: identity
                                          })
      layout_jsons << build_layout_json(layout_hash: layout_hash)
    end

    layout_jsons
  end

  def build_layout_json(layout_hash:)
    {
      "layout_type": 'premium',
      "golden_frame": false,
      "show_praja_logo": false,
      "shadow_color": nil,
      "share_text": ' ',
      "gradients": layout_hash[:gradients],
      "text_color": layout_hash[:text_color],
      "badge_text_color": layout_hash[:text_color],
      "identity": layout_hash[:identity],
      "header_1_photos": layout_hash[:header_1_photos],
      "header_2_photos": layout_hash[:header_2_photos],
      "is_locked": true,
      "is_bordered_layout": false,
      "neutral_frame": false,
      "enable_outer_frame": false,
      "frame_type": layout_hash[:frame_type],
      "fonts_config": layout_hash[:fonts_config],
      "analytics_params": {
        "layout_type": layout_hash[:frame_type],
        "is_locked": true,
        "golden_frame": false,
        "header_1_count": 2,
        "header_2_count": 4,
        "identity_type": layout_hash[:identity][:type],
        "is_user_position_back": layout_hash[:identity][:is_user_position_back],
        "party_icon_position": nil,
        "frame_type": layout_hash[:frame_type],
        "is_circle_sponsored_frame": false
      }
    }
  end
end
