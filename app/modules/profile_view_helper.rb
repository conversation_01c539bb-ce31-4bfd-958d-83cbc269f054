module ProfileViewHelper
  def fetch_views_data(user, loaded_user_ids, count)
    viewed_users = ProfileView.includes(:viewer)
                              .where(user_id: user.id)
                              .where.not(viewer_id: loaded_user_ids)
                              .order(viewed_at: :desc).limit(count)
                              .all

    viewed_user_ids = viewed_users.map(&:viewer_id)

    following_user_ids = UserFollower.where(user_id: viewed_user_ids, follower_id: user.id).pluck(:user_id)

    users = viewed_users.map do |u|
      u.viewer.json_for_premium_experience(following_user_ids:)
    end

    locked = !(user.is_poster_subscribed || user.is_trial_user?)

    { viewed_user_ids: viewed_user_ids, users: users, locked: locked }
  end

  def generate_title(user, loaded_user_ids)
    title = nil
    views_count = ProfileView.where(user_id: user.id).count if loaded_user_ids.blank?
    if loaded_user_ids.blank?
      title = if views_count == 0
                I18n.t('profile_views_feed_item.title.zero')
              else
                I18n.t('profile_views_feed_item.title.other', count: views_count)
              end
    end
    [title, views_count]
  end

  def generate_see_more_text(views_count, count)
    see_more_text = nil
    see_more_text_color = nil
    if views_count > count
      see_more_text = I18n.t('profile_views_feed_item.see_more_text')
      see_more_text_color = 0xff8F8F8F
    end
    [see_more_text, see_more_text_color]
  end
end
