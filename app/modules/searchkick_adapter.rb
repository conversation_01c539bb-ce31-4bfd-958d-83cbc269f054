module SearchkickAdapter
  def self.included(base)
    base.extend ClassMethods
    base.include InstanceMethods
  end

  module ClassMethods
    def searchkick(options = {})
      @index_name = options[:index_name]
      @mappings = options[:mappings]
    end

    def index_name
      @index_name.call if @index_name.is_a?(Proc)
    end

    def mappings
      @mappings
    end
  end

  module InstanceMethods
    def search_data
      raise NotImplementedError, "You must implement search_data method in #{self.class.name}"
    end

    def reindex
      # Implement custom indexing logic here
      # e.g., `Searchkick.client.index(index: self.class.index_name, id: id, body: search_data)`
    end
  end
end
