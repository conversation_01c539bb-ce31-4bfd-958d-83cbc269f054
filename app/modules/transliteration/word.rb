module Transliteration

  MATCH_THRESHOLD_PERCENTAGE = 80

  LANGUAGE_CODE_ENGLISH = 'en'.freeze
  LANGUAGE_CODE_TELUGU = 'te'.freeze
  LANGUAGE_CODE_HINDI = 'hi'.freeze
  LANGUAGE_CODE_TAMIL = 'ta'.freeze
  LANGUAGE_CODE_KANNADA = 'kn'.freeze
  LANGUAGE_CODE_MALAYALAM = 'ml'.freeze
  LANGUAGE_CODE_PUNJABI = 'pa'.freeze
  LANGUAGE_CODE_ORIYA = 'or'.freeze
  LANGUAGE_CODE_BENGALI = 'bn'.freeze

  # Class to cache and translate words
  class Word
    def initialize(text)
      @text = text
      @detected_language = nil
    end

    def language
      @language ||= detect_language
    end

    # @return [String]
    def detect_language
      if english?(@text)
        LANGUAGE_CODE_ENGLISH
      elsif telugu?(@text)
        LANGUAGE_CODE_TELUGU
      elsif hindi?(@text)
        LANGUAGE_CODE_HINDI
      elsif tamil?(@text)
        LANGUAGE_CODE_TAMIL
      elsif kannada?(@text)
        LANGUAGE_CODE_KANNADA
      elsif malayalam?(@text)
        LANGUAGE_CODE_MALAYALAM
      elsif punjabi?(@text)
        LANGUAGE_CODE_PUNJABI
      elsif oriya?(@text)
        LANGUAGE_CODE_ORIYA
      elsif bengali?(@text)
        LANGUAGE_CODE_BENGALI
      end
    end

    def english?(text)
      pattern = /[a-zA-Z0-9!@#$%^&*()]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def telugu?(text)
      pattern = /[\u0C00-\u0C7F]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def hindi?(text)
      pattern = /[\u0900-\u097F\uA8E0-\uA8FF]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def tamil?(text)
      pattern = /[\u0B80-\u0BFF]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def kannada?(text)
      pattern = /[\u0C80-\u0CFF]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def malayalam?(text)
      pattern = /[\u0D00-\u0D7F]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def punjabi?(text)
      pattern = /[\u0A00-\u0A7F]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def oriya?(text)
      pattern = /[\u0B00-\u0B7F]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def bengali?(text)
      pattern = /[\u0980-\u09FF\u09E6-\u09EF\u09F0-\u09F3\u09FA]/
      matching_chars = text.chars.count { |char| char =~ pattern }
      (matching_chars.to_f / text.length) * 100 >= MATCH_THRESHOLD_PERCENTAGE
    end

    def transliterate(target_language_code)
      if language.blank?
        Rails.logger.debug("Language not detected for #{@text}")
        return @text
      end

      if target_language_code != LANGUAGE_CODE_ENGLISH && language != LANGUAGE_CODE_ENGLISH
        # TODO: Support any language to any language transliteration
        # Solution: If it is telugu to hindi, transliterate to english first and then to hindi
        Rails.logger.error("Either target or source language needs to be English for transliteration. #{@text} is in #{language} and target is #{target_language_code}")
        return @text
      end

      if language == target_language_code
        Rails.logger.debug("Source language of #{@text} is same as target language #{target_language_code}")
        return @text
      end

      if target_language_code == LANGUAGE_CODE_ENGLISH
        url = "https://api.cognitive.microsofttranslator.com/transliterate?api-version=3.0&language=#{language}&fromScript=#{azure_script_code(language)}&toScript=#{azure_script_code(target_language_code)}"
      else
        url = "https://api.cognitive.microsofttranslator.com/transliterate?api-version=3.0&language=#{target_language_code}&fromScript=#{azure_script_code(language)}&toScript=#{azure_script_code(target_language_code)}"
      end

      request_uri = Timeout.timeout(0.1) do
        Rails.logger.debug("Transliteration request: #{url}")
        URI(url)
      end

      response = Net::HTTP.post(
        request_uri,
        [{"text": @text}].to_json,
        {"Content-Type" => "application/json",
        "Ocp-Apim-Subscription-Key" => Rails.application.credentials[:azure_translate_key],
        "Ocp-Apim-Subscription-Region" => 'centralindia',
        "Accept" => "application/json"}
      )
      response_arr = JSON.parse response.read_body
      Rails.logger.debug("Transliteration response: #{response_arr}")

      if response_arr.blank? || response_arr[0].blank? || response_arr[0]['text'].blank?
        Rails.logger.error("Transliteration failed for #{@text}")
        return @text
      end

      response_arr[0]['text']
    end

    private

    def azure_script_code(language_code)
      case language_code
      when LANGUAGE_CODE_TELUGU
        'Telu'
      when LANGUAGE_CODE_HINDI
        'Deva'
      when LANGUAGE_CODE_TAMIL
        'Taml'
      when LANGUAGE_CODE_KANNADA
        'Knda'
      when LANGUAGE_CODE_MALAYALAM
        'Mlym'
      when LANGUAGE_CODE_PUNJABI
        'Guru'
      when LANGUAGE_CODE_ORIYA
        'Orya'
      when LANGUAGE_CODE_BENGALI
        'Beng'
      when LANGUAGE_CODE_ENGLISH
        'Latn'
      else
        # type code here
      end
    end
  end
end
