# frozen_string_literal: true
require 'sidekiq-scheduler'

class GeneratePosterImagesForCampaignUsers
  include Sidekiq::Worker

  def perform
    current_time = Time.zone.now
    campaign_name = "wati_re_activation_campaign_#{current_time.strftime("%Y_%m_%d")}"
    metadata_key = Constants.premium_reactivation_wati_campaign_count
    user_metadata_insertions = []

    User.joins("INNER JOIN user_metadata ON user_metadata.user_id = users.id")
        .joins(user_plan: :plan)
        .where("user_plans.end_date < ?", current_time.beginning_of_day - 1.day)
        .where(plans: { duration_in_months: 1 })
        .where("user_metadata.key = ? AND user_metadata.value < 11", metadata_key)
        .find_each(batch_size: 1000) do |user|

      user_creative_data = user.get_user_creative_data(campaign_name)
      next unless user_creative_data
      user_metadata_insertions << user_creative_data
      GeneratePosterCampaignImage.perform_async(user.id, user_creative_data.value, campaign_name)
    end

    UserMetadatum.import(user_metadata_insertions, on_duplicate_key_ignore: true, batch_size: 100)

    target_time = current_time.beginning_of_day + 5.hours
    final_target_time = (target_time < current_time) ? target_time + 1.day : target_time
    InitiateWatiCampaignUsingCsv.perform_at(final_target_time, campaign_name, metadata_key)
  end
end
