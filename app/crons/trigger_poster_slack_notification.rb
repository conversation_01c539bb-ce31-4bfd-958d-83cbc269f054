class TriggerPosterSlackNotification
  include Sidekiq::Worker

  def perform
    return unless Rails.env.production?

    data = Poster.
      where(active: true).
      where("start_time BETWEEN ? AND ?",
                         Time.zone.tomorrow.beginning_of_day,
                         Time.zone.tomorrow.end_of_day).
      select(:id, :name, :circle_id).
      map do |res|
        "<a href='https://www.thecircleapp.in/admin/posters/#{res.id}'>#{res.name}</a> on <a href='https://www.thecircleapp.in/admin/circles/#{res.circle_id}'>#{res.circle.name_en}</a> (#{res.circle.level})"
    end

    return if data.nil? || data.empty?

    Poster.ping_to_slack("Posters tomorrow - #{data.join(', ')}")
  end
end
