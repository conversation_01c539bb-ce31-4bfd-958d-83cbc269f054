require 'sidekiq-scheduler'

class UserInvitesIndexing
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0

  def perform(badge_user_id = nil)
    logger.info("user invites index cron running")

    index = 0
    #gets last 25 hrs newly added user invites whose sent is false
    UserInvite.where('user_invites.created_at > ?', 25.hours.ago).where(sent: false)
              .find_each(batch_size: 10000) do |user_invite|
      index += 1
      phone = user_invite.phone.to_s
      next if phone.starts_with?("1", "2", "3", "4", "5")
      # performs indexing
      UserInviteIndexPhoneWorker.perform_async(user_invite.id)
      sleep 1 if ((index + 1) % 1000).zero?
    end
  end
end
