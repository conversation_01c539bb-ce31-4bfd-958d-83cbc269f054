require 'sidekiq-scheduler'

class CronForCreateUserRoles
  include Sidekiq::Worker

  def perform
    logger.info("create user roles cron running")

    index = 0

    checked_ids = []
    SingularDatum.where('created_at > ?', 12.minutes.ago).find_each(batch_size: 1000) do |singular_data|
      index += 1
      invited_id = singular_data.invited_by

      next if checked_ids.include? invited_id

      CreateUserRole.perform_async(invited_id)
      checked_ids << invited_id
      sleep 1 if ((index + 1) % 1000).zero?
    end
  end
end
