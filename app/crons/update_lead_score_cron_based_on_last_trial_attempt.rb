# frozen_string_literal: true

require 'sidekiq-scheduler'

class UpdateLeadScoreCronBasedOnLastTrialAttempt
  include Sidekiq::Worker

  def perform
    logger.info('Update lead score cron running')

    time = Time.zone.now.to_i
    user_ids = $redis.zrangebyscore(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, 0, time)
    user_ids.each do |user_id|
      # create a worker where i update the lead of user in flow as well as calculate the next run time of last trial
      # attempt
      LastTrialAttemptSchedulerWorker.perform_async(user_id.to_i)
    end

    $redis.zremrangebyscore(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, 0, time)
  end
end
