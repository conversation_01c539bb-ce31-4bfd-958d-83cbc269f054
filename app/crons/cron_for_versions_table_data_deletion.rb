require 'sidekiq-scheduler'

class CronForVersionsTableDataDeletion
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now

    start_id = $redis.get(Constants.versions_table_last_deleted_id_redis_key).to_i
    batch_size = 250
    end_date = Time.zone.parse('2024-11-01 00:00:00')
    records_to_delete = []

    records = PaperTrail::Version.where('id > ?', start_id)
                                 .limit(25000)
                                 .pluck(:id, :item_type, :tag, :created_at)

    return if records.empty?

    end_id = 0
    records.each do |id, item_type, tag, created_at|
      if (item_type == 'Circle' || item_type == 'User') && tag.nil? && created_at < end_date
        records_to_delete << id
      end
      end_id = id
    end

    return if records_to_delete.empty?

    records_to_delete.each_slice(batch_size) do |batch|
      PaperTrail::Version.where(id: batch).delete_all
    end

    $redis.set(Constants.versions_table_last_deleted_id_redis_key, end_id)

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Versions table deletion time: #{elapsed_time} sec || end_id: #{end_id}")
  end
end
