require 'sidekiq-scheduler'

class CreateRenewalOrderCron
  include Sidekiq::Worker

  def perform
    logger.info('Create renewal order cron running')
    # if user's last product end date is in 15 days, then create a new order for the user
    # pick only last user product subscription of the user
    UserProductSubscription.where(end_date: Time.zone.now.beginning_of_day..Time.zone.now.advance(days: 15).end_of_day,
                                  item_type: 'Product').each do |subscription|
      user = subscription.user

      # Get the last subscription of the user that matches the criteria
      last_subscription = UserProductSubscription.where(user: user, item_type: 'Product').order(end_date: :desc).first
      next unless last_subscription.id == subscription.id

      open_orders = user.orders.open

      # Skip users who have open orders with products
      if open_orders.any? && open_orders.joins(:order_items).where(order_items: { item_type: 'Product' }).exists?
        next
      else
        CreateRenewalOrderForUser.perform_async(user.id)
      end
    end
  end
end
