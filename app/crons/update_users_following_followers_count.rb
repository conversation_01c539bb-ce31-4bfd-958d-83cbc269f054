# frozen_string_literal: true

require 'sidekiq-scheduler'

class UpdateUsersFollowingFollowersCount
  include Sidekiq::Worker
  def perform
    logger.info('Updating last 30 min follower and following counts')

    unique_user_ids = []
    unique_follower_ids = []

    UserFollower.
      where("created_at > ?", 13.minutes.ago).
      where.not(user_id: Constants.praja_account_user_id).
      find_each do |user_follower|
      unique_user_ids << user_follower.user_id
      unique_follower_ids << user_follower.follower_id
    end

    unique_user_ids.uniq.each do |user_id|
      total_followers_count = UserFollower.active_followers.where(user_id: user_id).count
      User.find_by(id: user_id)&.update_column(:total_followers_count, total_followers_count)
    end

    unique_follower_ids.uniq.each do |follower_id|
      total_following_count = UserFollower.active_following.where(follower_id: follower_id).count
      User.find_by(id: follower_id)&.update_column(:total_following_count, total_following_count)
    end
  end
end
