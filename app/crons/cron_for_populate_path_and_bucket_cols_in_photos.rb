require 'sidekiq-scheduler'

class CronForPopulatePathAndBucketColsInPhotos
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.photos_table_last_processed_id_key).to_i

    records_to_populate = []

    records = Photo.where('id > ?', start_id)
                   .limit(25000)
                   .pluck(:id, :service, :bucket)

    return if records.empty?

    end_id = 0
    records.each do |id, service, bucket|
      if service.present? && bucket.nil?
        records_to_populate << [id, service]
      end
      end_id = id
    end

    $redis.set(Constants.photos_table_last_processed_id_key, end_id)

    return if records_to_populate.empty?

    records_to_populate.each_slice(250) do |batch|
      Photo.where(id: batch.map(&:first)).update_all(
        Arel.sql(
           "bucket = CASE service
                      WHEN 'azure' THEN 'prodprajarup'
                      WHEN 'aws' THEN 'circle-app-photos'
                      WHEN 'gcp' THEN 'praja-raw-user-photos'
                    END"
        )
      )
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Photos table bucket update time: #{elapsed_time} sec || end_id: #{end_id}")
  end
end
