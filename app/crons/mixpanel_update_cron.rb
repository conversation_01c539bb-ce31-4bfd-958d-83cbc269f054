require 'sidekiq-scheduler'

class MixpanelUpdateCron
  include Sidekiq::Worker

  # def perform
  #   logger.info("Mix panel update cron running")
  #
  #   uid = $redis.get('mix_panel_update_last_user_id_v2').to_i
  #   user_id = nil
  #   count = 0
  #   User.where(id: uid..uid + 10000).each do |user|
  #     user_id = user.id
  #     # if user.badge&.active
  #     #   badge = "Yes"
  #     #   role = user.badge&.badge_role&.text || 'null'
  #     # else
  #     #   badge = "No"
  #     #   role = 'null'
  #     # end
  #     MixpanelIntegration.perform_async(
  #       user.id,
  #       {
  #         "followers_count_backend": user.followers_count,
  #         "following_count_backend": user.following_count,
  #         "current_badge_role_ids": user.user_roles.pluck(:role_id),
  #         "joined_political_party_ids": user.get_user_joined_party_circle_ids,
  #         "joined_interest_circle_ids": user.get_user_joined_interest_circle_ids
  #       })
  #     # MixpanelIntegration.perform_async(
  #     #   user.id,
  #     #   {
  #     #     "is_internal": user.internal,
  #     #     "signed_up_backend": user.signed_up?,
  #     #     "is_internal_backend": user.internal,
  #     #     "badge": badge,
  #     #     "badge_role": role,
  #     #     "user_id_backend": user.id
  #     #   })
  #
  #     # MixpanelIntegration.perform_async(
  #     #   user.id,
  #     #   { "$name": user.name,
  #     #     "$phone": "+91#{user.phone}",
  #     #     "$created": (user.created_at - 5.hours - 30.minutes).strftime("%Y-%m-%dT%TZ"),
  #     #     "is_internal": user.internal,
  #     #     "village_id": user.village&.id,
  #     #     "mandal_id": user.mandal&.id,
  #     #     "district_id": user.district&.id,
  #     #     "default_feed": user.my_feed_enabled? ? 'my_feed' : 'trending_feed',
  #     #     "signed_up_backend": user.signed_up?,
  #     #     "is_internal_backend": user.internal,
  #     #     "village_id_backend": user.village&.id,
  #     #     "mandal_id_backend": user.mandal&.id,
  #     #     "district_id_backend": user.district&.id,
  #     #     "default_feed_backend": user.my_feed_enabled? ? 'my_feed' : 'trending_feed'
  #     #   })
  #
  #     count += 1
  #     sleep 1 if (count % 100).zero?
  #   end
  #
  #   $redis.set('mix_panel_update_last_user_id_v2', user_id.to_s) if count > 0
  # end
end
