# frozen_string_literal: true

require 'sidekiq-scheduler'

class UpdateCirclesMembersCount
  include Sidekiq::Worker

  def perform
    logger.info('Updating all parties, leaders & last 30 min joined circles count')

    Circle.where(level: [:mandal, :mla_constituency, :mp_constituency, :district, :state]).find_each do |circle|
      circle.update_members_count
    end

    circle_ids = $redis.hkeys(Constants.circle_members_count_redis_key).map(&:to_i)
    $redis.del(Constants.circle_members_count_redis_key)

    Circle.where(id: circle_ids).find_each do |circle|
      circle.update_members_count
    end
  end
end
