require 'sidekiq-scheduler'
class CreateConsecutiveCircleMonthlyUsages
  include Sidekiq::Worker

  # Run this cron on start of the new day
  # This cron is used to create the circle monthly usages for the next month
  def perform
    logger.info("create circle monthly usages cron running")

    today = Time.zone.today
    CircleMonthlyUsage.where(active: true).where('month_end < ?', today).each do |circle_monthly_usage|
      get_circle_package_mapping = circle_monthly_usage.circle.active_circle_package_mapping
      if get_circle_package_mapping.present? && get_circle_package_mapping.end_date > today
        month_end = get_circle_package_mapping.calculate_month_end(today)
        month_end = get_circle_package_mapping.end_date if month_end > get_circle_package_mapping.end_date
        CircleMonthlyUsage.create(circle_id: circle_monthly_usage.circle_id,
                                  month_start: today,
                                  month_end: month_end,
                                  channel_message_limit: get_circle_package_mapping.circle_package.channel_post_msg_limit,
                                  channel_message_usage: 0,
                                  channel_notification_limit: get_circle_package_mapping.circle_package.channel_post_msg_notification_limit,
                                  channel_notification_usage: 0,
                                  fan_posters_limit: get_circle_package_mapping.circle_package.fan_poster_creatives_limit,
                                  fan_posters_usage: 0)
      end
    end
  end
end
