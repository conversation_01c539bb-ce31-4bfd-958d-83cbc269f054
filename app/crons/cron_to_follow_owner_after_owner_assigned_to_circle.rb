# frozen_string_literal: true
require 'sidekiq-scheduler'

class CronToFollowOwnerAfterOwnerAssignedToCircle
  include Sidekiq::Worker

  def perform
    owner_permission_group_id = Constants.owner_permission_group_id
    new_user_circle_permissions_on_leader_circles = UserCirclePermissionGroup.joins(:circle)
                                                                             .where(circles: {level: Circle::CIRCLE_LEVELS_WITH_AUTO_FOLLOW_OWNER})
                                                                             .where(permission_group_id: owner_permission_group_id)
                                                                             .where('user_circle_permission_groups.created_at > ?', 1.day.ago)

    return if new_user_circle_permissions_on_leader_circles.blank?

    new_user_circle_permissions_on_leader_circles.each do |ucp|
      ImportCircleOwnerFollow.perform_async(ucp.circle_id, ucp.user_id, ucp.created_at)
    end
  end
end


