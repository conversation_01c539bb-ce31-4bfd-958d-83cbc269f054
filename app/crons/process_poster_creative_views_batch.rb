# frozen_string_literal: true

class ProcessPosterCreativeViewsBatch
  include Sidekiq::Worker

  def perform
    logger.info("ProcessPosterCreativeViewsBatch cron running")

    key = Constants.poster_creative_views_queue_redis_key
    poster_creative_views_batch = []
    creative_ids = []

    while (poster_creative_view = $redis.spop(key)) != nil
      poster_creative_view = JSON.parse(poster_creative_view)
      creative_ids << poster_creative_view["poster_creative_id"]

      poster_creative_views_batch << PosterCreativeView.new(poster_creative_id: poster_creative_view["poster_creative_id"],
                                                            user_id: poster_creative_view["user_id"],
                                                            viewed_at: poster_creative_view["viewed_at"])
    end

    PosterCreativeView.import(poster_creative_views_batch, on_duplicate_key_ignore: true, batch_size: 100) if poster_creative_views_batch.present?

    creative_ids.uniq!
    if creative_ids.present?
      # batch 100 creative ids and find creatives
      creative_ids.each_slice(100) do |ids|
        creatives = PosterCreative.where(id: ids).select(:id, :event_id)
        creatives.each do |creative|
          if creative.event_id.present?
            IndexCreativesForPostersFeed.perform_async("event_#{creative.event_id}")
          else
            IndexCreativesForPostersFeed.perform_async("creative_#{creative.id}")
          end
        end
      end
    end
  end
end
