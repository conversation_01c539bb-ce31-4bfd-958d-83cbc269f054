require 'sidekiq-scheduler'

class PosterPremiumExtensionStartedEventCron
  include Sidekiq::Worker
  #
  # def perform
  #   logger.info("poster premium extension started event cron running")
  #   batch_size = 100
  #   offset = 0
  #   today = Time.zone.today
  #
  #   loop do
  #     # get poster premium extension is in range of today
  #     premium_extended_data = UserProductSubscription.where(source: :premium_extension, item_type: :Product,
  #                                                           item_id: Constants.get_poster_product_id)
  #                                                    .where("start_date >= ? AND start_date <= ?",
  #                                                           today.beginning_of_day, today.end_of_day)
  #                                                    .limit(batch_size)
  #                                                    .offset(offset)
  #
  #     break if premium_extended_data.empty?
  #     premium_extended_data.each do |premium_extended_datum|
  #       user_id = premium_extended_datum.user_id
  #       start_date = premium_extended_datum.start_date
  #       end_date = premium_extended_datum.end_date
  #       source = premium_extended_datum.source
  #
  #       EventTracker.perform_async(user_id, "premium_extension_started", {
  #         "user_id" => user_id,
  #         "start_date" => start_date,
  #         "end_date" => end_date,
  #         "source" => source
  #       })
  #
  #       # sync mixpanel user for each user
  #       SyncMixpanelUser.perform_async(user_id)
  #     end
  #
  #     offset += batch_size
  #   end
  # end
end

