require 'sidekiq-scheduler'

class UpdateUserTrialEndDates
  include Sidekiq::Worker

  def perform
    logger.info("update user trial end dates cron running")
    batch_size = 1000
    offset = 0
    today = Time.zone.today

    loop do
      # list of user ids with trial start date but no trial end date
      user_ids_batch = Metadatum.where(key: Constants.user_poster_trial_start_date_key)
                                .where('created_at >= ?', 31.days.ago)
                                .limit(batch_size)
                                .offset(offset)
                                .pluck(:entity_id)

      break if user_ids_batch.empty?
      user_ids_batch.uniq!

      # set trial end date for each user if user trial is expired and trial end date is not set
      trial_end_metadatum_batch = []
      user_ids_batch.each do |user_id|
        trial_related_metadata = Metadatum.where(entity_type: 'User',
                                                 entity_id: user_id,
                                                 key: [Constants.user_poster_trial_start_date_key,
                                                       Constants.user_poster_trial_duration_key,
                                                       Constants.user_poster_trial_end_date_key])
        metadata_hash = {}
        trial_related_metadata.each do |metadatum|
          if metadatum.key == Constants.user_poster_trial_start_date_key
            if metadata_hash[:start_date].nil?
              metadata_hash[:start_date] = [metadatum.value]
            else
              metadata_hash[:start_date] << metadatum.value
            end
          elsif metadatum.key == Constants.user_poster_trial_duration_key
            if metadata_hash[:duration].nil?
              metadata_hash[:duration] = [metadatum.value]
            else
              metadata_hash[:duration] << metadatum.value
            end
          elsif metadatum.key == Constants.user_poster_trial_end_date_key
            if metadata_hash[:end_date].nil?
              metadata_hash[:end_date] = [metadatum.value]
            else
              metadata_hash[:end_date] << metadatum.value
            end
          end
        end
        # if end dates are not set, set them
        if metadata_hash[:end_date].blank?
          metadata_hash[:start_date].each_with_index do |start_date, index|
            duration = metadata_hash[:duration][index].to_i
            end_date = start_date.to_date.advance(days: duration - 1)
            if end_date < today
              trial_end_metadatum_batch << Metadatum.new(entity_id: user_id,
                                                         entity_type: 'User',
                                                         key: Constants.user_poster_trial_end_date_key,
                                                         value: end_date)
            end
          end
        elsif metadata_hash[:start_date].size != metadata_hash[:end_date]&.size
          metadata_hash[:start_date].each_with_index do |start_date, index|
            if metadata_hash[:end_date][index].nil?
              duration = metadata_hash[:duration][index].to_i
              end_date = start_date.to_date.advance(days: duration - 1)
              if end_date < today
                trial_end_metadatum_batch << Metadatum.new(entity_id: user_id,
                                                           entity_type: 'User',
                                                           key: Constants.user_poster_trial_end_date_key,
                                                           value: end_date)
              end
            end
          end
        end
      end

      # Bulk insert or update for each batch
      Metadatum.import(trial_end_metadatum_batch, on_duplicate_key_ignore: true, batch_size: 100)
      # run callbacks for each metadatum
      trial_end_metadatum_batch.each do |metadatum|
        metadatum.send_trial_ended_event_to_mixpanel
      end

      # sync mixpanel user for each user
      user_ids_batch.each do |user_id|
        SyncMixpanelUser.perform_async(user_id)
      end

      offset += batch_size
    end
  end
end
