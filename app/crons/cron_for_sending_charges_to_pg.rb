require 'sidekiq-scheduler'

class CronForSendingChargesToPg
  include Sidekiq::Worker

  def perform
    Rails.logger.warn("Starting cron for sending charges to PG at #{Time.zone.now}")

    SubscriptionCharge.joins(:subscription)
                      .where(subscription_charges: { status: :created }, subscriptions: { status: [:active, :on_hold] })
                      .where("charge_date >= ?  AND charge_date < ?",
                             Time.zone.now.tomorrow.beginning_of_day,
                             Time.zone.now.tomorrow.end_of_day)
                      .order("subscriptions.payment_gateway ASC")
                      .pluck(:id, :payment_gateway, :amount, :user_id)
                      .each do |subscription_charge_id, payment_gateway|
      SendChargeToPg.perform_async(subscription_charge_id, payment_gateway)
    end
    Rails.logger.warn("Ending cron for sending charges to PG at #{Time.zone.now}")
  end
end
