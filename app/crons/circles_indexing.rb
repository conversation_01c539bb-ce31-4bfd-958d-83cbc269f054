# frozen_string_literal: true

require 'sidekiq-scheduler'

class CirclesIndexing
  include Sidekiq::Worker

  def perform
    logger.info('Indexing circles cron running')

    cid = $redis.get('indexing_last_circle_id_v2').to_i
    circle_id = nil

    count = 0
    Circle.where(id: cid..cid + 5000).each do |circle|
      circle_id = circle.id
      circle.index_for_search
      circle.update_members_count
      count += 1
      sleep 1 if (count % 100).zero?
    end

    $redis.set('indexing_last_circle_id_v2', circle_id.to_s)
  end
end
