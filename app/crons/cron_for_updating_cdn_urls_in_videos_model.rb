require 'sidekiq-scheduler'

class CronForUpdatingCdnUrlsInVideosModel
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.videos_table_last_processed_id_key).to_i

    records_to_update = []

    records = Video.where('id > ?', start_id)
                   .limit(25000)
                   .pluck(:id, :url, :source_url)

    return if records.empty?

    end_id = 0
    records.each do |id, url, source_url|
      if url.present? && source_url.present?
        records_to_update << [id, url, source_url]
      end
      end_id = id
    end

    $redis.set(Constants.videos_table_last_processed_id_key, end_id)

    return if records_to_update.empty?

    records_to_update.each_slice(250) do |batch|
      Video.where(id: batch.map(&:first)).update_all(
        Arel.sql(
          "url = REGEXP_REPLACE(url, '\.praja\.buzz', '.thecircleapp.in'),
    source_url = REGEXP_REPLACE(source_url, '\.praja\.buzz', '.thecircleapp.in')
    "
        )
      )
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Videos table url and source url update time: #{elapsed_time} sec || end_id: #{end_id}")
  end
end
