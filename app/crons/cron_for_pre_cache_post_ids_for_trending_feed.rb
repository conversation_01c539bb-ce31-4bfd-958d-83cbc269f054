require 'sidekiq-scheduler'

class CronForPreCachePostIdsForTrendingFeed
  include Sidekiq::Worker
  include TrendingFeed<PERSON><PERSON>y

  def perform
    logger.info("pre cache post ids for trending feed cron running")
    state_ids = Circle.get_state_ids

    state_ids.each do |state_id|
      Honeybadger.context(state_id: state_id)

      loaded_post_ids = []

      # 10 posts at a time
      count = 10

      posts_count = 1
      array_of_post_ids_with_score = []

      50.times do
        # get posts from es query with loaded feed items and offset
        posts_es = trending_feed_query_v3(loaded_post_ids, count, state_id)

        break if posts_es['hits']['hits'].blank?

        posts_es['hits']['hits'].each do |post|
          post_es_item = post['fields']
          array_of_post_ids_with_score << [posts_count, post_es_item['id'].first.to_i]
          loaded_post_ids << post_es_item['id'].first.to_i
          posts_count += 1
        end
      end

      cache_key = "#{Constants.trending_feed_cache_key}_#{state_id}"

      if array_of_post_ids_with_score.present?
        # set the temporary cache key
        temp_cache_key = "temporary_trending_feed_#{state_id}"

        # add new post ids to temporary cache key
        array_of_post_ids_with_score.each_slice(100) do |post_ids_with_score|
          $redis.zadd(temp_cache_key, post_ids_with_score)
        end

        if $redis.exists(temp_cache_key)
          # temporary cache key will be renamed to the cache key, temporary cache key will be deleted
          $redis.rename(temp_cache_key, cache_key)
        else
          # notify if temporary cache key not found
          Honeybadger.notify("Temporary cache key not found", context: { array_of_post_ids_with_score: array_of_post_ids_with_score, state_id: state_id })
        end
      else
        # notify if no posts found for trending feed pre caching
        Honeybadger.notify("No posts found for trending feed pre caching", context: { state_id: state_id })
      end

      # set the expiry time for the cache key
      $redis.expire(cache_key, 2.hours.to_i)
    end
  end
end
