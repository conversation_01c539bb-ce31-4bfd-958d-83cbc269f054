require 'sidekiq-scheduler'

class UpdateHashtagLikesAndOpinionsCount
include Sidekiq::Worker

  def perform
    logger.info("Update hashtag likes and opinions count cron running")

    hashtag_ids = $redis.hkeys(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY).map(&:to_i)
    hashtag_ids += $redis.hkeys(Hashtag::HASHTAG_OPINIONS_COUNT_REDIS_KEY).map(&:to_i)
    hashtag_ids.uniq!

    Hashtag.where(id: hashtag_ids).find_each(batch_size: 100) do |hashtag|
      $redis.hdel(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, hashtag.id)
      $redis.hdel(Hashtag::HASHTAG_OPINIONS_COUNT_REDIS_KEY, hashtag.id)
      hashtag.update_likes_and_opinions_count!
      sleep 0.5
    end
  end
end
