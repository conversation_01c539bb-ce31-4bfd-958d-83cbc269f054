# frozen_string_literal: true

class ProcessPostViewsBatch
  include Sidekiq::Worker

  def perform
    logger.info("ProcessPostViewsBatch cron running")

    key = Constants.post_views_queue_redis_key
    post_views_batch = []

    while (post_view = $redis.spop(key)) != nil
      post_view = JSON.parse(post_view)

      post_views_batch << PostView.new(post_id: post_view["post_id"],
                                       user_id: post_view["user_id"])
    end

    PostView.import(post_views_batch, on_duplicate_key_ignore: true, batch_size: 100) if post_views_batch.present?
  end
end
