require 'sidekiq-scheduler'

class CronForRemovingInvalidTruecallerUrlsInPhotos
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.truecaller_invalid_images_last_processed_id_key).to_i

    records_to_delete = []
    records_to_update = []
    records_to_check = []
    user_ids = []

    records = Photo.where('id > ?', start_id)
                   .limit(10000)
                   .pluck(:id, :user_id, :url)

    return if records.empty?

    end_id = 0
    records.each do |id, user_id, url|
      if url.present? && url.match?(/https:\/\/images-noneu\.truecallerstatic\.com/)
        user_ids << user_id
        records_to_check << [id, user_id, url]
      end
      end_id = id
    end

    $redis.set(Constants.truecaller_invalid_images_last_processed_id_key, end_id)

    users = User.where(id: user_ids.uniq).index_by(&:id)

    records_to_check.each do |id, user_id, url|
      response = Net::HTTP.get_response(URI.parse(url))

      if response.code.to_i != 200
        user = users[user_id]
        records_to_update << id if user&.photo_id == id
        records_to_delete << id
      elsif user&.photo_id == id
        UploadPhotoToS3UsingUrl.perform_in(5.minutes, id, url, user_id)
      end
      sleep(0.05)
    end

    User.where(photo_id: records_to_update).update_all(photo_id: nil) unless records_to_update.empty?

    unless records_to_delete.empty?
      records_to_delete.each_slice(250) do |batch|
        Photo.where(id: batch).delete_all
      end
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Truecaller invalid images removal time: #{elapsed_time.round(2)} sec || end_id: #{end_id}")
  end
end
