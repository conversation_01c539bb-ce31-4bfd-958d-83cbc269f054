require 'sidekiq-scheduler'

class UserMixpanelEventAfterGracePeriodExpires
  include Sidekiq::Worker

  def perform
    Metadatum.where(entity_type: "User", key: [Constants.grace_period_given_string, Constants.grace_period_given_string_expired])
             .where("DATE(value) = ?", (Time.zone.yesterday.advance(months: -Subscription::GRACE_PERIOD_IN_MONTHS))).find_each(batch_size: 1000) do |user_entity|
      SyncMixpanelUser.perform_async(user_entity.entity_id)
    end
  end
end
