require 'sidekiq-scheduler'

class PosterTrialExtensionStartedEventCron
  include Sidekiq::Worker

  def perform
    logger.info("poster trial extension started event cron running")
    batch_size = 1000
    offset = 0
    today = Time.zone.today

    loop do
      # get poster trial start date is in range of today
      metadata = Metadatum.where(key: Constants.user_poster_trial_start_date_key)
                          .where(value: today.beginning_of_day..today.end_of_day)
                          .limit(batch_size)
                          .offset(offset)

      break if metadata.empty?
      admin_user = AdminUser.find_by(id: 1)

      metadata.each do |metadatum|
        trial_start_date = metadatum.value.to_date
        user_id = metadatum.entity_id
        poster_trial_duration = Metadatum.where(entity_type: 'User',
                                                entity_id: user_id,
                                                key: Constants.user_poster_trial_duration_key).last&.value

        Metadatum.send_trial_started_event_to_mixpanel(user_id: user_id, trial_start_date: trial_start_date,
                                                       poster_trial_duration: poster_trial_duration,
                                                       admin_user: admin_user)
        # sync mixpanel user for each user
        SyncMixpanelUser.perform_async(user_id)
      end

      offset += batch_size
    end
  end
end
