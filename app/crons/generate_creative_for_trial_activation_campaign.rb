# frozen_string_literal: true
require 'sidekiq-scheduler'
class GenerateCreativeForTrialActivationCampaign
  include Sidekiq::Worker

  def perform
    current_time = Time.zone.now
    campaign_name = "trial_activation_wati_campaign_#{current_time.strftime("%Y_%m_%d")}"
    metadata_key = Constants.trial_activation_wati_campaign_count
    user_metadata_insertions = []

    UserPosterLayout.joins("INNER JOIN user_metadata ON user_metadata.user_id = user_poster_layouts.entity_id and user_metadata.key = '#{metadata_key}'")
                    .joins("LEFT JOIN user_wati_campaigns On user_wati_campaigns.user_id = user_poster_layouts.entity_id and user_wati_campaigns.campaign_type = 'trial_activation'")
                    .where(entity_type: "User", active: true)
                    .where("user_wati_campaigns.id IS NULL OR user_wati_campaigns.campaign_date > ?", 6.months.ago)
                    .group("user_poster_layouts.entity_id")
                    .having("SUM(CASE WHEN user_wati_campaigns.campaign_date > ? THEN 1 ELSE 0 END) < 6", 7.days.ago)
                    .having("COUNT(user_wati_campaigns.campaign_date) < 11")
                    .pluck(:entity_id)
                    .uniq
                    .each_slice(1000) do |users_ids|
      users = User.where(id: users_ids, internal: false)
      eligible_users = users.select{|user| user.is_eligible_for_start_trial? && !user.is_test_user_for_floww?}
      eligible_users.each do |eligible_user|
        user_creative_data = eligible_user.get_user_creative_data(campaign_name)
        next unless user_creative_data
        user_metadata_insertions << user_creative_data
        GeneratePosterCampaignImage.perform_async(eligible_user.id, user_creative_data.value, campaign_name)
      end
    end
    UserMetadatum.import(user_metadata_insertions, on_duplicate_key_ignore: true, batch_size: 100)

    target_time = current_time.change(hour: 5, min: 30, sec: 0)
    final_target_time = (target_time < current_time) ? target_time + 1.day : target_time
    InitiateWatiCampaignUsingCsv.perform_at(final_target_time, campaign_name, metadata_key)
  end
end
