require 'sidekiq-scheduler'

## Runs every 24 hours

class RemoveOldFeedEntries
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    ES_CLIENT.perform_request('POST', "#{EsUtil.get_new_posts_index_v2}/_delete_by_query", {}, {
      "query": {
        "range": {
          "created_at": {
            "lte": (Time.zone.now - 3.days).to_i * 1000
          }
        }
      }
    })
  end
end
