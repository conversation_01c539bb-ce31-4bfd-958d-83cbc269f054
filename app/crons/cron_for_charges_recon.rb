require 'sidekiq-scheduler'

class CronForChargesRecon
  include Sidekiq::Worker

  def perform
    SubscriptionCharge
      .joins(:subscription)
      .where("charge_date >= ? AND charge_date < ?",
             Time.zone.now.yesterday.beginning_of_day,
             Time.zone.today.beginning_of_day)
      .where(subscription_charges: { status: :sent_to_pg })
      .where(subscriptions: { status: [:active, :on_hold] })
      .pluck(:id)
      .each do |charge_id|
      SubscriptionChargeReconcile.perform_async(charge_id)
    end
  end
end
