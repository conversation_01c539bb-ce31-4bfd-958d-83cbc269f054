require 'sidekiq-scheduler'

class CronForVerifyingChargesCreatedAndSent
  include Sidekiq::Worker

  sidekiq_options queue: :critical, retry: 0

  def perform
    recon_charge_creations
    recon_charge_sendings
  end

  def recon_charge_creations
    subscription_ids = Subscription
                         .joins("INNER JOIN user_plans ON subscriptions.user_id = user_plans.user_id AND subscriptions.plan_id = user_plans.plan_id")
                         .where('user_plans.end_date <= ?', Time.zone.now.tomorrow.end_of_day)
                         .where('user_plans.end_date >= ?', Time.zone.now.tomorrow.beginning_of_day)
                         .where('subscriptions.status = ?', Subscription.statuses[:active])
                         .where('subscriptions.max_amount >= user_plans.amount')
                         .pluck('subscriptions.id')

    raised_subscription_ids = SubscriptionCharge
                                .where(subscription_id: subscription_ids)
                                .where("created_at >= ?", Time.zone.now.change(hour: 14, min: 29, sec: 0))
                                .where("created_at < ?", Time.zone.now.change(hour: 14, min: 45, sec: 0))
                                .pluck(:subscription_id)

    if subscription_ids - raised_subscription_ids != [] || subscription_ids.uniq.count != raised_subscription_ids.uniq.count
      notifier = Slack::Notifier.new(
        "*******************************************************************************"
      )
      notifier.post(
        text: ":alert: *Charges created Mismatch* on #{Time.zone.now.strftime('%d %b, %Y')}\n" +
          "Subs count (#{subscription_ids.count}) != Raised Subs count (#{raised_subscription_ids.count})",
        attachments: [
          {
            text: "Subscriptions: #{subscription_ids.join(', ')}",
            color: 'danger'
          }, {
            text: "Raised Subs: #{raised_subscription_ids.join(', ')}",
            color: 'good'
          }
        ]
      )
    end
  end

  def recon_charge_sendings
    missed_sending_charge_ids = SubscriptionCharge.joins(:subscription)
                                                  .where(subscription_charges: { status: :created }, subscriptions: { status: [:active, :on_hold] })
                                                  .where("charge_date >= ?  AND charge_date < ?",
                                                         Time.zone.now.tomorrow.beginning_of_day,
                                                         Time.zone.now.tomorrow.end_of_day)
                                                  .pluck(:id)

    if missed_sending_charge_ids.count > 0
      notifier = Slack::Notifier.new(
        "*******************************************************************************"
      )
      notifier.post(
        text: ":alert: *Missed sending charges* on #{Time.zone.now.strftime('%d %b, %Y')}\n" +
          "Missed count: #{missed_sending_charge_ids.count}",
        attachments: [
          {
            text: "Missed subscription charge ids: #{missed_sending_charge_ids.join(', ')}",
            color: 'danger'
          }
        ]
      )
    end
  end
end
