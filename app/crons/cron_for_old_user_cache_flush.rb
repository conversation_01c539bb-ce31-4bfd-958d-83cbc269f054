# frozen_string_literal: true
require 'sidekiq-scheduler'

class CronForOldUserCacheFlush
  include Sidekiq::Worker

  def perform
    logger.info("delete old user cache clear cron running")
    key = 'cache_clear_last_user_id'
    uid = $redis.get(key).to_i
    user_id = nil
    User.where(id: uid..uid + 50000).find_each(batch_size: 10000) do |user|
      user_id = user.id
      cache_key_25 = 'user_v25'
      cache_key_26 = 'user_v26'
      Rails.cache.delete([cache_key_25, user_id])
      Rails.cache.delete([cache_key_26, user_id])
    end

    $redis.set(key, user_id.to_s)
    logger.info("delete old user cache clear cron ended")
  end
end
