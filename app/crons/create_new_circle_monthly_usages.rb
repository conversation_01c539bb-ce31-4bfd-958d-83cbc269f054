require 'sidekiq-scheduler'

class CreateNewCircleMonthlyUsages
  include Sidekiq::Worker

  # Run this cron on start of the new day
  # This cron is used to create the circle monthly usages for mappings that would become active today
  def perform
    logger.info("update circle monthly usages cron running")

    today = Time.zone.today
    CirclePackageMapping.where(active: true)
                        .where('start_date = ? AND ? < end_date', today, today)
                        .each do |circle_package_mapping|
      circle = circle_package_mapping.circle
      if circle_package_mapping.id == circle.active_circle_package_mapping.id
        month_end = circle_package_mapping.calculate_month_end(circle_package_mapping.start_date)
        CircleMonthlyUsage.create(circle_id: circle.id,
                                  month_start: circle_package_mapping.start_date,
                                  month_end: month_end,
                                  active: true,
                                  channel_message_limit: circle_package_mapping.circle_package.channel_post_msg_limit,
                                  channel_message_usage: 0,
                                  channel_notification_limit: circle_package_mapping.circle_package.channel_post_msg_notification_limit,
                                  channel_notification_usage: 0,
                                  fan_posters_limit: circle_package_mapping.circle_package.fan_poster_creatives_limit,
                                  fan_posters_usage: 0)
      end
    end

    #inactivate all circle package mappings of past
    CirclePackageMapping.where(active: true).where('end_date < ?', today).update_all(active: false)
  end
end
