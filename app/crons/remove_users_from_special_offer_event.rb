require 'sidekiq-scheduler'

class RemoveUsersFromSpecialOfferEvent
  include Sidekiq::Worker

  def perform
    Rails.logger.warn("Starting cron for removing users from special offer at #{Time.zone.now}")
    current_time = Time.zone.now.to_i
    begin
      $redis.pipelined do
        [
          Constants.premium_1_rs_user_redis_key,
          Constants.premium_29_rs_campaign_redis_key,
          Constants.premium_59_rs_campaign_redis_key,
          Constants.premium_half_price_campaign_redis_key,
          Constants.monthly_campaign_for_yearly_users_key
        ].each { |key| $redis.zremrangebyscore(key, '-inf', current_time) }
      end
    rescue => e
      Rails.logger.error("Error removing users from special offer: #{e.message}")
      raise e
    end
    Rails.logger.warn("Ending cron for removing users from special offer at #{Time.zone.now}")
  end
end
