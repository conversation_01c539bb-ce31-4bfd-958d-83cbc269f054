require 'sidekiq-scheduler'

class IndexAndSyncToMixpanelUsersCron
  include Sidekiq::Worker

  # def perform
  #   logger.info("Indexing users cron running")
  #
  #   key = "index_and_sync_to_mixpanel_users_cron"
  #   uid = $redis.get(key).to_i
  #   return if uid == 0
  #   last_user_id = uid
  #   # 1070202 is the first user id of 2023-10-14
  #
  #   (uid..(uid + 1000)).each do |user_id|
  #     threads = []
  #     threads << Thread.new do
  #       SyncMixpanelUser.new.perform(user_id)
  #     end
  #     threads << Thread.new do
  #       IndexSearchEntity.new.perform('user', user_id)
  #     end
  #     # Wait for both threads to finish
  #     threads.each(&:join)
  #     last_user_id = user_id
  #   end
  #
  #   last_user = User.find_by(id: last_user_id)
  #   if last_user.blank?
  #     # notify honeybadger
  #     Honeybadger.notify("IndexAndSyncToMixpanelUsersCron: last_user is blank and last user id is #{last_user_id}")
  #     $redis.del(key)
  #     return
  #   end
  #
  #   $redis.set(key, last_user_id.to_s)
  # end
end
