# frozen_string_literal: true

class CronForQualifiedLeads
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0

  def perform
    Rails.logger.info("Qualified leads cron running")
    today = Time.zone.today

    eligible_qualified_lead_user_ids = PremiumPitch.eligible_for_qualified_pitch.pluck(:user_id)
    return if eligible_qualified_lead_user_ids.empty?

    user_ids_start_dates_batch = Metadatum.where(key: Constants.user_poster_trial_start_date_key)
                                          .where('value >= ?', 31.days.ago.to_date)
                                          .where(entity_id: eligible_qualified_lead_user_ids, entity_type: 'User')
                                          .pluck(:entity_id, :value)
    return if user_ids_start_dates_batch.empty?

    user_ids = user_ids_start_dates_batch.map(&:first)

    user_ids_duration_batch = Metadatum.where(key: Constants.user_poster_trial_duration_key)
                                       .where(entity_id: user_ids, entity_type: 'User')
                                       .order(id: :asc)
                                       .pluck(:entity_id, :value)

    user_ids_batch = {}
    user_ids_start_dates_batch.each do |user_id, start_date|
      duration = (user_ids_duration_batch.find { |id, _| id == user_id }&.second).to_i
      next if duration.zero?

      end_date = start_date.to_date + (duration - 1).days
      next if end_date > today + (Constants.no_of_days_before_to_check_for_qualified_lead - 1).days || end_date < today

      user_ids_batch[user_id] = { start_date: start_date.to_date, duration: duration, end_date: end_date }
    end

    user_ids_batch.each do |user_id, data|
      user = User.find(user_id)
      poster_usage_count_after_trial_enabled = user.premium_poster_usage_count_after_trial_enabled

      if poster_usage_count_after_trial_enabled >= Constants.poster_usage_count_threshold_for_qualified_pitch_lead
        premium_pitch = user.premium_pitch
        premium_pitch.reached_milestone_2! if premium_pitch&.may_reached_milestone_2?
      elsif today >= data[:end_date] - (Constants.no_of_days_before_to_check_for_low_usage - 1).days
        premium_pitch = user.premium_pitch
        premium_pitch.mark_trial_low_usage! if premium_pitch&.may_mark_trial_low_usage?
      end
    end
  end
end
