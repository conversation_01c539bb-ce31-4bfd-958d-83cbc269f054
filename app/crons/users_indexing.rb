require 'sidekiq-scheduler'

class UsersIndexing
  # include Sidekiq::Worker
  #
  # def perform
  #   # return
  #
  #   logger.info("Indexing users cron running")
  #
  #   key = "indexing_last_user_id_for_search_entity_v3"
  #
  #   uid = $redis.get(key).to_i
  #   user_id = nil
  #   user_index_objects = []
  #   User.where(id: uid..uid + 10000).each do |user|
  #     user_id = user.id
  #     # { :update => { :_index => 'myindexB', :_type => 'mytype', :_id => '2', :data => { :doc => { :title => 'Update' } } } }
  #     user_index_objects << {
  #       update: {
  #         _index: EsUtil.get_search_entities_index,
  #         _type: '_doc',
  #         _id: "user-#{user.id}",
  #         data: {
  #           doc: user.search_entity_obj
  #         }
  #       }
  #     }
  #     # IndexSearchEntity.perform_async('user', user_id)
  #     # user.app_version = UserTokenUsage.joins(:user_token).where(user_tokens: { user_id: user_id }).last.try(:app_version)
  #     # user.save! #added to call triming user name by before_save callback
  #     # user.index_for_search #removed as user.commit already calling this function in model callbacks
  #     # sleep 1 if (count % 500).zero?
  #   end
  #
  #   if user_index_objects.present?
  #     user_index_objects.each_slice(500) do |slice|
  #       ES_CLIENT.bulk index: EsUtil.get_search_entities_index, body: slice
  #       sleep 1
  #     end
  #
  #     $redis.set(key, user_id.to_s)
  #   end
  # end
end
