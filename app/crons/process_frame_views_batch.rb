# frozen_string_literal: true

class ProcessFrameViewsBatch
  include Sidekiq::Worker

  def perform
    logger.info("ProcessFrameViewsBatch cron running")

    key = Constants.frame_views_queue_redis_key
    frame_views_batch = []

    while (frame_view = $redis.spop(key)) != nil
      frame_view = JSON.parse(frame_view)

      frame_views_batch << FrameView.new(frame_id: frame_view["frame_id"],
                                         user_id: frame_view["user_id"],
                                         viewed_at: frame_view["viewed_at"])
    end

    FrameView.import(frame_views_batch, on_duplicate_key_ignore: true, batch_size: 100) if frame_views_batch.present?
  end
end
