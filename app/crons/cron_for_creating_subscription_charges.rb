require 'sidekiq-scheduler'

class CronForCreatingSubscriptionCharges
  include Sidekiq::Worker

  sidekiq_options queue: :critical, retry: 0

  def perform
    Subscription
      .joins("INNER JOIN user_plans ON subscriptions.user_id = user_plans.user_id AND subscriptions.plan_id = user_plans.plan_id")
      .where('user_plans.end_date <= ?', Time.zone.now.tomorrow.end_of_day)
      .where('user_plans.end_date >= ?', Time.zone.now.tomorrow.beginning_of_day)
      .where('subscriptions.status = ?', Subscription.statuses[:active])
      .where('subscriptions.max_amount >= user_plans.amount')
      .pluck('subscriptions.id, subscriptions.user_id, user_plans.amount')
      .each do |e|
      user_id = e[1]
      user = User.find_by_id(user_id)
      subscription_id = e[0]
      if user.active_user_poster_layout.blank?
        subscription = Subscription.find_by_id(subscription_id)
        subscription.pause!
        next
      end

      # Create a subscription charge
      SubscriptionCharge.create!(subscription_id: subscription_id,
                                 user_id: user_id,
                                 amount: e[2],
                                 charge_amount: e[2],
                                 payment_id: Subscription.generate_payment_id,
                                 attempt_number: 1,
                                 charge_date: Time.zone.now.tomorrow.beginning_of_day,
                                 status: :created)
    end
  end
end
