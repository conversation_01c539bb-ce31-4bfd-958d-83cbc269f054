class UserPostsController < ApiController
  include ActionController::Cookies
  include TrendingFeedQuery
  include RelevantFeedItemConcern
  include KnowYourContestant
  include Manifesto
  include Prepoll
  include Elections2024
  include ElectionTicker
  include ElectionConstituencyStatus
  include ElectionAppVersionUpdate

  before_action :set_logged_in_user

  # POST /trending-feed
  def trending_feed
    trending_feed_v2
  end

  def trending_feed_v2
    return render json: { success: false, message: 'యూసర్ కనుగొనబడలేదు' }, status: :not_found if @user.blank?

    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    page = (offset / count) + 1

    toast_feed = []
    # if AppVersionSupport.is_feed_toast_supported? && page == 1 &&
    #   (Time.zone.today.to_s == '2023-12-03' || @user.is_test_user?)
    #   elections_toast = ElectionsUtil.get_tg_elections_feed_toast(@user.id)
    #   toast_feed << elections_toast if elections_toast.present?
    # end

    referral_feed = []
    referral_feed = [@user.user_referral] if @app_version >= Gem::Version.new('1.12.5') && @user.user_referral.present? && page == 1

    loaded_post_ids = []
    loaded_post_ids = params[:loaded_post_ids] if params[:loaded_post_ids].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?

    hashtags_feed = []
    hashtags_feed = Hashtag.get_trending_hashtags(@user.id) if page == 1

    if @app_version >= Gem::Version.new('1.14.2') && page == 1
      hashtags_feed = [{ "feed_type": 'trending_hashtags', 'hashtags': hashtags_feed }]
    end

    new_circles_feed = []

    if page == 1 && @app_version >= Gem::Version.new('0.6.1') && @user.village_id.present?
      new_circles_feed = get_circles_suggestions
      count -= 1 if new_circles_feed.present?
    end

    if count <= 0
      render json: hashtags_feed + [new_circles_feed], status: :ok
      return
    end

    suggested_users_feed = []
    if page >= 2 && @app_version > Gem::Version.new('1.16.1')
      list_feed = @user.get_suggested_users_feed_v1(loaded_feed_item_ids)
      if list_feed.present?
        list_feed[:custom_properties][:list_source] = 'TRENDING_FEED'
        suggested_users_feed << list_feed
        count -= 1
      end
    end

    suggested_contacts_feed = []
    list_feed = nil
    if page == 3 && @app_version >= Gem::Version.new('1.11.3')
      # Skylight.instrument title: 'User contacts suggestions' do
      list_feed = @user.get_suggested_contacts_feed_object
      # end

      if list_feed.present?
        suggested_contacts_feed << list_feed
        count -= 1
      end
    end

    posts_feed = []
    # Skylight.instrument title: 'Posts feed V2' do
    posts_feed = posts_feed_v3(loaded_post_ids, count - toast_feed.count - hashtags_feed.count - referral_feed.count)
    # end

    # Skylight.instrument title: 'Set feed' do
    posts_feed.insert(0, new_circles_feed) if new_circles_feed.present?

    if suggested_users_feed.present?
      i = 0

      suggested_users_feed.each do |su|
        i = [i, [(posts_feed.count - 1), 0].max].min

        posts_feed.insert(i, su)

        i += 2
      end
    end

    if suggested_contacts_feed.present?
      i = 6
      suggested_contacts_feed.each do |su|
        i = [i, [(posts_feed.count - 1), 0].max].min
        posts_feed.insert(i, su)
        i += 2
      end
    end
    # end

    feed_items = toast_feed + hashtags_feed + referral_feed + posts_feed
    feed_items.compact!

    render json: feed_items, status: :ok
  end

  def election_feed
    return render json: { success: false, message: 'యూసర్ కనుగొనబడలేదు' }, status: :not_found if @user.blank?

    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    page = (offset / count) + 1

    loaded_post_ids = []
    loaded_post_ids = params[:loaded_post_ids] if params[:loaded_post_ids].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?

    constituencies_feed = []
    if page == 1 && @user.mla_constituency_id.blank?
      constituencies_feed = @user.get_constituencies_feed
      count -= 1 if constituencies_feed.present?
    end
    carousel_feed = []
    posts_feed = []
    # adding this mla and mp contestant carousel feed for testing purpose. need to confirm the exact position of this in
    # the feed
    mla_contestant_carousel_feed = []
    mla_contestant_circles_feed = []
    mp_contestant_carousel_feed = []
    party_mla_contestant_carousel_feed = []
    party_mp_contestant_carousel_feed = []
    district_mla_contestant_carousel_feed = []
    district_constituency_status_feed = []
    user_constituency_congrats_carousel = []
    user_joined_party_mla_congrats_carousels = []
    user_joined_party_mp_congrats_carousels = []
    election_user_constituency_status_carousel = []
    state_parliament_constituency_status_feed = []
    user_district_assembly_winners_congrats_carousel = []

    if page == 1
      election_app_update_toast = update_for_election_live_updates_toast
      count -= 1 if election_app_update_toast.present?

      election_centre_ticker = election_ticker_by_ticker_id(CENTRE_TICKER_ID, @user)
      count -= 1 if election_centre_ticker.present?

      user_constituency_congrats_carousel = user_constituency_congrats_carousel(@user)
      count -= 1 if user_constituency_congrats_carousel.present?

      election_user_constituency_status_carousel = user_constituency_status_carousel(@user)
      count -= 1 if election_user_constituency_status_carousel.present?

      election_ticker_preview_toast = election_ticker_preview(@user)
      count -= 1 if election_ticker_preview_toast.present?

      user_joined_party_mla_congrats_carousels = user_joined_party_mla_congrats_carousel(@user)
      count -= user_joined_party_mla_congrats_carousels.count

      user_joined_party_mp_congrats_carousels = user_joined_party_mp_congrats_carousel(@user)
      count -= user_joined_party_mp_congrats_carousels.count

      user_district_assembly_winners_congrats_carousel = user_district_assembly_winners_congrats_carousel(user: @user, district_id: @user.district_id)
      count -= 1 if user_district_assembly_winners_congrats_carousel.present?

      district_constituency_status_feed = district_constituency_status_carousel(user_id: @user.id, district_id: @user.district_id)
      count -= 1 if district_constituency_status_feed.present?

      state_parliament_constituency_status_feed = state_parliament_constituency_status_carousel(user_id: @user.id, state_id: @user.state_id)
      count -= 1 if state_parliament_constituency_status_feed.present?

      secondary_tickers_feed = secondary_tickers(@user)
      count -= secondary_tickers_feed.count
    end

    if page == 1 && AppVersionSupport.know_your_contestant_carousel_supported?
      # prepoll_carousel = prepoll_carousel(@user)
      # count -= 1 if prepoll_carousel.present?
      #
      # mla_contestant_carousel_feed = get_mla_contestant_carousel(@user)
      # count -= 1 if mla_contestant_carousel_feed.present?
      #
      # mp_contestant_carousel_feed = get_mp_contestant_carousel(@user)
      # count -= 1 if mp_contestant_carousel_feed.present?

      # Stop showing manifesto carousels
      # manifesto_carousel_sections = manifesto_carousels(@user)
      # count -= manifesto_carousel_sections.count

      # Stop showing contestant carousels
      # party_mla_contestant_carousel_feed = get_party_mla_contestant_carousels(@user)
      # count -= 1 if party_mla_contestant_carousel_feed.present?

      # party_mp_contestant_carousel_feed = get_party_mp_contestant_carousels(@user)
      # count -= 1 if party_mp_contestant_carousel_feed.present?

      # district_mla_contestant_carousel_feed = get_district_mla_contestant_carousel(@user)
      # count -= 1 if district_mla_contestant_carousel_feed.present?
    elsif page == 1 && !AppVersionSupport.know_your_contestant_carousel_supported? && @user.village_id.present?
      # mla_contestant_circles_feed = get_mla_contestant_circle_suggestions
      # count -= 1 if mla_contestant_circles_feed.present?
    end

    if election_app_update_toast.present? && !AppVersionSupport.live_toast_available?
      carousel_feed << election_app_update_toast
    end

    carousel_feed << user_constituency_congrats_carousel if user_constituency_congrats_carousel.present?

    if election_user_constituency_status_carousel.present?
      carousel_feed << election_user_constituency_status_carousel
    end

    if election_ticker_preview_toast.present?
      carousel_feed << election_ticker_preview_toast
    end

    if user_joined_party_mla_congrats_carousels.present?
      user_joined_party_mla_congrats_carousels.each do |carousel|
        carousel_feed << carousel
      end
    end

    if user_joined_party_mp_congrats_carousels.present?
      user_joined_party_mp_congrats_carousels.each do |carousel|
        carousel_feed << carousel
      end
    end

    carousel_feed << user_district_assembly_winners_congrats_carousel if user_district_assembly_winners_congrats_carousel.present?

    if election_centre_ticker.present?
      carousel_feed << election_centre_ticker
    end

    carousel_feed << district_constituency_status_feed if district_constituency_status_feed.present?
    carousel_feed << state_parliament_constituency_status_feed if state_parliament_constituency_status_feed.present?

    if secondary_tickers_feed.present?
      secondary_tickers_feed.each do |secondary_ticker|
        carousel_feed << secondary_ticker
      end
    end

    # if prepoll_carousel.present?
    #   carousel_feed << prepoll_carousel
    # end
    #
    # if mla_contestant_carousel_feed.present?
    #   carousel_feed << mla_contestant_carousel_feed
    # end
    # if mp_contestant_carousel_feed.present?
    #   carousel_feed << mp_contestant_carousel_feed
    # end

    # if manifesto_carousel_sections.present?
    #   manifesto_carousel_sections.each do |section|
    #     carousel_feed << section
    #   end
    # end

    # if party_mla_contestant_carousel_feed.present?
    #   carousel_feed += party_mla_contestant_carousel_feed.flatten.compact
    # end
    # if party_mp_contestant_carousel_feed.present?
    #   carousel_feed += party_mp_contestant_carousel_feed.flatten.compact
    # end
    # if district_mla_contestant_carousel_feed.present?
    #   carousel_feed << district_mla_contestant_carousel_feed
    # end

    # if mla_contestant_circles_feed.present?
    #   posts_feed.insert(0, mla_contestant_circles_feed)
    # end

    # update carousels view count post election feed view
    update_carousels_view_count_post_election_view(@user.id)

    render json: constituencies_feed + posts_feed + carousel_feed, status: :ok
  end

  # POST /my-posts
  # POST /my-feed
  def my_feed
    my_feed_v2
  end

  def my_feed_v2
    return render json: { success: false, message: 'యూసర్ కనుగొనబడలేదు' }, status: :not_found if @user.blank?

    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    page = (offset / count) + 1

    loaded_post_ids = []
    loaded_post_ids = params[:loaded_post_ids] if params[:loaded_post_ids].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?

    Rails.logger.warn("My feed - user_id: #{@user.id}, offset: #{offset}, count: #{count}, loaded_post_ids: #{loaded_post_ids}, loaded_feed_item_ids: #{loaded_feed_item_ids}, app_version: #{@app_version}")

    user_blocked_ids = @user.get_user_blocked_ids

    post_view_ids = @user.get_post_view_ids
    relevant_my_feed_items = []
    # skip relevant_my_feed_items_for_user if page is 1 and loaded_feed_item_ids is not empty
    if page == 1 && loaded_feed_item_ids.count.positive?
      relevant_my_feed_items << nil
    else
      relevant_my_feed_items += relevant_my_feed_items_for_user(loaded_feed_item_ids:)
    end
    relevant_my_feed_items = relevant_my_feed_items.compact
    count -= relevant_my_feed_items.size if relevant_my_feed_items.present?

    election_tickers = election_tickers_my_feed(loaded_feed_item_ids).compact
    count -= election_tickers.size

    posts_feed = posts_my_feed_v2(post_view_ids, loaded_post_ids, user_blocked_ids, count, offset)

    feed_items = relevant_my_feed_items + election_tickers + posts_feed

    render json: feed_items, status: :ok
  end

  def posts_my_feed_v2(post_view_ids, loaded_post_ids, user_blocked_ids, count, offset)
    post_ids = []
    post_user_ids = []

    posts_es = get_posts_es_v1(post_view_ids, loaded_post_ids, user_blocked_ids, count)

    # ::NewRelic::Agent.record_metric('UserPostsController/MyFeed/SearchHits', posts_es['hits']['hits'].count)
    posts_es['hits']['hits'].each do |hit|
      post_es_item = hit['fields']
      post_ids << post_es_item['id'].first
      post_user_ids << post_es_item['user_id'].first
    end

    following_data = UserFollower.select(:user_id, :source_of_follow)
                                 .where(user_id: post_user_ids, follower_id: @user.id, active: true)
    followed_user_ids = following_data.map(&:user_id)
    source_of_follows = following_data.map(&:source_of_follow)

    liked_post_ids = PostLike.get_liked_post_ids(post_ids, @user.id)

    post_rank = 0
    posts_feed = posts_es['hits']['hits'].map do |hit|
      post_rank += 1
      post_es_item = hit['fields']

      post_obj = Post.new
      post_obj.id = post_es_item['id'].first
      post = Post.get_feed_item(post_es_item['id'].first, @user, @app_version)

      post[:user].follows = followed_user_ids.include? post_es_item['user_id'].first.to_i
      post[:user].source_of_follow = if post[:user].follows
                                       index = followed_user_ids.index(post_es_item['user_id'].first.to_i)
                                       source_of_follows[index]
                                     end

      post[:likes_count] = post_es_item['likes_count'].first
      post[:comments_count] = post_es_item['comments_count'].first
      post[:opinions_count] = post_es_item['opinions_count'].first
      post[:whatsapp_count] = post_es_item['whatsapp_count'].first
      post[:preview_comments] = []
      post[:user_liked] = liked_post_ids.include? post_es_item['id'].first.to_i
      post[:news_worthy_score] = post_es_item['news_worthy_score']&.first

      custom_properties = { post_type: 'normal', post_user_id: post_es_item['user_id'].first.to_i,
                            post_rank: offset + post_rank }.merge!(log_view_post_element(post_es_item, post))
      post[:custom_properties] = custom_properties

      post
    end

    posts_feed
  end

  def get_posts_es_v1(post_view_ids, loaded_post_ids, user_blocked_ids, count)
    internet_connection = if request.headers['X-Internet-Connection'].present?
                            request.headers['X-Internet-Connection'].strip.downcase
                          end

    if internet_connection.blank?
      internet_connection = 'mobile'
    end

    query = my_feed_query_v2(post_view_ids, loaded_post_ids, user_blocked_ids, count, internet_connection)
    ES_CLIENT.search index: EsUtil.get_new_posts_index_v2, body: query
  end

  def my_feed_query_v2(post_view_ids, loaded_post_ids, user_blocked_ids, count, internet_connection)
    is_telangana_user = @user.state_id == 33010
    {
      "fields": ['id', 'user_id', 'likes_count', 'comments_count', 'opinions_count', 'whatsapp_count',
                 'tagged_village_circle_ids', 'tagged_mandal_circle_ids', 'tagged_mla_constituency_circle_ids',
                 'tagged_mp_constituency_circle_ids', 'tagged_district_circle_ids', 'tagged_state_circle_ids',
                 'tagged_interest_circle_ids', 'has_poster_photo', 'news_worthy_score'],
      "_source": false,
      "query": {
        "script_score": {
          "query": {
            "bool": {
              "should": [
                {
                  "bool": {
                    "filter": [
                      {
                        "terms": {
                          "tagged_interest_circle_ids": @user.get_user_joined_party_circle_ids + @user.get_user_joined_leader_ids
                        }
                      },
                      {
                        "range": {
                          "created_at": {
                            "gte": 'now-2d/h'
                          }
                        }
                      },
                      {
                        "range": {
                          "likes_count": {
                            "gte": FeedConfig.get_likes_count_for_interest_circle_filter(@user.state_id)
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  "bool": {
                    "filter": [
                      {
                        "term": {
                          "post_user_followers": @user.id
                        }
                      },
                      {
                        "range": {
                          "created_at": {
                            "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  "bool": {
                    "filter": [
                      {
                        "term": {
                          "user_district_id": @user.district_id
                        }
                      },
                      {
                        "range": {
                          "created_at": {
                            "gte": 'now-1d/h'
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  "bool": {
                    "filter": [
                      {
                        "term": {
                          "user_state_id": @user.state_id
                        }
                      },
                      {
                        "range": {
                          "created_at": {
                            "gte": 'now-2d/h'
                          }
                        }
                      },
                      {
                        "range": {
                          "likes_count": {
                            "gte": FeedConfig.get_likes_count_for_state_level(@user.state_id)
                          }
                        }
                      },
                      {
                        "range": {
                          "comments_count": {
                            "gte": FeedConfig.get_comments_count_for_state_level(@user.state_id)
                          }
                        }
                      },
                      {
                        "range": {
                          "unique_users_whatsapp_count": {
                            "gte": FeedConfig.get_whatsapp_count_for_state_level(@user.state_id)
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  "bool": {
                    "filter": [
                      {
                        "range": {
                          "created_at": {
                            "gte": 'now-1d/h'
                          }
                        }
                      },
                      {
                        "range": {
                          "likes_count": {
                            "gte": 40
                          }
                        }
                      },
                      {
                        "range": {
                          "comments_count": {
                            "gte": 3
                          }
                        }
                      },
                      {
                        "range": {
                          "unique_users_whatsapp_count": {
                            "gte": 3
                          }
                        }
                      }
                    ]
                  }
                }
              ],
              "filter": [
                {
                  "range": {
                    "created_at": {
                      "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                    }
                  }
                },
                {
                  "term": {
                    "active": true
                  }
                }
              ],
              "must_not": [
                {
                  "terms": {
                    "id": loaded_post_ids
                  }
                },
                {
                  "terms": {
                    "user_id": user_blocked_ids
                  }
                }
              ],
              "minimum_should_match": 1
            }
          },
          "script": {
            "lang": 'painless',
            "source": '' "
                              double currentTime = new Date().getTime();
                              double timeDiff = (currentTime - params._source.created_at)/(1000*60*60);
                              double score = 0;
                              double reactions_score = 0;

                              if (params['is_ai_feed_enabled']){
                                  params._source.likes_count = params._source.likes_count + (params._source.news_worthy_score != null ? params._source.news_worthy_score : 0);
                              }

                              reactions_score =  (params._source.likes_count +
                                 (params._source.comments_count > 2 ? params._source.comments_count * 2 : 0) +
                                 (params._source.unique_users_whatsapp_count > 2 ? params._source.unique_users_whatsapp_count * 1.5 : 0));

                              score = (1 + 0.8 * Math.sqrt(Math.max(0, (reactions_score - params._source.reports_count))) * Math.exp(-timeDiff/20));

                              if (params._source.post_user_followers.contains(params['userId']) || params.userJoinedCircleOwnerIds.contains(params._source.user_id)) {
                                     score = score * params['followingWt'];
                              }

                              double organisation_score = 0;
                              double location_score = 0;
                              if (params._source.tagged_state_circle_ids.length > 0) {
                                if (params._source.tagged_village_circle_ids.contains(params['villageId'])){
                                  location_score = params['villageWt'];
                                }
                                else if (params._source.tagged_mandal_circle_ids.contains(params['mandalId'])){
                                  location_score = params['mandalWt'];
                                }
                                else if (params._source.tagged_mla_constituency_circle_ids.contains(params['mlaConstituencyId'])){
                                  location_score = params['mlaConstituencyWt'];
                                }
                                else if (params._source.tagged_mp_constituency_circle_ids.contains(params['mpConstituencyId'])){
                                  location_score = params['mpConstituencyWt'];
                                }
                                else if (params._source.tagged_district_circle_ids.contains(params['districtId'])){
                                  location_score = params['districtWt'];
                                }
                                else if (params._source.tagged_state_circle_ids.contains(params['stateId'])){
                                  location_score = params['stateWt'];
                                }
                                else {
                                  location_score = params['languageWt'];
                                }
                              }
                              else {
                                  if (params['core_user_ids_with_reduced_weightages'].contains(params._source.user_id)){
                                    location_score = params['districtWt'];
                                  }
                                  else if (params['core_party_user_ids'].contains(params._source.user_id)){
                                    location_score = params['stateWt'];
                                  }
                                  else if (params['villageId'] == params._source.user_village_id){
                                    location_score = params['villageWt'];
                                  }
                                  else if (params['mandalId'] ==  params._source.user_mandal_id){
                                    location_score = params['mandalWt'];
                                  }
                                  else if (params['mlaConstituencyId'] ==  params._source.user_mla_constituency_id){
                                    location_score = params['mlaConstituencyWt'];
                                  }
                                  else if (params['mpConstituencyId'] ==  params._source.user_mp_constituency_id){
                                    location_score = params['mpConstituencyWt'];
                                  }
                                  else if (params['districtId'] ==  params._source.user_district_id){
                                    location_score = params['districtWt'];
                                  }
                                  else if (params['stateId'] == params._source.user_state_id ){
                                    location_score = params['stateWt'];
                                  }
                                  else {
                                    location_score = params['languageWt'];
                                  }
                               }

                              double max_interest_score = 0;
                              double min_interest_score = 0;
                              for (interest_circle_id in params._source.tagged_interest_circle_ids) {
                                if (params['joinedPoliticalPartiesIds'].contains(interest_circle_id)) {
                                      if(params['partyWt'] > max_interest_score) {
                                          if (min_interest_score == 0 || min_interest_score > max_interest_score) {
                                          min_interest_score = max_interest_score;
                                          }
                                          max_interest_score = params['partyWt'];
                                      }
                                      else if(min_interest_score == 0 || params['partyWt'] < min_interest_score) {
                                          min_interest_score = params['partyWt'];
                                      }
                                }
                                else if (params['joinedStateLeadersIds'].contains(interest_circle_id)) {
                                      if(params['stateLeadersWt'] > max_interest_score) {
                                          if (min_interest_score == 0 || min_interest_score > max_interest_score) {
                                          min_interest_score = max_interest_score;
                                          }
                                          max_interest_score = params['stateLeadersWt'];
                                      }
                                      else if(min_interest_score == 0 || params['stateLeadersWt'] < min_interest_score) {
                                          min_interest_score = params['stateLeadersWt'];
                                      }
                                }
                                else if (params['joinedLocalLeadersIds'].contains(interest_circle_id)) {
                                      if(params['localLeadersWt'] > max_interest_score) {
                                          if (min_interest_score == 0 || min_interest_score > max_interest_score) {
                                          min_interest_score = max_interest_score;
                                          }
                                          max_interest_score = params['localLeadersWt'];
                                      }
                                      else if(min_interest_score == 0 || params['localLeadersWt'] < min_interest_score) {
                                          min_interest_score = params['localLeadersWt'];
                                      }
                                }
                              }

                              organisation_score = location_score + max_interest_score + (min_interest_score/2);
                              score = score * organisation_score;

                              if (params['post_view_ids'].contains(params._source.id)) {
                                score *= 0.01;
                              }

                              if (params._source.has_poster_photo == true) { score *= 0.0333; }

                              if (params._source.has_external_url == true) { score *= 0.1; }

                              if (params['internet_connection'] == 'mobile') {
                                if ( params._source.post_content_length > 0 && params._source.post_content_length <= 240 ) {
                                    score *= 1.5;
                                }
                              }
                              else if (params['internet_connection'] == 'wifi') {
                                if (params._source.post_videos_count > 0) {
                                  score *= 1.2;
                                }

                                if ((params._source.post_videos_count > 0 && params._source.post_first_video_duration <= 45)
                                    && ( params._source.post_content_length > 0 &&  params._source.post_content_length <= 240)) {
                                  score *= 1.5;
                                }
                                else if (params._source.post_videos_count > 0 && params._source.post_first_video_duration <= 45) {
                                  score *= 1.5;
                                }
                                else if ( params._source.post_content_length > 0 && params._source.post_content_length <= 240) {
                                  score *= 1.5;
                                }
                              }

                              return score;
                          " '',
            "params": {
              "userId": @user.id,
              "villageId": @user.village_id,
              "mandalId": @user.mandal_id,
              "mlaConstituencyId": @user.mla_constituency_id,
              "mpConstituencyId": @user.mp_constituency_id,
              "districtId": @user.district_id,
              "stateId": @user.state_id,
              "localLeadersWt": @user.get_local_leaders_weight,
              "villageWt": 8,
              "mandalWt": 7,
              "mlaConstituencyWt": is_telangana_user ? 7 : 6,
              "mpConstituencyWt": is_telangana_user ? 6 : 5,
              "districtWt": is_telangana_user ? 6 : 4,
              "stateLeadersWt": @user.get_state_leaders_weight,
              "partyWt": @user.get_party_weight,
              "stateWt": 3,
              "languageWt": 1,
              "followingWt": @user.get_following_weight,
              "joinedPoliticalPartiesIds": @user.get_user_joined_party_circle_ids,
              "joinedStateLeadersIds": @user.get_user_joined_state_level_leader_ids,
              "joinedLocalLeadersIds": @user.get_user_joined_leader_ids,
              "post_view_ids": post_view_ids,
              "userJoinedCircleOwnerIds": @user.get_user_joined_circles_owner_ids,
              "core_party_user_ids": User.core_party_user_ids,
              "core_user_ids_with_reduced_weightages": Constants.core_user_ids_with_reduced_weightages,
              "internet_connection": internet_connection,
              "is_ai_feed_enabled": @user.is_ai_feed_enabled?
            }
          },
          "min_score": 0.000001
        }
      },
      "sort": [
        {
          "_score": {
            "order": 'desc'
          }
        },
        {
          "created_at": {
            "order": 'desc'
          }
        }
      ],
      "collapse": {
        "field": 'user_id'
      },
      "size": count,
      "from": 0
    }
  end

  # new_post query has been removed. (new_post_discovery)

  def log_view_post_element(post_es_item, post)
    tagged_location_circle_level_to_post =
      case true
      when (post_es_item['tagged_village_circle_ids'].to_a.map(&:to_i) << post[:user].village_id).include?(@user.village_id)
        'village'
      when (post_es_item['tagged_mandal_circle_ids'].to_a.map(&:to_i) << post[:user].mandal_id).include?(@user.mandal_id)
        'mandal'
      when (post_es_item['tagged_mla_constituency_circle_ids'].to_a.map(&:to_i) << post[:user].mla_constituency_id).include?(@user.mla_constituency_id)
        'mla_constituency'
      when (post_es_item['tagged_mp_constituency_circle_ids'].to_a.map(&:to_i) << post[:user].mp_constituency_id).include?(@user.mp_constituency_id)
        'mp_constituency'
      when (post_es_item['tagged_district_circle_ids'].to_a.map(&:to_i) << post[:user].district_id).include?(@user.district_id)
        'district'
      when (post_es_item['tagged_state_circle_ids'].to_a.map(&:to_i) << post[:user].state_id).include?(@user.state_id)
        'state'
      else
        'language'
      end

    user_party_circle_ids = @user.get_user_joined_party_circle_ids
    user_leader_circle_ids = @user.get_user_joined_leader_ids

    tagged_interest_circles = (user_party_circle_ids + user_leader_circle_ids) & (post_es_item['tagged_interest_circle_ids'].to_a.map(&:to_i))
    is_interest_circle_tagged_to_post = tagged_interest_circles.present?
    tagged_interest_circle_level_to_post =
      if is_interest_circle_tagged_to_post
        case true
        when (user_party_circle_ids & tagged_interest_circles).present?
          'political_party'
        when (user_leader_circle_ids & tagged_interest_circles).present?
          'political_leader'
        end
      end

    is_following_user_post = post[:user].follows
    source_of_follow = post[:user].source_of_follow
    no_of_trends_for_post = post_es_item['likes_count'].first

    no_of_hours_back_post_created = ((Time.now.to_i * 1000) - (post[:created_at].to_i * 1000)) / 3600000
    tagged_circle_ids = post[:tagged_circles_ids]
    tagged_circle_names = Circle.where(id: tagged_circle_ids).pluck(:name_en)

    has_poster_photo = post_es_item['has_poster_photo'][0]


    {
      tagged_location_circle_level_to_post: tagged_location_circle_level_to_post,
      is_interest_circle_tagged_to_post: is_interest_circle_tagged_to_post,
      tagged_interest_circle_level_to_post: tagged_interest_circle_level_to_post,
      is_following_user_post: is_following_user_post,
      source_of_follow: source_of_follow,
      no_of_trends_for_post: no_of_trends_for_post,
      no_of_hours_back_post_created: no_of_hours_back_post_created,
      tagged_circle_ids: tagged_circle_ids,
      tagged_circle_names: tagged_circle_names,
      has_photos: post[:photos].present?,
      has_videos: post[:videos].present?,
      no_of_photos_for_post: post[:photos].size,
      has_poster_photo: has_poster_photo,
      post_user_is_internal: post[:user][:internal],
      is_ai_enabled_user: @user.is_ai_feed_enabled?,
      news_worthy_score: post[:news_worthy_score]
    }.compact
  end

  private

  def posts_feed_v3(loaded_post_ids, count)
    loaded_post_ids = loaded_post_ids.map(&:to_s)
    # it will return array of post ids in string format where score is greater than 0 and less than infinity and not in loaded_post_ids
    array_of_post_ids = $redis.zrangebyscore("#{Constants.trending_feed_cache_key}_#{@user.state_id}", '0', '+inf',
                                             limit: [0, loaded_post_ids.count + count]).reject { |post_id|
      loaded_post_ids.include?(post_id)
    }.first(count)

    if array_of_post_ids.blank? || array_of_post_ids.count < count
      count -= array_of_post_ids.count
      posts_es = trending_feed_query_v3(loaded_post_ids, count, @user.state_id)
      posts_es['hits']['hits'].each do |post|
        post_es_item = post['fields']

        array_of_post_ids << post_es_item['id'].first.to_i
      end
    end

    return [] if array_of_post_ids.blank?

    post_ids = array_of_post_ids.map(&:to_i)
    post_user_ids = []

    post_likes_count_map = PostLike.where(post_id: post_ids).group(:post_id).count

    post_comments_count_map = PostComment.where(post_id: post_ids, active: true).group(:post_id).count

    post_opinions_count_map = Post.where(parent_post_id: post_ids, active: true).group(:parent_post_id).count

    liked_post_ids = PostLike.get_liked_post_ids(post_ids, @user.id)

    posts_feed = post_ids.map do |post_id|
      post_obj = Post.new
      post_obj.id = post_id
      post = Post.get_feed_item(post_id, @user, @app_version)
      post_user_ids << post[:user].id

      post[:likes_count] = post_likes_count_map[post_id].to_i
      post[:comments_count] = post_comments_count_map[post_id].to_i
      post[:opinions_count] = post_opinions_count_map[post_id].to_i
      post[:whatsapp_count] = post_obj.whatsapp_count
      post[:preview_comments] = []
      post[:user_liked] = liked_post_ids.include? post_id
      custom_properties = {
        post_user_id: post[:user][:id],
        post_user_is_internal: post[:user][:internal],
      }
      post[:custom_properties] = custom_properties

      post
    end

    followed_user_ids = UserFollower.where(user_id: post_user_ids,
                                           follower_id: @user.id,
                                           active: true)
                                    .pluck(:user_id)

    posts_feed.each do |post|
      post[:user].follows = followed_user_ids.include? post[:user].id
    end
    posts_feed
  end

  def get_mla_contestant_circle_suggestions
    return nil if @user.nil?

    # show mla contestants of user mla constituency if user session count is greater than 2 and user mla constituency
    # is in ap_mla_const_for_suggested_circles
    return unless @user.state_id == 33009 && UserTokenUsage.where(user_id: @user.id).count > 2

    # show mla contestants of user mla constituency
    mla_contestants_circles_list = @user.mla_contestants_of_user_mla_constituency
    if mla_contestants_circles_list.present?
      return {
        header: 'మీ నియోజకవర్గంలో పోటీ చేస్తున్న "ఎమ్మెల్యే" అభ్యర్థులు గురుంచి తెలుసుకోండి.',
        sub_header: 'అభ్యర్థులు గురుంచి సమాచారం, కార్యకలాపాలు మరియు పార్టీ పథకాలు క్రింద ఉన్న సర్కిల్స్ లో పొందండి.',
        feed_item_id: nil,
        feed_type: 'suggested_list',
        suggested_type: 'CIRCLES',
        is_follow_all_present: false,
        circles: mla_contestants_circles_list
      }
    end
    nil

  end
end
