class SingularController < ActionController::API

  WHATSAPP_CAMPAIGNS = %w[gtcampaign2badgeusers gtcampaign1partynames gt3campaign gt2campaign2badgewithlink gtcampaign3withlink target_users_based_on_g2_leaders updated2_onboarding_badgeusers_withlink].freeze

  #TODO: Cleanup these campaign mappings after Feb 2024
  REFERRAL_CAMPAIGN_MAPPINGS = { "postshare1" => "Post share",
                                 "post_share_new" => "Post share",
                                 "mweb_install" => "Mobile web install",
                                 "Profile Share 1" => "Profile invite",
                                 "Profile Share 2" => "Profile invite",
                                 "profile_share_new" => "Profile invite",
                                 "circleshare1" => "Group invite",
                                 "circle_share_new" => "Group invite",
                                 "channel_share" => "Channel share",
                                 "badge Icon status" => "Badge_Icon_status",
                                 "hashtagshare 1" => "Hashtag share",
                                 "hashtag_share_new" => "Hashtag share",
                                 "Poster Share" => "Poster share",
                                 "poster_share_new" => "Poster share",
                                 "premium_poster_share" => "Premium Poster share",
                                 "poster_event_share" => "Poster Event share",
                                 "poster_channel_share" => "Poster Channel share",
                                 "tg_election_results_share" => "TG Election Results share",
                                 "constituency_winner_share" => "Constituency Winner share",
                                 "YSR Birthday" => "Poster share" }.freeze

  EVENT_SIGNUP = "Signup".freeze

  MEDIUM_SOCIAL_MEDIA = "Social media".freeze
  MEDIUM_SOCIAL = "Social".freeze
  MEDIUM_REFERRAL = "Referrals".freeze

  SOURCE_WHATSAPP = "Whatsapp".freeze
  SOURCE_FACEBOOK = "Facebook".freeze
  SOURCE_SHARES = "Shares".freeze

  # Process Singular Request
  # Process Singular Request & insert data into SingularDatum table & add attribution in Mixpanel
  def postback
    ::NewRelic::Agent.record_custom_event('SingularEvent', JSON.parse(request.raw_post))

    attribution = get_attribution(params)
    return render json: { success: true }, status: :ok if attribution.blank?

    user_id = params['user_id'].to_i
    begin
      SingularDatum.create!(user_id: user_id,
                            invited_by: attribution.inviter,
                            utm_medium: attribution.medium,
                            utm_source: attribution.source,
                            utm_campaign: attribution.campaign,
                            invited_via: attribution.invited_via,
                            creative_id: attribution.creative_id,
                            installed_on: attribution.installed_on)
    rescue => e
      Honeybadger.notify(e)
      return render json: { success: false }, status: :unprocessable_entity
    end

    mixpanel_data = {}
    mixpanel_data["UTM Source"] = attribution.source if attribution.source.present?
    mixpanel_data["UTM Medium"] = attribution.medium if attribution.medium.present?
    mixpanel_data["UTM Campaign"] = attribution.campaign if attribution.campaign.present?
    mixpanel_data["Inviter"] = attribution.inviter.to_s if attribution.inviter.present?
    mixpanel_data["Invited Via"] = attribution.invited_via if attribution.invited_via.present?
    mixpanel_data["Attributed Creative ID"] = attribution.creative_id if attribution.creative_id.present?

    MixpanelIntegration.perform_async(user_id, mixpanel_data)

    render json: { success: true }, status: :ok
  end

  private

  # Get attribution from Singular Request
  # @param [ActionController::Parameters] singular_request
  def get_attribution(singular_request)
    return nil if (singular_request['user_id'].blank? || singular_request['event_name'] != EVENT_SIGNUP)

    installed_on = singular_request['install_utc_timestamp'].present? ? Time.zone.at(singular_request['install_utc_timestamp'].to_i) : nil

    if singular_request['partner_site'] == SOURCE_FACEBOOK
      return SingularUserAttribution.new(
        medium: MEDIUM_SOCIAL_MEDIA,
        source: SOURCE_FACEBOOK,
        campaign: singular_request['campaign'],
        creative_id: singular_request['pcrid'],
        installed_on: installed_on
      )
    else
      return nil if singular_request['campaign'].blank?

      campaign = singular_request['campaign'].downcase

      if WHATSAPP_CAMPAIGNS.include? campaign
        return SingularUserAttribution.new(
          medium: MEDIUM_SOCIAL,
          source: SOURCE_WHATSAPP,
          campaign: singular_request['campaign'],
          creative_id: singular_request['pcrid'],
          installed_on: installed_on
        )
      end

      if campaign.start_with?("wati") || campaign.start_with?("social") || campaign.start_with?("growth")
        return SingularUserAttribution.new(
          medium: MEDIUM_SOCIAL,
          source: SOURCE_WHATSAPP,
          campaign: singular_request['campaign'],
          creative_id: singular_request['pcrid'],
          installed_on: installed_on
        )
      else
        referral_campaign = singular_request['campaign']
        source = 'Unknown'
        medium = 'Unknown'

        if REFERRAL_CAMPAIGN_MAPPINGS.key? referral_campaign
          referral_campaign = REFERRAL_CAMPAIGN_MAPPINGS[singular_request['campaign']]
          source = SOURCE_SHARES
          medium = MEDIUM_REFERRAL
        end

        return SingularUserAttribution.new(
          medium: medium,
          campaign: referral_campaign,
          source: source,
          invited_via: get_invited_via_from_deeplink(singular_request['deeplink'].to_s),
          inviter: singular_request['partner_affiliate_id'].blank? ? nil : singular_request['partner_affiliate_id'].to_i,
          creative_id: singular_request['pcrid'],
          installed_on: installed_on
        )
      end
    end
  end

  # Get Invited Via from Deeplink
  # Extract invited entity from deeplink
  # @param [String] deeplink
  def get_invited_via_from_deeplink(deeplink)
    uri = URI(deeplink)
    uri.path.gsub("/", "").gsub(/&.*/, "").gsub("&", "")
  end
end
