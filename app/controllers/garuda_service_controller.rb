# extend this class with ServiceApiController
require 'json'

class GarudaServiceController < ServiceApiController
  before_action :validate_service

  def deactivate_tokens
    unless params[:token_ids].present?
      return render json: { message: 'Missing token_ids' }, status: :bad_request
    end
    unless params[:token_ids].is_a?(Array)
      return render json: { message: 'Invalid token_ids' }, status: :bad_request
    end

    params[:token_ids].each_slice(250) do |batch|
      UserDeviceToken.where(id: batch).update_all(active: false)
    end
    render json: { message: 'Tokens deactivated' }, status: :ok
  end

  def mark_notification_as_sent
    unless params[:notification_id]
      return render json: { message: 'Missing notification_id' }, status: :bad_request
    end

    @garuda_notification = GarudaNotification.find_by(notification_id: params[:notification_id])
    unless @garuda_notification.present?
      return render json: { message: 'Notification not found' }, status: :not_found
    end

    begin
      @garuda_notification.sent_count = params[:sent_count] || nil
      @garuda_notification.mark_as_sent!
    rescue AASM::InvalidTransition
      return render json: { message: 'Notification is not in sending state' }, status: :bad_request
    end
    render json: { message: 'Notification marked as sent' }, status: :ok
  end

  private

  def validate_service
    if @service != 'garuda'
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end
end
