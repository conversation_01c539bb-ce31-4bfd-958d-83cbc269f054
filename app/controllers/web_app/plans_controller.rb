# frozen_string_literal: true

module WebApp
  class PlansController < WebAppController
    before_action :set_user
    before_action :set_plan

    def get_juspay_link
      @subscription = Subscription.create_plan_juspay_subscription(plan: @plan, user: @user)

      render json: {
        id: "sub_#{@subscription.id}",
        link: @subscription.auth_link,
      }, status: :ok
    end

    private

    def set_plan
      plan_id = params[:plan_id] || params[:id]
      @plan = Plan.find_by(id: plan_id)
      render json: { success: false, message: 'Plan not found' }, status: :not_found if @plan.blank?
    end

    def set_user
      user_hash_id = params[:user_id]
      @user = User.find_by_hashid(user_hash_id)
      render json: { success: false, message: 'User not found' }, status: :not_found if @user.blank?
    end
  end
end
