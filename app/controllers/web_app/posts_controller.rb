module WebApp
  class PostsController < WebAppController
    include PostsConcern
    before_action :set_post, only: %i[delete]

    def create_post
      begin
        @post = create_post_with_data
        @post.metadatum.build(key: "post_os", value: "web_app")
        @post.save!
        render json: { success: true, data: @post.hashid }, status: :ok
      rescue => e
        render json: {
          success: false,
          message: @post.errors.messages.first&.second&.first || e&.message || I18n.t('posts.post_create_failed_message')
        }, status: :bad_request
      end
    end

    def create_post_data
      render json: {
        success: true,
        data: {
          tagged_circles: get_pre_selected_circles,
          post_upload_urls: Constants.get_user_post_media_upload_urls,
        },
      }, status: :ok
    end

    def delete
      delete_post
      render json: { success: true, message: I18n.t('posts.post_deleted') }, status: :ok
    end
  end
end
