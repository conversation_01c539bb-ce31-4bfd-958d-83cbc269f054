# frozen_string_literal: true

module WebApp
  class OrdersController < WebAppController
    before_action :set_order

    def get_cashfree_payment_session_id
      if @order.pending?
        return render json: { success: false, message: 'Payment is still processing' }, status: :bad_request
      end
      if @order.successful?
        return render json: { success: false, message: 'Already paid' }, status: :bad_request
      end

      # begin
        render json: {
          success: true,
          payment_session_id: CashfreePaymentUtils.generate_payment_session_id(@order)
        }, status: :ok
      # rescue StandardError
      #   render json: { success: false, message: 'Unable to generate payment session id' }, status: :bad_request
      # end
    end

    private

    def set_order
      order_id = params[:order_id].present? ? params[:order_id] : params[:id]
      @order = Order.find_by(id: order_id)
      render json: { success: false, message: 'Order not found' }, status: :not_found if @order.blank?
    end
  end
end
