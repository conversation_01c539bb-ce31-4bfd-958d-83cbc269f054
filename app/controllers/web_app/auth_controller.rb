module WebApp
  class AuthController < WebAppController
    def get_email_otp
      email = params[:email].downcase
      @user = User.find_by_email(email)
      if @user.blank?
        return render json: { success: false, message: 'ఈ ఇమెయిల్ కి సంబంధించిన యూసర్ కనుగొనబడలేదు' }, status: :bad_request
      end

      can_send_otp = @user.check_email_eligibility_for_otp

      unless can_send_otp
        return render json: { success: false, message: 'ఈ ఇమెయిల్ తో లాగిన్ అవ్వడం సాధ్యం కాదు' }, status: :bad_request
      end

      if email != '<EMAIL>'
        otp = @user.get_unique_otp_for_email
        success = @user.send_otp_email(otp)
        # UserMailer.send_otp_via_email(@user.name, @user.email, otp).deliver_now
      else
        success = true
      end

      if success
        render json: { success: true, message: 'OTP మీ ఇమెయిల్ కి పంపబడింది' }, status: :ok
      else
        render json: { success: false, message: 'ఈ ఇమెయిల్ కి OTP పంపబడలేదు' }, status: :bad_request
      end
    end

    def login
      email = params[:email].downcase
      otp = params[:otp].to_i

      @user = User.find_by_email(email)
      if @user.blank?
        return render json: { success: false, message: 'ఈ ఇమెయిల్ తో యూజర్ లేరు' }, status: :bad_request
      elsif !@user.can_login?
        return render json: { success: false, message: 'మీ అకౌంట్ నిలిపివేయబడింది!' }, status: :bad_request
      end

      is_valid = @user.validate_email_otp(otp)
      unless is_valid
        return render json: { success: false, message: 'OTP సరిచూసుకోండి!' }, status: :unprocessable_entity
      end

      token, nano_id = @user.generate_web_app_jwt_token
      $redis.sadd(Constants.web_app_tokens_redis_key, nano_id)
      response.set_header(Constants.jwt_access_token_header, token)

      user_photo_url = @user.photo.present? ? @user.photo.url : nil

      circle_ids = UserCirclePermissionGroup.joins(:permission_group)
                                            .where(
                                              user_id: @user.id,
                                              permission_group: { name: PermissionGroup::WEB_APP_ENABLED_GROUPS }
                                            ).pluck(:circle_id)
      circle = Circle.find(circle_ids.first)

      user_hash = {
        id: @user.hashid,
        name: @user.name,
        email: @user.email,
        short_bio: @user.short_bio,
        photo_url: user_photo_url,
        access_token: token,
        default_circle_id: circle.hashid,
      }

      render json: { success: true, data: user_hash }, status: :ok
    end

    def logout
      token = request.headers['Authorization'].sub('Bearer ', '')
      payload = JsonWebToken.decode(token)
      if payload.present?
        nano_id = payload['nano_id']
        $redis.srem(Constants.web_app_tokens_redis_key, nano_id)

        render json: { success: true, message: 'మీరు లాగౌట్ అయ్యారు' }, status: :ok
      else
        render json: { success: false, message: 'ఏదో సరిగ్గా లేదు. కాసేపు ఆగి ప్రయత్నించండి.' }, status: :bad_request
      end
    end
  end
end
