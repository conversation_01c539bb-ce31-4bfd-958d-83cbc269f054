# frozen_string_literal: true
module WebApp
  module Admin
    class WebAppAdminController < WebAppController
      before_action :set_circle, :check_user_permission, except: [:get_initial_data]

      def get_initial_data
        selected_circle = nil
        circle_hash_id = request.headers['X-Circle-HashId']
        selected_circle = Circle.find_by_hashid(circle_hash_id) if circle_hash_id.present?

        circle_ids = UserCirclePermissionGroup.joins(:permission_group)
                                              .where(
                                                user_id: @user.id,
                                                permission_group: { name: PermissionGroup::WEB_APP_ENABLED_GROUPS }
                                              ).pluck(:circle_id)
        circles = Circle.where(id: circle_ids)

        if circles.blank?
          return render json: { success: false, message: 'మీకు సర్కిల్స్ లేవు' }, status: :not_found
        end

        # most of the scenarios we would get circle hashid in the request header
        # but in the case where user deleted the circle hash id cookie or the circle permission has been removed for the user,
        # we would want to select first circle of the lot as default.
        circle = circles.first
        if selected_circle.present? && circles.filter{ |c| c.hashid == selected_circle.hashid }.present?
          circle = selected_circle
        end

        user = { id: @user.hashid, name: @user.name, email: @user.email, photo_url: @user.photo&.url, short_bio: @user.short_bio }

        hash = {
          user: user,
          circle: circle.webapp_json,
          circles: circles.map { |c| c.webapp_json },
          create_config: [:create_post],
          support_config: %i[whatsapp call_me_now],
          praja_support_number: '+919741384621',
          mixpanel_embed_link: circle.get_webapp_mixpanel_embed_link
        }

        render json: { success: true, data: hash }, status: :ok
      end

      def update_owner_profile
        owner = @circle.get_owner
        return render json: { success: false, message: 'ఏదో సరిగ్గా లేదు.' }, status: :bad_request if owner.blank?

        owner.name = params[:name] if params[:name].present?
        owner.short_bio = params[:short_bio] if params[:short_bio].present?

        if params[:photo].present?
          owner.photo = Photo.upload(params[:photo], Constants.praja_account_user_id)

          if owner.photo.blank?
            return render json: { success: false, message: 'ఫోటో అప్లోడ్ ఫెయిల్ అయినది. కాసేపు తర్వాత ప్రయత్నించండి' }, status: :bad_request
          end
        end

        if owner.save
          render json: owner.get_short_json, status: :ok
        else
          render json: { success: false, message: 'ప్రొఫైల్ అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.' }, status: :bad_request
        end
      end

      def set_circle
        circle_hash_id = request.headers['X-Circle-HashId']
        if circle_hash_id.blank?
          return render json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :bad_request
        end
        @circle = Circle.find_by_hashid(circle_hash_id)

        if @circle.blank?
          return render json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :not_found
        end
        Honeybadger.context({ circle_id: @circle.id })
      end

      def check_user_permission
        if @circle.blank?
          return render json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :not_found
        end

        has_owner_or_admin_permission = @circle.user_circle_permission_groups
                                               .joins(:permission_group)
                                               .where(
                                                 user_id: @user.id,
                                                 permission_group: { name: PermissionGroup::WEB_APP_ENABLED_GROUPS }
                                               )
                                               .exists?

        unless has_owner_or_admin_permission
          render json: { success: false, message: 'మీకు ఈ పర్మిషన్ లేదు' }, status: :forbidden
        end
      end
    end
  end
end

