module WebApp
  module Admin
    class PostsController < WebAppAdminController
      def index
        page = 0
        page = params[:page].to_i if params[:page].present?

        count = 10
        count = params[:rowsPerPage].to_i if params[:rowsPerPage].present?

        posts = @user.get_posts(@user, page * count, count, nil, true)
        all_posts_count = @user.get_posts_count(true)

        render json: { success: true, data: { posts: posts, count: all_posts_count } }, status: :ok
      end
    end
  end
end
