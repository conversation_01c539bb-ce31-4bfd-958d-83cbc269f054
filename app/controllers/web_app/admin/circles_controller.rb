module WebApp
  module Admin
    class CirclesController < WebAppAdminController
      def overview
        render json: {
          success: true,
          data: {
            todays_stats: get_stats_data(Time.zone.today, Time.zone.today),
            last_7_days_stats: get_stats_data(Time.zone.yesterday - 7.days, Time.zone.yesterday),
          },
        }, status: :ok
      end

      def update_circle
        if params[:photo].present?
          @circle.photo = Photo.upload(params[:photo], Constants.praja_account_user_id)

          if @circle.photo_id.blank?
            return render json: { message: 'ఫోటో అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.' }, status: :bad_request
          end
        end

        if @circle.save
          render json: @circle.get_short_json, status: :ok
        else
          render json: { message: 'అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.' }, status: :bad_request
        end
      end

      private

      def get_stats_data(start_date, end_date)
        range = start_date.beginning_of_day..end_date.end_of_day
        owner_posts_views_count = 0
        circle_posts = @circle.posts.where(created_at: range)
        posts_count = circle_posts.count
        owner_posts = @user.posts.where(created_at: range)
        owner_posts.each do |post|
          owner_posts_views_count += post.get_total_views_count
        end

        {
          posts_count: posts_count,
          owner_posts_count: owner_posts.count,
          owner_posts_views_count: owner_posts_views_count,
        }
      end
    end
  end
end
