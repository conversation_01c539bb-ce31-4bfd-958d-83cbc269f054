# frozen_string_literal: true

class NotificationsController < ApiController
  before_action :set_logged_in_user
  before_action :set_notification, only: %i[update mark_as_read]

  # GET /my-notifications
  def index
    @notification_type_exclude_list = %i[new_post_public new_post_party new_post_leader new_post_location]
    @notifications = if @app_version >= Gem::Version.new('0.4.7')
                       Notification.where(user: @user, active: true).where.not(notification_type: @notification_type_exclude_list)
                                   .order(id: :desc)
                                   .offset(params[:offset])
                                   .limit(params[:count])

                     else
                       Notification.where(user: @user, active: true)
                                   .where.not(notification_type: @notification_type_exclude_list)
                                   .order(id: :desc)
                                   .all
                     end

    @notifications.where(received: false).update_all(received: true, updated_at: Time.now)

    @notifications = @notifications.map do |notif|
      if notif.entity.present?
        if notif.entity_id == @user.id
          nil
        else
          notif.entity
        end
      end

      notif
    end
    render json: @notifications, status: :ok
  end

  # GET /my-notifications-count
  def notifications_count
    notifications_count = Notification.where(user: @user, active: true, received: false).count

    render json: { count: notifications_count }, status: :ok
  end

  # PUT /notifications/1/read
  def mark_as_read
    if @notification.update(received: true, read: true)
      render json: { success: true }, status: :ok
    else
      render json: {
        success: false,
        message: 'నోటిఫికేషన్‌ను చదివినట్లుగా గుర్తించడం సాధ్యం కాలేదు', # Unable to mark as read
        error: @notification.errors
      }, status: :unprocessable_entity
    end
  end

  # PUT /notifications/mark-all-as-read
  def mark_all_as_read
    if Notification.where(user: @user, active: true, read: false).update_all(received: true, read: true, updated_at: Time.now)
      UpdateUserUnreadNotificationsCount.perform_async(@user.id)
      render json: { success: true }, status: :ok
    else
      render json: {
        success: false,
        message: 'అన్ని నోటిఫికేషన్‌లను చదివినట్లుగా గుర్తించడం సాధ్యం కాలేదు' # Unable to mark all as read
      }, status: :unprocessable_entity
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_notification
    @notification = Notification.find(params[:notification_id])
  end

  # Only allow a trusted parameter "white list" through.
  def notification_params
    params.require(:notification).permit(:notification_id, :description, :type, :user_id, :delivered, :active,
                                         :deep_link)
  end
end
