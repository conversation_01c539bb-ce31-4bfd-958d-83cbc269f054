class PostCommentsController < ApiController
  before_action :set_logged_in_user
  before_action :set_post
  before_action :set_comment, only: [:report, :delete]

  # GET /posts/1/comments
  def index
    offset = 0
    if params[:offset].present?
      offset = params[:offset].to_i
    end

    count = 10
    if params[:count].present?
      count = params[:count].to_i
    end

    comments = PostComment.where(post: @post, active: true).offset(offset).limit(count).all.map do |c|
      c.user.loggedInUser = (c.user_id == @user.id)
      c.user.phone = c.user.generate_random_phone
      c.user.badge = c.user.get_badge_role&.get_json
      c
    end

    render json: comments, status: :ok
  end

  # POST /posts/1/comment
  def create
    if !@post.active
      render json: { success: false, message: "పోస్ట్ తొలగించబడింది!" }, status: :bad_request
    elsif !params[:comment].present?
      render json: { success: false, message: "కామెంట్ ఖాళీ గా ఉండకూడదు!" }, status: :bad_request
    elsif (@post.user != @user && @post.comments_type.to_sym == :none) || @user.is_blocked_for_commenting?
      render json: { success: false, message: "కామెంట్ చేయడం నిలిపివేయబడింది" }, status: :bad_request
    else
      begin
        comment = params[:comment]
        if RedWord.check(comment.downcase)
          EventTracker.perform_async(@user.id, "comment_with_red_words", { "post_id" => @post.id, "comment" => comment })
          render json: { success: false, message: "మీ కామెంట్ లో ద్వేషపూరిత / దుర్భాష ప్రసంగాలు కలిగిఉన్నవి" }, status: :unprocessable_entity
          return
        end
        pc = PostComment.create!(comment: comment, user: @user, post: @post)
        pc.user.loggedInUser = true
        pc.user.badge = pc.user.get_badge_role&.get_json

        render json: { success: true, comment: pc }, status: :ok
      rescue => exception
        logger.error(exception.message.to_s)
        render json: {
          success: false,
          message: "కామెంట్ చేయలేకపోతున్నారు" # Failed to comment
        }, status: :unprocessable_entity
      end
    end
  end

  # PUT /posts/1/comments/1/report
  def report
    if params[:reason].present? && params[:reason]['name'].present?
      Report.create!(reference: @comment, user: @user, report_reason: params[:reason]['name'])

      render json: {
        success: true,
        message: "కామెంట్ నివేదించబడింది!" # Reported!
      }, status: :ok
    else
      render json: { success: false, message: "Reason should be specified" }, status: :bad_request
    end
  end

  def delete
    if @post.id != @comment.post_id
      render json: { success: false, message: "కామెంట్ యొక్క పోస్టు కనుగొనబడలేదు!" }, status: :bad_request
    elsif !@post.active
      render json: { success: false, message: "కామెంట్ యొక్క పోస్టు తొలగించబడింది!" }, status: :bad_request
    elsif @comment.user != @user
      render json: { success: false, message: "కామెంట్ తొలగించడం మీకు అనుమతి లేదు" }, status: :forbidden
    else
      @comment.active = false
      @comment.save!
      render json: { success: true, message: "కామెంట్ తొలగించబడింది!" }, status: :ok
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_post
    @post = Post.find(params[:post_id])
  end

  def set_comment
    @comment = PostComment.find(params[:post_comment_id])
  end

  # Only allow a trusted parameter "white list" through.
  def post_comment_params
    params.require(:post_comment).permit(:comment, :user_id, :post_id)
  end
end

