class ProfessionsController < ApiController
  before_action :set_logged_in_user

  def index
    professions = Profession
                    .all
                    .order(:ordinal, :id)
                    .map(&:get_json)

    render json: professions, status: :ok
  end

  def submit
    profession_id = params[:profession_id]&.to_i
    sub_profession_id = params[:sub_profession_id]&.to_i

    return render json: { success: false, message: "Missing profession_id" }, status: :bad_request if profession_id.nil?

    profession = Profession.find_by_id(profession_id)
    return render json: { success: false, message: "Profession id does not exit" }, status: :unprocessable_entity if profession.nil?

    if profession.sub_professions.present? && sub_profession_id.nil?
      return render json: { success: false, message: "Missing sub_profession_id" }, status: :bad_request
    end

    if sub_profession_id.present?
      sub_profession = SubProfession.find_by_id(sub_profession_id)
      return render json: { success: false, message: "Sub Profession id does not exist" }, status: :unprocessable_entity if sub_profession.nil?

      return render json: { success: false, message: "Sub Profession does not belong to Profession" }, status: :bad_request if sub_profession.profession_id != profession.id
    end

    user_profession = UserProfession.find_or_initialize_by(user: @user)
    user_profession.profession_id = profession.id
    user_profession.sub_profession_id = sub_profession&.id
    user_profession.save!

    # delete user metadata for skip if present
    UserMetadatum.where(user: @user, key: Constants.profession_selection_skipped_key).delete_all

    if @user.verify_eligibility_rules_for_premium_pitch? && (@user.leader_profession? || @user.high_end_device_user?)
      source = @user.leader_profession? ? :LEADER_PROFESSION : :HIGH_END_DEVICE_NON_LEADER
      ProfessionSelectionLead.perform_in(@user.is_test_user_for_floww? ? 5.seconds : 3.hours, @user.id, source)
    end

    # Suggest party selection only to users who have selected leader profession
    # Do not suggest parties if the user has already joined any party circle
    should_suggest_parties = user_profession.leader? && !@user.get_user_joined_party_circle_ids.present?

    render json: {
      success: true,
      next_page_deeplink: should_suggest_parties ? '/party-suggestion' : nil
    }, status: :ok
  end

  def skip
    profession_skip_metadatum = UserMetadatum.find_or_initialize_by(user: @user, key: Constants.profession_selection_skipped_key)
    profession_skip_metadatum.value = 'true'
    profession_skip_metadatum.save!

    if @user.high_end_device_user?
      ProfessionSelectionLead.perform_in(@user.is_test_user_for_floww? ? 5.seconds : 3.hours, @user.id, :HIGH_END_DEVICE)
    end
    render json: { success: true }, status: :ok
  end
end
