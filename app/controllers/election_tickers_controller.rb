# frozen_string_literal: true

class ElectionTickersController < ApiController
  include ElectionTicker
  include Elections2024
  include ElectionConstituencyStatus

  before_action :set_logged_in_user, only: [:live_ticker, :location_constituency_status_carousel, :live_user_constituency_status_carousel, :live_leader_constituency_status_carousel]

  def ticker_link_preview
    # mostly for fallback. We ran by state_id for the first day
    state_id = params[:state_id]&.to_i
    ticker_id = params[:ticker_id]
    image_url = election_ticker_preview_image_url(ticker_id) if ticker_id.present?
    image_url = election_ticker_preview_image_url(primary_ticker_by_state(state_id)) if image_url.blank? && state_id.present?

    if image_url.blank?
      render status: :not_found
      return
    end

    image_url = Capture.apply_img_transform(image_url, width: 600, height: 600, quality: 25)

    page_title = "#{ticker_title(ticker_id)} #{ticker_sub_title(ticker_id)} | ప్రజా యాప్"
    page_description = 'ఫిబ్రవరి 8న లైవ్ అప్డేట్స్ '
    page_image = image_url
    page_url = "https://m.praja.buzz/"
    page_type = 'profile'

    locals = { page_title:, page_description:, page_image:, page_url:, page_type: }

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/circles/preview.html.erb')),
      locals: locals
    )
    render html: html_content.html_safe
  end

  def constituency_status_link_preview
    constituency_id = params[:constituency_id]&.to_i

    if constituency_id.blank?
      render status: :not_found
      return
    end

    photo_id = get_constituency_status_photo_id(constituency_id)&.to_i
    if photo_id.blank?
      render status: :not_found
      return
    end
    photo = Photo.find_by(id: photo_id)
    if photo.blank?
      render status: :not_found
      return
    end

    constituency = Circle.find_by(id: constituency_id)

    page_title = "#{constituency&.name} 2024 ఎన్నికలు | ప్రజా యాప్"
    page_description = 'జూన్ 4న లైవ్ అప్డేట్స్'
    page_image = Capture.apply_img_transform(photo.url, width: 600, height: 600, quality: 25)
    page_url = "https://m.praja.buzz/feeds/election_feed"
    page_type = 'profile'

    locals = { page_title:, page_description:, page_image:, page_url:, page_type: }

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/circles/preview.html.erb')),
      locals: locals
    )
    render html: html_content.html_safe
  end

  def live_ticker
    ticker_id = params[:ticker_id]

    if ticker_id.blank?
      render status: :not_found
      return
    end

    render json: election_ticker_by_ticker_id(ticker_id, @user), status: :ok
  end

  def location_constituency_status_carousel
    circle_id = params[:circle_id]&.to_i

    if circle_id.blank?
      render status: :not_found
      return
    end

    circle = Circle.find_by(id: circle_id)
    if circle.blank?
      render status: :not_found
      return
    end

    render json: location_level_constituency_status_carousels(user_id: @user.id, circle: circle), status: :ok
  end

  def live_user_constituency_status_carousel
    render json: user_constituency_status_carousel(@user), status: :ok
  end

  def live_leader_constituency_status_carousel
    circle_id = params[:circle_id]&.to_i

    if circle_id.blank?
      render status: :not_found
      return
    end

    circle = Circle.find_by(id: circle_id)
    if circle.blank?
      render status: :not_found
      return
    end

    render json: interest_level_constituency_status_carousels(user_id: @user.id, circle: circle), status: :ok
  end
end
