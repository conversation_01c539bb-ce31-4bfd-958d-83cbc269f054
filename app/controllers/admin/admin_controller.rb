class Admin::AdminController < ApplicationController
  # Only authenticated admin users can access these endpoints
  before_action :authenticate_admin_user!, except: %i[external_redirect validate_admin_session get_dashboard_link]

  rescue_from ApiError, with: :handle_api_error

  def external_redirect
    redirect_url = session.delete(:external_redirect_url)
    if redirect_url.present?
      Rails.logger.debug("Redirecting to external URL: #{redirect_url}")
      redirect_to redirect_url, allow_other_host: true
    else
      redirect_to admin_dashboard_path
    end
  end

  # @note: This is to validate whether the admin user signed in or not in the active admin dashboard
  # Don't use this method for any other purpose other than validating the admin user session
  # Right now using in Jathara(Internal Tool)
  def validate_admin_session
    if admin_user_signed_in?
      render json: { success: true }, status: :ok
    else
      render json: { success: false }, status: :unauthorized
    end
  end

  # Generate OE and BOE dashboard links for a given user ID
  def get_dashboard_link
    user_id = params["user_id"]
    type = params["type"]

    if user_id.blank?
      render json: { error: 'User ID is required' }, status: :bad_request
      return
    end

    user = User.find_by(id: user_id)

    if user.nil?
      render json: { error: 'User not found' }, status: :not_found
      return
    end

    # Generate the dashboard links
    if type == "oe"
      redirect_url = "#{Constants.get_admin_host}/admin/users/#{user.hashid}/oe_work_flow"
    elsif type == "boe"
      redirect_url = "#{Constants.get_admin_host}/admin/users/#{user.hashid}/boe_work_flow"
    else
      render json: { error: 'Invalid type parameter' }, status: :bad_request
      return
    end

    render json: {
      success: true,
      redirect_url: redirect_url, }
  end

  private

  def handle_api_error(exception)
    # Log the error (see Logging section)
    Rails.logger.error "ApiError: #{exception.message}"
    # Return a JSON response with an error message and appropriate HTTP status
    render json: { error: exception.message }, status: exception.status
  end
end
