class Admin::OmniauthCallbacksController < Devise::OmniauthCallbacksController
  def all
    begin
      admin_user = AdminUser.from_omniauth(auth_hash)

      flash.notice = t("active_admin.sessions.signed_in")
      sign_in_and_redirect admin_user
    rescue => e
      flash.alert = "#{e}"
      redirect_to new_admin_user_session_path
    end
  end

  alias_method :google_oauth2, :all

  private

  def auth_hash
    request.env["omniauth.auth"]
  end

  def after_sign_in_path_for(resource)
    # Check if a specific redirect path was provided in the omniauth origin param
    stored_location = stored_location_for(resource)
    Rails.logger.debug("omniauth_params #{omniauth_params.inspect}")
    redirect_url = omniauth_params["redirect_url"] ||
      params[:redirect_url] ||
      request.env["omniauth.origin"]&.match(/redirect_url=([^&]+)/)&.captures&.first

    if redirect_url.present?
      # Decode the URL
      decoded_url = URI.decode_www_form_component(redirect_url)
      
      # Parse the URL
      uri = URI.parse(decoded_url)

      # Check if it's a valid absolute URL with http/https scheme
      if uri.scheme.in?(["http", "https"]) && uri.host.present?
        # Allow the external redirect by setting a flag in the session
        session[:external_redirect_url] = decoded_url
        # Redirect to our special controller action that will handle the external redirect
        return admin_external_redirect_path
      end
    end

    stored_location || admin_dashboard_path
  end

  def omniauth_params
    request.env["omniauth.params"] || {}
  end
end
