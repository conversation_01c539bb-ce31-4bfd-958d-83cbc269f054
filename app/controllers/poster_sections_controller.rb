class PosterSectionsController < ApiController
  before_action :set_logged_in_user

  def my_poster_sections
    poster_sections = @user.my_poster_sections unless @user.nil?
    render json: { feed_items: poster_sections }, status: :ok
  end

  def my_poster_sections_v2
    poster_sections = @user.my_poster_sections_v2 unless @user.nil?
    render json: { feed_items: poster_sections }, status: :ok
  end
end
