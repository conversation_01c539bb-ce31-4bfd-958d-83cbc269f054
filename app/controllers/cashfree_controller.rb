# frozen_string_literal: true

class CashfreeController < ActionController::API
  before_action :log_request, :verify_test_callback, only: [:subscription_callback]
  after_action :log_response

  def callback
    render json: { success: true, message: 'deprecated callback' }, status: :ok
  end

  def subscription_callback
    # Validate presence of required fields
    if params[:cf_event].blank? || params[:cf_subReferenceId].blank?
      Honeybadger.notify('Invalid cashfree subscription callback', context: { params: params })
      render json: { success: false, message: 'Invalid callback' }, status: :bad_request
      return
    end

    # Skip verify signature if callback is for `SUBSCRIPTION_REFUND_STATUS` event, because it doesn't work somehow
    # Need to check with Cashfree team
    return unless verify_signature unless params[:cf_event] == 'SUBSCRIPTION_REFUND_STATUS'

    subscription = Subscription.find_by(pg_reference_id: params[:cf_subReferenceId])
    if subscription.blank?
      Honeybadger.notify('Subscription not found', context: { params: params })
      return render json: { success: false, message: 'Subscription not found' }, status: :bad_request
    end

    case params[:cf_event]
      # when 'SUBSCRIPTION_AUTH_STATUS'
      #   subscription.activate! if subscription.may_activate? && params[:cf_authStatus] == 'ACTIVE'
    when 'SUBSCRIPTION_STATUS_CHANGE'
      status = params[:cf_status]
      case status
      when 'ACTIVE'
        subscription.resume! if subscription.may_resume?
      when 'CUSTOMER_CANCELLED'
        subscription.customer_cancel! if subscription.may_customer_cancel?
      when 'CUSTOMER_PAUSED'
        subscription.customer_pause! if subscription.may_customer_pause?
      when 'LINK_EXPIRED'
        subscription.close! if subscription.may_close?
      else
        # ignore
      end
    when 'SUBSCRIPTION_NEW_PAYMENT'
      pg_id = params[:cf_merchantTxnId]
      cf_order_id = params[:cf_orderId]
      # cf_order_id format "SUB_12337011_AUTH_24491871"
      # if cf_order_id has AUTH, consider it as auth charge
      if subscription.subscription_charges.count == 1
        # Consider this as auth charge, if pg_id is not present
        auth_charge = subscription.subscription_charges.first
        auth_charge.pg_reference_id = params[:cf_paymentId]
        if params[:cf_merchantTxnId].present? && auth_charge.pg_id != params[:cf_merchantTxnId]
          auth_charge.pg_id = params[:cf_merchantTxnId]
        end
        auth_charge.pg_json = params
        auth_charge.success! if auth_charge.may_success?
      else
        subscription_charge = subscription.subscription_charges.find_by(pg_id: pg_id)
        if subscription_charge.blank?
          Honeybadger.notify('Subscription charge not found', context: { params: params })
          return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
        end

        subscription_charge.pg_json = params
        subscription_charge.success! if subscription_charge.may_success?
      end
    when 'SUBSCRIPTION_PAYMENT_DECLINED'
      pg_id = params[:cf_merchantTxnId]
      # retry_count = params[:cf_retryAttempts].to_i
      subscription_charge = subscription.subscription_charges.find_by(pg_id: pg_id)
      if subscription_charge.blank?
        Honeybadger.notify('Subscription charge not found', context: { params: params })
        return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
      end

      subscription_charge.pg_json = params
      # if retry_count >= Constants.duration_days_after_premium_end_date
      subscription_charge.fail! if subscription_charge.may_fail?
      # else
      #   subscription_charge.mark_as_pending! if subscription_charge.may_mark_as_pending?
      # end
    when 'SUBSCRIPTION_REFUND_STATUS'
      refund_status = params[:cf_refund_status]
      merchant_refund_id = params[:cf_merchant_refund_id]

      if refund_status == 'PENDING'
        # Handle pending refund status
        return render json: { success: true }, status: :ok
      elsif refund_status != 'SUCCESS'

        if merchant_refund_id.present? && merchant_refund_id.start_with?('refund-')
          Honeybadger.notify('Refund failed', context: { params: params })
          
          # Extract the refund ID from the merchant refund ID format: "refund-{refund_id}-{pg_id}"
          refund_id = merchant_refund_id.split('-')[1]

          if refund_id.present?
            refund = SubscriptionChargeRefund.find_by(id: refund_id)
            if refund.present?
              # Update the refund record with the response and mark as failed
              refund.update(pg_json: params)
              refund.mark_as_failed! if refund.may_mark_as_failed?
            else
              Honeybadger.notify('Refund not found', context: { params: params })
            end
          end
          return render json: { success: false, message: 'Refund failed' }, status: :unprocessable_entity
        end
      end

      # Case 1: Refund with a specific refund ID
      if merchant_refund_id.present? && merchant_refund_id.start_with?('refund-')
        # Extract the refund ID from the merchant refund ID format: "refund-{refund_id}-{pg_id}"
        refund_id = merchant_refund_id.split('-')[1]

        if refund_id.present?
          refund = SubscriptionChargeRefund.find_by(id: refund_id)

          if refund.present?
            # Update the refund record with the response and mark as success
            refund.update(pg_json: params)
            refund.mark_as_success! if refund.may_mark_as_success?
            return render json: { success: true }, status: :ok
          end
        end
      elsif merchant_refund_id.present? && !merchant_refund_id.start_with?('refund-')
        pg_reference_id = params[:cf_payment_id]
        if pg_reference_id.blank?
          Honeybadger.notify('Invalid refund callback', context: { params: params })
          return render json: { success: false, message: 'Invalid refund callback' }, status: :bad_request
        end

        subscription_charge = subscription.subscription_charges.find_by(pg_reference_id: pg_reference_id)
        if subscription_charge.blank?
          Honeybadger.notify('Subscription charge not found', context: { params: params })
          return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
        end

        # For automatic refunds, create a SubscriptionChargeRefund record if one doesn't exist
        subscription_charge.handle_initial_payment_refund if refund_status == 'SUCCESS'
      end
    end

    render json: { success: true }, status: :ok
  end

  private

  def verify_test_callback
    # For test callback
    if params[:data].present? && params[:data][:test_object].present?
      render json: { success: true }, status: :ok
    end
  end

  def verify_signature
    received_signature = params[:signature]
    if received_signature.blank?
      Honeybadger.notify('Signature missing in cashfree callback', context: { params: params })
      render json: { success: false, message: 'Signature missing' }, status: :unauthorized
      return false
    end

    signature_params = {}
    params.each do |key, value|
      signature_params[key] = value if key.start_with?('cf_')
    end

    calculated_signature = CashfreePaymentUtils.calculate_signature(signature_params)

    unless ActiveSupport::SecurityUtils.secure_compare(received_signature, calculated_signature)
      Honeybadger.notify('Invalid signature in cashfree callback', context: { params: params })
      render json: { success: false, message: 'Invalid signature' }, status: :unauthorized
      return false
    end

    true
  end

  def log_request
    Rails.logger.warn("[keep-it-forever] #{controller_name}#{action_name} :: PARAMS: #{params}")
  end

  def log_response
    Rails.logger.warn("[keep-it-forever] #{controller_name}#{action_name} :: RESPONSE: #{response.body} :: PARAMS: #{params}")
  end

end
