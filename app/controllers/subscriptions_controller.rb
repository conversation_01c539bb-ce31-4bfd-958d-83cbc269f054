class SubscriptionsController < ApiController
  before_action :set_logged_in_user
  before_action :set_subscription

  # GET /subscriptions/1
  def show
    status = nil
    message = nil
    if @subscription.status.to_sym == :active
      status = 'successful'
    else
      if @subscription.status.to_sym == :created
        status = 'pending'

        if @subscription.juspay?
          subscription_charge = @subscription.subscription_charges.first
          if subscription_charge.present?
            response = JuspayPaymentUtils.get_order_status(subscription_charge.pg_id)
            if response['mandate'].present? &&
              response['mandate']['mandate_id'].present?
              # Save mandate data
              @subscription.pg_json = response
              @subscription.pg_reference_id = response['mandate']['mandate_id']
              @subscription.save!

              if JuspayPaymentUtils::ORDER_STATUS_SUCCESS_IDS.include?(response['status_id'].to_i) ||
                (JuspayPaymentUtils::ORDER_STATUS_AUTO_REFUND_IDS.include?(response['status_id'].to_i) &&
                  subscription_charge.is_trial_charge?)
                subscription_charge.pg_json = response
                subscription_charge.success! if subscription_charge.may_success?
                status = 'successful'
              elsif JuspayPaymentUtils::ORDER_STATUS_FAILURE_IDS.include?(response['status_id'].to_i) ||
                (JuspayPaymentUtils::ORDER_STATUS_AUTO_REFUND_IDS.include?(response['status_id'].to_i) &&
                  !subscription_charge.is_trial_charge?)
                # reload the subscription object once, as it might have been updated from callbacks
                @subscription.reload
                if @subscription.may_close?
                  @subscription.pg_json = params
                  @subscription.close!
                  status = 'failed'
                  message = I18n.t('errors.subscriptions.juspay_order_failed')
                end
              end
            end
          end
        end
      else
        status = 'failed'
      end
    end

    render json: {
      success: true,
      id: @subscription.id.to_s,
      status: status,
      message: message,
    }, status: :ok
  end

  def failure_callback
    Rails.logger.warn("Subscription failure callback: #{params}")

    error_code = params[:error_code]

    if error_code.present?
      if JuspayPaymentUtils::SDK_FAILURE_ERROR_CODES.include?(error_code)
        if @subscription.may_close?
          @subscription.pg_json = params
          @subscription.close!
        else
          Honeybadger.notify("Subscription with id: #{@subscription.id} is in invalid state",
                             context: { params: params })
        end
      elsif !JuspayPaymentUtils::SDK_PROBABLE_SUCCESS_ERROR_CODES.include?(error_code)
        Honeybadger.notify("Subscription failure callback with unknown error code",
                           context: { params: params })
      end
    else
      Honeybadger.notify("Subscription failure callback called without error_code",
                         context: { params: params })
    end

    render json: { success: true }, status: :ok
  end

  def cancel_subscription
    subscription_id = @user.cancellable_latest_subscription.id
    if subscription_id != @subscription.id
      return render json: { success: false, message: I18n.t('subscription_cancel.subscription_id_mismatch') },
                    status: :bad_request
    end
    reason = params[:reason]
    if reason.blank?
      return render json: { success: false, message: I18n.t('subscription_cancel.reason_required') },
                    status: :bad_request
    end

    begin
      @user.cancel_subscription(subscription_id, reason)
      render json: { success: true, message: I18n.t('subscription_cancel.success') }, status: :ok
    rescue StandardError => e
      Honeybadger.notify(e, context: { user_id: @user.id })
      render json: { success: false, message: I18n.t('subscription_cancel.failure') }, status: :internal_server_error
    end
  end

  private

  def set_subscription
    id = params[:id] || params[:subscription_id]
    # id may be in the format of 'sub_1234567890'
    # so we need to extract the actual id from it
    id = id.split('_').last if id.include?('_')
    @subscription = Subscription.find_by(id: id, user_id: @user.id)
    if @subscription.blank?
      render json: { success: false, message: I18n.t('subscription_not_found') }, status: :not_found
    end
  end
end
