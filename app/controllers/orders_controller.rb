class OrdersController < ApiController
  before_action :set_logged_in_user
  before_action :set_order, only: [:get_checkout_url, :show]

  def get_checkout_url
    if @order.status.to_sym == :pending
      render json: { success: false, message: 'Payment is still processing' }, status: :bad_request and return
    elsif @order.status.to_sym == :successful
      render json: { success: false, message: 'Already paid' }, status: :bad_request and return
    end

    begin
      url = "https://praja.app/orders/#{@order.id}/cashfree-checkout"
      render json: {
        success: true,
        url: url,
        share_text: url
      }, status: :ok
    rescue
      render json: { success: false, message: 'Unable to generate payment url' },
             status: :bad_request
    end
  end

  def get_poster_order
    @order = Order.where(user_id: @user.id, status: :pending).first
    render json: @order
  end

  # GET /orders/1
  def show
    render json: @order
  end

  private

  def set_order
    order_id = params[:order_id].present? ? params[:order_id] : params[:id]
    @order = Order.find_by(id: order_id, user_id: @user.id)
    return if @order.blank? || @order.status.to_sym != :pending

    begin
      CashfreePaymentUtils.get_transaction_status(@order.order_transactions.last.transaction_id)
      @order.reload
    rescue StandardError => exception
      logger.error(exception.message.to_s)
    end

  end

  # Only allow a list of trusted parameters through.
  def order_params
    params.fetch(:order, {})
  end
end
