# frozen_string_literal: true

class JuspayController < ExternalServiceController
  before_action :log_request
  after_action :log_response

  def log_request
    Rails.logger.warn("[keep-it-forever] #{controller_name}#{action_name} :: PARAMS: #{params}")
  end
  def log_response
    Rails.logger.warn("[keep-it-forever] #{controller_name}#{action_name} :: RESPONSE: #{response.body} :: PARAMS: #{params}")
  end

  def webhook
    event_name = params[:event_name]
    if event_name.blank?
      Honeybadger.notify('Invalid juspay callback', context: { params: params })
      return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
    end

    subscription = nil
    if event_name.include?('MANDATE_')
      subscription_pg_reference_id = params[:content][:mandate][:mandate_id]
      if subscription_pg_reference_id.present?
        subscription = Subscription.find_by(pg_reference_id: subscription_pg_reference_id)
      end

      # handle first time MANDATE_ACTIVATE webhook event
      if event_name == 'MANDATE_ACTIVATED' && subscription.blank?
        return handle_first_mandate_activated_event(params)
      end

      # Handle for non-MANDATE_ACTIVATED events, and when order_id is present
      if params[:content][:mandate][:order_id].present?
        subscription_charge_pg_id = params[:content][:mandate][:order_id]
        subscription_charge = SubscriptionCharge.find_by(pg_id: subscription_charge_pg_id)
        if subscription_charge.present?
          subscription = subscription_charge.subscription
        end
      end
    elsif event_name.include?('ORDER_')
      subscription_id = params[:content][:order][:udf1]
      subscription = Subscription.find_by(pg_id: subscription_id)
    end

    if subscription.blank?
      Honeybadger.notify('No Order ID in Juspay callback', context: { params: params })
      return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
    end

    case event_name

    when 'MANDATE_ACTIVATED'
      if subscription.pg_reference_id.blank? && params[:content][:mandate][:mandate_id].present?
        subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
        subscription.save!
      end

      if subscription.may_resume?
        subscription.pg_json = params
        subscription.resume!
      elsif !subscription.active?
        Honeybadger.notify('Invalid state for MANDATE_ACTIVATED',
                           context: { subscription_id: subscription.id, params: params })
      end

    when 'MANDATE_REVOKED'
      if subscription.pg_reference_id.blank? && params[:content][:mandate][:mandate_id].present?
        subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
        subscription.save!
      end

      if subscription.may_customer_cancel?
        subscription.pg_json = params
        subscription.customer_cancel!
      elsif !subscription.cancelled?
        Honeybadger.notify('Invalid state for MANDATE_REVOKED',
                           context: { subscription_id: subscription.id, params: params })
      end

    when 'MANDATE_PAUSED'
      if subscription.pg_reference_id.blank? && params[:content][:mandate][:mandate_id].present?
        subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
        subscription.save!
      end

      if subscription.may_customer_pause?
        subscription.pg_json = params
        subscription.customer_pause!
      elsif !subscription.cancelled?
        Honeybadger.notify('Invalid state for MANDATE_PAUSED',
                           context: { subscription_id: subscription.id, params: params })
      end

    when 'MANDATE_EXPIRED'
      if subscription.pg_reference_id.blank? && params[:content][:mandate][:mandate_id].present?
        subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
        subscription.save!
      end

      if subscription.may_close?
        subscription.pg_json = params
        subscription.close!
      elsif !subscription.closed?
        Honeybadger.notify('Invalid state for MANDATE_EXPIRED',
                           context: { subscription_id: subscription.id, params: params })
      end

    when 'MANDATE_FAILED'
      if subscription.pg_reference_id.blank? && params[:content][:mandate][:mandate_id].present?
        subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
        subscription.save!
      end

      if subscription.may_close?
        subscription.pg_json = params
        subscription.close!
      elsif !subscription.closed?
        Honeybadger.notify('Invalid state for MANDATE_FAILED',
                           context: { subscription_id: subscription.id, params: params })
      end

    when 'ORDER_SUCCEEDED'
      if params[:content].blank? ||
        params[:content][:order].blank? ||
        params[:content][:order][:order_id].blank?
        Honeybadger.notify('Invalid Juspay ORDER_SUCCEEDED callback', context: { params: params })
        return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
      end

      order = params[:content][:order]

      subscription_charge_pg_id = order[:order_id]
      subscription_charge = subscription.subscription_charges.find_by(pg_id: subscription_charge_pg_id)
      if subscription_charge.blank?
        Honeybadger.notify('Subscription charge not found', context: { params: params })
        return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
      end

      # Ignore if this callback is for the first subscription charge of the subscription,
      # because that will be handled in MANDATE_ACTIVATED callback
      if subscription.subscription_charges.count == 1
        return render json: { success: true, message: 'Already consumed' }, status: :ok
      end

      # assume that if both ids are same, then the reference_id is our id.
      # Happens when raising a charge with Juspay. There is no other way here.
      if subscription_charge.pg_id == subscription_charge.pg_reference_id || subscription_charge.pg_reference_id.blank?
        subscription_charge.pg_reference_id = order[:id]
      end

      subscription_charge.pg_json = params
      if subscription_charge.may_success?
        subscription_charge.success!
      else
        Honeybadger.notify('Invalid state for ORDER_SUCCEEDED',
                            context: { subscription_charge_id: subscription_charge.id, params: params })
      end

    when 'ORDER_REFUNDED'
      if params[:content].blank? ||
        params[:content][:order].blank? ||
        params[:content][:order][:order_id].blank?
        Honeybadger.notify('Invalid Juspay ORDER_REFUNDED callback',
                           context: { subscription_id: subscription.id, params: params })
        return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
      end

      order = params[:content][:order]
      refunds = params[:content][:order][:refunds]

      subscription_charge_pg_id = order[:order_id]
      subscription_charge = subscription.subscription_charges.find_by(pg_id: subscription_charge_pg_id)

      # Return error if subscription charge is not found
      if subscription_charge.blank?
        Honeybadger.notify('Subscription charge not found',
                          context: { subscription_id: subscription.id, params: params })
        return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
      end

      # assume that if both ids are same, then the reference_id is our id.
      # Happens when raising a charge with Juspay. There is no other way here.
      if subscription_charge.pg_id == subscription_charge.pg_reference_id || subscription_charge.pg_reference_id.blank?
        subscription_charge.pg_reference_id = order[:id]
        subscription_charge.save
      end
      # Case 1: Refund with a specific refund ID
      if refunds.present? && refunds.is_a?(Array) && refunds.any?
        refunds.each do |refund_data|
          unique_request_id = refund_data[:unique_request_id]
          initiated_by = refund_data[:initiated_by]
          if subscription_charge.present? && initiated_by == 'AUTO_REFUNDED'
            subscription_charge.handle_initial_payment_refund
          end
          # Extract the refund ID from the unique_request_id format: "R-{refund_id}-{pg_id}"
          refund_id = nil
          if unique_request_id.present? && unique_request_id.start_with?('R-')
            refund_id = unique_request_id.split('-')[1]
          end

          # If we have a refund ID, try to find the SubscriptionChargeRefund record
          if refund_id.present?
            refund = SubscriptionChargeRefund.find_by(id: refund_id)

            if refund.present?
              # Update the refund record with the response and mark as success
              refund.update(pg_json: params)
              refund.mark_as_success! if refund.may_mark_as_success?
            end
          end

        end
        return render json: { success: true }, status: :ok
      end

    when 'ORDER_FAILED'
      if params[:content].blank? ||
        params[:content][:order].blank? ||
        params[:content][:order][:order_id].blank?
        Honeybadger.notify('Invalid Juspay ORDER_FAILED callback',
                           context: { subscription_id: subscription.id, params: params })
        return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
      end

      order = params[:content][:order]

      subscription_charge_pg_id = order[:order_id]
      subscription_charge = subscription.subscription_charges.find_by(pg_id: subscription_charge_pg_id)
      if subscription_charge.blank?
        Honeybadger.notify('Subscription charge not found',
                           context: { subscription_id: subscription.id, params: params })
        return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
      end

      # assume that if both ids are same, then the reference_id is our id.
      # Happens when raising a charge with Juspay. There is no other way here.
      if subscription_charge.pg_id == subscription_charge.pg_reference_id || subscription_charge.pg_reference_id.blank?
        subscription_charge.pg_reference_id = order[:id]
      end

      if subscription_charge.may_fail?
        subscription_charge.pg_json = params
        subscription_charge.fail!
      else
        Honeybadger.notify('Invalid state for ORDER_FAILED',
                            context: { subscription_charge_id: subscription_charge.id, params: params })
      end
    else
      # ignore
    end

    render json: { success: true }, status: :ok
  end

  private

  def handle_first_mandate_activated_event(params)
    if params[:content].blank? ||
      params[:content][:mandate].blank? ||
      params[:content][:mandate][:order_id].blank? ||
      params[:content][:mandate][:mandate_id].blank?
      Honeybadger.notify('Invalid Juspay first MANDATE_ACTIVATED callback', context: { params: params })
      return render json: { success: false, message: 'Invalid callback' }, status: :bad_request
    end

    subscription_charge_pg_id = params[:content][:mandate][:order_id]
    subscription_charge = SubscriptionCharge.find_by(pg_id: subscription_charge_pg_id)
    if subscription_charge.blank?
      Honeybadger.notify('Subscription charge not found', context: { params: params })
      return render json: { success: false, message: 'Subscription charge not found' }, status: :bad_request
    end

    # As we don't get the mandate id while creating the mandate order, it is mandatory to save it here.
    subscription = subscription_charge.subscription
    subscription.pg_json = params
    subscription.pg_reference_id = params[:content][:mandate][:mandate_id]
    subscription.save!

    subscription_charge.pg_json = params
    if subscription_charge.may_success?
      subscription_charge.success!
    else
      Honeybadger.notify('Invalid state for first MANDATE_ACTIVATED',
                          context: { subscription_charge_id: subscription_charge.id, params: params })
    end

    render json: { success: true }, status: :ok
  end
end
