# frozen_string_literal: true

class WebAppController < ActionController::API
  before_action :set_logged_in_user, except: %i[get_email_otp login get_poster_web_tool_frames get_metadata_for_poster_web_tool get_cashfree_payment_session_id get_juspay_link]
  before_action :set_logged_in_user_optional, only: %i[get_poster_web_tool_frames get_cashfree_payment_session_id]

  def set_logged_in_user
    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')

      @user, has_active_token = get_user_from_token(token)

      if !has_active_token
        render json: { success: false, message: 'మీకు అనుమతి లేదు' }, status: :unauthorized
      elsif @user.nil?
        render json: { success: false, message: 'యూసర్ కనుగొనబడలేదు' }, status: :unauthorized
      else
        Honeybadger.context({ user_id: @user.id })
        ::NewRelic::Agent.add_custom_attributes({ user_id: @user.id })
      end
    else
      render json: { success: false, message: 'మీకు అనుమతి లేదు!' }, status: :unauthorized
    end
  end

  def set_logged_in_user_optional
    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')
      @user = User.get_user_from_jwt_token(token)
      if @user.present?
        Honeybadger.context({ user_id: @user.id })
        ::NewRelic::Agent.add_custom_attributes({ user_id: @user.id })
      end
    end
  end

  private
  def get_user_from_token(token)
    user = nil
    has_active_web_token = false
    payload = JsonWebToken.decode(token)
    if payload.present?
      if payload['nano_id'].present?
        has_active_web_token = $redis.sismember(Constants.web_app_tokens_redis_key, payload['nano_id'])

        if has_active_web_token
          user = User.find_by_hashid(payload['user_hash_id'])
          user = nil if user.present? && !user.active_status?
        end
      end
    end

    [user, has_active_web_token]
  end
end
