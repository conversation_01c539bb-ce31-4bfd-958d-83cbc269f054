# extend this class with ServiceApiController
require 'json'

class DmServiceController < ServiceApiController
  before_action :validate_service
  before_action :set_user, except: [:index, :filter_circle_users]

  def index
    render json: { message: 'Hello from RoR Service' }
  end

  def authorize_user_to_create_conversation
    recipient_id = params[:recipient_id]
    count_of_last_24_hours_active_conversations_created = params[:total_active_conversations_created_with_in_24_hrs] || 0
    count_of_last_7_days_active_conversations_created = params[:total_active_conversations_created_with_in_7_days] || 0
    count_of_last_7_days_active_other_conversations_created = params[:total_active_other_conversations_created_with_in_7_days] || 0
    count_of_last_24_hours_active_other_conversations_created = params[:total_active_other_conversations_created_with_in_24_hrs] || 0
    if recipient_id.present?
      auth_info = DmUtil.authorize_user_to_create_one_to_one_conversation(@user, recipient_id,
                                                                          count_of_last_7_days_active_conversations_created,
                                                                          count_of_last_24_hours_active_conversations_created,
                                                                          count_of_last_7_days_active_other_conversations_created,
                                                                          count_of_last_24_hours_active_other_conversations_created)
      if auth_info.present?
        return render json: { success: true, auth_status: auth_info[:status], :parameters => auth_info[:parameters] }, status: :ok
      end
    end
    render json: { success: false }, status: :unprocessable_entity
  end

  def send_dm_one_to_one_notification
    user_ids = params[:user_ids]
    notification_body = params[:body]
    message_id = params[:message_id]
    conversation_id = params[:conversation_id]

    # checking for the required params and returning bad request if any of the required params are missing
    if user_ids.blank? || notification_body.blank? || message_id.blank? || conversation_id.blank?
      return render json: { success: false, message: 'Invalid Params' }, status: :bad_request
    end

    Honeybadger.context({ message_id: message_id })
    # converting the user ids to integer
    user_ids = user_ids.map(&:to_i)

    # sending message notification to the eligible users with required app versions
    if user_ids.present?
      notification_title = @user.name
      message = params[:data][:message]
      path = params[:data][:path]

      sender_photo_url = @user.photo.present? ? @user.photo.placeholder_url : nil
      # creating payload
      payload = {
        "title": notification_title,
        "body": notification_body,
        "message_channel": "message",
        "data": {
          "sender_photo_url": sender_photo_url,
          "sender_id": @user.id.to_s,
          "name": notification_title,
          "text": notification_body,
          "channel_id": "message",
          "channel_name": "Messages",
          "channel_description": "Messages channel for Praja Users",
          "message": message.to_json,
          "path": path,
        }
      }

      user_ids.each do |user_id|

        GarudaNotification.send_user_notification(user_id, payload, Constants.get_dm_enabled_app_version.to_s)

      end
    end

    render json: { success: true }, status: :ok
  end

  def send_dm_private_group_notification
    circle_id = params[:circle_id]
    excluded_user_ids = params[:excluded_user_ids]
    message_id = params[:message_id]
    conversation_id = params[:conversation_id]
    notification_body = params[:body]

    if circle_id.blank? || message_id.blank? || conversation_id.blank?
      return render json: { success: false, message: 'Invalid Params' }, status: :bad_request
    end

    circle = Circle.find_by(id: circle_id)
    if circle.blank?
      return render json: { success: false, message: 'Circle not found' }, status: :not_found
    end

    excluded_user_ids = excluded_user_ids.present? ? excluded_user_ids.map(&:to_i) : []
    users_left_channel_instead_of_circle = ExcludedUserCircle.where(circle_id: circle_id).pluck(:user_id)
    excluded_user_ids += users_left_channel_instead_of_circle
    excluded_user_ids.uniq!

    circle_user_ids = circle.users.pluck(:id)
    to_be_sent_user_ids = circle_user_ids - excluded_user_ids - [@user.id]

    to_be_sent_user_ids.each do |user_id|
      message = params[:data][:message]
      path = params[:data][:path]

      circle_photo_url = if circle.photo.present?
                           circle.photo.placeholder_url
                         else
                           Constants.default_dm_private_group_icon
                         end

      sender_photo_url = @user.photo.present? ? @user.photo.placeholder_url : nil
      # payload
      payload = {
        "message_channel": "message",
        "title": circle.name,
        "body": "#{@user.name}: #{notification_body}",
        "data": {
          "circle_name": circle.name,
          "circle_photo_url": circle_photo_url,
          "sender_photo_url": sender_photo_url,
          "sender_id": @user.id.to_s,
          "name": @user.name,
          "text": notification_body,
          "channel_id": "message",
          "channel_name": "Messages",
          "channel_description": "Messages channel for Praja Users",
          "message": message.to_json,
          "path": path,
        }
      }

      GarudaNotification.send_user_notification(user_id, payload, Constants.get_dm_private_groups_enabled.to_s)

    end
    render json: { success: true }, status: :ok
  end

  def send_dm_channel_notification
    circle_id = params[:circle_id]
    excluded_user_ids = params[:excluded_user_ids]
    message_id = params[:message_id]
    conversation_id = params[:conversation_id]
    notification_body = params[:body]
    data = params[:data]

    if circle_id.blank? || message_id.blank? || conversation_id.blank? || data.blank?
      return render json: { success: false, message: 'Invalid Params' }, status: :bad_request
    end

    circle = Circle.find_by(id: circle_id)
    if circle.blank?
      return render json: { success: false, message: 'Circle not found' }, status: :not_found
    end

    message = data[:message]
    path = data[:path]

    users_left_channel_instead_of_circle = ExcludedUserCircle.where(circle_id: circle_id).pluck(:user_id)

    excluded_user_ids = excluded_user_ids.present? ? excluded_user_ids.map(&:to_i) : []
    excluded_user_ids << @user.id
    excluded_user_ids += users_left_channel_instead_of_circle
    excluded_user_ids.uniq!

    circle_photo_url = if circle.photo.present?
                         circle.photo.placeholder_url
                       else
                         Constants.default_dm_channel_icon
                       end

    payload = {
      "title": circle.name,
      "body": notification_body,
      "message_channel": "message",
      "data": {
        "sender_photo_url": circle_photo_url,
        "sender_id": @user.id.to_s,
        "name": circle.name,
        "text": notification_body,
        "channel_id": "message",
        "channel_name": "Messages",
        "channel_description": "Messages channel for Praja Users",
        "message": message.to_json,
        "path": path,
      }
    }

    android_payload = {
      "platform": "android",
      "min_app_version": Constants.get_dm_channels_enabled_version.to_s,
      "payload": payload,
      "excluded_user_ids": excluded_user_ids
    }

    ios_payload = {
      "platform": "ios",
      "min_app_version": Constants.get_dm_channels_enabled_version.to_s,
      "payload": payload,
      "excluded_user_ids": excluded_user_ids
    }

    android_notification_call_thread = Thread.new do
      begin
        GarudaNotification.send_channel_notification(circle_id, android_payload)
      rescue => e
        Honeybadger.notify(e, context: { circle_id: circle_id, android_payload: android_payload })
        throw e
      end
    end

    ios_notification_call_thread = Thread.new do
      begin
        GarudaNotification.send_channel_notification(circle_id, ios_payload)
      rescue => e
        Honeybadger.notify(e, context: { circle_id: circle_id, ios_payload: ios_payload })
        throw e
      end
    end

    android_notification_call_thread.join
    ios_notification_call_thread.join

    render json: { success: true }, status: :ok
  end

  def get_user_dm_circles_info
    render json: @user.get_user_dm_circles_info, status: :ok
  end

  def get_user_dm_circles_info_v2
    get_user_dm_circles_info
  end

  # user permissions and joined info against a circle
  def get_user_dm_circle_info
    circle_id = params[:circle_id].to_i
    return render json: { success: false, message: 'Circle id should be present' }, status: :bad_request if circle_id.blank?

    user_circle_permission_hash = @user.get_user_dm_circle_info(circle_id)
    render json: user_circle_permission_hash, status: :ok
  end

  def filter_circle_users
    circle_id = params[:circle_id]
    user_ids = params[:user_ids].map(&:to_i)

    return render json: { success: false, message: 'Circle id should be present' }, status: :bad_request if circle_id.blank?

    circle_user_ids_to_send = []
    user_ids.each_slice(500).each do |user_ids_batch|
      circle_user_ids_to_send += UserCircle.where(circle_id: circle_id.to_i, user_id: user_ids_batch).pluck(:user_id)
    end

    render json: { success: true, userIds: circle_user_ids_to_send }, status: :ok
  end

  private

  def validate_service
    if @service != 'dm'
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end

  def set_user
    token = request.headers['X-User-Jwt-Token']
    if token.blank?
      render json: { message: 'Unauthorized' }, status: :unauthorized
      return
    end

    token = token.sub('Bearer ', '')
    decoded_token_hash = JsonWebToken.decode(token)
    if decoded_token_hash.blank?
      render json: { message: 'Unauthorized' }, status: :unauthorized
      return
    end

    @user = User.find_by(id: decoded_token_hash['user_id'])
    if @user.blank?
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end
end
