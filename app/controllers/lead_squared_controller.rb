# frozen_string_literal: true

class LeadSquaredController < ExternalServiceController
  def lead_stage_update
    lead_squared_params = params[:lead_squared]
    if lead_squared_params.present? &&
      lead_squared_params['After']['ProspectStage'].present? &&
      (lead_squared_params['After']['Phone'].present? || lead_squared_params['After']['Mobile'].present?)
      lead_stage = lead_squared_params['After']['ProspectStage'].strip.downcase

      phone_str = lead_squared_params['After']['Phone'] || lead_squared_params['After']['Mobile']
      phone_object = TelephoneNumber.parse(phone_str, :IN)

      if phone_object.valid?([:mobile])
        phone = phone_object.national_number(formatted: false).slice(1..-1).to_i
        user = User.find_by_phone(phone)
        if user.present?
          MixpanelIntegration.perform_async(user.id, { lead_stage: lead_stage })
          return render json: { message: 'Updated!' }, status: :ok
        end
      end
    end

    render json: { message: 'Invalid request!' }, status: :unprocessable_entity
  end
end
