class PhotosController < ApiController
  before_action :set_logged_in_user

  # POST /upload
  def upload
    photo = Photo.upload(params[:data], @user.id)

    if !photo.nil?
      render json: photo, status: :ok
    else
      render json: {success: false, message: "Unable to save the photo"}, status: :unprocessable_entity
    end
  end

  private

  # Only allow a trusted parameter "white list" through.
  def photo_params
    params.require(:photo).permit(:data)
  end
end

