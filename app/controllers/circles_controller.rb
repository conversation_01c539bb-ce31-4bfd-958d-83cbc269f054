class CirclesController < ApiController
  include Pundit::Authorization
  include CirclesControllerSearchConcern
  include ActionView::Helpers::NumberHelper
  include JsonBuilder
  include ElectionConstituencyStatus

  before_action :set_logged_in_user, except: %i[all_districts district_mandals mandal_villages get_info get_all_leader_circle_owner_positions get_preview_html get_channel_preview_html]
  before_action :set_circle, except: %i[all_districts district_mandals mandal_villages create_private get_political_parties get_circles_search_results get_conversation_members]

  before_action :set_district, only: [:district_mandals]
  before_action :set_mandal, only: [:mandal_villages]

  # this is the old trending_feed endpoint
  def trending_feed
    offset = params[:offset].to_i if params[:offset].present?

    count = params[:count].to_i if params[:count].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?
    fetch_data_using_sql = params[:is_sql].blank? ? false : params[:is_sql]

    fetch_data_using_sql, feed_items = get_trending_posts_feed(offset, count, loaded_feed_item_ids,
                                                               fetch_data_using_sql)
    post_feed_json = {
      is_sql: fetch_data_using_sql,
      feed_items: feed_items
    }
    render json: post_feed_json, status: :ok
  end

  # this is the new trending_feed_v1 endpoint
  def trending_feed_v1
    offset = params[:offset].to_i if params[:offset].present?

    count = params[:count].to_i if params[:count].present?

    page = (offset / count) + 1

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?
    fetch_data_using_sql = params[:is_sql].blank? ? false : params[:is_sql]

    if page == 1 && loaded_feed_item_ids.exclude?(Constants.fan_request_feed_item_id)
      fan_poster_feed_item = get_circle_fan_poster_feed_item
      # add fan_poster_feed item for the circle owner if circle requirements met
      count -= 1 if fan_poster_feed_item.present?
    end

    fetch_data_using_sql, feed_items = get_trending_posts_feed(offset, count, loaded_feed_item_ids,
                                                               fetch_data_using_sql)

    # insert fan_poster_feed_item at the beginning of the feed
    feed_items.insert(0, fan_poster_feed_item) if fan_poster_feed_item.present?

    post_feed_json = {
      is_sql: fetch_data_using_sql,
      feed_items: feed_items
    }
    render json: post_feed_json, status: :ok
  end

  def get_trending_posts_feed(offset, count, loaded_feed_item_ids, fetch_data_using_sql)
    loaded_feed_item_ids_without_list_ids = []
    loaded_feed_item_ids_without_list_ids = loaded_feed_item_ids.grep(/\d+/, &:to_i) if loaded_feed_item_ids.present?

    page = (offset / count) + 1

    # for posters feed
    posters_feed = get_poster_feed(loaded_feed_item_ids, page)
    count -= 1 if posters_feed.present?

    # for suggested users feed
    suggested_users_feed_v1 = get_suggested_users_feed(loaded_feed_item_ids, page)
    count -= 1 if suggested_users_feed_v1.present?

    circle_ids = []

    circle_ids << @circle.id
    circle_ids += @circle.get_all_child_circle_ids

    news_feed_records = []
    feed_item_ids = []
    feed_item_user_ids = []

    # fetch data from es if fetch_data_using_sql is false
    unless fetch_data_using_sql
      query = get_circle_trending_feed_es_query(circle_ids, loaded_feed_item_ids_without_list_ids, offset, count)
      posts_es = ES_CLIENT.search index: EsUtil.get_new_posts_index_v2, body: query

      posts_es['hits']['hits'].each do |hit|
        post_es_item = hit['fields']
        feed_item_ids << post_es_item['id'].first.to_i
        loaded_feed_item_ids_without_list_ids << post_es_item['id'].first.to_i
        feed_item_user_ids << post_es_item['user_id'].first.to_i

        news_feed_record = {
          id: post_es_item['id'].first.to_i,
          timestamp: post_es_item['created_at'].first.to_time,
          feed_type: 'circle_posted'
        }

        news_feed_records << news_feed_record
      end
      count -= posts_es['hits']['hits'].count
    end

    # fetch data from sql if count is greater than 0 i.e we don't have posts in es to retrieve
    if fetch_data_using_sql || count > 0
      fetch_data_using_sql = true
      news_feed_records, feed_item_ids, feed_item_user_ids = get_post_data_from_sql(circle_ids,
                                                                                    loaded_feed_item_ids_without_list_ids,
                                                                                    count, news_feed_records, feed_item_ids,
                                                                                    feed_item_user_ids)
    end

    news_feed_record_hash = {}

    news_feed_records.map do |record|
      news_feed_record_hash[record[:id]] = record
    end

    circle_feed = get_feed_util_v2(news_feed_record_hash, feed_item_ids, feed_item_user_ids)

    circle_feed.insert([0, [(circle_feed.count - 1), 0].max].min, suggested_users_feed_v1) if suggested_users_feed_v1.present?

    circle_feed.insert([0, [(circle_feed.count - 1), 0].max].min, posters_feed) if posters_feed.present?
    
    [fetch_data_using_sql, circle_feed]
  end

  def get_circle_trending_feed_es_query(circle_ids, loaded_feed_item_ids_without_list_ids, offset, count)
    {
      "fields": ["id", "user_id", "created_at"],
      "_source": false,
      "query": {
        "function_score": {
          "query": {
            "bool": {
              "must_not": {
                "terms": { "id": loaded_feed_item_ids_without_list_ids }
              },
              "filter": [
                {
                  "terms": { "tagged_circles_ids": circle_ids }
                },
                {
                  "term": { "active": true }
                },
                {
                  "range": {
                    "created_at": {
                      "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                    }
                  }
                }
              ]
            }
          },
          "script_score": {
            "script": {
              "lang": 'painless',
              "source": '' "
                            double currentTime = new Date().getTime();
                            double timeDiff = (currentTime - params._source.created_at)/(1000*60*60);
                            double score = 0;
                            int timeDiffFactor = timeDiff > 24 ? 10 : 30;

                            score = (Math.sqrt(params._source.likes_count +
                                 (params._source.comments_count > 2 ? params._source.comments_count * 2 : 0) +
                                 (params._source.unique_users_whatsapp_count > 2 ? params._source.unique_users_whatsapp_count * 1.5 : 0) )) *
                                  Math.exp(-timeDiff/timeDiffFactor);

                            if (params._source.has_poster_photo == true) { score = 0; }

                            return score;
                        " '',
              "params": {
                "stateId": @user.state_id,
                "teStateWt": 1.5,
                "otherStateWt": 3,
                "languageWt": 1,
                "teStateId": 33010
              }
            }
          },
          "boost_mode": 'replace',
          "min_score": 0.000001
        }
      },
      "sort": [
        { "_score": { "order": 'desc' } },
        { "created_at": { "order": 'desc' } }
      ],
      "collapse": {
        "field": "user_id"
      },
      "size": count,
      "from": offset
    }
  end

  def get_post_data_from_sql(circle_ids, loaded_feed_item_ids_without_list_ids, count, news_feed_records, feed_item_ids, feed_item_user_ids)

    sql = "SELECT a.entity_id, a.entity_type, GROUP_CONCAT(a.feed_type), GROUP_CONCAT(a.timestamp), MAX(a.timestamp), a.userid FROM
      ("

    if loaded_feed_item_ids_without_list_ids.present?
      # last_loaded_post_id_query helps to fetch the posts which are created before that post.
      last_loaded_post_id_query = " AND p.id < #{loaded_feed_item_ids_without_list_ids.min}"
      feed_ids_query = "AND p.id NOT IN (#{loaded_feed_item_ids_without_list_ids.join(',')})"
    else
      feed_ids_query = ""
      last_loaded_post_id_query = ""
    end
    if circle_ids.present?
      sql += "(SELECT p.id as entity_id, 3 as entity_type, 'circle_forwarded' as feed_type, pb.created_at as timestamp, p.user_id as userid
        FROM posts p
        INNER JOIN post_bounces pb ON p.id = pb.post_id AND pb.active = 1
        WHERE p.active = 1 #{last_loaded_post_id_query} AND pb.circle_id IN (#{circle_ids.join(',')}) #{feed_ids_query} )
        UNION DISTINCT "
    end

    user_ids_sql = ''

    sql += "(SELECT p.id as entity_id, 3 as entity_type, 'circle_posted' as feed_type, p.created_at as timestamp, p.user_id as userid
        FROM posts p
        INNER JOIN post_circles pc ON p.id = pc.post_id AND pc.active = 1
        WHERE p.active = 1 #{last_loaded_post_id_query} AND pc.circle_id IN (#{circle_ids.join(',')}) #{feed_ids_query}
    #{user_ids_sql})) as a
        group by 2, 1
        order by 2 ASC, 5 DESC
        limit #{count}"

    query_results = ActiveRecord::Base.connection.execute(sql)
    query_results.each do |result|
      news_feed_record = {
        id: result[0].to_i,
        timestamp: result[4]
      }
      feed_item_ids << result[0].to_i
      feed_item_user_ids << result[5].to_i
      grouped_feed_types = result[2].split(',')
      grouped_timestamps = result[3].split(',')

      index = 0
      grouped_timestamps.map do |grouped_timestamp|
        if news_feed_record[:timestamp] == grouped_timestamp
          news_feed_record[:feed_type] = grouped_feed_types[index]
          break
        end
        index += 1
      end

      news_feed_records << news_feed_record
    end
    [news_feed_records, feed_item_ids, feed_item_user_ids]
  end

  def get_poster_feed(loaded_feed_item_ids, page)
    posters_feed_json if page == 1 && loaded_feed_item_ids.exclude?(Poster::POSTER_FEED_ITEM_ID)
  end

  def get_suggested_users_feed(loaded_feed_item_ids, page)
    list_feed = @user.get_suggested_users_feed_v1(loaded_feed_item_ids, true) if page >= 2 && @user.user_exclusive_political_party == @circle.id
    if list_feed.present?
      custom_properties = list_feed[:custom_properties]
      custom_properties[:list_source] = "CIRCLE_FEED"
      custom_properties[:source_circle_id] = @circle.id
      custom_properties[:source_circle_name] = @circle.name
    end
    list_feed
  end

  def get_political_parties
    render json: @user.get_user_political_parties, status: :ok
  end

  def create_private
    if params[:name].nil? || params[:name].empty?
      render json: { success: false, message: 'name is required' }, status: :bad_request
    else
      photo = Photo.upload(params[:photo], @user.id) unless params[:photo].nil?

      @circle = Circle.new(
        name: params[:name],
        name_en: params[:name_en],
        level: :private,
        circle_type: :my_circle,
        head_user: @user,
        photo: photo,
        )

      if @circle.save
        UserCircle.create(circle_id: @circle.id, user_id: @user.id)
        render json: @circle, status: :ok
      else
        render json: @circle.errors, status: :unprocessable_entity
      end
    end
  end

  # search_contacts_for_invite api is removed

  # GET /circle/123
  def show
    if @circle.level == 'private' && @circle.circle_type == 'my_circle'
      @circle.is_user_joined = @circle.check_user_joined(@user)
      unless @circle.is_user_joined || @circle.head_user == @user
        return render json: { success: false, message: 'Unauthorized' }, status: :forbidden
      end
    end
    @circle.set_user_last_open(@user)

    render json: @circle.get_json(@user, @app_version), status: :ok
  end

  def sorting_users_with_permission(users_and_permissions_hash)
    order_of_permission_groups = [:owner, :admin, :designer, :creator]
    users_and_permissions_hash.keys.sort_by do |key|
      value = users_and_permissions_hash[key].to_sym
      [-(order_of_permission_groups.index(value) || order_of_permission_groups.length), key]
    end
  end

  def get_conversation_members

    circle = params[:circle_id].present? ? Circle.find_by_id(params[:circle_id]) : nil
    return render json: { success: false, message: 'Circle not found' }, status: :not_found if circle.blank?

    loaded_user_ids = params[:loaded_user_ids] || []
    count = 10
    count = params[:count].to_i if params[:count].present?

    exclude_user_ids = loaded_user_ids + ExcludedUserCircle.where(circle_id: circle.id).pluck(:user_id)

    user_with_permissions_hash = UserCirclePermissionGroup.joins(:permission_group).where(circle_id: circle.id).pluck(:user_id, "permission_groups.name").to_h

    sorted_admin_ids = sorting_users_with_permission(user_with_permissions_hash)

    owner_id = user_with_permissions_hash.key("owner")

    members_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: {
      "fields": ["id"],
      "_source": false,
      "query": get_circle_user_ids_with_sorted_order_query(circle, owner_id, sorted_admin_ids, exclude_user_ids, []),
      "sort": {
        "_score": 'desc',
        "badge_grade_level": "asc",
        "followers_count": 'desc'
      },
      "size": count
    }

    members_ids = []
    members_es['hits']['hits'].each do |hit|
      search_feed = hit['fields']
      members_ids << search_feed['id'].first
    end

    users_from_cache = User.get_users_batch(members_ids)
    members_list = users_from_cache.map do |user|
      member_hash = {}
      member_hash[:user] = user.get_user_response_hash_for_dm(@user)
      permission_group_name = user.get_permission_group_name_of_user_on_circle(circle)
      member_hash[:label] = permission_group_name.to_s.humanize
      member_hash[:type] = Circle.get_member_type_for_conversation(permission_group_name)
      member_hash
    end
    members_list.compact!
    members_list.sort_by { |element| members_ids.index(element['user'] & ['id']) || members_ids.length }

    render json: { members: members_list }, status: :ok
  end

  # GET /circle/123/circle-v1
  def show_v1
    if @circle.level == 'private' && @circle.circle_type == 'my_circle'
      @circle.is_user_joined = @circle.check_user_joined(@user)
      unless @circle.is_user_joined
        return render json: { success: false, message: 'Unauthorized' }, status: :forbidden
      end
    end
    @circle.set_user_last_open(@user)

    circle_data = @circle.get_json_v1(@user, @app_version, params[:feed_option])
    if circle_data[:feed_options].present? && circle_data[:feed_options].first[:key] == "trending"
      circle_data[:preload_feed_items] = get_trending_posts_feed(0, 2, [], false).last
    else
      circle_data[:preload_feed_items] = get_feed_items(0, 2, [], false).last
    end
    render json: circle_data, status: :ok
  end

  # GET /circle/123/circle-v2
  def show_v2
    if @circle.level == 'private' && @circle.circle_type == 'my_circle'
      @circle.is_user_joined = @circle.check_user_joined(@user)
      unless @circle.is_user_joined
        return render json: { success: false, message: 'Unauthorized' }, status: :forbidden
      end
    end
    @circle.set_user_last_open(@user)

    count = 2
    circle_data = @circle.get_json_v2(@user, params[:feed_option])
    loaded_feed_item_ids = []
    fan_poster_feed_item = get_circle_fan_poster_feed_item
    # add fan_poster_feed item for the circle owner if circle requirements met
    if fan_poster_feed_item.present?
      count -= 1
      loaded_feed_item_ids << Constants.fan_request_feed_item_id
    end
    if circle_data[:feed_options].present? && circle_data[:feed_options].first[:key] == "trending"
      circle_data[:preload_feed_items] = get_trending_posts_feed(0, count, loaded_feed_item_ids, false).last
    else
      circle_data[:preload_feed_items] = get_feed_items(0, count, loaded_feed_item_ids, false).last
    end
    # insert fan_poster_feed_item at the beginning of the feed
    circle_data[:preload_feed_items].insert(0, fan_poster_feed_item) if fan_poster_feed_item.present?
    render json: circle_data, status: :ok
  end

  def get_circle_fan_poster_feed_item
    if (@circle.political_party_level? || @circle.political_leader_level?) && @user.owner_of_circle?(@circle.id) &&
      !@user.already_requested_for_premium_poster?(@circle.id)
      exact_users_count = CirclePremiumInterest.fan_poster_interested_users_count(@circle.id)
      if exact_users_count > Constants.min_no_of_users_to_be_interested
        @user.fan_poster_feed_item(exact_users_count, @circle)
      end
    end
  end

  def posters_feed_json
    if AppVersionSupport.creative_carousel_in_circle_feed_supported?
      creatives = @circle.get_creatives_for_feed_carousel(@user)
      if creatives.present?
        CirclesController.build_creative_carousel(
          title: I18n.t('circle_posters_feed.title'),
          items: creatives,
          analytics_params: { circle_id: @circle.id, circle_name_en: @circle.name_en, circle_level: @circle.level, circle_type: @circle.circle_type },
          feed_item_id: Poster::POSTER_FEED_ITEM_ID,
          next_page_url: nil,
          )
      end
    else
      posters_feed = @circle.get_posters_feed(@user, @app_version)
      get_gradients_of_poster_header = @circle.get_gradients_of_poster_header
      header_text_color = get_gradients_of_poster_header[:header_text_color]
      header_gradients = get_gradients_of_poster_header[:header_gradients]
      if posters_feed.present?
        {
          "feed_type": "posters",
          "feed_item_id": Poster::POSTER_FEED_ITEM_ID,
          "header": I18n.t('circle_posters_feed.header'),
          "header_text_color": header_text_color,
          "header_gradients": header_gradients,
          "posters": posters_feed
        }
      end
    end
  end

  def get_info
    json = {}
    json = @circle.get_about_page_info if @app_version > Gem::Version.new('1.17.2') && @circle.political_leader_level?

    render json: json, status: :ok
  end

  def get_all_leader_circle_owner_positions
    if @circle.political_leader_level?
      render json: @circle.get_owner_all_positions, status: :ok
    else
      render json: { success: false, message: 'Positions shown only on political leader circle' }, status: :bad_request
    end
  end

  def leave_channel
    if @circle.conversation_type.to_sym == :channel
      success_response = { success: true, message: I18n.t('leave_channel.success_message') }
      if UserCircle.where(circle_id: @circle.id, user_id: @user.id).exists?
        ExcludedUserCircle.where(user_id: @user.id, circle_id: @circle.id, excluded_by_user_id: @user.id, conversation_type: @circle.conversation_type).first_or_create
      end
      render json: success_response, status: :ok
    else
      render json: { success: false, message: I18n.t('leave_channel.channel_not_found') }, status: :bad_request
    end
  end

  def leave_private_group
    success_response = { success: true, message: I18n.t('leave_private_group.success_message') }
    if @circle.conversation_type.to_sym == :private_group
      if @circle.level.to_sym == :private_group and @circle.circle_type.to_sym == :user_created
        UserCircle.find_by(circle_id: @circle.id, user_id: @user.id)&.destroy
      elsif UserCircle.where(circle_id: @circle.id, user_id: @user.id).exists?
        ExcludedUserCircle.where(user_id: @user.id, circle_id: @circle.id, excluded_by_user_id: @user.id, conversation_type: @circle.conversation_type).first_or_create
      end
      render json: success_response, status: :ok
      return
    else
      render json: { success: false, message: I18n.t('leave_private_group.group_not_found') }, status: :bad_request
    end
  end

  def get_share_text
    if @circle.level == 'private' && @circle.circle_type == 'my_circle' && @circle.head_user.id != @user.id
      return render json: { success: false }, status: :forbidden
    end

    circle_url = if @app_version >= Gem::Version.new('1.11.3')
                   Singular.shorten_link("https://prajaapp.sng.link/A3x5b/8mt6?_dl=praja%3A%2F%2Fcircles/#{@circle.id}&_ddl=praja%3A%2F%2Fcircles/#{@circle.id}&_fallback_redirect=https%3A%2F%2Fapi.thecircleapp.in%2Fcircles%2F#{@circle.id}%2Fpreview&paffid=#{@user.id}")
                 else
                   'https://m.praja.buzz/circles/' + @circle.hashid
                 end

    case @circle.circle_type
    when 'interest'
      text = I18n.t('circle_share_text.interest_circle_type.share_text', circle_name: @circle.name, members_count: @circle.get_members_count.to_s.gsub(/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/, "\\1,"))
    when 'location'
      text = I18n.t('circle_share_text.location_circle_type.share_text', circle_name: @circle.name)
    else
      text = I18n.t('circle_share_text.default_share_text', circle_name: @circle.name, circle_name_en: @circle.name_en)
    end

    if @app_version < Gem::Version.new('1.11.3')
      link = @user.get_dynamic_link
      msg = I18n.t('circle_share_text.older_version.message', text: text, circle_url: circle_url, older_version_message: I18n.t('circle_share_text.app_download_text') ,link: link.to_s)
    else
      msg = I18n.t('circle_share_text.latest_version.message', text: text, circle_url: circle_url)
    end

    render json: { success: true, text: msg }, status: :ok
  end

  def get_users
    if @app_version > Gem::Version.new('1.12.0')
      get_users_v2
    else
      get_users_v1
    end
  end

  def get_users_v1
    render json: @circle.get_users(@user, params[:offset].to_i, params[:count].to_i), status: :ok
  end

  def get_circle_user_ids_with_sorted_order_query(circle, owner_id, admin_user_ids = [], loaded_user_ids = [], child_circle_ids = [])
    {
      "script_score": {
        "query": {
          "bool": {
            # commented for now, as we are not getting the jagan as owner of the circle in his circle
            # need to confirm with dheeraj
            # "must_not": {
            #   "terms": {
            #     "id": User.core_party_user_ids # TODO: Remove this hack
            #   }
            # },
            "must": {
              "terms": {
                "circle_ids": [circle.id] + child_circle_ids
              }
            },
            "must_not": {
              "terms": {
                "id": loaded_user_ids
              }
            }
          }
        },
        "script": {
          "source": "" " double score = 0 ;
                              if ( params._source.circle_ids.contains(params.circle_id) &&
                                   params.owner_id != 0 && params._source.id == params.owner_id) {
                                score = score + 10000;
                              }

                              if (params.admin_user_ids.contains(params._source.id)) {
                                score = score + params.admin_user_ids.indexOf(params._source.id) + 1;
                              }

                              if (params.user_id == params._source.id) {
                                score = score + 0.1;
                              }

                              if ( params._source.badge != 0 ) {
                                if ( params._source.affiliated_political_party_id == params.circle_id ||
                                     params._source.village_id == params.circle_id ||
                                     params._source.mandal_id == params.circle_id ||
                                     params._source.district_id == params.circle_id ) {
                                    score = score + params.factor2;
                                }
                                else if (params.circle_level == 'political_leader' &&
                                         params._source.affiliated_political_party_id == params.affiliated_political_party_id) {
                                    score = score + params.factor2;
                                }
                                else if ( params._source.district_id ==  params.circle_id ||
                                          params._source.mandal_id ==  params.circle_id ||
                                          params._source.village_id == params.circle_id) {
                                    score = score + params.factor1;
                                }
                                _score * score;
                              }
                              else { _score; }
                          " "",
          "params": {
            "factor2": 3,
            "factor1": 2,
            "circle_id": circle.id,
            "circle_level": circle.level,
            "affiliated_political_party_id": circle.get_leader_circle_affiliated_party_id,
            "owner_id": owner_id,
            "admin_user_ids": admin_user_ids,
            "user_id": @user.id
          }
        }
      }
    }
  end

  def get_users_v2
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    child_circle_ids = @circle.get_all_child_circle_ids

    begin
      owner_id = @circle.get_owner_id.to_i
      if @circle.political_party_level?
        members_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: {
          "fields": ["id"],
          "_source": false,
          "query": {
            "bool": {
              "filter": {
                "terms": {
                  "circle_ids": [@circle.id] + child_circle_ids
                }
              }
            }
          },
          "sort": {
            "_script": {
              "script": "" "
                        if ( doc['circle_ids'].contains('#{@circle.id}') && #{owner_id} != 0 &&
                        doc['id'].value =='#{owner_id}') {
                          0;
                        }
                        else if ( doc['affiliated_political_party_id'].value == '#{@circle.id}') {
                          1;
                        } else if ( doc['affiliated_political_party_id'].value == '0' ) {
                          2;
                        } else {
                          3;
                        }" "",
              "type": "number",
              "order": "asc"
            },
            "badge_grade_level": "asc",
            "followers_count": "desc"
          },
          "size": count,
          "from": offset
        }
      else
        members_es = ES_CLIENT.search index: EsUtil.get_search_entities_index, body: {
          "fields": ["id"],
          "_source": false,
          "query": get_circle_user_ids_with_sorted_order_query(@circle, owner_id, [], [], child_circle_ids),
          "sort": {
            "_score": 'desc',
            "badge_grade_level": "asc",
            "followers_count": 'desc'
          },
          "size": count,
          "from": offset
        }
      end

      members_ids = []
      members_es['hits']['hits'].each do |hit|
        search_feed = hit['fields']
        members_ids << search_feed['id'].first

      end
      members_result = members_info(members_ids)
    rescue => e
      render json: { success: false, error: e.to_s }
      return
    end
    render json: members_result, status: :ok
  end

  def members_info(ids)
    users = User.includes(:photo, :village, user_roles: :role).find(ids)
    followed_user_ids = UserFollower.where(user_id: ids,
                                           follower_id: @user.id,
                                           active: true)
                                    .pluck(:user_id)

    members_info = []
    users.each do |user|
      members_info << user.user_json_for_members_tab(@user, followed_user_ids)
    end
    members_info
  end

  def get_posts
    posts = @circle.get_posts(@user, params[:offset].to_i, params[:count].to_i, @app_version)

    render json: posts, status: :ok
  end

  def get_feed
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?

    page = (offset / count) + 1

    suggested_users_feed_v1 = []
    if page >= 2 && @app_version > Gem::Version.new('1.16.1') && @user.user_exclusive_political_party == @circle.id
      list_feed = @user.get_suggested_users_feed_v1(loaded_feed_item_ids, true)
      if list_feed.present?
        custom_properties = list_feed[:custom_properties]
        custom_properties[:list_source] = "CIRCLE_FEED"
        custom_properties[:source_circle_id] = @circle.id
        custom_properties[:source_circle_name] = @circle.name
        suggested_users_feed_v1 = list_feed
        count -= 1
      end
    end

    circle_ids = []

    circle_ids << @circle.id
    circle_ids += @circle.get_all_child_circle_ids

    sql = "SELECT a.entity_id, a.entity_type, GROUP_CONCAT(a.feed_type), GROUP_CONCAT(a.timestamp), MAX(a.timestamp), a.userid FROM
      ("

    if @app_version > Gem::Version.new('1.7.9') && circle_ids.present?
      sql += "(SELECT p.id as entity_id, 3 as entity_type, 'circle_forwarded' as feed_type, pb.created_at as timestamp, p.user_id as userid
        FROM posts p
        INNER JOIN post_bounces pb ON p.id = pb.post_id AND pb.active = 1
        WHERE p.active = 1 AND pb.circle_id IN (#{circle_ids.join(',')}))
        UNION DISTINCT "
    end

    user_ids_sql = ''

    sql += "(SELECT p.id as entity_id, 3 as entity_type, 'circle_posted' as feed_type, p.created_at as timestamp, p.user_id as userid
        FROM posts p
        INNER JOIN post_circles pc ON p.id = pc.post_id AND pc.active = 1
        WHERE p.active = 1 AND pc.circle_id IN (#{circle_ids.join(',')}) #{user_ids_sql})) as a
      group by 2, 1
      order by 2 ASC, 5 DESC
      limit #{offset}, #{count}"

    news_feed_records = []
    feed_item_ids = []
    feed_item_user_ids = []
    query_results = ActiveRecord::Base.connection.execute(sql)
    query_results.each do |result|
      news_feed_record = {
        id: result[0].to_i,
        timestamp: result[4]
      }
      feed_item_ids << result[0].to_i
      feed_item_user_ids << result[5].to_i
      grouped_feed_types = result[2].split(',')
      grouped_timestamps = result[3].split(',')

      index = 0
      grouped_timestamps.map do |grouped_timestamp|
        if news_feed_record[:timestamp] == grouped_timestamp
          news_feed_record[:feed_type] = grouped_feed_types[index]
          break
        end
        index += 1
      end

      news_feed_records << news_feed_record
    end

    news_feed_record_hash = {}

    news_feed_records.map do |record|
      news_feed_record_hash[record[:id]] = record
    end

    circle_feed = get_feed_util_v2(news_feed_record_hash, feed_item_ids, feed_item_user_ids)

    circle_feed.insert([0, [(circle_feed.count - 1), 0].max].min, suggested_users_feed_v1) if suggested_users_feed_v1.present?
    render json: circle_feed, status: :ok
  end

  # this is old endpoint
  # GET /circle/123/feed-v1
  def get_feed_v1
    if @circle.political_party_level? && AppVersionSupport.is_trending_feed_default?
      return trending_feed
    end

    offset = params[:offset].to_i if params[:offset].present?

    count = params[:count].to_i if params[:count].present?

    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?
    fetch_data_using_sql = params[:is_sql].blank? ? false : params[:is_sql]

    fetch_data_using_sql, feed_items = get_feed_items(offset, count, loaded_feed_item_ids, fetch_data_using_sql)
    post_feed_json = {
      is_sql: fetch_data_using_sql,
      feed_items: feed_items
    }
    render json: post_feed_json, status: :ok
  end

  def get_feed_v2
    if @circle.political_party_level? && AppVersionSupport.is_trending_feed_default?
      return trending_feed
    end

    offset = params[:offset].to_i if params[:offset].present?

    count = params[:count].to_i if params[:count].present?
    page = (offset / count) + 1
    loaded_feed_item_ids = []
    loaded_feed_item_ids = params[:loaded_feed_item_ids] if params[:loaded_feed_item_ids].present?
    fetch_data_using_sql = params[:is_sql].blank? ? false : params[:is_sql]

    if page == 1 && loaded_feed_item_ids.exclude?(Constants.fan_request_feed_item_id)
      fan_poster_feed_item = get_circle_fan_poster_feed_item
      # add fan_poster_feed item for the circle owner if circle requirements met
      count -= 1 if fan_poster_feed_item.present?
    end

    fetch_data_using_sql, feed_items = get_feed_items(offset, count, loaded_feed_item_ids, fetch_data_using_sql)
    feed_items.insert([0, [(feed_items.count - 1), 0].max].min, fan_poster_feed_item) if fan_poster_feed_item.present?
    post_feed_json = {
      is_sql: fetch_data_using_sql,
      feed_items: feed_items
    }
    render json: post_feed_json, status: :ok
  end

  def get_feed_items(offset, count, loaded_feed_item_ids, fetch_data_using_sql)

    loaded_feed_item_ids_without_list_ids = []
    loaded_feed_item_ids_without_list_ids = loaded_feed_item_ids.grep(/\d+/, &:to_i) if loaded_feed_item_ids.present?

    page = (offset / count) + 1

    posters_feed = get_poster_feed(loaded_feed_item_ids, page)
    count -= 1 if posters_feed.present?

    suggested_users_feed_v1 = get_suggested_users_feed(loaded_feed_item_ids, page)
    count -= 1 if suggested_users_feed_v1.present?

    circle_ids = []

    circle_ids << @circle.id
    circle_ids += @circle.get_all_child_circle_ids

    news_feed_records = []
    feed_item_ids = []
    feed_item_user_ids = []

    # fetch data from es if fetch_data_using_sql is false
    unless fetch_data_using_sql
      query = get_circle_posts_es_query(circle_ids, loaded_feed_item_ids_without_list_ids, count)
      posts_es = ES_CLIENT.search index: EsUtil.get_new_posts_index_v2, body: query

      posts_es['hits']['hits'].each do |hit|
        post_es_item = hit['fields']
        feed_item_ids << post_es_item['id'].first.to_i
        loaded_feed_item_ids_without_list_ids << post_es_item['id'].first.to_i
        feed_item_user_ids << post_es_item['user_id'].first.to_i

        news_feed_record = {
          id: post_es_item['id'].first.to_i,
          timestamp: post_es_item['created_at'].first.to_time,
          feed_type: 'circle_posted'
        }

        news_feed_records << news_feed_record
      end
      count -= posts_es['hits']['hits'].count
    end

    # fetch data from sql if count is greater than 0 i.e we don't have posts in es to retrieve
    if fetch_data_using_sql || count > 0
      fetch_data_using_sql = true
      news_feed_records, feed_item_ids, feed_item_user_ids = get_post_data_from_sql(circle_ids,
                                                                                    loaded_feed_item_ids_without_list_ids,
                                                                                    count, news_feed_records, feed_item_ids,
                                                                                    feed_item_user_ids)
    end

    news_feed_record_hash = {}

    news_feed_records.map do |record|
      news_feed_record_hash[record[:id]] = record
    end

    circle_feed = get_feed_util_v2(news_feed_record_hash, feed_item_ids, feed_item_user_ids)

    circle_feed.insert([0, [(circle_feed.count - 1), 0].max].min, suggested_users_feed_v1) if suggested_users_feed_v1.present?

    circle_feed.insert([0, [(circle_feed.count - 1), 0].max].min, posters_feed) if posters_feed.present?
    
    [fetch_data_using_sql, circle_feed]
  end

  def get_circle_level_congrats_carousels(loaded_feed_item_ids, page)
    carousels = []
    if page == 1 && AppVersionSupport.creative_carousel_in_circle_feed_supported?
      case @circle.level.to_sym
      when :political_party
        feed_item_id = "party_mla_congrats_carousel_#{@circle.id}"
        if loaded_feed_item_ids.exclude?(feed_item_id)
          carousels << party_level_mla_congrats_carousels(user: @user, circle: @circle)
        end

        feed_item_id = "party_mp_congrats_carousel_#{@circle.id}"
        if loaded_feed_item_ids.exclude?(feed_item_id)
          carousels << party_level_mp_congrats_carousels(user: @user, circle: @circle)
        end
      when :political_leader
        feed_item_id = "#{@circle.level}_congrats_carousel"
        if loaded_feed_item_ids.exclude?(feed_item_id)
          carousels << leader_level_congrats_carousel(user: @user, circle: @circle)
        end
      end
    end

    carousels.compact
  end

  def get_circle_level_constituency_status_carousels(loaded_feed_item_ids, page)
    if page == 1 && AppVersionSupport.creative_carousel_in_circle_feed_supported?
      case @circle.circle_type.to_sym
      when :location
        feed_item_id = "#{@circle.level}_constituency_status_carousel"
        if loaded_feed_item_ids.exclude?(feed_item_id)
          location_level_constituency_status_carousels(user_id: @user.id, circle: @circle)
        end
      when :interest
        feed_item_id = "#{@circle.level}_constituency_status_carousel"
        if loaded_feed_item_ids.exclude?(feed_item_id)
          interest_level_constituency_status_carousels(user_id: @user.id, circle: @circle)
        end
      end
    end
  end

  def get_circle_posts_es_query(circle_ids, loaded_feed_item_ids_without_list_ids, count)
    if loaded_feed_item_ids_without_list_ids.present?
      # last_loaded_post_id_query helps to fetch the posts which are created before that post.
      last_loaded_post_id = loaded_feed_item_ids_without_list_ids.min
      {
        "fields": ["id", "user_id", "created_at"],
        "_source": false,
        "query": {
          "bool": {
            "must": [
              {
                "range": {
                  "created_at": {
                    "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                  }
                }
              },
              {
                "range": {
                  "id": {
                    "lt": last_loaded_post_id
                  }
                }
              },
              {
                "terms": {
                  "tagged_circles_ids": circle_ids
                }
              },
              {
                "term": {
                  "active": true
                }
              }
            ]
          }
        },
        "sort": [
          {
            "created_at": {
              "order": "desc"
            }
          }
        ],
        "size": count,
        "from": 0
      }
    else
      {
        "fields": ["id", "user_id", "created_at"],
        "_source": false,
        "query": {
          "bool": {
            "must": [
              {
                "range": {
                  "created_at": {
                    "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                  }
                }
              },
              {
                "terms": {
                  "tagged_circles_ids": circle_ids
                }
              },
              {
                "term": {
                  "active": true
                }
              }
            ]
          }
        },
        "sort": [
          {
            "created_at": {
              "order": "desc"
            }
          }
        ],
        "size": count,
        "from": 0
      }
    end
  end

  def get_feed_util_v2(news_feed_record_hash, feed_item_ids, feed_item_user_ids)
    circle_feed = []
    liked_post_ids = PostLike.where(user: @user,
                                    post_id: feed_item_ids,
                                    active: true)
                             .pluck(:post_id)
    post_likes_count_map = PostLike.where(post_id: feed_item_ids).group(:post_id).count

    post_comments_count_map = PostComment.where(post_id: feed_item_ids, active: true).group(:post_id).count

    post_opinions_count_map = Post.where(parent_post_id: feed_item_ids, active: true).group(:parent_post_id).count

    user_followers_from_post_users_map = UserFollower.where(user_id: feed_item_user_ids, follower_id: @user.id).group_by(&:user_id).transform_values(&:size)
    feed_item_user_ids.each do |user_id|
      if user_followers_from_post_users_map.has_key?(user_id)
        user_followers_from_post_users_map[user_id] = true
      else
        user_followers_from_post_users_map[user_id] = false
      end
    end

    news_feed_record_hash.each do |_index, news_feed_record|
      feed_type = news_feed_record[:feed_type]

      # if feed_type != 'story_thread'
      post = Post.get_feed_item(news_feed_record[:id], @user, @app_version, @circle)

      post[:feed_type] = feed_type
      post[:feed_type_timestamp] = Time.at(news_feed_record[:timestamp]).to_datetime
      post[:is_logged_in_user_forward] = false

      post[:feed_reason] = ''
      post_obj = Post.new
      post_obj.id = news_feed_record[:id]
      post_obj.user = post[:user]
      post_obj.circle = post[:circle]
      post[:user_circled] = false # post.is_circled(@user)
      post[:user_seen] = post_obj.is_seen(@user)
      post[:user].badge = post_obj.user.get_badge_role&.get_json
      post[:user].follows = post[:user][:id] == @user.id ? nil : user_followers_from_post_users_map[post[:user][:id]]
      post[:likes_count] = post_likes_count_map[post[:id]] || 0
      post[:comments_count] = post_comments_count_map[post[:id]] || 0
      post[:opinions_count] = post_opinions_count_map[post[:id]] || 0
      post[:whatsapp_count] = post_obj.whatsapp_count
      post[:preview_comments] = []
      post[:user_liked] = liked_post_ids.include? post[:id]
      post[:is_logged_in_user_post] = post[:user].id == @user.id

      # post[:dynmic_link] = nil
      # post[:hashid] = nil
      # post[:liked_users] = nil
      # post[:link_id] = nil
      # post[:parent_post_id] = nil
      circle_feed << post
    end
    circle_feed
  end

  # add_users method is removed

  def add_user
    if @circle.location_circle_type?
      return render json: { success: false, message: I18n.t('feature_unavailable') }, status: :bad_request
    end

    unless @user.circles.map { |c| c.id }.include?(@circle.id)
      @user.circles << @circle
      # If a prime account joins an interest circle, all the prime accounts join the circle
      if @circle.circle_type.to_sym == :interest
        user_inserts = []
        @user.user_groups.map do |ug|
          user_inserts += ug.user_group_members.map do |member|
            { circle_id: @circle.id, user_id: member.user.id, created_at: Time.now, updated_at: Time.now }
          end
        end
        UserCircle.insert_all(user_inserts) if user_inserts.count > 0
      end
    end

    ExcludedUserCircle.where(user_id: @user.id, circle_id: @circle.id, conversation_type: @circle.conversation_type).first&.destroy

    render json: { success: true, message: I18n.t('circle_user_join.joined') }, status: :ok # joined
  end

  def remove_user
    @user.circles.destroy(@circle)

    render json: {
      success: true,
      message: I18n.t('circle_user_unjoin.unjoined') # Unjoined!
    }, status: :ok
  end

  def add_as_admin
    if @circle.conversation_type.to_sym != :private_group
      return render json: { success: false, message: I18n.t('feature_unavailable') }, status: :bad_request
    end
    permissions = @user.get_all_circle_permissions(@circle.id).map(&:to_sym)

    if permissions.include?(:add_admin)
      participant_id = params[:participant_id]
      return render json: { success: false, message: I18n.t('errors.user_not_found') }, status: :not_found unless participant_id.present?
      @circle.add_user_as_admin(participant_id)
      render json: { success: true, message: I18n.t('circle_add_admin.added_as_admin_text') }, status: :ok
    else
      render json: { success: false, message: I18n.t('authorisation_error') }, status: :forbidden
    end
  end

  def remove_as_admin
    if @circle.conversation_type.to_sym != :private_group
      return render json: { success: false, message: I18n.t('feature_unavailable') }, status: :bad_request
    end
    permissions = @user.get_all_circle_permissions(@circle.id).map(&:to_sym)

    if permissions.include?(:remove_admin)
      participant_id = params[:participant_id]
      return render json: { success: false, message: I18n.t('errors.user_not_found') }, status: :not_found unless participant_id.present?
      @circle.remove_user_as_admin(participant_id)
      render json: { success: true, message: I18n.t('circle_remove_admin.removed_as_admin_text') }, status: :ok
    else
      render json: { success: false, message: I18n.t('authorisation_error') }, status: :forbidden
    end
  end

  def remove_user_as_sender_from_private_group
    if @circle.conversation_type.to_sym != :private_group
      return render json: { success: false, message: I18n.t('feature_unavailable') }, status: :bad_request
    end
    participant_id = params[:participant_id]
    participant = User.find_by(id: participant_id)
    return render json: { success: false, message: I18n.t('errors.user_not_found') }, status: :not_found unless participant.present?
    permissions = @user.get_all_circle_permissions(@circle.id).map(&:to_sym)

    if permissions.include?(:remove_sender)
      ExcludedUserCirclePermission.where(user_id: @user.id, circle_id: @circle.id, permission_identifier: :private_group_send_message).first_or_create
      render json: { success: true, message: I18n.t('remove_user_as_sender.removed_sender_text') }, status: :ok
    else
      render json: { success: false, message: I18n.t('authorisation_error') }, status: :forbidden
    end
  end

  def add_user_as_sender_to_private_group
    if @circle.conversation_type.to_sym != :private_group
      return render json: { success: false, message: I18n.t('feature_unavailable') }, status: :bad_request
    end
    participant_id = params[:participant_id]
    participant = User.find_by(id: participant_id)
    return render json: { success: false, message: I18n.t('errors.user_not_found') }, status: :not_found unless participant.present?
    permissions = @user.get_all_circle_permissions(@circle.id).map(&:to_sym)

    if permissions.include?(:add_sender)
      ExcludedUserCirclePermission.where(user_id: participant.id, circle_id: @circle.id, permission_identifier: :private_group_send_message).first&.destroy
      render json: { success: true, message: I18n.t('add_user_as_sender.added_as_sender_text') }, status: :ok
    else
      render json: { success: false, message: I18n.t('authorisation_error') }, status: :forbidden
    end
  end

  def all_districts
    render json: Circle.where(active: true, level: :district).where.not(id: 39808).order(name_en: :asc).all, status: :ok
  end

  def district_mandals
    render json: Circle.where(active: true, level: :mandal, parent_circle: @district).order(name_en: :asc).all, status: :ok
  end

  def mandal_villages
    render json: Circle.where(active: true, level: Constants.village_levels, parent_circle: @mandal).order(name_en: :asc).all,
           status: :ok
  end

  def suggested_seen
    @circle.mark_suggested_as_seen(@user.id)
    render json: { success: true }, status: :ok
  end

  def get_suggested_users_lists
    suggested_users_lists = @circle.suggested_users_lists(@user)
    render json: { success: true, suggested_users_lists: suggested_users_lists }, status: :ok
  end

  def upload_creative
    authorize @circle, :can_upload_creative?

    unless params[:photo].present?
      return render json: {
        message: I18n.t('upload_creative.no_photo_found')
      }, status: :bad_request
    end

    poster_creative = @circle.upload_creative(params[:photo], @user)
    if poster_creative.present?
      return render json: {
        creative_id: poster_creative.id,
        creative_kind: poster_creative.creative_kind,
        circle_id: @circle.id
      }, status: :ok
    end

    render json: {
      message: I18n.t('upload_creative.upload_failed')
    }, status: :bad_request
  rescue Pundit::NotAuthorizedError
    render json: {
      message: I18n.t('authorisation_error')
    }, status: :forbidden
  end

  def share_channel
    circle_has_channel = @circle.conversation_type.to_sym == :channel
    return render json: { success: false, message: 'circle has no enabled channel' }, status: :bad_request unless circle_has_channel

    is_official = @circle.official?
    link = "https://prajaapp.sng.link/A3x5b/slxz?_dl=praja%3A%2F%2Fbuzz.praja.app/circles/#{@circle.id}/channel&_ddl=praja%3A%2F%2Fbuzz.praja.app/circles/#{@circle.id}/channel&_fallback_redirect=https%3A%2F%2Fapi.thecircleapp.in%2Fcircles%2F#{@circle.id}%2Fchannel-preview&paffid=#{@user.id}"
    link = Singular.shorten_link(link)
    share_text = I18n.t('share_channel.text', circle_name: @circle[:name], official_name: is_official ? I18n.t('share_channel.official_channel') : I18n.t('share_channel.channel'),official_name2: is_official ? I18n.t('share_channel.official_team') : '' , link: link)
    render json: { success: true, share_text: share_text }, status: :ok
  end

  def get_preview_html
    app_icon_url = 'https://a-cdn.thecircleapp.in/512x512/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg'
    is_official = @circle.official?
    page_title = I18n.t('circle_preview.page_title', circle_name: @circle[:name], official_name: is_official ? I18n.t('circle_preview.official_circle') : '', members_count: @circle.get_members_count.to_s.gsub(/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/, "\\1,"))
    page_description = I18n.t('circle_preview.description')

    page_image = Metadatum.where(entity: @circle, key: 'circle_preview_image_url').first&.value
    # apply quality transform to use a smaller image
    page_image = Capture.apply_img_transform(page_image, width: 600, height: 315) if page_image.present?

    # fallback to circle photo if channel preview image is not generated yet
    page_image = @circle.photo&.compressed_url(size: 512, fit: false) if page_image.blank?
    # fallback to app icon if circle photo is not present
    page_image = app_icon_url if page_image.blank?

    page_url = "https://m.praja.buzz/circles/#{@circle.hashid}"
    page_type = 'profile'

    locals = { page_title:, page_description:, page_image:, page_url:, page_type: }

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/circles/preview.html.erb')),
      locals: locals
    )
    render html: html_content.html_safe
  end

  def get_channel_preview_html
    app_icon_url = 'https://a-cdn.thecircleapp.in/512x512/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg'

    is_official = @circle.official?
    page_title = I18n.t('circle_preview.page_title', circle_name: @circle[:name], official_name: is_official ? I18n.t('circle_preview.official_circle') : '', members_count: @circle.get_members_count.to_s.gsub(/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/, "\\1,"))
    page_description = I18n.t('circle_preview.description')

    page_image = Metadatum.where(entity: @circle, key: 'channel_preview_image_url').first&.value
    # apply quality transform to use a smaller image
    page_image = Capture.apply_img_transform(page_image, width: 600, height: 315) if page_image.present?

    # fallback to circle photo if channel preview image is not generated yet
    page_image = @circle.photo&.compressed_url(size: 512, fit: false) if page_image.blank?
    # fallback to app icon if circle photo is not present
    page_image = app_icon_url if page_image.blank?

    page_url = "https://m.praja.buzz/circles/#{@circle.hashid}/channel"
    page_type = 'profile'

    locals = { page_title:, page_description:, page_image:, page_url:, page_type: }

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/circles/preview.html.erb')),
      locals: locals
    )
    render html: html_content.html_safe
  end

  private

  def set_district
    @district = Circle.where(id: params[:district_id], active: true).first
  end

  def set_mandal
    @mandal = Circle.where(id: params[:mandal_id], active: true).first
  end

  def set_circle
    if !params[:id].nil? && !params[:id].empty? && params[:id].to_i.to_s == params[:id].to_s
      query = Circle.where(id: params[:id])

      query.where(active: true) if params[:id].to_i > 0

      @circle = query.first
    elsif !params[:circle_id].nil? && !params[:circle_id].empty? && params[:circle_id].to_i.to_s == params[:circle_id].to_s
      query = Circle.where(id: params[:circle_id])

      query.where(active: true) if params[:circle_id].to_i > 0

      @circle = query.first
    elsif !params[:id].nil? && !params[:id].empty?
      c = Circle.find_by_hashid(params[:id])
      @circle = c if !c.nil? && c.active?
    elsif !params[:circle_id].nil? && !params[:circle_id].empty?
      c = Circle.find_by_hashid(params[:circle_id])
      @circle = c if !c.nil? && c.active?
    elsif !params[:hashid].nil? && !params[:hashid].empty?
      c = Circle.find_by_hashid(params[:hashid])
      @circle = c if !c.nil? && c.active?
    end

    render json: { success: false, message: I18n.t('circle_group.not_found') }, status: :not_found if @circle.nil?
  end
end
