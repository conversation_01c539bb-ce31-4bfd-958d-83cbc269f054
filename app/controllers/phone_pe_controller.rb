class PhonePeController < ActionController::API

  before_action :validate_payload, except: [:redirect]

  def callback
    payload_str = Base64.strict_decode64(params[:response])
    PaymentUtils.update_transaction_status(payload_str)
  end

  def redirect
    PaymentUtils.get_transaction_status(params[:transactionId].to_s)
    redirect_to("praja://buzz.praja.app/payments/success", allow_other_host: true)
  end

  private

  def validate_payload
    response = params[:response].to_s
    x_verify = request.headers['X-VERIFY']
    is_valid = PaymentUtils.validate_payload(response, x_verify)
    unless is_valid
      raise 'Invalid callback'
    end
  end

  def index
    render json: { message: "Hello World" }
  end

  def create
    render json: { message: "Hello World" }
  end
end