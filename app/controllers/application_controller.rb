require 'open-uri'

class ApplicationController < ActionController::Base
  include Pundit::Authorization
  protect_from_forgery with: :exception, prepend: true
  before_action :set_paper_trail_whodunnit

  protected

  def user_for_paper_trail
    admin_user_signed_in? ? current_admin_user.try(:id) : 'unknown'
  end

  # Override the default authenticate_admin_user! method
  def authenticate_admin_user!
    if admin_user_signed_in?
      true
    else
      if request.get? && request.fullpath.start_with?('/admin') && !request.xhr?
        redirect_url = request.url
        redirect_to "#{new_admin_user_session_path}?redirect_url=#{redirect_url}"
      else
        redirect_to new_admin_user_session_path
      end
      false
    end
  end
end
