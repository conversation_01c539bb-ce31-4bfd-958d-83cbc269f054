# frozen_string_literal: true

class FlowwController < ExternalServiceController
  before_action :set_user
  before_action :set_event_params, only: [:send_to_mixpanel, :stage_updated]

  def send_to_mixpanel
    if params[:event_name].nil?
      return render json: { success: false, message: 'Event name not present' }, status: :bad_request
    end

    EventTracker.perform_async(@user.id, params[:event_name], @event_properties)

    render json: { success: true }, status: :ok
  end

  def rm_assigned
    rm_id = params[:rm_id]
    rm_user = AdminUser.find_by(floww_user_id: rm_id)
    if rm_user.nil?
      return render json: { error: "AdminUser with #{rm_id} not found" }, status: :not_found
    end

    @user.save_rm_user(rm_user_id: rm_user.id)

    render json: { success: true }, status: :ok
  end

  def layout_setup_callback
    Floww::AssignOe.perform_async(@user.id)
    
    render json: { success: true }, status: :ok
  end

  def stage_updated
    if params[:to_stage].nil?
      return render json: { success: false, message: 'To stage not present' }, status: :bad_request
    end

    @user.update_user_stage_in_premium_pitch(stage: params[:to_stage])

    EventTracker.perform_async(@user.id, "crm_new_stage_update_backend", @event_properties)

    render json: { success: true }, status: :ok
  end

  private

  def set_user
    user_id = params[:user_id]
    return render json: { success: false, message: 'User ID not present' }, status: :bad_request if user_id.nil?

    @user = nil
    @user = User.find_by(id: user_id)

    render json: { success: false, message: 'User not found' }, status: :not_found if @user.nil?
  end

  def set_event_params
    params[:floww].delete(:user_id)
    params[:floww].delete(:event_name)
    @event_properties = JSON.parse(params[:floww].to_json)
  end
end
