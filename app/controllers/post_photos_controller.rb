class PostPhotosController < ApiController
  before_action :set_post

  # GET /posts/1/photos
  def index
    @post_photos = PostPhoto.where(post: @post).all

    render json: @post_photos
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_post
    @post = Post.find(params[:post_id])
  end

  # Only allow a trusted parameter "white list" through.
  def post_photo_params
    params.require(:post_photos).permit(:photo_id, :post_id, :active)
  end
end
