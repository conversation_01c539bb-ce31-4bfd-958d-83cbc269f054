class PostLikesController < ApiController
  before_action :set_logged_in_user
  before_action :set_post, only: [:like, :unlike]

  def like
    begin
      @post.do_like(@user)

      render json: { success: true }, status: :ok
    rescue ActiveRecord::RecordNotUnique
      render json: { success: true }, status: :ok # Already trended!
    rescue
      render json: {
        success: false,
        message: "ట్రెండ్ చేయలేకపోయాము!"
      }, status: :unprocessable_entity
    end
  end

  def unlike
    begin
      PostLike.where(post: @post, user: @user).first&.destroy
      $redis.srem("user_like_for_post_#{@post.id}", @user.id.to_s)

      render json: { success: true }, status: :ok
    rescue
      render json: {
        success: false,
        message: "ఇంతక ముందే ట్రెండ్ తీసేసారు!" # Already untrended!
      }, status: :unprocessable_entity
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_post
    @post = Post.find(params[:post_id])
  end
end
