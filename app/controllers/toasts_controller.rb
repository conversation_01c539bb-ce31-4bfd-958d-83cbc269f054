class ToastsController < ApiController
  before_action :set_logged_in_user, :set_toast

  def close
    @user.mark_toast_as_close(@toast.id)
    render json: {success: true, message: "Marked as closed!"}, status: :ok
  end

  private

  def set_toast
    if !params[:id].nil? && !params[:id].empty? && params[:id].to_i.to_s == params[:id].to_s
      query = Toast.where(id: params[:id])

      query.where(active: true) if params[:id].to_i > 0

      @toast = query.first
    elsif !params[:toast_id].nil? && !params[:toast_id].empty? && params[:toast_id].to_i.to_s == params[:toast_id].to_s
      query = Toast.where(id: params[:toast_id])

      query.where(active: true) if params[:toast_id].to_i > 0

      @toast = query.first
    end

    render json: { success: false, message: 'గ్రూపు కనుగొనబడలేదు' }, status: :not_found if @toast.nil?
  end
end
