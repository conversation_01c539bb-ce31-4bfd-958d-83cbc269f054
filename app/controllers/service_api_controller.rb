class ServiceApiController < ActionController::API
  before_action :set_service

  ALLOWED_SERVICES = %w[media dm garuda]

  def set_service
    @service = nil
    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')
      if token.present?
        payload = JsonWebToken.decode(token)

        # Extract service name from payload
        service_name = nil
        service_name = payload["service"]&.strip&.downcase if payload.present?

        # Verify if there is a valid service name
        @service = service_name if service_name.present? && ALLOWED_SERVICES.include?(service_name)
      end
    end

    if @service.nil?
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end
end
