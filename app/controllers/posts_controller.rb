class PostsController < ApiController
  include Pundit::Authorization
  include PostsConcern
  include RelevantFeedItemConcern
  before_action :set_logged_in_user, except: [:get_public_post]
  before_action :set_post, except: [:mark_as_seen_bulk, :create, :create_v2]

  # GET /posts/1
  def show
    post = Post.get_feed_item(params[:id], @user, @app_version)

    post_obj = Post.new
    post_obj.id = post[:id]

    post[:user].follows = post[:user][:id] == @user.id ? nil : UserFollower.where(user_id: post[:user][:id], follower: @user).exists?
    post[:likes_count] = post_obj.likes_count
    post[:comments_count] = post_obj.comments_count
    post[:opinions_count] = post_obj.opinions_count
    post[:whatsapp_count] = post_obj.whatsapp_count
    post[:preview_comments] = []
    post[:user_liked] = post_obj.is_liked(@user)

    render json: post, status: :ok
  end

  # POST /posts
  def create
    @post = set_post_data
    @post.app_version = @app_version

    # associate photos
    if !params[:photos].nil? && !params[:photos].empty?
      has_poster_photo = false
      params[:photos].each do |data|
        @post.post_photos.build(photo: Photo.new(blob_data: data[1], user: @user, service: :aws))

        if !has_poster_photo && File.basename(data[1].original_filename, ".*") == "poster"
          has_poster_photo = true
        end
      end

      @post.metadatum.build(key: Constants.get_is_poster_photo_key_for_post, value: "true") if has_poster_photo
    end

    # associate videos
    if !params[:videos].nil? && !params[:videos].empty?
      index = 0
      params[:videos].each do |data|
        @post.post_videos.build(video: Video.new(blob_data: data[1], thumbnail: params[:video_thumbnails][index.to_s], user: @user, service: :aws))
        index += 1
      end
    end

    begin
      @post.save!
      if @app_version >= Gem::Version.new('0.7.3')
        render json: { success: true, post_id: @post.id }, status: :ok
      else
        render json: @post, status: :ok
      end
    rescue => e
      render json: {
        success: false,
        message: @post.errors.messages.first&.second&.first || e&.message || I18n.t('posts.post_create_failed_message'),
      }, status: :unprocessable_entity
    end
  end

  def create_v2
    begin
      @post = create_post_with_data
      @post.app_version = @app_version
      @post.save!
      render json: { success: true, post_id: @post.id }, status: :ok
    rescue => e
      render json: {
        success: false,
        message: @post.errors.messages.first&.second&.first || e&.message || I18n.t('posts.post_create_failed_message'),
      }, status: :unprocessable_entity
    end
  end

  def get_post_preview
    post_hash = Post.get_feed_item(@post.id, @user, @app_version)
    user = post_hash[:user]
    post_preview_hash = {
      id: post_hash[:id],
      content: post_hash[:content],
      link: post_hash[:link],
      user: user.get_user_response_hash_for_dm(@user),
      parent_post: post_hash[:parent_post],
      photos: post_hash[:photos],
      videos: post_hash[:videos]
    }
    render json: post_preview_hash, status: :ok
  end

  # PUT /posts/1/delete
  def delete
    delete_post

    render json: {
      success: true,
      message: I18n.t('posts.post_deleted') # Deleted!
    }, status: :ok
  end

  # PUT /posts/1/report
  def report
    if params[:reason].present? && params[:reason]['name'].present?
      Report.create!(reference: @post, user: @user, report_reason: params[:reason]['name'])

      render json: {
        success: true,
        message: I18n.t('posts.post_reported') # Reported!!
      }, status: :ok
    else
      render json: { success: false, message: I18n.t('mention_reason') }, status: :bad_request
    end
  end

  def get_public_post
    render json: @post.to_json(include: [:user, :photos, :comments]), status: :ok
  end

  # POST /posts/1/remove-tag
  def remove_circle_tag
    begin
      circle = Circle.find(params[:circle_id])
      authorize circle, :is_user_can_remove_tag?

      @post.circles.destroy(circle)

      post = Post.get_feed_item(@post.id, @user, @app_version)
      post_obj = Post.new
      post_obj.id = @post.id
      post[:user].follows = post[:user][:id] == @user.id ? nil : UserFollower.where(user_id: post[:user][:id], follower: @user).exists?
      post[:likes_count] = post_obj.likes_count
      post[:comments_count] = post_obj.comments_count
      post[:opinions_count] = post_obj.opinions_count
      post[:whatsapp_count] = post_obj.whatsapp_count
      post[:preview_comments] = []
      post[:user_liked] = post_obj.is_liked(@user)

      render json: post, status: :ok
    rescue
      render json: {
        success: false,
        message: I18n.t('circle_tag.denied_message')
      }, status: :forbidden
    end
  end

  def share_to_whatsapp
    $redis.hincrby("post_whatsapp_shares_#{@post.id}", @user.id.to_s, 1)
    IndexPostNewV2.perform_async(@post.id)
    render json: { success: true }, status: :ok
  end

  def share
    $redis.hincrby("post_whatsapp_shares_#{@post.id}", @user.id.to_s, 1)
    IndexPostNewV2.perform_async(@post.id)
    share_text = get_share_text_based_on_version
    render json: { success: true, share_text: share_text }, status: :ok
  end

  def get_post_opinions
    count = 10
    count = params[:count].to_i if params[:count].present?

    last_opinion_id = 0
    last_opinion_id = params[:last_opinion_id].to_i if params[:last_opinion_id].present?

    posts_sql = Post.where(parent_post: @post, active: true)
    posts_sql = posts_sql.where("id < ?", last_opinion_id) if last_opinion_id > 0

    posts = posts_sql
              .order(id: :desc)
              .limit(count)
              .map do |p|
      post = Post.get_feed_item(p.id, @user, @app_version)

      post[:user][:follows] = p.user.id == @user.id ? nil : UserFollower.where(user: p.user, follower: @user, active: true).exists?
      post[:user_liked] = p.is_liked(@user)
      post[:likes_count] = p.likes_count
      post[:comments_count] = p.comments_count
      post[:opinions_count] = p.opinions_count
      post[:whatsapp_count] = p.whatsapp_count
      post[:preview_comments] = []

      post
    end

    render json: posts, status: :ok
  end

  def liked_users
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?
    liked_users = if @app_version > Gem::Version.new('1.16.1')
                    PostLike.where(post: @post).order(id: :desc).offset(offset).limit(count).map do |like|
                      like.user.phone = like.user.generate_random_phone
                      like.user.follows = like.user.id == @user.id ? nil : UserFollower.where(user: like.user, follower: @user, active: true).exists?
                      like.user.loggedInUser = like.user == @user
                      like.user.photo&.compressed_url!(size: 200)
                      like.user
                    end
                  else
                    @post.likes.map do |like|
                      like.user.follows = like.user.id == @user.id ? nil : UserFollower.where(user: like.user, follower: @user, active: true).exists?
                      like.user.loggedInUser = like.user == @user
                      like.user
                    end
                  end

    render json: liked_users, status: :ok
  end

  def mark_as_seen
    @post.queue_mark_as_seen(@user.id)
    # unless $redis.hexists("post_views_#{@post.id}", @user.id.to_s)
    #   $redis.hset("post_views_#{@post.id}", @user.id.to_s, Time.now.to_i.to_s)
    # end
    render json: { success: true }, status: :ok
  end

  def mark_as_seen_bulk
    unless params[:post_ids].present?
      render json: { success: false }, status: :bad_request
      return
    end
    logger.debug "Marking as seen for user: #{@user.id} and posts: #{params[:post_ids]}"

    params[:post_ids].keys.each_slice(50) do |post_ids|
      post_with_timestamps = post_ids.map { |post_id| [post_id, params[:post_ids][post_id]] }.to_h
      logger.debug "Marking as seen for user: #{@user.id} and posts: #{post_with_timestamps}"
      QueueUserPostViews.perform_async(@user.id, post_with_timestamps)
    end
    render json: { success: true }, status: :ok
  end

  def get_share_text_based_on_version
    if @app_version >= Gem::Version.new('1.11.3')
      get_share_text_v2
    else
      get_share_text_v1
    end
  end

  def get_share_text
    share_text = get_share_text_based_on_version
    render json: { success: true, text: share_text }, status: :ok
  end

  def get_share_text_v1
    link = @user.get_dynamic_link

    post_link = "https://m.praja.buzz/posts/" + @post.hashid

    post_content = @post.content.present? ? @post.content.truncate(47, separator: " ") : ""

    if @user.get_badge_role.present?
      user_role = @user.get_badge_role
      I18n.t('get_share_text.share_text_v1.badge_user_text', primary_circle: user_role.get_primary_circle.name, role_name: user_role.get_role_name, post_link: post_link)
    else
      I18n.t('get_share_text.share_text_v1.normal_user_text', post_content: post_content, post_link: post_link, link: link)
    end
  end

  def get_share_text_v2
    post_long_link = "https://prajaapp.sng.link/A3x5b/oe88?" +
      "_dl=praja%3A%2F%2Fposts/#{@post.id}&" +
      "_android_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
      "_ios_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
      "_samsung_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
      "paffid=#{@user.id}"

    post_link = Singular.shorten_link(post_long_link)

    if @user.get_badge_role.present?
      user_role = @user.get_badge_role
      I18n.t('get_share_text.share_text_v1.badge_user_text', primary_circle: user_role.get_primary_circle.name, role_name: user_role.get_role_name, post_link: post_link)
    else
      I18n.t('get_share_text.share_text_v2', post_link: post_link)
    end
  end

  def update_comments_config
    comments_type = params[:comments_type].to_i
    @post.comments_type = comments_type
    @post.save!

    render json: { success: true, message: "Comments privacy updated" }, status: :ok
  end

  def notification_post_feed
    # NOTE: [DEPRECATED] Use notification_post_feed_v2 instead, after 2401.22.xx version"
    render json: get_notification_post_feed, status: :ok
  end

  def notification_post_feed_v2
    feed_items = get_notification_post_feed

    relevant_feed_items = relevant_my_feed_items_for_user(loaded_feed_item_ids: [])
    feed_items.insert(1, *relevant_feed_items) if relevant_feed_items.present?

    render json: feed_items, status: :ok
  end
end
