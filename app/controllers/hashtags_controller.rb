class HashtagsController < ApiController
  before_action :set_logged_in_user, except: [:get_public_hashtag]
  before_action :set_hashtag, except: [:search]

  def show
    render json: {
      id: @hashtag.id,
      hashid: @hashtag.hashid,
      name: @hashtag.name,
      likes_count: @hashtag.get_likes_count,
      opinions_count: @hashtag.get_opinions_count,
      whatsapp_count: @hashtag.whatsapp_count,
      created_at: @hashtag.created_at,
      user_liked: @hashtag.is_liked(@user),
    }, status: :ok
  end

  def get_public_hashtag
    opinions = Post
                 .joins("INNER JOIN post_hashtags ph ON ph.post_id = posts.id")
                 .where("ph.hashtag_id = ? AND ph.active = 1 AND posts.active = 1", @hashtag.id)
                 .group("posts.id")
                 .order("posts.created_at DESC")
                 .limit(3)

    if @hashtag.dynamic_link.nil? || @hashtag.dynamic_link.empty?
      parameters = {
        link: "https://m.praja.buzz/hashtags/" + @hashtag.hashid,
        android_info: {
          android_package_name: "buzz.praja.app",
        },
        ios_info: {
        }
      }

      result = $firebase_dl_client.shorten_parameters(parameters)
      if !result[:link].nil? && result[:link] != ""
        @hashtag.dynamic_link = result[:link]
        @hashtag.save
      else
        @hashtag.dynamic_link = ''
      end
    end

    render json: {
      id: @hashtag.id,
      hashid: @hashtag.hashid,
      name: @hashtag.name,
      likes_count: @hashtag.get_likes_count,
      opinions_count: @hashtag.get_opinions_count,
      whatsapp_count: @hashtag.whatsapp_count,
      created_at: @hashtag.created_at,
      dynamic_link: @hashtag.dynamic_link,
      opinions: opinions,
    }, status: :ok
  end

  def search
    search_v2
  end

  # removed search v1

  def search_v2
    hashtags = []

    if params[:name].present? && params[:name] != ""
      is_new = true
      name = params[:name].to_s

      # begin
      search_es = ES_CLIENT.search index: EsUtil.get_hashtags_index, body: {
        "fields": ["id", "name"],
        "_source": false,
        "query": {
          "bool": {
            "should": [
              {
                "regexp": {
                  "name": {
                    "value": name + ".*",
                    "flags": "ALL",
                    "case_insensitive": true,
                    "max_determinized_states": 10000,
                    "rewrite": "scoring_boolean"
                  }
                }
              },
              {
                "match": {
                  "name": name
                }
              }
            ],
            "minimum_should_match": 1
          }
        },
        "size": 5
      }

      hashtags = []
      search_es['hits']['hits'].each do |hit|
        if hit.present? && hit['fields'].present?
          search_feed = hit['fields']
          hashtag_name = search_feed['name'].first

          is_new = false if hashtag_name.casecmp(name) == 0

          hashtag_obj = {
            "id": search_feed['id'].first.to_i,
            "name": hashtag_name
          }

          hashtags << hashtag_obj
        end
      end
      hashtags.compact!
      # rescue => e
      #   render json: { success: false, error: "Server is busy. Please try again." }
      #   return
      # end
      hashtags.unshift({ "id": 0, "name": params[:name] }) if is_new
    end

    render json: hashtags, status: :ok
  end

  # GET /hashtags/1/posts
  def get_posts
    get_posts_v2
  end

  def get_posts_v2
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?

    loaded_post_ids = []
    loaded_post_ids = params[:loaded_post_ids] if params[:loaded_post_ids].present?

    is_sql = params[:is_sql].present? ? params[:is_sql] : false

    posts_feed = []
    unless is_sql
      query = get_hashtag_es_query(loaded_post_ids, count)

      posts_es = ES_CLIENT.search index: EsUtil.get_new_posts_index_v2, body: query

      post_ids = []
      post_user_ids = []
      posts_es['hits']['hits'].each do |hit|
        post_es_item = hit['fields']
        post_ids << post_es_item['id'].first
        post_user_ids << post_es_item['user_id'].first
      end

      followed_user_ids = UserFollower.where(user_id: post_user_ids,
                                             follower_id: @user.id,
                                             active: true)
                                      .pluck(:user_id)

      liked_post_ids = PostLike.get_liked_post_ids(post_ids, @user.id)

      posts_feed = posts_es['hits']['hits'].map do |hit|
        post_es_item = hit['fields']

        post_obj = Post.new
        post_obj.id = post_es_item['id'].first
        post = Post.get_feed_item(post_es_item['id'].first, @user, @app_version)

        post[:user].follows = followed_user_ids.include? post_es_item['user_id'].first
        post[:likes_count] = post_es_item['likes_count'].first
        post[:comments_count] = post_es_item['comments_count'].first
        post[:opinions_count] = post_es_item['opinions_count'].first
        post[:whatsapp_count] = post_es_item['whatsapp_count'].first
        post[:preview_comments] = []
        post[:user_liked] = liked_post_ids.include? post_es_item['id'].first.to_i

        post
      end
    end

    posts_feed_es_count = posts_feed.count
    if is_sql || posts_feed_es_count < count
      posts_feed += get_hashtag_posts_from_db(count - posts_feed_es_count, loaded_post_ids + posts_feed.map { |e| e[:id] })
      is_sql = true
    end

    posts_feed_json = if AppVersionSupport.is_new_response_in_hashtag_feed_enabled?
                        {
                          "is_sql": is_sql,
                          "posts": posts_feed,
                        }
                      else
                        posts_feed
                      end

    render json: posts_feed_json, status: :ok
  end

  def get_hashtag_posts_from_db(count, loaded_post_ids)
    post_ids = Post.joins(:post_hashtags)
                   .where(post_hashtags: { hashtag_id: @hashtag.id, active: true }, active: true)
                   .where.not(id: loaded_post_ids)
                   .order(id: :desc)
                   .limit(count)
                   .pluck(:id)

    posts_feed = []
    post_ids.each do |post_id|
      post_obj = Post.new
      post_obj.id = post_id

      post = Post.get_feed_item(post_id, @user, @app_version)

      post[:likes_count] = post_obj.likes_count
      post[:comments_count] = post_obj.comments_count
      post[:opinions_count] = post_obj.opinions_count
      post[:whatsapp_count] = post_obj.whatsapp_count
      post[:preview_comments] = []

      posts_feed << post
    end

    posts_feed
  end

  def get_hashtag_es_query(loaded_post_ids, count)
    {
      "fields": ["id", "user_id", "likes_count", "comments_count", "opinions_count", "whatsapp_count"],
      "_source": false,
      "query": {
        "function_score": {
          "query": {
            "bool": {
              "must_not": {
                "terms": { "id": loaded_post_ids }
              },
              "filter": [{
                           "term": { "active": true }
                         },
                         {
                           "terms": {
                             "hashtag_ids": [@hashtag.id]
                           }
                         },
                         {
                           "range": {
                             "created_at": {
                               "gte": 'now-3d/m'
                             }
                           }
                         }]
            }
          },
          "script_score": {
            "script": {
              "lang": "painless",
              "source": "" "
                              double currentTime = new Date().getTime();
                              float weightedTrends = 0;
                              double score = 0;

                              for (trends_timestamp in params._source.trends_timestamps) {
                                weightedTrends += (1 - Math.min(1, (currentTime - trends_timestamp) / (36*60*60*1000)));
                              }
                              score += Math.max(0, weightedTrends);

                              if (params._source.has_poster_photo == true) { score *= 0.0333; }

                              return score;
                          " ""
            }
          },
          "boost_mode": "replace"
        }
      },
      "sort": [
        { "_score": { "order": "desc" } },
        { "created_at": { "order": "desc" } }
      ],
      "collapse": {
        "field": "user_id"
      },
      "size": count,
      "from": 0
    }
  end

  def like
    begin
      HashtagLike.create!(hashtag: @hashtag, user: @user)

      user_inserts = []
      @user.user_groups.map do |ug|
        associate_users = ug.user_group_members.sample(rand(4..8))

        user_inserts += associate_users.map do |member|
          { hashtag_id: @hashtag.id, user_id: member.user.id, created_at: Time.now, updated_at: Time.now }
        end
      end

      if user_inserts.count.positive?
        HashtagLike.insert_all(user_inserts)
        $redis.hincrby(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, @hashtag.id, user_inserts.count)
      end

      IndexHashtag.perform_async(@hashtag.id)

      render json: { success: true }, status: :ok
    rescue
      render json: {
        success: false,
        message: "ఇంతక ముందే ట్రెండ్ చేసారు!" # Already trended!
      }, status: :unprocessable_entity
    end
  end

  def unlike
    begin
      HashtagLike.where(hashtag: @hashtag, user: @user).first.destroy
      IndexHashtag.perform_async(@hashtag.id)

      render json: { success: true }, status: :ok
    rescue
      render json: {
        success: false,
        message: "ఇంతక ముందే ట్రెండ్ తీసేసారు!" # Already untrended!
      }, status: :unprocessable_entity
    end
  end

  def liked_users
    offset = 0
    offset = params[:offset].to_i if params[:offset].present?

    count = 10
    count = params[:count].to_i if params[:count].present?
    liked_users = if @app_version > Gem::Version.new('1.16.1')
                    HashtagLike.where(hashtag: @hashtag).offset(offset).limit(count).map do |like|
                      like.user.follows = like.user.id == @user.id ? nil : UserFollower.where(user: like.user, follower: @user, active: true).exists?
                      like.user.loggedInUser = like.user == @user
                      like.user
                    end
                  else
                    @hashtag.likes.map do |like|
                      like.user.follows = like.user.id == @user.id ? nil : UserFollower.where(user: like.user, follower: @user, active: true).exists?
                      like.user.loggedInUser = like.user == @user
                      like.user
                    end
                  end

    render json: liked_users, status: :ok
  end

  def share_to_whatsapp
    $redis.hincrby("hashtag_whatsapp_shares_#{@hashtag.id}", @user.id.to_s, 1)
    render json: { success: true }, status: :ok
  end

  def get_share_text
    if @app_version >= Gem::Version.new('1.11.3')
      get_share_text_v2
    else
      get_share_text_v1
    end
  end

  def get_share_text_v1
    link = @user.get_dynamic_link

    hashtag_url = "https://m.praja.buzz/hashtags/" + @hashtag.hashid

    msg = "Praja యాప్ లో ఇప్పుడు #" +
      @hashtag.name +
      " ట్రెండ్ అవుతుంది. " +
      "\n" +
      hashtag_url +
      "\n" +
      "\n" +
      "#" +
      @hashtag.name +
      " టాపిక్ మీద మరిన్ని పోస్టులకు ఇప్పుడే డౌన్లోడ్ చేయండి, Praja యాప్." +
      "\n" +
      "#{link}"

    render json: { success: true, text: msg }, status: :ok
  end

  def get_share_text_v2
    link = "https://prajaapp.sng.link/A3x5b/44rs?_dl=praja%3A%2F%2Fhashtags/#{@hashtag.id}&_ddl=praja%3A%2F%2Fhashtags/#{@hashtag.id}&paffid=#{@user.id}"
    link = Singular.shorten_link(link)

    msg = "*Praja* యాప్ లో ఇప్పుడు ##{@hashtag.name} ట్రెండ్ అవుతుంది.\n##{@hashtag.name} టాపిక్ మీద మరిన్ని పోస్టుల కోసం ఇప్పుడే ఉచితంగా *Praja* యాప్ డౌన్లోడ్ చేసుకోండి.\n" +
      "👇👇👇\n" +
      "#{link}"

    render json: { success: true, text: msg }, status: :ok
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_hashtag
    if !params[:id].nil? && !params[:id].empty? && params[:id].to_i.to_s == params[:id].to_s
      @hashtag = Hashtag.where(id: params[:id], active: true).first
    elsif !params[:hashtag_id].nil? && !params[:hashtag_id].empty? && params[:hashtag_id].to_i.to_s == params[:hashtag_id].to_s
      @hashtag = Hashtag.where(id: params[:hashtag_id], active: true).first
    elsif !params[:id].nil? && !params[:id].empty?
      c = Hashtag.find_by_hashid(params[:id])
      if !c.nil? && c.active?
        @hashtag = c
      end
    elsif !params[:hashtag_id].nil? && !params[:hashtag_id].empty?
      c = Hashtag.find_by_hashid(params[:hashtag_id])
      if !c.nil? && c.active?
        @hashtag = c
      end
    elsif !params[:hashid].nil? && !params[:hashid].empty?
      c = Hashtag.find_by_hashid(params[:hashid])
      if !c.nil? && c.active?
        @hashtag = c
      end
    end

    if @hashtag.nil?
      render json: { message: "హాష్ ట్యాగ్ కనుగొనబడలేదు" }, status: :not_found
    end
  end
end
