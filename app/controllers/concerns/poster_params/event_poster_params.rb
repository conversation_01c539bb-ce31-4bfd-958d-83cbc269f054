# frozen_string_literal: true

module PosterParams
  # {id: $id}
  class EventPosterParams < BasePosterParams
    attr_reader :id

    def initialize(params)
      @id = params[:id].to_i
      @circle_id = params[:circle_id].to_i if params[:circle_id].present?
      super({ id: @id, circle_id: @circle_id })
    end

    def event
      Event.find_by(id: @id)
    end

    def circle_ids
      if @circle_id.present?
        [@circle_id]
      else
        self.event.event_circles.pluck(:circle_id)
      end
    end

    def get_creatives(include_paid: false, include_expired: true, include_inactive: true)
      PosterCreative.of_event(event_id: @id, include_paid:, include_expired:, include_inactive:)
    end
  end
end
