# frozen_string_literal: true

module PosterParams
  # {category_kind: $category_kind, circle_id: $circle_id}
  class KindInCirclePosterParams < BasePosterParams
    attr_reader :category_kind, :circle_id

    def initialize(params)
      @category_kind = params[:category_kind]
      @circle_id = params[:circle_id].to_i
      super({ category_kind: @category_kind, circle_id: @circle_id })
    end

    def get_creatives(include_paid: false, include_expired: true, include_inactive: true)
      PosterCreative.of_kind_in_circle(
        kind: @category_kind,
        circle_id: @circle_id,
        include_paid: include_paid,
        include_expired: include_expired,
        include_inactive: include_inactive
      )
    end

    def circle_ids
      [@circle_id]
    end
  end
end
