# frozen_string_literal: true

module PosterParams
  # {creative_id: $creative_id}
  class CreativePosterParams < BasePosterParams
    attr_reader :creative_id

    def initialize(params)
      @creative_id = params[:creative_id].to_i
      @category_kind = params[:category_kind]
      @circle_id = params[:circle_id].to_i if params[:circle_id].present?
      super({ creative_id: @creative_id, circle_id: @circle_id, category_kind: @category_kind })
    end

    def event
      creative = PosterCreative.find_by(id: @creative_id)
      creative.event if creative.present?
    end

    def creative
      PosterCreative.find_by_id(@creative_id)
    end

    def circle_ids
      if @circle_id.present?
        [@circle_id]
      elsif self.event.present?
        self.event.event_circles.pluck(:circle_id)
      elsif self.creative.present?
        self.creative.poster_creative_circles.pluck(:circle_id)
      else
        []
      end
    end

    def get_creatives(include_paid: false, include_expired: true, include_inactive: true)
      PosterCreative.get_creatives(creative_id: @creative_id, include_paid:, include_expired:, include_inactive:)
    end

    def share_text(user_id)
      creative = self.creative
      return nil unless creative.present? && creative.active? && @circle_id.present?

      circle = Circle.find_by_id(@circle_id) if @circle_id.present?

      share_text = I18n.t('poster_share_text.default')
      if circle.present?
        is_official = circle.official?
        if circle.political_leader_level? && is_official
          share_text = I18n.t('poster_share_text.political_leader', circle_name: circle.name, event_name: event.present? ? event.name : I18n.t('poster_share_text.special'))
        elsif circle.political_party_level? && is_official
          share_text = I18n.t('poster_share_text.political_party', circle_name: circle.name, event_name: event.present? ? event.name : I18n.t('poster_share_text.special'))
        else
          share_text = I18n.t('poster_share_text.circle', circle_name: circle.name, event_name: event.present? ? event.name : I18n.t('poster_share_text.special'))
        end
      end

      share_text += "\n\n#{I18n.t('poster_share_text.get_poster')}"

      deeplink_uri = URI.parse('praja://buzz.praja.app/posters/layout')
      deeplink_uri.query = URI.encode_www_form(@params)

      fallback_web_url = "https://api.thecircleapp.in/poster_creatives/#{@creative_id}/preview"

      link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/lnpr')
      link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s,
                                             _fallback_redirect: fallback_web_url, paffid: user_id })

      link = link_uri.to_s
      link = Singular.shorten_link(link)

      share_text += "\n#{link}"

      share_text
    end
  end
end
