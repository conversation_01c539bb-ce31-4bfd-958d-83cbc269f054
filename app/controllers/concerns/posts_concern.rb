module PostsConcern
  extend ActiveSupport::Concern

  included do
    def get_pre_selected_circles
      pre_selected_circles = []
      tagging_circle_ids = []
      circle_id = params[:circle_id].to_i if params[:circle_id].present?
      user_role = @user.get_badge_role
      badge_circle_id = user_role&.get_badge_user_affiliated_party_circle_id
      badge_location_circle_id = user_role&.get_badge_user_affiliated_location_circle_id
      user_owned_circle_ids = UserCirclePermissionGroup.where(user_id: @user.id, permission_group_id: 3).pluck(:circle_id)

      tagging_circle_ids << circle_id
      tagging_circle_ids << badge_circle_id if badge_circle_id != 0
      tagging_circle_ids << badge_location_circle_id if badge_location_circle_id != 0
      tagging_circle_ids += user_owned_circle_ids unless user_owned_circle_ids.size > 1
      tagging_circle_ids = tagging_circle_ids.uniq.compact

      unless tagging_circle_ids.blank?
        tagging_circle_ids.each do |circle_id|
          circle = Circle.find(circle_id.to_i)
          next if circle.political_party_level? && pre_selected_circles.any? do |c|
            c[:level] == 'political_party'
          end

          # if circle level is municipality or corporation send circle level as village other than that send circle level as circle level
          pre_selected_circles << {
            id: circle.id,
            name: circle.name,
            name_en: circle.name_en,
            photo_url: circle.photo&.url,
            members_count: circle.get_members_count,
            level: if circle.is_village_level?
                     Circle.levels.key(1)
                   else
                     %w[private village mandal district public_figure topic political_party political_leader mp_constituency mla_constituency state language public].include?(circle.level) ? circle.level : :private
                   end,
          }
        end
      end
      pre_selected_circles
    end

    def create_post_with_data
      set_post_data
      set_post_photos_data
      set_post_videos_data
      set_attachments_data

      @post
    end

    def set_post_data
      # build post object with primary parameters
      @post = Post.new(content: params[:content])
      @post.user = @user

      if params[:comments_type].present?
        @post.comments_type = params[:comments_type].to_i
        @post.party_id_on_comments_type = @user.affiliated_party_circle_id
      end

      # associate parent post
      if params[:parent_post_id].present? && params[:parent_post_id].to_i > 0 && Post.where(id: params[:parent_post_id]).exists?
        @post.parent_post_id = params[:parent_post_id].to_i
      end

      # associate tagged circle ids
      circle_ids = if params[:circle_id].present?
                     [params[:circle_id].to_i]
                   elsif params[:circle_ids].present?
                     params[:circle_ids].map(&:to_i)
                   else
                     []
                   end
      circle_ids.each do |circle_id|
        @post.post_circles.build(post: @post, circle_id: circle_id) if circle_id.to_i != 0
      end

      # associate hashtags
      parsed_hashtags = []
      unless params[:content].blank?
        parsed_hashtags = params[:content].scan(/(?:\s|^)(?:#(?!(?:\d+|\S+?_|_\S*?)(?:\s|$)))(\S+)(?=\s|$)/i)
        parsed_hashtags = parsed_hashtags.map { |h| h[0] }
      end

      received_hashtags = []
      if params[:hashtags].present?
        received_hashtags = params[:hashtags].values.map { |h| h[:tag].strip[1..-1] }
      end

      all_hashtags = parsed_hashtags + received_hashtags
      all_hashtags = all_hashtags.uniq { |x| x }

      all_hashtags.each do |hashtag|
        next if @post.post_hashtags.map { |hh| hh.tag }.include?("#" + hashtag)

        h = Hashtag.where(identifier: hashtag.downcase, active: true).first
        h = Hashtag.new(name: hashtag, identifier: hashtag.downcase) if h.nil?

        begin
          @post.post_hashtags.build(hashtag: h, tag: '#' + hashtag)
        rescue
          next
        end
      end

      @post
    end

    def set_post_photos_data
      # create post_photos
      if params[:photos].present?
        has_poster_photo = false
        params[:photos].each do |photo_data|
          @post.post_photos.build(photo: Photo.new(ms_data: photo_data, user: @user, service: photo_data[:service]))

          unless has_poster_photo
            photo_file_name = File.basename(photo_data[:original_file_name])
            # regex supports the following string
            # poster.png , poster(1).png , poster(31).png
            # premium_poster.png , premium_poster_1234324235324.png
            if (/^poster\(?\d*\)?.png/.match?(photo_file_name) || /^premium_poster_?\d*.png/.match?(photo_file_name))
              has_poster_photo = true
            end
          end
        end

        @post.metadatum.build(key: Constants.get_is_poster_photo_key_for_post, value: "true") if has_poster_photo
      end
    end

    def set_post_videos_data
      if params[:videos].present?
        params[:videos].each do |video_data|
          @post.post_videos.build(video: Video.new(ms_data: video_data,
                                                   user: @user,
                                                   service: video_data[:service],
                                                   bitrate: video_data[:bitrate_mbps],
                                                   duration: video_data[:duration_secs],
                                                   width: video_data[:width],
                                                   height: video_data[:height],
                                                   mode: video_data[:mode]))
        end
      end
    end

    def set_attachments_data
      if params[:attachments].present?
        params[:attachments].each do |attachment_data|
          attachment_type = attachment_data[:type]
          attachment_id = attachment_data[:id]
          if attachment_type == "creative"
            poster_creative = PosterCreative.find_by_id(attachment_id)
            # raise honey_badger_error("Poster creative not found") if poster_creative.nil?
            if poster_creative.nil?
              Honeybadger.notify("Poster creative not found", context: { creative_id: attachment_id,
                                                                         user_id: @user.id })
              raise StandardError.new("పోస్టు క్రియేటివ్ కనుగొనబడలేదు")
            end
            # if poster creative photo_v3_type is photo then create post photo with that photo_id
            case poster_creative.photo_v3_type
            when "Photo"
              @post.post_photos.build(photo_id: poster_creative.photo_v3_id)
              # if poster creative photo_v3_type is admin_medium then create post photo with admin medium url
              # For posts, we need to use photo model only so we need to create photo object with admin medium url
            when "AdminMedium"
              photo = AdminMedium.find_by_id(poster_creative.photo_v3_id)
              if photo.nil?
                Honeybadger.notify("Admin media not found", context: { admin_media_id: poster_creative.photo_v3_id,
                                                                       user_id: @user.id })
                raise StandardError.new("పోస్టు ఫోటో కనుగొనబడలేదు")
              end
              @post.post_photos.build(photo: Photo.new(url: photo.attributes['url'],
                                                       width: 800,
                                                       height: 1000,
                                                       user: @user,
                                                       service: :aws))
            end
          end
        end
      end
    end

    def delete_post
      @post.active = false
      @post.save!
    end

    def get_notification_post_feed
      notification_post = Post.get_feed_item(@post.id, @user, @app_version)

      notification_posts = []
      post_user_ids = []

      post_user_ids << @post.user_id
      notification_posts << notification_post

      user_viewed_post_ids = @user.get_post_view_ids
      user_viewed_post_ids << @post.id
      trending_feed_post_ids = $redis.zrangebyscore("#{Constants.trending_feed_cache_key}_#{@user.state_id}", '0', '+inf', limit: [0, user_viewed_post_ids.count + 1]).reject { |post_id| user_viewed_post_ids.include?(post_id.to_i) }

      if trending_feed_post_ids.blank?
        Honeybadger.notify('No unseen post in notification post feed', context: { notification_post_id: @post.id, user_id: @user.id })
      else
        trending_feed_post = Post.get_feed_item(trending_feed_post_ids.first, @user, @app_version)
        post_user_ids << trending_feed_post[:user][:id]
        notification_posts << trending_feed_post
      end

      following_data = UserFollower.select(:user_id, :source_of_follow)
                                   .where(user_id: post_user_ids, follower_id: @user.id, active: true)
      followed_user_ids = following_data.map(&:user_id)
      source_of_follows = following_data.map(&:source_of_follow)

      notification_posts.each do |post|
        post_obj = Post.new
        post_obj.id = post[:id]

        post[:user].follows = followed_user_ids.include? post[:user][:id]
        post[:user].source_of_follow =
          if post[:user].follows
            index = followed_user_ids.index(post[:user][:id])
            source_of_follows[index]
          end

        post[:likes_count] = post_obj.likes_count
        post[:comments_count] = post_obj.comments_count
        post[:opinions_count] = post_obj.opinions_count
        post[:whatsapp_count] = post_obj.whatsapp_count
        post[:preview_comments] = []
        post[:user_liked] = post_obj.is_liked(@user)

        custom_properties = {
          post_type: "notification",
          post_user_id: post[:user][:id],
          post_user_is_internal: post[:user][:internal],
        }
        post[:custom_properties] = custom_properties

        post
      end
    end

    def get_first_unseen_trending_feed_post_feed_item
      user_viewed_post_ids = @user.get_post_view_ids
      user_viewed_post_ids << @post.id if @post.present?
      trending_feed_post_ids = $redis.zrangebyscore("#{Constants.trending_feed_cache_key}_#{@user.state_id}",
                                                    '0',
                                                    '+inf',
                                                    limit: [0, user_viewed_post_ids.count + 1])
                                     .reject { |post_id| user_viewed_post_ids.include?(post_id.to_i) }
      if trending_feed_post_ids.blank?
        Honeybadger.notify('No unseen post in notification post feed',
                           context: { notification_post_id: @post.id, user_id: @user.id })
        return nil
      end

      Post.get_feed_item(trending_feed_post_ids.first, @user, @app_version)
    end

    private

    def set_post
      if !params[:id].nil? && !params[:id].empty? && params[:id].to_i.to_s == params[:id].to_s
        @post = Post.where(id: params[:id], active: true).first
      elsif !params[:post_id].nil? && !params[:post_id].empty? && params[:post_id].to_i.to_s == params[:post_id].to_s
        @post = Post.where(id: params[:post_id], active: true).first
      elsif !params[:id].nil? && !params[:id].empty?
        p = Post.find_by_hashid(params[:id])
        if !p.nil? && p.active?
          @post = p
        end
      elsif !params[:post_id].nil? && !params[:post_id].empty?
        p = Post.find_by_hashid(params[:post_id])
        if !p.nil? && p.active?
          @post = p
        end
      elsif !params[:hashid].nil? && !params[:hashid].empty?
        p = Post.find_by_hashid(params[:hashid])
        if !p.nil? && p.active?
          @post = p
        end
      end

      if @post.nil?
        render json: { message: "పోస్టు కనుగొనబడలేదు" }, status: :not_found
      end
    end
  end
end
