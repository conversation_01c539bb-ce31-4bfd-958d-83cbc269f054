module CirclesControllerSearchConcern
  extend ActiveSupport::Concern
  include Search::ControllerConcern

  included do
    # def get_recommended_circles
    #   render json: @user.get_suggested_circles(@offset, @count), status: :ok
    # end

    def get_circles_search_results
      circle_search_response = Circle.search_circles(user: @user, query: @query, offset: @offset, count: @count)
      render json: circle_search_response, status: :ok
    end
  end

end
