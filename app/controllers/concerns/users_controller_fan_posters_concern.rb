module UsersControllerFanPostersConcern
  extend ActiveSupport::Concern

  included do

    def get_fan_poster_requests_prompt_data
      circle_id, exact_users_count = @user.circle_id_for_fan_poster_prompt_with_max_interest
      circle = Circle.find_by(id: circle_id)

      if circle.blank? || !(circle.political_party_level? || circle.political_leader_level?)
        return render json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' },
                      status: :not_found
      end
      # check if user prompt limit is reached
      return render json: { success: false, message: 'దయచేసి కాసేపాగి ప్రయత్నించండి!' },
                    status: :unprocessable_entity unless @user.fan_posters_prompt_within_limit?(circle_id)

      fan_poster_feed_item = @user.fan_poster_feed_item(exact_users_count, circle)
      feed_type = fan_poster_feed_item[:feed_type]
      header = fan_poster_feed_item[:header]
      users = fan_poster_feed_item[:users]
      users_count = fan_poster_feed_item[:users_count]
      cta_text = fan_poster_feed_item[:cta_text]
      circle_details = fan_poster_feed_item[:circle_details]
      analytics_params = {
        feed_type: feed_type,
        circle_id: circle_id,
        circle_name: circle.name,
        users_count: users_count
      }

      # update redis key for showing fan poster request prompt
      @user.increment_fan_posters_prompt_seen_count(circle_id)

      render json: {
        feed_type: feed_type,
        circle_details: circle_details,
        header: header,
        users: users,
        users_count: users_count,
        cta_text: cta_text,
        analytics_params: analytics_params
      }, status: :ok
    end

    def request_fan_poster
      if !(@circle.political_party_level? || @circle.political_leader_level?)
        return render json: { success: false, message: 'ఈ సర్కిల్ ఫ్యాన్ పోస్టర్లను స్పాన్సర్ చేయడానికి అనుమతి లేదు' },
                      status: :unprocessable_entity
      end
      circle_id = @circle.id
      # check if user has already requested for the circle
      circle_interest = CirclePremiumInterest.has_fan_poster_interest?(circle_id: circle_id, user_id: @user.id)
      if circle_interest.present?
        return render json: { success: false, message: 'మీరు ఇప్పటికే ఈ సర్కిల్ ఫ్యాన్ పోస్టర్స్ కోసం రిక్వెస్ట్ చేసారు' },
                      status: :unprocessable_entity
      end
      # create a new circle interest
      begin
        CirclePremiumInterest.create(user_id: @user.id, circle_id: circle_id,
                                     key: Constants.user_fan_poster_interests_key)
      rescue => e
        Honeybadger.notify(e, context: { user_id: @user.id, circle_id: circle_id })
        return render json: { success: false, message: 'రిక్వెస్ట్ సబ్మిట్ చేయబడలేదు. కాసేపాగి ప్రయత్నించండి' }, status: :unprocessable_entity
      end
      render json: { success: true, message: 'మీ రిక్వెస్ట్ సబ్మిట్ చేయబడింది' }, status: :ok
    end

    def request_circle_premium
      if !(@circle.political_party_level? || @circle.political_leader_level?)
        return render json: { success: false, message: 'ఈ సర్కిల్ ఫ్యాన్ పోస్టర్లను స్పాన్సర్ చేయడానికి అనుమతి లేదు' },
                      status: :unprocessable_entity
      end
      circle_id = @circle.id
      # check if user has already requested for the circle
      circle_interest = @user.already_requested_for_premium_poster?(circle_id)
      if circle_interest.present?
        return render json: { success: false, message: 'మీరు ఇప్పటికే ఈ సర్కిల్ ఫ్యాన్ పోస్టర్స్ కోసం రిక్వెస్ట్ చేసారు' },
                      status: :unprocessable_entity
      end
      # create a new circle interest
      begin
        CirclePremiumInterest.create(user_id: @user.id, circle_id: circle_id,
                                     key: Constants.owner_premium_interests_key)
        user_id = @user.id
        user_phone = @user.phone
        user_name = @user.name
        circle_id = @circle.id
        circle_name = @circle.name
        requests_count = CirclePremiumInterest.fan_poster_interested_users_count(circle_id)
        time_stamp = Time.zone.now.strftime('%Y-%m-%d %H:%M:%S')
        data = [
          [user_id, user_phone, user_name, circle_id, circle_name, requests_count, time_stamp]
        ]
        spreadsheet_id = "1RlB0u7kVV6KxfAz3G6SwRJ8UQxBEn4txWHS7lU4K7Uc"
        ExportDataToGoogleSheets.perform_async(user_id, data, spreadsheet_id)
      rescue => e
        Honeybadger.notify(e, context: { user_id: @user.id, circle_id: circle_id })
        return render json: { success: false, message: 'రిక్వెస్ట్ సబ్మిట్ చేయబడలేదు. కాసేపాగి ప్రయత్నించండి' },
                      status: :unprocessable_entity
      end
      render json: { success: true, message: 'రిక్వెస్ట్ పంపబడింది!' }, status: :ok
    end
  end

  def get_fan_poster_requests_data
    latest_poster_creative = PosterCreative.get_latest_creative_of_circle(@circle.id)
    if latest_poster_creative.blank?
      return render json: { success: false, message: 'క్రియేటివ్ లేదు' }, status: :unprocessable_entity
    end
    # build poster creative json
    # kind = latest_poster_creative.creative_kind
    circle_id = @circle.id
    poster_creative_json = latest_poster_creative.build_creative_json(user: @user, is_layout_locked: false,
                                                                      is_eligible_for_premium_creatives: true,
                                                                      category_kind: nil, circle_id: circle_id,
                                                                      category_id: nil,
                                                                      creative_id: latest_poster_creative.id)

    # build requested users json
    users = CirclePremiumInterest.fan_poster_interested_users(@circle, count: 20)
    users_count = CirclePremiumInterest.fan_poster_interested_users_count(circle_id)
    layouts = get_circle_layouts_for_fan_posters
    requested_users = {
      header: I18n.t('fan_poster_requests.requested_user_header', count: users_count),
      caption: I18n.t('fan_poster_requests.requested_user_caption'),
      users: users
    }
    # build lead section details json
    lead_section_details = {
      header: I18n.t('fan_poster_requests.lead_section_header'),
      sub_header: I18n.t('fan_poster_requests.lead_section_sub_header'),
      cta_text: I18n.t('fan_poster_requests.lead_section_cta_text'),
      analytics_params: {
        circle_id: circle_id,
        circle_name: @circle.name,
        users_count: users_count
      },
      requested_header: I18n.t('fan_poster_requests.requested_header'),
      requested_cta_text: I18n.t('fan_poster_requests.requested_cta_text'),
      is_already_requested: @user.already_requested_for_premium_poster?(@circle.id)
    }
    render json: {
      creative: poster_creative_json,
      layouts: layouts,
      lead_section_details: lead_section_details,
      requested_users: requested_users
    }, status: :ok
  end

  def get_circle_layouts_for_fan_posters
    circle_id = @circle.id
    user_role = @user.get_badge_role
    if Circle.has_active_layout?(circle_id)
      circle_frames = CircleFrame.get_circle_frames
    end

    # get circle layout variation and common hash for layout of circle for circle_frames.
    circle_common_hash_for_layout = {}
    if circle_frames.present?
      circle_layout_variation, circle_common_hash_for_layout = @user.get_common_hash_for_layout_and_layout_variation(
        nil, entity: @circle)

      share_text = @user.get_circle_framed_poster_share_text(@circle)
      circle_common_hash_for_layout.merge!({ share_text: }) if share_text.present?
    end
    gradient_circle = @circle
    if @circle.political_leader_level?
      # add leader affiliation circle only so that party gradients will come up
      party_circle = @circle.get_leader_circle_party
      gradient_circle = party_circle if party_circle.present?
    end
    circle_layouts = @user.get_circle_layouts_for_non_layout_user(circle: @circle, frames: circle_frames,
                                                                  common_hash: circle_common_hash_for_layout,
                                                                  user_role: user_role, gradient_circle: gradient_circle)
    user_dummy_photo = AdminMedium.find_by(id: Constants.fan_user_dummy_photo_id)
    circle_layouts.map do |layout|
      layout[:identity][:user][:name] = I18n.t('fan_poster_requests.your_fan_name')
      layout[:identity][:user][:photo_url] = user_dummy_photo.compressed_url(size: 200) if user_dummy_photo.present?
      layout[:identity][:user][:badge] = {
        "id": 1,
        "active": true,
        "badge_text": I18n.t('fan_poster_requests.fan_badge_text'),
        "description": I18n.t('fan_poster_requests.fan_badge_text'),
        "badge_banner": "SILVER"
      }
    end
    circle_layouts
  end

end
