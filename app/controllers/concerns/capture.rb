# frozen_string_literal: true

# For capturing a HTML as an image and receiving a cloud object with it's url
module Capture
  extend ActiveSupport::Concern

  included do
    :capture_html_as_image
  end

  def capture_html_as_image(html, selector, transparent = false)
    Capture.capture_html_as_image(html, selector, transparent)
  end

  def self.capture_html_as_image(html, selector, transparent = false)
    url = URI(Constants.get_media_service_capture_upload_url)
    http = Net::HTTP.new(url.host, url.port)
    # http.use_ssl = true
    request = Net::HTTP::Post.new(url)
    jwt_token = JsonWebToken.get_token_to_send_media_service

    # headers
    request['Authorization'] = "Bearer #{jwt_token}"
    request['Content-Type'] = 'application/x-www-form-urlencoded'

    # body
    request.body = URI.encode_www_form(html: html, selector: selector, transparent: transparent)

    response = http.request(request)

    # raise error if response is not success
    raise "Error while capturing html as image: #{response.code}" unless response.is_a? Net::HTTPSuccess

    JSON.parse(response.body)
  end

  def self.apply_img_transform(url, quality: 80, fit: false, width: 600, height: 315)
    uri = URI(url)

    transform = "/#{fit ? 'fit-in/': ''}#{width}x#{height}/filters:quality(#{quality})"
    uri.host = Constants.cdn_domain
    uri.path = transform + uri.path
    uri.to_s
  end
end
