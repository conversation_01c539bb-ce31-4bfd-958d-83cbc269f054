# frozen_string_literal: true

# made for handling all cases of poster params using polymorphism
module <PERSON>erP<PERSON><PERSON>
  def self.from(params)
    category_id = params[:id].to_i if params[:id].present?
    category_kind = params[:category_kind] if params[:category_kind].present?
    circle_id = params[:circle_id].to_i if params[:circle_id].present?
    creative_id = params[:creative_id].to_i if params[:creative_id].present?

    if creative_id.present?
      CreativePosterParams.new(params)
    elsif category_id.present?
      EventPosterParams.new(params)
    elsif category_kind.present? && circle_id.present?
      KindInCirclePosterParams.new(params)
    else
      BasePosterParams.new({})
    end
  end

  # BasePosterParams is a base class for all poster params
  class BasePosterParams
    attr_reader :params

    def initialize(params)
      @params = params
    end

    def event
      nil
    end

    def creative
      nil
    end

    def circle_ids
      []
    end

    def get_creatives(*)
      []
    end

    def share_text(user_id)
      nil
    end
  end
end
