module RelevantFeedItemConcern
  extend ActiveSupport::Concern
  include PosterCarouselFeedItem
  included do

    def nudge_feed_item_for_user(loaded_feed_item_ids)
      if @user.eligible_for_self_trial?
        self_trial_nudge_feed_item(loaded_feed_item_ids)
      elsif @user.is_eligible_for_start_trial?
        trial_nudge_feed_item(loaded_feed_item_ids)
      elsif !@user.is_poster_subscribed && @user.last_subscription.present? &&
        (@user.last_subscription.cancelled? || @user.last_subscription.paused?)
        premium_expired_nudge_feed_item(loaded_feed_item_ids)
      elsif SubscriptionUtils.has_user_subscribed?(@user.id, allow_grace_period: false)
        offer_nudge_feed_item(loaded_feed_item_ids) ||
          upgrade_package_nudge_feed_item(loaded_feed_item_ids)
      else
        payment_failed_nudge_feed_item(loaded_feed_item_ids)
      end
    end

    def relevant_my_feed_items_for_user(loaded_feed_item_ids:)
      return nil if @user.nil?

      if @user.premium_pitch_hot_lead? || SubscriptionUtils.has_user_ever_subscribed?(@user.id)
        nudge_feed_item = nudge_feed_item_for_user(loaded_feed_item_ids)
        feed_items = [nudge_feed_item, get_poster_carousel_or_fan_poster_feed_item(loaded_feed_item_ids, nudge_present: nudge_feed_item.present?)] ||
          [get_circles_suggestions_feed_item(loaded_feed_item_ids) ||
             badge_card_feed_item(loaded_feed_item_ids) ||
             suggested_contacts_feed_item(loaded_feed_item_ids) || suggested_users_feed_item(loaded_feed_item_ids)]
      elsif @user.is_eligible_for_start_trial? && @user.has_premium_layout?
        nudge_feed_item = nudge_feed_item_for_user(loaded_feed_item_ids)
        feed_items = [nudge_feed_item, get_poster_carousel_or_fan_poster_feed_item(loaded_feed_item_ids, nudge_present: nudge_feed_item.present?)] ||
          [get_circles_suggestions_feed_item(loaded_feed_item_ids) ||
             badge_card_feed_item(loaded_feed_item_ids) ||
             suggested_contacts_feed_item(loaded_feed_item_ids) || suggested_users_feed_item(loaded_feed_item_ids)]
      else
        feed_items = [nudge_feed_item_for_user(loaded_feed_item_ids) || get_circles_suggestions_feed_item(loaded_feed_item_ids) ||
                        badge_card_feed_item(loaded_feed_item_ids) ||
                        get_poster_carousel_or_fan_poster_feed_item(loaded_feed_item_ids) ||
                        suggested_contacts_feed_item(loaded_feed_item_ids) || suggested_users_feed_item(loaded_feed_item_ids)]
      end
      feed_items.compact
    end

    private

    def premium_expired_nudge_feed_item(loaded_feed_item_ids)
      return nil unless AppVersionSupport.supports_autopay?
      return nil if loaded_feed_item_ids.include?('premium_expired_nudge')

      premium_end_date = SubscriptionUtils.last_poster_premium_end_date(@user.id)
      return if premium_end_date.present? && premium_end_date.advance(days: Constants.duration_days_after_premium_end_date).end_of_day >= Time.zone.now
      user_plan = UserPlan.find_by(user_id: @user.id)

      {
        feed_type: 'payment',
        feed_item_id: 'premium_expired_nudge',
        text: I18n.t('premium_expired_nudge.text'),
        sub_text: I18n.t('premium_expired_nudge.sub_text'),
        discount_text: nil,
        discount_text_color: nil,
        deeplink: '/premium-experience?payment-sheet=true&source=payment',
        analytics_params: {
          deeplink: '/premium-experience?payment-sheet=true&source=payment',
          feed_item_id: 'premium_expired_nudge'
        }
      }
    end

    def payment_failed_nudge_feed_item(loaded_feed_item_ids)
      return nil unless AppVersionSupport.supports_autopay?
      return nil if loaded_feed_item_ids.include?('payment_failed_nudge')
      subscription = @user.active_subscription
      if subscription.blank? && SubscriptionUtils.is_user_in_grace_period?(@user.id)
        subscription = @user.on_hold_subscription
      end
      return nil if subscription.blank?
      user_plan = UserPlan.find_by(user_id: @user.id)
      attempt_number = subscription.last_failed_retry_attempt_number
      return if attempt_number < 3
      next_retry_date = subscription.get_next_retry_charge_date
      if next_retry_date.blank?
        text = I18n.t('payment_failed_nudge.text', charge: user_plan.amount)
        sub_text = I18n.t('payment_failed_nudge.sub_text')
      else
        if attempt_number < 6
          text = ""
          sub_text = I18n.t('premium_experience_subscription_retry_string_soft_nudge.text', retry_date: next_retry_date.strftime('%d-%b, %Y'), charge: user_plan.amount) + " " + I18n.t('premium_experience_subscription_retry_string_soft_nudge.sub_text')
        elsif attempt_number < 9
          text = ""
          sub_text = I18n.t('premium_experience_subscription_retry_string_medium_nudge.text', retry_date: next_retry_date.strftime('%d-%b, %Y'), charge: user_plan.amount) + " " + I18n.t('premium_experience_subscription_retry_string_medium_nudge.sub_text')
        else
          text = I18n.t('premium_experience_subscription_retry_string_hard_nudge.text', retry_date: next_retry_date.strftime('%d-%b, %Y'), charge: user_plan.amount)
          sub_text = I18n.t('premium_experience_subscription_retry_string_hard_nudge.sub_text')
        end
      end

      {
        feed_type: 'payment',
        feed_item_id: 'payment_failed_nudge',
        text: text,
        sub_text: sub_text,
        discount_text: nil,
        discount_text_color: nil,
        deeplink: '/premium-experience?payment-sheet=true&source=payment',
        analytics_params: {
          deeplink: '/premium-experience?payment-sheet=true&source=payment',
          feed_item_id: 'payment_failed_nudge'
        }
      }
    end

    def trial_nudge_feed_item(loaded_feed_item_ids)
      return nil unless AppVersionSupport.supports_autopay?
      return nil if loaded_feed_item_ids.include?('trial_nudge')
      layout = @user.get_user_poster_layout
      return nil if layout.blank?
      # show nudge for 7 days from the layout creation date
      if layout.created_at.advance(days: 7) > Time.zone.now
        {
          feed_type: 'payment',
          feed_item_id: 'trial_nudge',
          text: nil,
          sub_text: I18n.t('trial_nudge.sub_text', duration: Constants.poster_trial_default_duration),
          discount_text: nil,
          discount_text_color: nil,
          deeplink: '/premium-experience?source=payment',
          analytics_params: {
            deeplink: '/premium-experience?source=payment',
            feed_item_id: 'trial_nudge'
          }
        }
      end
    end

    def self_trial_nudge_feed_item(loaded_feed_item_ids)
      return nil unless AppVersionSupport.supports_autopay?

      feed_item_id = 'self_trial_nudge'
      return nil if loaded_feed_item_ids.include?(feed_item_id)
      # if eligible user has not seen this nudge at least thrice then show this
      return nil unless @user.self_trial_nudge_seen_count_within_limit?

      nudge_json = {
        feed_type: 'payment',
        feed_item_id: feed_item_id,
        text: nil,
        sub_text: I18n.t('trial_nudge.sub_text', duration: Constants.poster_trial_default_duration),
        discount_text: nil,
        discount_text_color: nil,
        deeplink: '/premium-experience?source=payment',
        analytics_params: {
          deeplink: '/premium-experience?source=payment',
          feed_item_id: feed_item_id,
        }
      }

      @user.increment_self_trial_nudge_seen_count
      nudge_json
    end

    def upgrade_package_nudge_feed_item(loaded_feed_item_ids)
      return nil unless @user.show_upgrade_package_nudge?
      feed_item_id = "upgrade_package_nudge"
      return nil if loaded_feed_item_ids.include?(feed_item_id)
      user_plan = UserPlan.find_by(user_id: @user.id)
      return nil if user_plan.blank?
      target_plan_duration_in_months = 12
      target_plan = Plan.get_plan_based_on_duration(user: @user, duration_in_months: target_plan_duration_in_months)
      return nil if target_plan.blank?
      monthly_savings = user_plan.amount - (target_plan.amount / target_plan_duration_in_months)

      sub_text = I18n.t('upgrade_package_nudge.text', duration: target_plan_duration_in_months)
      discount_text = I18n.t('upgrade_package_nudge.sub_text', monthly_savings: monthly_savings)

      # increment the count of nudge seen
      @user.increment_upgrade_package_nudge_seen_count

      {
        feed_type: 'payment',
        feed_item_id: feed_item_id,
        text: nil,
        sub_text: sub_text,
        discount_text: discount_text,
        discount_text_color: nil,
        deeplink: '/upgrade?source=upgrade_nudge',
        analytics_params: {
          deeplink: '/upgrade?source=upgrade_nudge',
          feed_item_id: feed_item_id
        }
      }
    end

    def offer_nudge_feed_item(loaded_feed_item_ids)
      return nil unless @user.app_version_supports_upgrade_package_sheet?
      return nil unless @user.upgrade_package_using_offer_conditions_met?
      campaign = @user.user_eligible_1_year_campaign
      return nil unless campaign.present?

      feed_item_id = "offer_nudge"
      return nil if loaded_feed_item_ids.include?(feed_item_id)

      campaign_name = campaign.name
      text = I18n.t('offer_nudge.text', campaign_name: campaign_name)
      sub_text = I18n.t('offer_nudge.sub_text')
      button_text = I18n.t('offer_nudge.button_text')
      deeplink = "/upgrade?source=offer_nudge&campaign_id=#{campaign.id}&offer=true"

      {
        feed_type: 'offer',
        feed_item_id: feed_item_id,
        text: text,
        sub_text: sub_text,
        button_text: button_text,
        deeplink: deeplink,
        analytics_params: {
          deeplink: deeplink,
          feed_item_id: feed_item_id,
          campaign_name: campaign_name,
          campaign_id: campaign.id,
        }.compact
      }
    end

    # show only any one among them (fan poster or poster carousel feed item)
    def get_poster_carousel_or_fan_poster_feed_item(loaded_feed_item_ids, nudge_present: false)
      return if loaded_feed_item_ids.any? do |id|
        id.present? && (id.include?(Constants.poster_carousel_feed_item_id) || id == Constants.relevant_fan_poster_toast_id)
      end

      # if nudge is there we should send poster carousel in feed
      if nudge_present || eligible_for_poster_carousel_in_feed?(user: @user)
        poster_carousel_in_feed(user: @user)
        # relevant fan poster for user should be shown only if poster carousel is not shown and that app version
        # supports it and should not support poster carousel in feed
      elsif AppVersionSupport.is_feed_toast_supported? && !AppVersionSupport.poster_carousel_in_feed_supported?
        get_relevant_fan_poster_for_user
      end
    end

    def get_circles_suggestions_feed_item(loaded_feed_item_ids)
      get_circles_suggestions if @user.village_id.present? && loaded_feed_item_ids.exclude?('circle_suggestions')
    end

    def badge_card_feed_item(loaded_feed_item_ids)
      get_badge_card_feed_item if loaded_feed_item_ids.exclude?('badge_card')
    end

    def suggested_contacts_feed_item(loaded_feed_item_ids)
      feed_item = @user.get_suggested_contacts_feed_object if loaded_feed_item_ids.exclude?('CONTACTS_LIST')
      return if feed_item.blank?
      feed_item
    end

    def suggested_users_feed_item(loaded_feed_item_ids)
      return unless AppVersionSupport.eligible_for_suggested_users?

      list_feed = @user.get_suggested_users_feed_v1(loaded_feed_item_ids)
      return if list_feed.blank?
      list_feed[:custom_properties][:list_source] = 'MY_FEED'
      list_feed
    end

    def get_circles_suggestions
      return nil if @user.nil?

      disable, session_count = @user.should_show_circle_suggestions?
      return nil unless disable

      new_circles_feed = nil

      user_obj = User.where(id: @user.id).includes(:circles).last

      feed_type = @app_version >= Gem::Version.new('1.11.3') ? 'suggested_list' : 'circles_section'

      new_circles_list = if @app_version >= Gem::Version.new('1.11.3')
                           user_obj.get_feed_circle_suggestions_v2(session_count)
                         else
                           user_obj.get_feed_circle_suggestions
                         end

      if new_circles_list.count.positive?
        new_circles_feed = {
          header: I18n.t('circle_suggestions_text.header'),
          sub_header: I18n.t('circle_suggestions_text.sub_header'),
          feed_item_id: 'circle_suggestions',
          feed_type: feed_type,
          suggested_type: 'CIRCLES',
          is_follow_all_present: false,
          circles: new_circles_list
        }
      end

      new_circles_feed
    end

    def get_badge_card_feed_item
      is_eligible_for_badge_card, political_party_id = @user.is_eligible_for_badge_card
      if is_eligible_for_badge_card &&
        @user.should_show_badge_card? &&
        @app_version > Gem::Version.new('1.17.0')
        @user.get_badge_card(political_party_id, AppVersionSupport.is_full_badge_card_supported?)
      end
    end

    # Latest fan poster that is uploaded to user's circles and not seen by the user
    def get_relevant_fan_poster_for_user
      leader_ids = @user.get_user_joined_leader_ids
      leader_ids = leader_ids.first(50) if leader_ids.size > 50

      recent_leader_fan_poster = nil
      recent_leader_fan_poster = PosterCreative.
        get_relevant_fan_poster_as_toast(user_id: @user.id, circle_ids: leader_ids,
                                         created_from: 2.days.ago) unless leader_ids.blank?
      return recent_leader_fan_poster if recent_leader_fan_poster.present?

      party_ids = @user.get_user_joined_party_circle_ids
      party_ids = party_ids.first(50) if party_ids.size > 50

      recent_party_fan_poster = nil
      recent_party_fan_poster = PosterCreative.
        get_relevant_fan_poster_as_toast(user_id: @user.id, circle_ids: party_ids,
                                         created_from: 2.days.ago) if party_ids.size == 1
      return recent_party_fan_poster if recent_party_fan_poster.present?

      old_leader_fan_poster = nil
      old_leader_fan_poster = PosterCreative.
        get_relevant_fan_poster_as_toast(user_id: @user.id, circle_ids: leader_ids, created_from: 7.days.ago,
                                         created_upto: 2.days.ago) unless leader_ids.blank?
      return old_leader_fan_poster if old_leader_fan_poster.present?

      old_party_fan_poster = nil
      old_party_fan_poster = PosterCreative.
        get_relevant_fan_poster_as_toast(user_id: @user.id, circle_ids: party_ids, created_from: 7.days.ago,
                                         created_upto: 2.days.ago) if party_ids.size == 1
      return old_party_fan_poster if old_party_fan_poster.present?

      nil
    end
  end
end
