# frozen_string_literal: true

module ElectionTickerPanel
  include Elections2024

  def all_tickers
    mla_constituency_ids = Circle.where(level: :mla_constituency).pluck(:id)

    photos = []
    mla_constituency_ids.each do |mla_constituency_id|
      photo_id = get_constituency_status_photo_id(mla_constituency_id)
      photos << Photo.find(photo_id) if photo_id.present?
    end

    panel "Image" do
      photos.each do |photo|
        div style: 'width: 200px; float: left; margin-right: 10px; margin-bottom: 10px;' do
          img src: photo.url, style: 'width: 100%;'
        end
      end
    end
  end

  def ticker_panel(ticker_id)
    party_or_coalitions = ticker_coalitions(ticker_id) + [Elections2024::OTHERS_COALITION]
    ticker_data = get_ticker_data(ticker_id)
    counts = ticker_data["counts"]
    updated_at = ticker_data["updated_at"]
    last_updated_at = ''
    last_updated_at = Time.parse(updated_at).in_time_zone('Kolkata').strftime('%d-%m-%Y %H:%M:%S') if updated_at.present?

    panel "Last Updated: #{last_updated_at}" do
      form action: submit_form_path(ticker_id), method: :post do
        input type: 'hidden', name: 'authenticity_token', value: form_authenticity_token

        table class: 'index_table', style: 'width: 600px;' do
          thead do
            tr do
              th 'Party / Coalition'
              th 'Lead Count'
              th 'Win Count'
            end
          end
          tbody do
            party_or_coalitions.each_with_index do |party, index|
              id = party.id
              name = party.name
              party_data = counts[id] || {}
              lead_count = win_count = 0
              if party_data.present?
                lead_count = party_data["lead_count"] || 0
                win_count = party_data["win_count"] || 0
              end
              tr do
                td name do
                  input type: 'hidden', name: "party_data[#{index}][party]", value: id
                end
                td do
                  input type: 'number', name: "party_data[#{index}][lead_count]", value: lead_count, style: 'width: 100px;', min: 0, oninput: 'this.value = Math.abs(this.value)'
                end
                td do
                  input type: 'number', name: "party_data[#{index}][win_count]", value: win_count, style: 'width: 100px;', min: 0, oninput: 'this.value = Math.abs(this.value)'
                end
              end
            end
          end
        end

        div style: 'margin-top: 15px;' do
          label do
            input type: 'checkbox', name: 'declare_majority_as_winner', checked: ticker_data["declare_majority_as_winner"]
            span 'Declare Majority as Winner'
          end
        end

        div style: 'margin-top: 15px;' do
          input type: 'submit', value: 'Update'
        end
      end
    end
  end

  def submit_form_path(ticker_id)
    case ticker_id
    when Elections2024::AP_ASSEMBLY_TICKER_ID
      admin_ap_assembly_ticker_submit_form_path
    when Elections2024::TG_PARLIAMENT_TICKER_ID
      admin_tg_parliament_ticker_submit_form_path
    when Elections2024::CENTRE_TICKER_ID
      admin_centre_ticker_submit_form_path
    when Elections2024::AP_PARLIAMENT_TICKER_ID
      admin_ap_parliament_ticker_submit_form_path
    when Elections2024::MAHARASHTRA_ASSEMBLY_TICKER_ID
      admin_maharashtra_assembly_ticker_submit_form_path
    when Elections2024::JHARKHAND_ASSEMBLY_TICKER_ID
      admin_jharkhand_assembly_ticker_submit_form_path
    when Elections2024::DELHI_ASSEMBLY_TICKER_ID
      admin_delhi_assembly_ticker_submit_form_path
    else
      raise "Invalid ticker_id: #{ticker_id}"
    end
  end
end
