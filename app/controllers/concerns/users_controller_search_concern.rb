module UsersControllerSearchConcern
  extend ActiveSupport::Concern
  include Search::ControllerConcern

  included do
    # def get_recommended_users_lists
    #   render json: @user.get_suggested_users_lists, status: :ok
    # end

    def get_users_search_results
      user_search_response = User.search_users(user: @user, query: @query, offset: @offset, count: @count)
      render json: user_search_response, status: :ok
    end
  end
end
