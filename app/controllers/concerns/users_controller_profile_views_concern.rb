module UsersControllerProfileViewsConcern
  extend ActiveSupport::Concern
  include ProfileViewHelper

  included do

    def user_profile_views
      count = 10
      loaded_user_ids = params[:loaded_user_ids].present? ? params[:loaded_user_ids] : []
      views_data = fetch_views_data(@user, loaded_user_ids, count)

      title, _views_count = generate_title(@user, loaded_user_ids)

      render json: { success: true, title: title, users: views_data[:users] }.compact, status: :ok
    end
  end
end
