class ExternalServiceController < ActionController::API
  before_action :set_service, except: [:head_route]

  ALLOWED_SERVICES = %w[mixpanel lead_squared floww juspay]

  def head_route
    render json: {message: "Valid!"}, status: :ok
  end

  def set_service
    @service = nil
    if request.headers['Authorization'].present?
      token = ::Base64.decode64(request.headers['Authorization'].split(' ', 2).last || '')
      if token.present?
        auth_username, auth_secret = token.split(/:/, 2)
        service_name = auth_username&.strip&.downcase

        # Verify if there is a valid service name
        if ALLOWED_SERVICES.include?(service_name) &&
          auth_username == Rails.application.credentials[service_name.to_sym][:auth_username] &&
          auth_secret == Rails.application.credentials[service_name.to_sym][:auth_secret]
          @service = service_name
        end
      end
    end

    if @service.nil?
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end
end
