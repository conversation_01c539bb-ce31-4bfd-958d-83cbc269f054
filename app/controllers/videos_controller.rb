# frozen_string_literal: true

class VideosController < ApiController
  before_action :set_logged_in_user, except: [:processing_update]

  # POST /upload-video
  def upload
    # if params[:data].present?
    #   begin
    #     data = params[:data]
    #     uploader = VideoUploader.new
    #     uploader.set_user_id(@user.id)
    #     uploader.store!(data)
    #
    #     video = Video.create(
    #       url: uploader.url,
    #       thumbnail_url: uploader.thumb.url,
    #       user: @user
    #     )
    #
    #     render json: video, status: :ok
    #   rescue StandardError => e
    #     render json: { success: false, message: 'Upload failed! Please try again' }, status: :unprocessable_entity
    #   end
    # else
      render json: { success: false, message: 'Upload failed! Please try again' }, status: :unprocessable_entity
    # end
  end

  def processing_update
    data = JSON.parse request.raw_post
    logger.warn("Data -- #{data}")

    # To be used when we intend to serve all resolutions
    output_url = data['cdn_url']

    if output_url.present?
      video_hash_key = output_url.split('/').last.gsub('.m3u8', '')

      if video_hash_key.present?
        video = Video.find_by_hash_key!(video_hash_key)

        video.url = output_url
        video.status = :processed
        video.save!

        post_video = PostVideo.where(video: video).last
        if post_video&.post
          post = post_video.post
          post.flush_cache
        end

        render json: { success: true }, status: :ok
        return
      end
    end

    render json: { success: false }, status: :unprocessable_entity
  end
end
