class SingularDatum < ApplicationRecord
  belongs_to :user
  has_one :user_points_ledger, as: :reference

  # after_create_commit :add_user_points_ledger

  # def add_user_points_ledger
  #   self.user_points_ledger.create(
  #     user_id: invited_by,
  #     action: :credit,
  #     actioned_at: Time.zone.now,
  #     actioned_by: nil,
  #     points: get_signup_type_points
  #   )
  # end
  #
  # def get_user_type
  #   is_new_install = created_at.to_date == user.created_at.to_date
  #   is_leader = user.get_badge_role.present?
  #
  #   if is_leader
  #     is_new_install ? :leader_new_install : :leader_re_install
  #   else
  #     is_new_install ? :normal_new_install : :normal_re_install
  #   end
  # end
  #
  # def get_signup_type_points
  #   case get_user_type
  #   when :leader_new_install
  #     400
  #   when :leader_re_install
  #     150
  #   when :normal_new_install
  #     150
  #   when :normal_re_install
  #     0
  #   else
  #     0
  #   end
  # end
end
