class HashtagLike < ApplicationRecord
  belongs_to :hashtag, -> { where(active: true) }
  belongs_to :user, -> { active }

  after_create_commit :increase_hashtag_likes_count
  after_destroy_commit :decrease_hashtag_likes_count

  def increase_hashtag_likes_count
    $redis.hincrby(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, hashtag_id, 1)
  end
  def decrease_hashtag_likes_count
    $redis.hincrby(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, hashtag_id, -1)
  end
end
