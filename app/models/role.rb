class Role < ApplicationRecord
  has_paper_trail
  after_update_commit :update_user_roles, :flush_user_cache, :update_invite_cards, :trigger_user_roles_transliteration

  GRADE_LEVEL = {
    grade_1: 1,
    grade_2: 2,
    grade_3: 3,
    grade_4: 4,
    grade_5: 5
  }
  BADGE_COLOR = {
    GOLD: 'GOLD',
    SILVER: 'SILVER',
    WHITE: 'WHITE'
  }

  DISPLAY_NAME = {
    parent_circle: 'parent',
    role: 'role',
    purview_circle: 'purview',
    purview_circle_suffix: 'purview_suffix',
    badge_circle_text: 'badge_text'
  }

  enum parent_circle_level: Circle::CIRCLE_LEVEL, _suffix: true
  enum purview_level: Circle::CIRCLE_LEVEL, _suffix: true
  enum quota_type: %i[absolute percentage no_limit], _suffix: true
  enum grade_level: GRADE_LEVEL
  enum badge_color: BADGE_COLOR
  # TODO : Need to remove this after roles restructuring
  enum primary_circle_level: Circle::CIRCLE_LEVEL, _suffix: true
  enum primary_circle_type: Circle::CIRCLE_TYPE, _suffix: true

  validates_uniqueness_of :name, scope: [:parent_circle_id, :parent_circle_level, :purview_level],
                          message: "#{name} with this parent circle id or parent circle level and purview_level is already taken"

  # it should not allow below specified special characters and numbers in name without letters
  validates :name, format: { without: /\A\d+\z|[@_!#$%^*<>?|~:]+/, message: "only allows letters and digits" },
            presence: true, allow_blank: false
  validates :has_badge, :active,
            :show_badge_banner, :has_purview, :has_badge_icon, inclusion: [true, false]
  validates :quota_type, :permission_group_id, presence: true
  validates :grade_level, inclusion: { in: grade_levels.keys }
  validate :validate_purview_level

  before_save :check_show_badge_banner
  belongs_to :permission_group
  belongs_to :parent_circle, class_name: 'Circle', optional: true

  with_options if: ->(role) { role.has_badge } do |role|
    role.validates :badge_color, inclusion: { in: badge_colors.keys }
  end

  validate :validations_if_badge_present, :check_for_purview_level, :validate_parent_circle_and_level,

    def validations_if_badge_present
      if has_badge
        if badge_ring.nil?
          errors.add(:badge_ring, 'badge ring should be a boolean value if role has badge')
        end
        if badge_color.nil?
          errors.add(:badge_color, 'badge color should be present if role has badge')
        end
      else
        errors.add(:has_badge, "badge ring should be empty if role don't have badge") unless badge_ring.nil?
        errors.add(:has_badge, "badge color should be empty if role don't have badge") if badge_color.present?
      end
    end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def check_for_purview_level
    if has_purview
      errors.add(:purview_level, 'purview_level should be a boolean value if has purview
                                                  circle is true') if purview_level.nil?
    else
      errors.add(:has_purview, "purview level should be empty if
                                        role don't have purview") if purview_level.present?
    end
  end

  def validate_purview_level
    if has_purview
      allowed_levels = Circle.level_options_for("location")
      unless allowed_levels.include?(purview_level.to_sym)
        errors.add(:purview_level, "should be one of #{allowed_levels.join(', ')}")
      end
    end
  end

  def validate_parent_circle_and_level
    if parent_circle_id.present? && parent_circle_level.present?
      errors.add(:parent_circle_id, 'Parent circle id and parent circle level both are not required')
    elsif parent_circle_id.blank? && parent_circle_level.blank?
      errors.add(:parent_circle_id, 'Either the parent circle id or parent circle level is required')
    end

    if parent_circle_id.present?
      parent_circle = Circle.find_by(id: parent_circle_id)
      if parent_circle.present? && Circle.level_options_for("location").include?(parent_circle.level.to_sym)
        errors.add(:parent_circle_level, 'Parent circle level should not be a location circle type')
      end
    elsif parent_circle_level.present?
      if Circle.level_options_for("location").include?(parent_circle_level.to_sym)
        errors.add(:parent_circle_level, 'Parent circle level should not be a location circle type')
      end
    end
  end

  def flush_user_cache
    users_list = UserRole.where(role: self).pluck(:user_id)
    User.find(users_list).each do |u|
      u.flush_cache
    end
  end

  def check_show_badge_banner
    self.show_badge_banner = false unless has_badge
  end

  def get_role_data_for_badge_card(circle_id)
    role_badge_icon = get_badge_icon_url(circle_id)
    {
      "id": id,
      "active": true,
      "icon_url": role_badge_icon,
      "badge_banner": nil,
      "badge_icon": role_badge_icon,
      "badge_ring": get_badge_ring,
      "badge_text": nil,
      "description": nil
    }
  end

  def get_badge_icon_url(circle_id)
    badge_icon_url = nil
    if has_badge && has_badge_icon
      badge_icon_url = get_badge_icon(circle_id)&.admin_medium.url
    end
    badge_icon_url || Constants.get_transparent_badge_icon_url
  end

  def get_badge_icon(circle_id)
    BadgeIcon.joins(:badge_icon_group).where("badge_icons.color = '#{badge_color}'
      AND badge_icon_groups.circle_id = #{circle_id}").first
  end

  def get_badge_ring
    case true
    when badge_ring && badge_color == "GOLD"
      "GOLD_RING"
    when badge_ring && badge_color == "SILVER"
      "SILVER_RING"
    else
      "NO_RING"
    end
  end

  def searchable_select_name_for_sub_circles
    if parent_circle.present?
      "#{name} (parent circle: #{parent_circle.name}, purview level: #{purview_level})"
    else
      "#{name} (parent level: #{parent_circle_level}, purview level: #{purview_level})"
    end
  end

  def update_invite_cards
    if saved_change_to_name? || saved_change_to_parent_circle_to_badge_text? ||
      saved_change_to_purview_circle_to_badge_text? || saved_change_to_badge_color? || saved_change_to_badge_ring? ||
      saved_change_to_show_badge_banner? || saved_change_to_parent_circle_id? ||
      saved_change_to_has_badge_icon?
      UserRole.where(role: self).find_in_batches(batch_size: 250) do |batch|
        batch.each do |user_role|
          GenerateInviteCard.perform_async(user_role.user_id)
        end
      end
    end
  end

  private

  def update_user_roles
    UserRole.where(role: self).update_all(active: false) unless self.active?
  end

  def trigger_user_roles_transliteration
    if saved_change_to_name? || saved_change_to_parent_circle_id? || saved_change_to_purview_level? || saved_change_to_active?
      TransliterateUserRoleOnRoleUpdate.perform_async(id)
    end
  end

end
