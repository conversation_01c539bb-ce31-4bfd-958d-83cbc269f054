class Campaign < ApplicationRecord
  has_paper_trail
  belongs_to :admin_medium
  scope :active, -> { where(active: true) }

  def image_url
    admin_medium.url
  end

  def self.latest_campaign_for_cohort(cohort:, duration_in_months:)
    active.where("JSON_CONTAINS_PATH(cohort_details_json, 'one', '$.#{cohort}')")
          .where("JSON_UNQUOTE(JSON_EXTRACT(cohort_details_json, '$.#{cohort}[0].plan_duration_in_months')) = ?",
                 duration_in_months)
          .where("start_time <= ? AND end_time >= ?", Time.zone.now, Time.zone.now)
          .order(start_time: :desc)
          .first
  end

  def cohort_discount_percentage_for_campaign(cohort:, plan_duration_in_months:)
    # This method will return the discount percentage for the given cohort
    cohort_details_json[cohort].find { |details| details['plan_duration_in_months'] == plan_duration_in_months }['discount_percentage']
  end
end

