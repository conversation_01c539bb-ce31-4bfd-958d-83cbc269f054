# frozen_string_literal: true

class Video < ApplicationRecord
  belongs_to :user, polymorphic: true

  enum status: { uploaded: 'uploaded', processed: 'processed', failed: 'failed' }
  enum service: { aws: 'aws', gcp: 'gcp', azure: 'azure' }

  attribute :blob_data
  attribute :ms_data
  attribute :thumbnail
  attribute :get_thumbnail_url

  before_create :upload_new, if: -> { self.blob_data.present? }
  before_create :set_video_data, if: -> { self.ms_data.present? }

  validate :presence_of_service, if: -> { self.service.blank? }
  validate :presence_of_url, if: -> { errors.blank? && self.ms_data.present? }
  validate :presence_of_path, if: -> { errors.blank? && self.ms_data.present? }

  def presence_of_service
    raise "Video service is blank"
  end

  def presence_of_url
    if (new_record? && ms_data[:cdn_url].blank?) || (!new_record? && self.source_url.blank?)
      raise "Video url is blank"
    end
  end

  def presence_of_path
    if (new_record? && ms_data[:path].blank?) || (!new_record? && self.path.blank?)
      raise "Video path is blank"
    end
  end

  def set_video_data
    self.source_url = ms_data[:cdn_url]
    self.hash_key = ms_data[:hash_key] || ms_data[:file_id]
    self.url = ms_data[:cdn_url]
    self.path = ms_data[:path]
    self.bitrate = ms_data[:bitrate_mbps] if ms_data[:bitrate_mbps].present?
    self.width = ms_data[:width] if ms_data[:width].present?
    self.height = ms_data[:height] if ms_data[:height].present?
    self.duration = ms_data[:duration_secs] if  ms_data[:duration_secs].present?
    self.mode = ms_data[:mode] if ms_data[:mode].present?

    self.status = :uploaded

    if ms_data[:thumbnail].present?
      self.thumbnail_url = ms_data[:thumbnail][:cdn_url] if ms_data[:thumbnail][:cdn_url].present?
      self.thumbnail_path = ms_data[:thumbnail][:path] if ms_data[:thumbnail][:path].present?
    end
  end

  def get_thumbnail_url
    if self.thumbnail_url.blank? || (( self.service.to_sym == :gcp || self.service.to_sym == :azure ) && self.thumbnail_path.blank?)
      return Constants.default_video_thumbnail_url
    end
    service_domain =
      case self.service.to_sym
      when :azure
        'https://az-ip-cdn.thecircleapp.in'
      when :gcp
        'https://grup.thecircleapp.in'
      else
        'https://l-cdn.praja.buzz'
      end
    thumbnail_url.gsub(Photo::PHOTO_REGEX, service_domain + '/fit-in/750x750/filters:quality(60)/')
  end

  def upload_new
    upload_new_v2
  end

  def upload_new_v2
    return if blob_data.nil? || thumbnail.nil?

    begin
      resource = Aws::S3::Resource.new(
        region: 'ap-south-1',
        credentials: Aws::Credentials.new(
          Rails.application.credentials[:aws_access_key_id],
          Rails.application.credentials[:aws_secret_access_key]
        ))

      file_extension = File.extname(thumbnail.original_filename)
      hashed_file_name = Digest::MD5.hexdigest(thumbnail.original_filename + '_' + Time.now.to_i.to_s) + file_extension.downcase

      s3_object_path = "#{Rails.env}/videos/thumbnails/#{user.class.name.downcase}/#{user.id}/#{hashed_file_name}"

      thumbnail_object = resource
                           .bucket(Rails.application.credentials[:aws_s3_bucket_name])
                           .object(s3_object_path)
      thumbnail_object.put(body: thumbnail)

      thumbnail_url = nil
      if thumbnail_object && thumbnail_object.exists?
        thumbnail_url = "https://a-cdn.thecircleapp.in/#{s3_object_path}"
      end

      # Upload video
      video_file_extension = File.extname(blob_data.original_filename)
      video_file_extension = '.mp4' if video_file_extension == '.jpg'
      video_hash_key = Digest::MD5.hexdigest("#{blob_data.original_filename}_#{Time.now.to_i}")
      hashed_file_name = video_hash_key + video_file_extension.downcase

      video_s3_object_path = "assets01/#{hashed_file_name}"

      video_object = resource
                       .bucket('praja-raw-user-videos')
                       .object(video_s3_object_path)
      video_object.put(body: blob_data)

      if video_object && !video_object.exists?
        raise "Video object doesn't exist"
      else
        self.url = nil
        self.source_url = "https://ruv-cdn.thecircleapp.in/#{video_s3_object_path}"
        self.hash_key = video_hash_key
        self.thumbnail_url = thumbnail_url
        self.status = :uploaded
        self.service = :aws
      end
    rescue => e
      Honeybadger.notify(e)
      raise "వీడియో అప్‌లోడ్ విఫలమైంది!"
    end
  end

  def self.upload(video, thumbnail, user)
    if (user.is_a?(User) && [5,50].include?(user.id))
      upload_v2(video, thumbnail, user)
    else
      upload_v1(video, thumbnail, user)
    end
  end

  def self.get_s3_resource
    Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    )
  end

  def self.upload_v1(video, thumbnail, user)
    resource = get_s3_resource

    # Upload thumbnail
    file_extension = File.extname(thumbnail.original_filename)
    hashed_file_name = Digest::MD5.hexdigest("#{thumbnail.original_filename}_#{Time.now.to_i}") + file_extension.downcase

    s3_object_path = "#{Rails.env}/videos/thumbnails/#{user.class.name.downcase}/#{user.id}/#{hashed_file_name}"

    begin
      resource
        .bucket(Rails.application.credentials[:aws_s3_bucket_name])
        .object(s3_object_path)
        .put(body: thumbnail)

      thumbnail_url = "https://a-cdn.thecircleapp.in/#{s3_object_path}"

      # Upload video
      file_extension = File.extname(video.original_filename)
      file_extension = '.mp4' if file_extension == '.jpg'
      video_hash_key = Digest::MD5.hexdigest("#{video.original_filename}_#{Time.now.to_i}")
      hashed_file_name = video_hash_key + file_extension.downcase

      s3_object_path = "#{Rails.env}/videos/#{user.class.name.downcase}/#{user.id}/#{hashed_file_name}"

      begin
        resource
          .bucket(Rails.application.credentials[:aws_s3_bucket_name])
          .object(s3_object_path)
          .put(body: video)

        uploaded_video = new(
          url: "https://a-cdn.thecircleapp.in/#{s3_object_path}",
          source_url: "https://a-cdn.thecircleapp.in/#{s3_object_path}",
          hash_key: video_hash_key,
          thumbnail_url: thumbnail_url,
          user: user,
          status: :processed,
          service: :aws
        )

        uploaded_video if uploaded_video.save
      rescue StandardError => e
        nil
      end
    rescue StandardError => e
      nil
    end
  end

  def self.upload_v2(video, thumbnail, user)
    resource = get_s3_resource

    # Upload thumbnail
    file_extension = File.extname(thumbnail.original_filename)
    hashed_file_name = Digest::MD5.hexdigest("#{thumbnail.original_filename}_#{Time.now.to_i}") + file_extension.downcase

    s3_object_path = "#{Rails.env}/videos/thumbnails/#{user.class.name.downcase}/#{user.id}/#{hashed_file_name}"

    begin
      resource
        .bucket(Rails.application.credentials[:aws_s3_bucket_name])
        .object(s3_object_path)
        .put(body: thumbnail)

      thumbnail_url = "https://a-cdn.thecircleapp.in/#{s3_object_path}"

      # Upload video
      file_extension = File.extname(video.original_filename)
      file_extension = '.mp4' if file_extension == '.jpg'
      video_hash_key = Digest::MD5.hexdigest("#{video.original_filename}_#{Time.now.to_i}")
      hashed_file_name = video_hash_key + file_extension.downcase

      s3_object_path = "assets01/#{hashed_file_name}"

      begin
        resource
          .bucket('praja-raw-user-videos')
          .object(s3_object_path)
          .put(body: video)

        uploaded_video = new(
          source_url: "https://ruv-cdn.praja.buzz/#{s3_object_path}",
          thumbnail_url: thumbnail_url,
          hash_key: video_hash_key,
          user: user,
          status: :uploaded,
          service: :aws
        )

        uploaded_video if uploaded_video.save
      rescue StandardError => e
        nil
      end
    rescue StandardError => e
      nil
    end
  end

  def get_media_service_callback_url
    if service.to_sym == :azure
      Constants.media_service_callbacks_baseurl + "/videos/post-created"
    elsif service.to_sym == :gcp
      "https://media-service-api.thecircleapp.in/videos/post-created"
    end
  end
end
