class Hashtag < ApplicationRecord
  include Hashid::Rails

  has_many :hashtag_circles, -> { where(active: true) }
  has_many :hashtag_likes, -> { where(active: true) }
  has_many :circles, through: :hashtag_circles
  has_many :likes, class_name: "HashtagLike"
  has_many :post_hashtags, -> { where(active: true) }
  has_many :posts, through: :post_hashtags

  attribute :hashid
  attribute :feed_type
  attribute :feed_item_id
  attribute :feed_type_timestamp

  validate :validate_hashtag_identifier
  after_commit :queue_for_index

  #TRENDING_HASHTAGS_CACHE_KEY = 'trending_hashtags_v1'
  TRENDING_HASHTAGS_CACHE_KEY = 'trending_hashtags_new_v3'
  TRENDING_HASHTAGS_DURATION_IN_HOURS = 36

  HASHTAG_LIKES_COUNT_REDIS_KEY = "hashtag_likes_count"
  HASHTAG_OPINIONS_COUNT_REDIS_KEY = "hashtag_opinions_count"

  def queue_for_index
    IndexHashtag.perform_async(self.id)
  end

  def validate_hashtag_identifier
    if self.identifier != self.name.downcase
      errors.add(:name, "is invalid. Only case modification allowed!")
    end
  end

  def get_likes_count
    likes_count.to_i + $redis.hget(HASHTAG_OPINIONS_COUNT_REDIS_KEY, self.id).to_i + $redis.hget(HASHTAG_LIKES_COUNT_REDIS_KEY, self.id).to_i
  end

  def get_opinions_count
    opinions_count.to_i + $redis.hget(HASHTAG_OPINIONS_COUNT_REDIS_KEY, self.id).to_i
  end

  def whatsapp_count
    $redis.hvals("hashtag_whatsapp_shares_#{self.id}").map { |v| v.to_i }.sum
  end

  def update_likes_and_opinions_count!
    # Opinions count
    self.opinions_count = PostHashtag.where(hashtag: self, active: true).
      joins("INNER JOIN posts ON posts.id = post_hashtags.post_id").
      where(posts: { active: true }).
      count

    # Likes count
    opinion_likes_count = PostLike.
      joins("INNER JOIN posts p ON p.id = post_likes.post_id").
      joins("INNER JOIN post_hashtags ph ON ph.post_id = p.id").
      where("p.active = 1 AND post_likes.active = 1 AND ph.active = 1 AND ph.hashtag_id = #{self.id}").
      count

    self.likes_count = likes.count.to_i + opinion_likes_count.to_i + self.opinions_count.to_i

    self.save!
  end

  def opinions
    PostHashtag.where(hashtag: self, active: true)
               .joins("INNER JOIN posts ON posts.id = post_hashtags.post_id")
               .where(posts: { active: true })
               .map do |ph|
      ph.post
    end
  end

  def is_liked(user)
    likes.where(user: user).exists?
  end

  def user_circled
    false
  end

  def circles
    Circle
      .joins("INNER JOIN post_circles pc ON pc.circle_id = circles.id")
      .joins("INNER JOIN posts p ON p.id = pc.post_id")
      .joins("INNER JOIN post_hashtags ph ON ph.post_id = p.id")
      .joins("INNER JOIN hashtags h ON ph.hashtag_id = h.id")
      .where("circles.id != 0")
      .where("h.id = ?", self.id)
      .where(circle_type: :location)
      .group("circles.id")
      .all
  end

  # removed get circles from hashtag

  def self.get_trending_hashtags(user_id = nil)
    cache_key = TRENDING_HASHTAGS_CACHE_KEY
    Rails.cache.fetch(cache_key, expires_in: 1.hours) do
      get_trending_hashtags_based_on_query_version
    end
  end

  def self.get_trending_hashtags_query(hours)
    #trending hashtags v3 query
    {
      "fields": ["id", "name", "active", "created_at"],
      "_source": false,
      "query": {
        "function_score": {
          "query": {
            "bool": {
              "filter": [
                {
                  "range": {
                    "likes_count": {
                      "gte": 100
                    }
                  }
                },
                {
                  "range": {
                    "opinions_count": {
                      "gte": 5
                    }
                  }
                },
                {
                  "exists": {
                    "field": "post_trends_and_hashtag_position_info"
                  }
                }
              ]
            }
          },
          "script_score": {
            "script": {
              "lang": "painless",
              "source": "" "
                            if (params._source.post_user_ids.length < 5) {
                              return 0;
                            }

                            double currentTime = new Date().getTime();
                            float weightedTrends = 0;

                            for (trend_timestamp in params._source.trends) {
                              weightedTrends += (1 - Math.min(1, (currentTime - trend_timestamp) / (#{hours}*60*60*1000)));
                            }

                            for (post_details in params._source.post_trends_and_hashtag_position_info) {

                                double position = post_details.hashtag_position;
                                int no_of_trends = post_details.trends_time_stamps.length;
                                double position_weightage = position > 0 ? 0.1 : 0.9;
                                double post_trends_weightage = 0;

                                for(time_stamp in post_details.trends_time_stamps){
                                     post_trends_weightage += (1 - Math.min(1, (currentTime - time_stamp) / (#{hours}*60*60*1000)));
                                }

                                weightedTrends += post_trends_weightage*position_weightage;
                            }
                            for (post_timestamp in params._source.posts) {
                              weightedTrends += (1 - Math.min(1, (currentTime - post_timestamp) / (#{hours}*60*60*1000)));
                            }

                            return weightedTrends;
                          " ""
            }
          },
          "boost_mode": "replace",
          "min_score": 0.000001
        }
      },
      "sort": [
        { "_score": { "order": "desc" } },
        { "created_at": { "order": "desc" } }
      ],
      "size": 5,
      "from": 0
    }
    #trending hashtags query v2
    # {
    #   "query": {
    #     "function_score": {
    #       "query": {
    #         "bool": {
    #           "filter": [
    #             {
    #               "range": {
    #                 "likes_count": {
    #                   "gte": 100
    #                 }
    #               }
    #             },
    #             {
    #               "range": {
    #                 "opinions_count": {
    #                   "gte": 5
    #                 }
    #               }
    #             }
    #           ]
    #         }
    #       },
    #       "script_score": {
    #         "script": {
    #           "lang": "painless",
    #           "source": "" "
    #                   double currentTime = new Date().getTime();
    #                   float weightedTrends = 0;
    #
    #                   for (trend_timestamp in params._source.trends) {
    #                     weightedTrends += (1 - Math.min(1, (currentTime - trend_timestamp) / (#{hours}*60*60*1000)));
    #                   }
    #                   for (post_trend_timestamp in params._source.posts_trends) {
    #                     weightedTrends += (1 - Math.min(1, (currentTime - post_trend_timestamp) / (#{hours}*60*60*1000)));
    #                   }
    #                   for (post_timestamp in params._source.posts) {
    #                     weightedTrends += (1 - Math.min(1, (currentTime - post_timestamp) / (#{hours}*60*60*1000)));
    #                   }
    #
    #                   return weightedTrends;
    #                 " ""
    #         }
    #       },
    #       "boost_mode": "replace",
    #       "min_score": 0.000001
    #     }
    #   },
    #   "sort": [
    #     { "_score": { "order": "desc" } },
    #     { "created_at": { "order": "desc" } }
    #   ],
    #   "size": 5,
    #   "from": 0
    # }
  end

  def self.get_trending_hashtags_based_on_query_version
    hours = TRENDING_HASHTAGS_DURATION_IN_HOURS
    hashtags_es = ES_CLIENT.search index: EsUtil.get_hashtags_index, body: get_trending_hashtags_query(hours)
    if !hashtags_es['timed_out'] && !hashtags_es["hits"].nil? && hashtags_es["hits"]["total"]["value"] > 0
      return hashtags_es["hits"]["hits"].map do |hit|
        hashtag_feed = hit["fields"]

        hashtag_feed['id'] = hashtag_feed['id'].first
        hashtag_feed['name'] = hashtag_feed['name'].first
        hashtag_feed['active'] = hashtag_feed['active'].first
        hashtag_feed['created_at'] = Time.at(hashtag_feed['created_at'].first.to_f / 1000)
        hashtag_feed['feed_type'] = 'hashtag'
        hashtag_feed['feed_item_id'] = hashtag_feed['id'].to_s
        hashtag_feed['new_circles'] = []

        hashtag_feed
      end
    else
      return []
    end
  end

  # removed clustering circles and return circles
end
