class SubscriptionChargeRefund < ApplicationRecord
  include AASM
  has_paper_trail

  belongs_to :subscription_charge
  belongs_to :user

  # Validations
  validates :amount, presence: true, numericality: { greater_than: 0 }
  validate :amount_within_available_limit, on: :create

  after_create_commit :initiate_refund_worker


  enum status: {
    initiated: 'initiated',
    failed: 'failed',
    success: 'success'
  }

  enum reason: {
    manual: 'manual',
    downgrade: 'downgrade',
    extension: 'extension',
    initial_charge: 'initial_charge'
  }

  aasm column: :status, enum: true do
    state :initiated, initial: true
    state :failed
    state :success

    event :mark_as_failed do
      transitions from: :initiated, to: :failed
    end

    event :mark_as_success do
      transitions from: :initiated, to: :success, after: :after_success
    end
  end


  def initiate_refund_worker
    return if reason.to_sym == :initial_charge
    current_time = Time.zone.now
    # Schedule the refund worker based on the time of day
    if current_time < Time.zone.parse('18:00:00')
      trigger_at = Time.zone.today + 18.hours
      RefundSubscriptionCharge.perform_at(trigger_at, id)
    elsif current_time.between?(Time.zone.parse('18:00:00'), Time.zone.parse('21:00:00'))
      RefundSubscriptionCharge.perform_async(id)
    else
      trigger_at = Time.zone.today + 1.day + 18.hours
      RefundSubscriptionCharge.perform_at(trigger_at, id)
    end

  end

  # After a refund is marked as successful, update the subscription charge
  def after_success
    subscription_charge.process_refund_completion(self)
  end



  private

  # Validate that the refund amount doesn't exceed the available refund amount
  def amount_within_available_limit
    return unless subscription_charge && amount

    available_amount = subscription_charge.amount
    if amount > available_amount
      errors.add(:amount, "exceeds available refund amount of #{available_amount}")
    end
  end
end
