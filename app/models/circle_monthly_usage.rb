class CircleMonthlyUsage < ApplicationRecord
  has_paper_trail
  belongs_to :circle

  validates_presence_of :circle_id, :month_start, :month_end
  validate :check_month_end_greater_than_month_start
  after_create_commit :make_other_monthly_usages_inactive

  def check_month_end_greater_than_month_start
    if month_end.present? && month_start.present? && month_end <= month_start
      errors.add(:month_end, "can't be less than month start")
    end
  end

  def make_other_monthly_usages_inactive
    circle.circle_monthly_usages.where.not(id: id).update_all(active: false)
  end
end
