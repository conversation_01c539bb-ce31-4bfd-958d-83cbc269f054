class SubscriptionCharge < ApplicationRecord
  include AASM
  has_paper_trail
  belongs_to :subscription
  belongs_to :user

  has_one :user_plan, class_name: 'User<PERSON>lan', foreign_key: 'user_id', primary_key: 'user_id'

  before_create :set_pg_id
  after_commit :update_floww_lead_score, on: :create
  after_commit :sync_mixpanel_user

  validate :duplicate_charge_check, on: :create

  enum status: {
    created: 'created',
    sent_to_pg: 'sent_to_pg',
    success: 'success',
    refunded: 'refunded',
    failed: 'failed',
  }

  has_many :subscription_charge_refunds

  scope :success_ever, -> { where(status: [:success, :refunded]) }
  scope :special_offer, -> { where(charge_amount: [1, 29, 59]) }

  aasm column: :status, enum: true do
    state :created, initial: true
    state :sent_to_pg
    state :refunded
    state :success
    state :failed

    before_all_events :before_all_events

    event :success, after_commit: :after_success do
      transitions from: [:created, :sent_to_pg], to: :success
    end

    event :mark_as_sent_to_pg do
      transitions from: :created, to: :sent_to_pg, after: :send_to_pg
    end

    event :refund_complete do
      transitions from: :success, to: :refunded
    end

    event :fail, after_commit: :after_fail do
      transitions from: [:created, :sent_to_pg], to: :failed
    end
  end

  def before_all_events
    self.paper_trail_event = aasm.current_event
  end

  def is_success_ever?
    success? || refunded?
  end

  def may_initiate_refund?
    success? && amount > 0 && !is_trial_charge?
  end

  def set_pg_id
    self.pg_id = JuspayPaymentUtils.generate_pg_id if pg_id.blank?
  end

  def duplicate_charge_check
    if SubscriptionCharge.where(subscription_id: subscription_id, charge_date: charge_date).first.present?
      errors.add(:subscription_id, :invalid, message: "charge is already created for this subscription with the same charge date")
    end
  end

  def is_trial_charge?
    activated_metadatum = Metadatum.find_by(entity: subscription, key: Constants.subscription_activated_date_key)
    charge_amount == 1 &&
      (user_plan.nil? ||
        (activated_metadatum.present? && (activated_metadatum.created_at - user_plan.created_at).abs < 1.minute))
  end

  def reconcile
    if subscription.juspay?
      response = JuspayPaymentUtils.get_order_status(self.pg_id)
      Rails.logger.warn("JUSPAY RESPONSE: #{response}")

      if response['mandate'].present? &&
        response['mandate']['mandate_id'].present?
        # Save mandate data
        if subscription.pg_reference_id.blank?
          subscription.pg_json = response
          subscription.pg_reference_id = response['mandate']['mandate_id']
          subscription.save!
        end

        if JuspayPaymentUtils::ORDER_STATUS_SUCCESS_IDS.include?(response['status_id'].to_i) ||
          (JuspayPaymentUtils::ORDER_STATUS_AUTO_REFUND_IDS.include?(response['status_id'].to_i) &&
            self.is_trial_charge?)
          self.pg_json = response
          self.success! if self.may_success?
        elsif JuspayPaymentUtils::ORDER_STATUS_FAILURE_IDS.include?(response['status_id'].to_i) ||
          (JuspayPaymentUtils::ORDER_STATUS_AUTO_REFUND_IDS.include?(response['status_id'].to_i) &&
            !self.is_trial_charge?)
          self.pg_json = response
          self.fail! if self.may_fail?
        end
      end
    elsif subscription.cashfree?
      response = CashfreePaymentUtils.get_subscription_charge_status(self.pg_id, subscription.pg_id)
      Rails.logger.warn("CASHFREE RESPONSE: #{response}")

      if response['payment_status'] == 'SUCCESS'
        self.pg_json = response
        self.success! if self.may_success?
      elsif response['payment_status'] == 'FAILED' || response['payment_status'] == 'CANCELLED' || response['payment_status'] == 'VOID'
        self.pg_json = response
        self.fail! if self.may_fail?
      end
    end
  end

  def sync_mixpanel_user
    SyncMixpanelUser.perform_async(user_id)
  end

  def payment_cycle
    SubscriptionCharge.where(subscription_id: subscription_id, status: :success).where("charge_amount > 1").where.not(id: id).count + 1
  end

  def log_payment_mixpanel_event(event_name:, extra_params: {})

    params = {
      "MRR" => charge_amount / subscription.plan.duration_in_months,
      "subscription_type" => "autopay",
      "duration_in_months" => subscription.plan.duration_in_months,
      "payable_amount" => charge_amount,
      "plan_amount" => subscription.plan.amount,
      "discount_amount" => subscription.plan.total_amount - charge_amount,
      "mandate_charge_type" => payment_cycle == 1 ? "first_charge" : "recharge",
      "payment_cycle" => payment_cycle,
      "attempt_number" => attempt_number,
      "subscription_id" => subscription_id,
      "payment_gateway" => subscription.payment_gateway,
      "subscription_activated_date" => subscription.get_activated_date
    }
    EventTracker.perform_async(user_id, event_name, params.merge(extra_params))
  end

  def send_to_pg
    if subscription.cashfree?
      payload = {
        amount: charge_amount,
        scheduledOn: charge_date.strftime('%Y-%m-%d'),
        merchantTxnId: pg_id,
      }
      response = CashfreePaymentUtils.cashfree_post_v1("/subscriptions/#{subscription.pg_reference_id}/charge",
                                                       payload)
      if response['status'] == 'OK'
        # No callbacks, if any defined, should be triggered with this update.
        self.update_columns(pg_reference_id: response['payment']['paymentId'], pg_json: response)
        log_payment_mixpanel_event(event_name: 'payment_initiated_backend')
        return true
      else
        Honeybadger.notify('Subscription charge failed',
                           context: { user_id: user_id, subscription_charge_id: id, response: response })
        raise 'Unable to send to PG'
      end
    elsif subscription.juspay?
      payload = {
        'order.order_id': pg_id,
        'order.amount': charge_amount,
        'order.customer_id': user.get_juspay_customer_id,
        'order.currency': 'INR',
        'order.first_name': user.name,
        'order.customer_email': user.email.present? ? user.email : "user-#{user.id}@praja.buzz",
        'order.customer_phone': user.phone,
        'order.description': subscription.plan.name,
        'order.udf1': subscription.pg_id,
        merchant_id: 'praja',
        mandate_id: subscription.pg_reference_id,
        'mandate.execution_date': charge_date.to_i,
        format: 'json',
      }

      response = JuspayPaymentUtils.post('/txns', payload, false)

      if response['status'] == 'PENDING_VBV'
        self.update_columns(pg_reference_id: response['order_id'], pg_json: response)
        log_payment_mixpanel_event(event_name: 'payment_initiated_backend')
        return true
      elsif response['status'] == 'DUPLICATE_ORDER_ID'
        order_status_response = JuspayPaymentUtils.get_order_status(pg_id)
        self.update_columns(pg_reference_id: order_status_response['order_id'], pg_json: order_status_response)
        log_payment_mixpanel_event(event_name: 'payment_initiated_backend')
        return true
      else
        Honeybadger.notify('Subscription charge failed',
                           context: { user_id: user_id, subscription_charge_id: id, response: response })
        raise 'Unable to send to PG'
      end
    else
      raise 'Unsupported payment gateway'
    end
  end

  def trigger_ivr
    if self.attempt_number == 1 &&
      self.subscription.active? &&
      self.amount == 2399 &&
      user.is_ivr_experiment_user?
      TriggerIvr.perform_async(user_id)
    end
  end

  def is_user_has_premium_for_last_3_months(user_plan)
    duration_and_amount = SubscriptionUtils.get_subscription_duration_in_months_and_amount(user_id)
    duration = duration_and_amount[0].months
    return true if duration == 12.months

    # adding one more day to handle 31 day months case
    extra_days_to_be_added = (Time.zone.now.to_date - user_plan.end_date.to_date).to_i + 1.day

    date_to_be_checked = 3.months.ago - extra_days_to_be_added

    user_subscription_charges = SubscriptionCharge.where(subscription_id: subscription_id, status: :success, user_id: user_id)
                                                  .where('charge_amount > ?', 1)
                                                  .where('charge_date > ?', date_to_be_checked)
                                                  .order(charge_date: :desc)

    if duration == 1.month && user_subscription_charges.count < 3
      return false
    elsif duration == 3.months && user_subscription_charges.count <= 1
      return false
    end

    true
  end

  # executed after commit
  def after_success
    grace_period_start_date = user.metadatum.find_by(key: Constants.grace_period_given_string)&.value
    if subscription.may_activate?
      subscription.activate!
    elsif subscription.may_unhold?
      subscription.unhold!
    end

    self.update(success_at: Time.zone.now)

    up = user_plan

    # Check if user is in subscription extension and initiate refund if needed
    if up.present? && user.in_subscription_extension? && charge_date < up.end_date && payment_cycle != 1
      # Initiate refund for the payment
      if may_initiate_refund?
        initiate_refund(charge_amount, reason: :extension)
      end

      # Log mixpanel event for payment during extension
      log_payment_mixpanel_event(event_name: 'payment_during_extension_backend', extra_params: {
        "refund_initiated": true,
        "user_plan_in_extension": true
      })

      # Return early as we don't want to process the rest of the after_success logic
      return
    elsif subscription.downgraded_date.present? && charge_amount > up.amount
      # Initiate refund for the payment
      if may_initiate_refund?
        initiate_refund(charge_amount - subscription.plan.amount, reason: :downgrade)
      end
      # Log mixpanel event for payment during downgrade
      log_payment_mixpanel_event(event_name: 'payment_during_downgrade_backend', extra_params: {
        "refund_initiated": true,
        "user_plan_in_downgrade": true
      })
    end


    if up.blank? && is_trial_charge?

      trial_duration = Metadatum.get_user_trail_duration(user_id: user_id)

      if trial_duration == 0
        Honeybadger.notify('Trial duration not found', context: { user_id: user_id, subscription_charge_id: id })
      end

      end_date = (Time.zone.now + (trial_duration - 1).days).end_of_day
      up = UserPlan.new(user_id: user_id,
                        plan_id: subscription.plan_id,
                        end_date: end_date,
                        amount: subscription.plan.amount,
                        source: "charge-#{id}")
      up.user_plan_logs.build(user_id: user_id,
                              plan_id: subscription.plan_id,
                              start_date: Time.zone.now.beginning_of_day,
                              end_date: end_date,
                              entity: self,
                              active: true)
      up.save!

      Metadatum.create(entity: user, key: Constants.user_poster_trial_start_date_key, value: Time.zone.today)
      # delete all the previous records of the user in zset of floww lead score of last trial attempt
      $redis.zrem(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, user.id)

      # update floww lead score for user after trial start
      UpdateLeadScore.set(queue: :critical).perform_async(user.id)
    end

    if up.blank?
      Honeybadger.notify('User plan not found', context: { user_id: user_id, subscription_charge_id: id })
      return
    end

    # TODO: Figure out Add-on related flow here
    unless is_trial_charge?
      new_start_date = up.get_new_start_date(grace_period_start_date: grace_period_start_date)
      up.amount = subscription.plan.amount
      up.plan_id = subscription.plan_id if up.plan_id != subscription.plan_id
      new_end_date = (new_start_date + subscription.plan.duration_in_months.months).end_of_day
      up.end_date = new_end_date
      up.source = "charge-#{id}"
      up.user_plan_logs.build(user_id: user_id,
                              plan_id: subscription.plan_id,
                              start_date: new_start_date.beginning_of_day,
                              end_date: new_end_date,
                              entity: self,
                              active: true)
      up.save!


      user_referral = UserReferral.find_by(referred_user_id: user_id, status: :created)
      user_referral.redeem! if user_referral.present?

      user.remove_special_offer_for_user

      last_successful_charge_excl_current = SubscriptionCharge.success_ever.where.not(id: id)
                                                              .where(user: user)
                                                              .last
      if last_successful_charge_excl_current.present?
        is_upgrade = last_successful_charge_excl_current.subscription.plan.duration_in_months < subscription.plan.duration_in_months
      end

      log_payment_mixpanel_event(event_name: 'payment_done_backend', extra_params: {
        is_upgrade: is_upgrade
      })

      # to track revenue in mixpanel
      MixpanelUserRevenueTracker.perform_async(user_id, charge_amount, {
        "$time" => Time.zone.now,
      })
    end

    if is_user_has_premium_for_last_3_months(up) || (grace_period_start_date.present? && ((Time.zone.parse(grace_period_start_date).advance(months: Subscription::GRACE_PERIOD_IN_MONTHS)) >= Time.zone.now))
      count = user.metadatum.find_by(key: Constants.grace_period_given_count_string)&.value&.to_i || 0
      if count > 0
        user.metadatum.where(key: Constants.grace_period_given_count_string).update(value: count - 1)
      end
    end

    if user.common_upgrade_package_conditions_met?
      user.mark_as_to_be_shown_upgrade_package_sheet
      user.delete_upgrade_package_nudge_count
    end
  end

  def after_fail
    return if SubscriptionCharge.where(subscription_id: subscription_id).first.id == self.id

    up = user_plan

    # Check if user is in subscription extension
    if up.present? && user.in_subscription_extension?
      # Log mixpanel event for failed payment during extension
      log_payment_mixpanel_event(event_name: 'payment_failed_during_extension', extra_params: {
        "user_plan_in_extension": true,
        "grace_period_not_added": true
      })

      # Return early as we don't want to add grace period for failed payments during extension
      return
    end

    subscription.handle_charge_payment_failure(last_failed_subscription_charge: self)
  end

  def initiate_refund(amount, reason: :manual)
    return false unless can_refund_amount?(amount)

    # Create a refund record
    subscription_charge_refunds.create!(
      user: user,
      amount: amount,
      status: :initiated,
      pg_id: pg_id,
      reason: reason
    )
  end

  # Check if a refund can be processed for the given amount
  def can_refund_amount?(refund_amount)
    return false unless success?
    return false if refund_amount <= 0
    return false if refund_amount > amount
    return false if (charge_amount - subscription_charge_refunds.where.not(status: :failed).sum(:amount)) < refund_amount
    true
  end

  def handle_initial_payment_refund
    if subscription.subscription_charges.count == 1 && subscription.subscription_charges.first.is_trial_charge?
      # Check if a refund already exists for this charge with the same amount
      existing_refund = subscription_charge_refunds.find_by(
        amount: charge_amount,
        reason: :initial_charge
      )

      # Only create a new refund if one doesn't already exist
      unless existing_refund
        refund = subscription_charge_refunds.create!(
          user: user,
          amount: charge_amount,
          status: :initiated,
          pg_json: pg_json,
          pg_id: pg_id,
          reason: :initial_charge
        )
        refund.mark_as_success! if refund.may_mark_as_success?
      end
    end
  end

  # Process a refund completion
  def process_refund_completion(refund)
    total_refunded_amount = refunded_amount + refund.amount
    update!(refunded_amount: total_refunded_amount, amount: charge_amount - total_refunded_amount)
    log_payment_mixpanel_event(event_name: 'payment_refunded_backend', extra_params: {
      refund_amount: refund.amount,
      refund_reason: refund.reason,
      refund_id: refund.id,
    }) unless is_trial_charge?
    # If fully refunded, mark the charge as refunded
    if total_refunded_amount == charge_amount
      refund_complete! if may_refund_complete?
    end
  end

  # check if the user's last recharge failed for the subscription
  def self.last_recharge_failed?(subscription_id)
    subscription_charge = SubscriptionCharge.where(subscription_id: subscription_id).last
    if subscription_charge.present?
      if subscription_charge.failed?
        return true
      elsif subscription_charge.created? or subscription_charge.sent_to_pg?
        return SubscriptionCharge.where(subscription_id: subscription_id).second_to_last&.failed?
      end
      return false
    end
  end

  private

  def update_floww_lead_score
    return if SubscriptionUtils.has_user_ever_subscribed?(user.id)

    # delete all the previous records of the user in zset of floww lead score of last trial attempt
    $redis.zrem(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, user.id)
    UpdateLeadScore.set(queue: :critical).perform_async(user.id)
    # next time run at after 1 hour
    $redis.zadd(Constants.update_floww_lead_score_based_on_last_trial_attempt_key, Time.zone.now.to_i + 1.hour.to_i,
                user.id)
  end
end
