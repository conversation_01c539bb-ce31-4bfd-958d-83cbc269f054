class UserCircle < ApplicationRecord
  belongs_to :user
  belongs_to :circle, -> { where(active: true) }
  # get the circle irrespective of active or inactive
  belongs_to :circle_with_inactive, class_name: 'Circle', foreign_key: 'circle_id'

  JOIN_TYPES = %i[auto sub_circle protocol]
  enum source_of_join: JOIN_TYPES, _suffix: true

  # callbacks are called in FILO order
  after_commit :index_for_search, :update_affiliated_party_on_user, :update_user_mix_panel_data, :revaluate_sub_circles_of_user
  after_create_commit :auto_follow_circle_owner, :join_callback_to_dm, :increment_circle_members_count_in_redis
  after_destroy_commit :un_follow_owner_if_auto_followed, :un_join_callback_to_dm, :decrement_circle_members_count_in_redis

  def increment_circle_members_count_in_redis
    $redis.hincrby(Constants.circle_members_count_redis_key, self.circle_id, 1)
  end

  def decrement_circle_members_count_in_redis
    $redis.hincrby(Constants.circle_members_count_redis_key, self.circle_id, -1)
  end

  def revaluate_sub_circles_of_user
    UserSubCirclesRevaluation.perform_async(self.user_id) if self.circle.eligibile_for_sub_circle_flow?
  end

  def index_for_search
    user.index_for_search(true)
  end

  def update_affiliated_party_on_user
    user.update_affiliated_party_circle_id
  end

  # def update_members_count
  #   UpdateCircleMembersCount.perform_async(circle.id)
  # end

  def update_user_mix_panel_data
    user.send_to_mixpanel
  end

  def auto_follow_circle_owner
    if circle.check_circle_level_to_perform_auto_actions
      AutoFollowCircleOwner.perform_async(self.circle_id, self.user_id)
    end
  end

  def un_follow_owner_if_auto_followed
    if circle.check_circle_level_to_perform_auto_actions
      AutoUnFollowCircleOwner.perform_async(self.circle_id, self.user_id)
    end
  end

  def join_callback_to_dm
    if circle.channel_conversation_type?
      DmUtil.send_user_join_callback_to_dm_service(circle_id, user_id)
    end
  end

  def un_join_callback_to_dm
    if circle_with_inactive.channel_conversation_type?
      DmUtil.send_user_unjoin_callback_to_dm_service(circle_id, user_id)
    end
  end
end
