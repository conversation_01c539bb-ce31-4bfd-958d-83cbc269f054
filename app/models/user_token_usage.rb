class UserTokenUsage < ApplicationRecord
  belongs_to :user_token, optional: true
  belongs_to :user
  after_create_commit :record_last_online_time

  def record_last_online_time
    return if SubscriptionUtils.has_user_ever_subscribed?(user.id)

    # delete all the previous records of the user in zset of users_next_run_at
    $redis.zrem(Constants.update_floww_lead_score_based_on_last_online_key, user_id)
    UpdateLeadScore.perform_async(user_id)
    # next time run at after 1 hour
    $redis.zadd(Constants.update_floww_lead_score_based_on_last_online_key, Time.zone.now.to_i + 1.hour.to_i, user_id)
  end
end
