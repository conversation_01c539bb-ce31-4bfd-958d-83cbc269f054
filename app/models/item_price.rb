class ItemPrice < ApplicationRecord
  belongs_to :product, optional: true, foreign_key: :item_id
  belongs_to :frame, optional: true, foreign_key: :item_id
  belongs_to :item, polymorphic: true

  def self.get_frames_price(frame_ids, duration)
    item_prices = ItemPrice.joins(:frame).where(frames: { id: frame_ids }, item_prices: { duration_in_months: duration,
                                                                                          active: true })
    return [] if item_prices.blank?

    item_prices.map { |item_price| item_price.price.to_i + item_price.maintenance_price.to_i }
  end

  def self.create_item_prices_for_frames(frame_ids)
    frames = Frame.where(id: frame_ids)
    item_prices = []
    # create object for 6 months and 12 months
    frames.each do |frame|
      if frame.frame_type == 'premium'
        item_prices << ItemPrice.new(item: frame, price: Constants.premium_frame_6_month_price, maintenance_price: 0, duration_in_months: 6)
        item_prices << ItemPrice.new(item: frame, price: Constants.premium_frame_12_month_price, maintenance_price: 0, duration_in_months: 12)
      elsif frame.frame_type == 'status'
        item_prices << ItemPrice.new(item: frame, price: Constants.status_frame_6_month_price, maintenance_price: 0, duration_in_months: 6)
        item_prices << ItemPrice.new(item: frame, price: Constants.status_frame_12_month_price, maintenance_price: 0, duration_in_months: 12)
      end
    end
    ItemPrice.import(item_prices, on_duplicate_key_ignore: true, batch_size: 100) if item_prices.present?
  end
end
