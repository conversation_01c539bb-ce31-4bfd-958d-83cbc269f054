# frozen_string_literal: true

class BlockedUser < ApplicationRecord
  belongs_to :user
  belongs_to :blocked_user, class_name: 'User'
  after_destroy_commit :on_destroy_remove_blocked_user_ids
  after_create_commit :on_create_commit_cache_blocked_user_ids
  validates_uniqueness_of :blocked_user_id, scope: :user_id

  enum active: { active: true, deleted: false }

  def on_create_commit_cache_blocked_user_ids
    $redis.sadd(Constants.get_user_blocked_ids_key(user_id), blocked_user_id)
    $redis.sadd(Constants.get_user_blocked_by_ids_key(blocked_user_id), user_id)
    $redis.expireat(Constants.get_user_blocked_ids_key(user_id), (Time.zone.now.end_of_day + Constants.blocked_users_cache_expiry_in_days.days).to_i)
    $redis.expireat(Constants.get_user_blocked_by_ids_key(blocked_user_id), (Time.zone.now.end_of_day + Constants.blocked_users_cache_expiry_in_days.days).to_i)
  end

  def on_destroy_remove_blocked_user_ids
    $redis.srem(Constants.get_user_blocked_ids_key(user_id), blocked_user_id)
    $redis.srem(Constants.get_user_blocked_by_ids_key(blocked_user_id), user_id)
  end

end
