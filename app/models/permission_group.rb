class PermissionGroup < ApplicationRecord
  has_many :permission_group_permissions
  validates_presence_of :permission_group_permissions
  validates :name, format: { with: /\A[a-zA-Z_]+\z/, message: "only allows letters with under_score" },
            presence: true, allow_blank: false

  WEB_APP_ENABLED_GROUPS = [:owner]

  accepts_nested_attributes_for :permission_group_permissions, allow_destroy: true

  def self.get_permission_data_for_post(permission, circle_id, user, post_id)
    circle = Circle.find circle_id
    permission_id = PermissionGroupPermission.permission_identifiers[permission]
    case true
    when permission == PermissionGroupPermission.permission_identifiers.key(0)
      {
        id: permission_id,
        image: circle.photo&.url.present? ? Photo.get_compressed_photo_url(circle.photo&.url) : nil,
        option_text: "పోస్ట్ నుండి సర్కిల్ ట్యాగ్‌ని తీసివేయండి",
        end_point: "/posts/#{post_id}/remove-tag",
        confirm_pop_up: {
          confirm_text: "ట్యాగ్‌ను తీసివేయడం వలన పోస్ట్ #{circle.name} ఫీడ్‌లో ప్రదర్శించబడకుండా తీసివేయబడుతుంది",
          cancel_btn_text: "వద్దు",
          confirm_btn_text: "ట్యాగ్‌ని తీసివేయండి"
        },
        data: {
          circle_id: circle.id
        }
      }
    else
      # type code here
    end
  end

end
