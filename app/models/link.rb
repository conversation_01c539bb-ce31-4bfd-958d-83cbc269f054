class Link < ApplicationRecord
  belongs_to :user

  def self.extract_preview_info(url, user)
    if url != ""
      link = Link.where(url: url, active: true).first
      if link.nil?
        begin
          request_uri = URI("http://api.linkpreview.net/?key=#{Rails.application.credentials[:link_preview_key]}&q=#{url}")
          response = Net::HTTP.get_response(request_uri)

          if response.code.to_i == 200
            body = ActiveSupport::JSON.decode(response.body)
            link = Link.create(user: user, title: body['title'], description: body['description'], image: body['image'], url: body['url'])

            return link unless link.nil?
          end
        rescue => exception
          return nil
        end
      else
        return link
      end
    end

    return nil
  end
end
