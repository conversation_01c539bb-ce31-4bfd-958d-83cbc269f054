require 'slack-notifier'

class Poster < ApplicationRecord
  belongs_to :admin_user
  belongs_to :circle

  enum poster_type: %i[normal frame], _suffix: true

  attr_accessor :circle_ids
  has_many :poster_photos
  validates_presence_of :name, :start_time, :end_time, :poster_photos
  validates :poster_type, inclusion: { in: poster_types.keys }, allow_nil: false
  accepts_nested_attributes_for :poster_photos

  POSTER_FEED_ITEM_ID = 'poster'

  def self.ping_to_slack(message)
    notifier = Slack::Notifier.new "*******************************************************************************"

    notifier.ping message
  end

  def seen_check(user)
    is_user_seen = $redis.sismember("poster_share_card_#{id}", user.id)
    return is_user_seen
  end

  def get_gradients_v1(user)
    tool_tip_color = 0xff606060
    tool_tip_text_color = 0xffFFFFFF

    circle_id = circle.political_leader_level? ?
                  CirclesRelation.where(first_circle_id: self.circle_id, active: true, relation: 'Leader2Party').first&.second_circle_id : self.circle_id
    case circle_id
    when 31403 # YCP
      background_colors = [0xff002090, 0xff214DFF, 0xff5A7DFF, 0xff5379FE, 0xff6BBD6B, 0xff45BB45, 0xff003500]
      background_color_stops = [0.04, 0.13, 0.26, 0.35, 0.61, 0.6, 0.95]
      badge_banner_colors = [0xff055e18, 0xff0b9d08, 0xff055e08]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9306]
      footer_background_color = 0xff263166
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    when 31402 # TDP
      background_colors = [0xffFFED4F, 0xffFFFF60, 0xffE78F28, 0xffFFFF60, 0xffF9CE54]
      background_color_stops = [0.0, 0.28, 0.52, 0.65, 0.83]
      badge_banner_colors = [0xfff5b607, 0xfff5b607]
      badge_banner_color_stops = nil
      footer_background_color = 0xffFEF25D
      footer_text_color = 0xff000000
      badge_text_color = 0xff000000
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    when 31406 # Janasena
      background_colors = [0xffBE382E, 0xffCD564D, 0xffFEC9C5, 0xffF9E3E2, 0xff000000]
      background_color_stops = [0.08, 0.56, 0.77, 0.79, 1]
      badge_banner_colors = [0xff8d0d04, 0xffd81305, 0xff8d0d04]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9543]
      footer_background_color = 0xffBE382E
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    when 31401, 37967 # INC TG || INC AP
      background_colors = [0xffF19E4B, 0xffDBD7D4, 0xffF8F8F8, 0xff359B44]
      background_color_stops = [0.18, 0.33, 0.59, 1]
      badge_banner_colors = [0xff055e18, 0xff0b9d08, 0xff055e08]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9306]
      footer_background_color = 0xFF42A150
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    when 31398, 37788 # BJP TG || BJP AP
      background_colors = [0xffFF6A01, 0xffFF984F, 0xffFED5B7, 0xffF16705]
      background_color_stops = [0.04, 0.25, 0.57, 1.0]
      badge_banner_colors = [0xff7D2F03, 0xffBC5205, 0xff5E0F05]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9306]
      footer_background_color = 0xffEF6402
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    when 31405 # TRS
      background_colors = [0xffEC4E9B, 0xffFFCAEC, 0xffEC4E9B]
      background_color_stops = [0.04, 0.51, 0.93]
      badge_banner_colors = [0xffA00451, 0xffEC4E9C, 0xff9b044f]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9306]
      footer_background_color = 0xffA00451
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0
    else
      # User
      background_colors = [0xff3F89FF, 0xffDBD7D4, 0xffF8F8F8, 0xff3F89FF]
      background_color_stops = [0.2175, 0.4383, 0.5067, 0.6871]
      badge_banner_colors = [0xff05305E, 0xff3490F3, 0xff05305E]
      badge_banner_color_stops = [0.0472, 0.3968, 0.9306]
      footer_background_color = 0xff263166
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 0.0, -1.0, 0.0, 1.0

    end
    {
      footer_color: footer_background_color,
      footer_text_color: footer_text_color,
      badge_text_color: badge_text_color,
      gradient_direction: {
        begin_x: gradient_begin_x,
        begin_y: gradient_begin_y,
        end_x: gradient_end_x,
        end_y: gradient_end_y,
      },
      upload_tool_tip_color: tool_tip_color,
      upload_tool_tip_text_color: tool_tip_text_color,
      background_gradients: {
        colors: background_colors,
        stops: background_color_stops,
      },
      badge_banner_gradients: {
        colors: user.get_badge_role&.role&.show_badge_banner? ? badge_banner_colors : [0x00ffffff, 0x00ffffff],
        stops: user.get_badge_role&.role&.show_badge_banner? ? badge_banner_color_stops : [0, 1],
      }
    }
  end

  def get_share_text(user)
    link = Singular.shorten_link(
      "https://prajaapp.sng.link/A3x5b/5oqr?_dl=praja%3A%2F%2Fbuzz.praja.app/posters&_ddl=praja%3A%2F%2Fbuzz.praja.app/posters&paffid=#{user.id}",
      1.month
    )

    share_text = "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు." + "\n" +
      "తెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇" + "\n" +
      "#{link}"

    share_text
  end

  def poster_share_card_text(user)
    tip_text = "మీ ప్రొఫైల్ ఫోటోను జోడించండి"
    {
      photo_upload_tool_tip: tip_text,
      share_text: get_share_text(user),
    }
  end

  def get_poster_photo_and_ring_color(user)
    poster_photos_info = poster_photos.all.map { |poster_photo| [poster_photo.photo_orientation, poster_photo.photo.url] }

    index = user.id % poster_photos_info.count
    poster_photo_info = poster_photos_info[index]

    leader_photo_rings = self.poster_photos.map(&:leader_photo_ring_color)
    leaders_photo_ring_color = leader_photo_rings[index].to_sym == :dark ? 0xff000000 : 0xffFFFFFF

    return poster_photo_info, leaders_photo_ring_color
  end

  def get_leader_photo_urls(app_version, leader_circle)
    leaders_photo_urls = []

    if app_version > Gem::Version.new('1.16.0')
      leaders_photo_urls = if leader_circle.present? || self.circle.political_leader_level?
                             c = leader_circle || self.circle
                             political_party = CirclesRelation.where(first_circle: c, active: true, relation: 'Leader2Party').first&.second_circle
                             leader_photos = political_party.present? ? political_party.get_circle_photos : []
                             leader_photos << c.photo&.url unless [30898, 30841, 31411, 31190, 31263].include?(c.id)
                             leader_photos.map { |cp| cp&.gsub('https://cdn.thecircleapp.in/', 'https://a-cdn.thecircleapp.in/120x120/') }.compact
                           elsif self.circle.political_party_level?
                             self.circle.get_circle_photos
                           else
                             []
                           end
    end

    return leaders_photo_urls
  end

  def get_poster_hash(user, app_version, skip_seen_check = false, leader_circle = nil)
    return if !skip_seen_check && seen_check(user)

    leaders_photo_urls = get_leader_photo_urls(app_version, leader_circle)
    poster_photo_info, leader_photo_ring_color = get_poster_photo_and_ring_color(user)

    user.badge = user.get_badge_role&.get_json

    poster = {
      id: id,
      user: user,
      photo_url: poster_photo_info.last,
      frame_upload_info: "",
      frame_upload_button_text: "ఫోటో జోడించండి",
      poster_variant: get_poster_variant(poster_type, poster_photo_info.first),
      leaders_photo_urls: leaders_photo_urls,
      leader_photo_ring_color: leader_photo_ring_color,
      share_card_text: poster_share_card_text(user),
      gradients: app_version > Gem::Version.new('1.14.5') ? circle.get_gradients_v2(user) : get_gradients_v1(user)
    }

    return poster
  end

  def get_posters_hash_with_all_photos(user, app_version, leader_circle)
    leaders_photo_urls = get_leader_photo_urls(app_version, leader_circle)

    user.badge = user.get_badge_role&.get_json
    user.phone = user.generate_random_phone

    posters = poster_photos.map { |poster_photo| {
      id: id,
      user: user,
      photo_url: poster_photo.photo.url,
      frame_upload_info: "",
      frame_upload_button_text: "ఫోటో జోడించండి",
      poster_variant: get_poster_variant(poster_type.to_sym, poster_photo.photo_orientation&.to_sym),
      leaders_photo_urls: leaders_photo_urls,
      leader_photo_ring_color: poster_photo.leader_photo_ring_color.to_sym == :dark ? 0xff000000 : 0xffFFFFFF,
      share_card_text: poster_share_card_text(user),
      gradients: circle.get_gradients_v2(user)
    } }

    posters
  end

  def get_poster_variant(poster_type, photo_orientation)
    case [poster_type, photo_orientation]
    when [:frame, :portrait]
      'PORTRAIT_FRAME'
    when [:frame, :landscape]
      'LANDSCAPE_FRAME'
    else
      'NORMAL'
    end
  end

end
