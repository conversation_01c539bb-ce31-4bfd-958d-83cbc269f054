class UserReferral < ApplicationRecord
  include AASM
  has_paper_trail

  belongs_to :user
  belongs_to :referred_user, class_name: 'User', foreign_key: 'referred_user_id'

  enum status: { created: 'created', redeemed: 'redeemed' }

  aasm column: :status, enum: true do
    state :created, initial: true
    state :redeemed

    event :redeem do
      transitions from: :created, to: :redeemed, after: :after_redeem
    end
  end

  def after_redeem
    grace_period_start_date = user.metadatum.find_by(key: Constants.grace_period_given_string)&.value
    subscription = Subscription.where(user_id: user_id, status: [:active, :on_hold]).last
    if subscription.present?
      if subscription.may_activate?
        subscription.activate!
      elsif subscription.may_unhold?
        subscription.unhold!
      end
    end
    up = UserPlan.find_by(user_id: user_id)
    if up.present?
      new_start_date = up.get_new_start_date(grace_period_start_date: grace_period_start_date)
      new_end_date = (new_start_date + 1.month).end_of_day
      up.end_date = new_end_date
      up.source = "referral-#{id}"
      up.user_plan_logs.build(user_id: user_id, plan_id: up.plan_id,
                              start_date: new_start_date.beginning_of_day,
                              end_date: new_end_date, entity: self, active: true)
      up.save!
    end
  end
end
