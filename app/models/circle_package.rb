class CirclePackage < ApplicationRecord
  has_paper_trail

  has_many :circle_package_mappings
  has_many :circle_monthly_usages

  validates_presence_of :name
  validates_uniqueness_of :name
  validate :check_enable_flags_for_limits
  before_save :set_default_limits_if_disabled

  def check_enable_flags_for_limits
    if !enable_channel && channel_post_msg_limit.present?
      errors.add(:channel_post_msg_limit, "can't assign limit without enable channel")
    end
    if !enable_fan_posters && fan_poster_creatives_limit.present?
      errors.add(:fan_poster_creatives_limit, "can't assign limit without enable fan posters")
    end
  end

  def set_default_limits_if_disabled
    if !enable_channel && channel_post_msg_limit.blank?
      self.channel_post_msg_limit = 0
    end
    if !enable_channel && channel_post_msg_notification_limit.blank?
      self.channel_post_msg_notification_limit = 0
    end
    if !enable_fan_posters && fan_poster_creatives_limit.blank?
      self.fan_poster_creatives_limit = 0
    end
  end
end
