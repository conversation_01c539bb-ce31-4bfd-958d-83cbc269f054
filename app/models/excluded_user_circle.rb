class ExcludedUserCircle < ApplicationRecord
  enum conversation_type: Constants.conversation_types, _suffix: true
  belongs_to :user
  belongs_to :circle
  belongs_to :excluded_by_user, class_name: 'User', foreign_key: 'excluded_by_user_id'
  after_create_commit :leave_conversation_callback_to_dm
  after_destroy_commit :join_conversation_callback_to_dm

  def join_conversation_callback_to_dm
    if circle.channel_conversation_type?
      DmUtil.send_user_join_callback_to_dm_service(circle_id, user_id)
    end
  end

  def leave_conversation_callback_to_dm
    if circle.channel_conversation_type?
      DmUtil.send_user_unjoin_callback_to_dm_service(circle_id, user_id)
    end
  end
end
