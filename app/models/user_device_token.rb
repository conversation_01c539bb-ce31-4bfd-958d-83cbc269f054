class UserDeviceToken < ApplicationRecord
  belongs_to :user, optional: true
  has_many :user_device_group_tokens, -> { where active: true }
  has_one :user_device_group

  after_commit :sync_user_in_mixpanel

  belongs_to :mapped_device, primary_key: :id, foreign_key: :mapped_device_id, class_name: 'Device', optional: true

  def user_device_group
    user_device_group_tokens.last
  end

  def sync_user_in_mixpanel
    SyncMixpanelUser.perform_async(self.user_id) if self.user_id.present?
  end
end
