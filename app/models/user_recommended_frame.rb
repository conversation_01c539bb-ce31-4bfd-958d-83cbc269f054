class UserRecommendedFrame < ApplicationRecord
  belongs_to :user, -> { where active: true }
  belongs_to :frame, -> { where active: true }
  validates :user_id, uniqueness: { scope: :frame_id }

  def self.recommended_frames(user:, exclude_frame_ids: [])
    recommended_frames = self.where(user: user).where.not(frame_id: exclude_frame_ids).map(&:frame)
    family_frame = Frame.where(frame_type: :family_frame_premium, identity_type: :gold_lettered_user, active: true)
                        .last
    hero_frame = Frame.where(frame_type: :hero_frame_premium, identity_type: :top_trapezoidal_identity, active: true)
                      .last
    recommended_frames << family_frame if family_frame.present?
    recommended_frames << hero_frame if hero_frame.present?
    recommended_frames
  end
end
