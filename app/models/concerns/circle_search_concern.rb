module CircleSearchConcern
  extend ActiveSupport::Concern

  VILLAGE_MATCH_BOOST = 4
  MANDAL_MATCH_BOOST = 2
  MLA_CONSTITUENCY_MATCH_BOOST = 2
  MP_CONSTITUENCY_MATCH_BOOST = 2
  DISTRICT_MATCH_BOOST = 2
  STATE_MATCH_BOOST = 1
  PARTY_MATCH_BOOST = 32
  LEADER_MATCH_BOOST = 16
  NAME_MATCH_BOOST = 5
  LOCATION_CIRCLE_BOOST = 0.0001
  PARTY_CIRCLE_BOOST = 32
  MEMBERS_COUNT_BOOST = 0.01
  GOLD = "gold"

  included do
    searchkick index_name: -> { EsUtil.get_index_name("#{name.tableize}_v2") }, callbacks: :queue, mappings: {
      "properties": {
        "active": {
          "type": "keyword"
        },
        "affiliated_political_party_id": {
          "type": "keyword"
        },
        "circle_type": {
          "type": "keyword"
        },
        "district_id": {
          "type": "keyword"
        },
        "level": {
          "type": "keyword"
        },
        "mandal_id": {
          "type": "keyword"
        },
        "members_count": {
          "type": "rank_feature"
        },
        "mla_constituency_id": {
          "type": "keyword"
        },
        "mp_constituency_id": {
          "type": "keyword"
        },
        "name": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "name_en": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "short_name": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "political_party": {
          "type": "keyword"
        },
        "state_id": {
          "type": "keyword"
        },
        "village_id": {
          "type": "keyword"
        },
        "photo_id": {
          "type": "keyword"
        },
        "has_poster_photo": {
          "type": "boolean"
        }
      }
    }
  end

  def search_data
    data = search_entity_obj
    if self.location_circle_type?
      self.get_location_circle_data.each do |k, v|
        data[k.to_sym] = v
      end
    end
    if data[:members_count].to_i == 0
      data[:members_count] = 0.000001
    end
    data.delete(:photo_url) if data[:photo_url].present?
    data.delete('photo_url') if data['photo_url'].present?
    data
  end

  class_methods do
    def search_circles(user:, query:, offset:, count:, search_type: nil)
      if search_type == :layout_search
        must = [
          {
            term: {
              has_poster_photo: true
            }
          }
        ]

        should = [
          {
            terms: {
              circle_type: %w[business interest]
            }
          }
        ]

        minimum_should_match = 1
        must_not_query = []
      else
        must = [
          {
            terms: {
              level: %w[village mandal municipality corporation district state political_leader political_party media]
            }
          }
        ]

        should = []
        minimum_should_match = 0
        must_not_query = [{ "term": { "active": false } }]
      end

      party_match_queries = []
      user.get_user_joined_party_circle_ids.each do |party_id|
        party_match_queries << {
          match: {
            affiliated_political_party_id: {
              query: party_id,
              boost: CircleSearchConcern::PARTY_MATCH_BOOST
            },
          }
        }
      end

      circles = Circle.search(
        body: {
          _source: ["id"],
          query: {
            bool: {
              must: [
                {
                  bool: {
                    must: must,
                    should: should,
                    minimum_should_match: minimum_should_match
                  },
                },
                {
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        "match_bool_prefix": {
                          "name": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "name_en": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "short_name": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "name_en": {
                            "query": query,
                            "boost": CircleSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "name": {
                            "query": query,
                            "boost": CircleSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "short_name": {
                            "query": query,
                            "boost": CircleSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "name": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "name_en": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "short_name": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      }
                    ]
                  }
                },
              ],
              should: {
                bool: {
                  minimum_should_match: 1,
                  should: [
                    {
                      match: {
                        village_id: {
                          query: user.village_id.to_i,
                          boost: CircleSearchConcern::VILLAGE_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mandal_id: {
                          query: user.mandal_id.to_i,
                          boost: CircleSearchConcern::MANDAL_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        district_id: {
                          query: user.district_id.to_i,
                          boost: CircleSearchConcern::DISTRICT_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mla_constituency_id: {
                          query: user.mla_constituency_id.to_i,
                          boost: CircleSearchConcern::MLA_CONSTITUENCY_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mp_constituency_id: {
                          query: user.mp_constituency_id.to_i,
                          boost: CircleSearchConcern::MP_CONSTITUENCY_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        state_id: {
                          query: user.state_id.to_i,
                          boost: CircleSearchConcern::STATE_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        circle_type: {
                          query: 'location',
                          boost: CircleSearchConcern::LOCATION_CIRCLE_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        circle_level: {
                          query: 'political_party',
                          boost: CircleSearchConcern::PARTY_CIRCLE_BOOST
                        },
                      }
                    },
                    {
                      "rank_feature": {
                        "field": "members_count",
                        "linear": {},
                        "boost": CircleSearchConcern::MEMBERS_COUNT_BOOST
                      }
                    },
                  ] + party_match_queries,
                },
              },
              "must_not": must_not_query
            }
          },
        },
        page: offset / count,
        per_page: count,
        load: false,
      )

      circles.map do |circle_result|
        circle_search_info(circle_result.id, user)
      end
    end

    def circle_search_info(id, user)
      circle = Circle.find(id)
      circle.photo&.compressed_url!(size: 200)

      result = {
        type: 'circle',
        id: circle.id,
        name: circle.name,
        name_en: circle.name_en,
        photo: circle.photo,
        level: circle.level,
        level_verbose: circle.level_verbose,
        type_of_organisation: circle.type_of_organisation,
        profile_photo: circle.photo,
        circle_background: circle.circle_background,
        circle_type: circle.circle_type,
        is_user_joined: circle.location_circle_type? ? true : circle.check_user_joined(user),
        members_count: circle.get_members_count,
        banner: circle.banner
      }

      # Only include short_name if it's present
      result[:short_name] = circle.short_name if circle.short_name.present?

      result
    end

    def search_circles_for_protocol(user:, term:)
      circles = []
      # Check if term is numeric to directly find by id.
      c = Circle.where(id: term).first if term.to_s == term.to_i.to_s
      if c.present? && c.get_poster_photos.present?
        circles << c
      end

      # If no circle is found by ID or term is not numeric, perform the ES search.
      if circles.blank?
        es_circles = search_circles(
          user: user,
          query: term,
          offset: 0,
          count: 10,
          search_type: :layout_search
        )
        circles = Circle.where(id: es_circles.pluck(:id)) if es_circles
      end
      circles
    end
  end

end
