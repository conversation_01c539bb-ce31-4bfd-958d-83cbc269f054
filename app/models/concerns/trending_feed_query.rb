module Trending<PERSON><PERSON><PERSON><PERSON><PERSON>

  def trending_feed_query_v3(loaded_post_ids, count, state_id)
    ES_CLIENT.search index: EsUtil.get_new_posts_index_v2, body: {
      "fields": ["id", "user_id"],
      "_source": false,
      "query": {
        "function_score": {
          "query": {
            "bool": {
              "must_not": {
                "terms": { "id": loaded_post_ids }
              },
              "filter": [
                {
                  "term": { "active": true }
                },
                {
                  "range": {
                    "created_at": {
                      "gte": "now-#{Constants.no_of_days_post_to_be_retrieve_for_feed}d/h"
                    }
                  }
                },
                {
                  "range": {
                    "likes_count": {
                      "gte": 25
                    }
                  }
                }
              ]
            }
          },
          "script_score": {
            "script": {
              "lang": 'painless',
              "source": '' "
                            double currentTime = new Date().getTime();
                            double timeDiff = (currentTime - params._source.created_at)/(1000*60*60);
                            double score = 0;
                            int timeDiffFactor = timeDiff > 24 ? 10 : 30;

                            score = (Math.sqrt(params._source.likes_count +
                                 (params._source.comments_count > 2 ? params._source.comments_count * 2 : 0) +
                                 (params._source.unique_users_whatsapp_count > 2 ? params._source.unique_users_whatsapp_count * 1.5 : 0) )) *
                                  Math.exp(-timeDiff/timeDiffFactor);

                            double location_score = 1;
                            if (params._source.tagged_state_circle_ids.length > 0) {
                              if (params._source.tagged_state_circle_ids.contains(params['stateId'])){
                                if (params['stateId'] == params['teStateId']) {
                                  location_score = params['teStateWt'];
                                }
                                else {
                                  location_score = params['languageWt'];
                                }
                              }
                              else {
                                if (params['stateId'] == params['teStateId']) {
                                  location_score = params['languageWt'];
                                }
                                else {
                                location_score = params['languageWt'];
                                }
                              }
                            }
                            else {
                                if (params['stateId'] == params._source.user_state_id ){
                                  if (params['stateId'] == params['teStateId']) {
                                    location_score = params['teStateWt'];
                                  }
                                  else {
                                    location_score = params['languageWt'];
                                  }
                                }
                                else {
                                  if (params['stateId'] == params['teStateId']) {
                                    location_score = params['languageWt'];
                                  }
                                  else {
                                    location_score = params['languageWt'];
                                  }
                                }
                            }
                            score = score * location_score;

                            if (params._source.has_poster_photo == true) { score *= 0.0333; }

                            return score;
                        " '',
              "params": {
                "stateId": state_id,
                "teStateWt": 1.5,
                "otherStateWt": 3,
                "languageWt": 1,
                "teStateId": 33010
              }
            }
          },
          "boost_mode": 'replace',
          "min_score": 0.000001
        }
      },
      "sort": [
        { "_score": { "order": 'desc' } },
        { "created_at": { "order": 'desc' } }
      ],
      "collapse": {
        "field": "user_id"
      },
      "size": count,
      "from": 0
    }
  end
end
