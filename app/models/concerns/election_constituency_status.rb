# frozen_string_literal: true

module ElectionConstituencyStatus
  extend ActiveSupport::Concern
  include Elections2024

  def election_user_constituency_status_carousel_my_feed(loaded_feed_item_ids)
    return if loaded_feed_item_ids.include?('user_constituency_status_carousel')

    user = @user
    return if user.blank?

    hash_key = "user_constituency_status_carousel"
    field = user.id.to_s
    max_count = Constants.max_count_user_constituency_status_carousel_per_week

    carousel = nil
    if $redis.hget(hash_key, field).to_i < max_count
      carousel = user_constituency_status_carousel(user)
      $redis.hincrby(hash_key, field, 1)
      $redis.expireat(hash_key, Time.zone.now.end_of_week.to_i)
    end
    return if carousel.present? && carousel[:items].blank?

    carousel
  end

  def election_district_constituency_status_carousel_my_feed(loaded_feed_item_ids)
    hash_key = feed_item_id = 'district_constituency_status_carousel'
    return if loaded_feed_item_ids.include?(feed_item_id)

    user = @user
    field = user.id.to_s
    district_id = user.district_id
    return if user.blank? || district_id.blank?

    max_count = Constants.max_count_district_constituency_status_carousel_per_week

    carousel = nil
    if $redis.hget(hash_key, field).to_i < max_count
      carousel = district_constituency_status_carousel(user_id: user.id, district_id: district_id)
      $redis.hincrby(hash_key, field, 1)
      $redis.expireat(hash_key, Time.zone.now.end_of_week.to_i)
    end
    return if carousel.present? && carousel[:items].blank?

    carousel
  end

  def user_constituency_status_carousel(user)
    mla_constituency_id = user.mla_constituency_id
    mp_constituency_id = user.mp_constituency_id

    items = [mla_constituency_id, mp_constituency_id].compact.map do |constituency_id|
      get_creative_carousel_item(constituency_id, user.id)
    end.compact

    return nil if items.blank?

    title = 'మీ నియోజకవర్గ ఎన్నికలు'
    feed_item_id = "user_constituency_status_carousel"
    creative_width = 400
    creative_height = 400
    next_page_url = nil
    analytics_params = {
      feed_item_id: feed_item_id,
      mla_constituency_id: mla_constituency_id,
      mp_constituency_id: mp_constituency_id,
    }

    live_config = {
      refresh_interval: carousel_refresh_interval,
      polling_url: "/election_tickers/live_user_constituency_status",
    } if counting_time?

    PosterCreative.build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:, creative_width:, creative_height:, live_config:)
  end

  def district_constituency_status_carousel(user_id:, district_id:, offset: 0, count: Constants.creatives_count)
    mla_constituency_ids = get_mla_constituency_ids(district_id)

    items = mla_constituency_ids[offset, count + 1].compact.flat_map do |constituency_id|
      get_creative_carousel_item(constituency_id, user_id)
    end.compact

    return nil if items.blank?

    feed_item_id = carousel_type = 'district_constituency_status_carousel'
    has_next_page = items.size > count
    # If there's a next page, remove the extra item not meant to be returned.
    items = items.first(count) if has_next_page
    next_page_url = has_next_page ? "/creatives?carousel_type=#{carousel_type}&district_id=#{district_id}&offset=#{offset + count}&count=#{count}" : nil

    district = Circle.find(district_id)
    title = "#{district.name} జిల్లా అసెంబ్లీ ఎన్నికలు"
    creative_width = 400
    creative_height = 400
    next_page_url = next_page_url
    analytics_params = {
      feed_item_id: feed_item_id,
      location_circle_id: district_id,
      location_level: 'district',
    }

    live_config = {
      refresh_interval: carousel_refresh_interval,
      polling_url: "/election_tickers/live_location_constituency_status?circle_id=#{district_id}",
    } if counting_time?

    PosterCreative.build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:, creative_width:, creative_height:, live_config:)
  end

  def state_parliament_constituency_status_carousel(user_id:, state_id:, offset: 0, count: Constants.creatives_count)
    mp_constituency_ids = get_mp_constituency_ids(state_id)

    items = mp_constituency_ids[offset, count + 1].compact.flat_map do |constituency_id|
      get_creative_carousel_item(constituency_id, user_id)
    end.compact

    return nil if items.blank?

    feed_item_id = carousel_type = 'state_constituency_status_carousel'
    has_next_page = items.size > count
    # If there's a next page, remove the extra item not meant to be returned.
    items = items.first(count) if has_next_page
    next_page_url = has_next_page ? "/creatives?carousel_type=#{carousel_type}&state_id=#{state_id}&offset=#{offset + count}&count=#{count}" : nil

    state = Circle.find(state_id)
    title = "#{state.name} రాష్ట్ర పార్లమెంట్ ఎన్నికలు"
    creative_width = 400
    creative_height = 400
    next_page_url = next_page_url
    analytics_params = {
      feed_item_id: feed_item_id,
      location_circle_id: state_id,
      location_level: 'state',
    }

    live_config = {
      refresh_interval: carousel_refresh_interval,
      polling_url: "/election_tickers/live_location_constituency_status?circle_id=#{state_id}",
    } if counting_time?

    PosterCreative.build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:, creative_width:, creative_height:, live_config:)
  end

  def location_level_constituency_status_carousels(user_id:, circle:, offset: 0, count: Constants.creatives_count)
    case circle.level.to_sym
    when :state
      return state_parliament_constituency_status_carousel(user_id: user_id, state_id: circle.id, offset: offset, count: count)
    when :district
      return district_constituency_status_carousel(user_id: user_id, district_id: circle.id, offset: offset, count: count)
    when :mandal, :village, :municipality, :corporation
      return other_location_levels_constituency_status_carousel(user_id: user_id, circle: circle)
    else
      nil
    end
  end

  def other_location_levels_constituency_status_carousel(user_id:, circle:)
    first_circle_id = circle.level.to_sym == :mandal ? circle.id : circle.parent_circle_id
    mla_constituencies = CirclesRelation.where(first_circle_id: first_circle_id, relation: :Mandal2MLA).map(&:second_circle)
    return nil if mla_constituencies.blank?

    mp_constituency_ids = mla_constituencies.flat_map { |mla_constituency| mla_constituency.parent_circle_id }.uniq
    mla_constituency_ids = mla_constituencies.map(&:id)

    constituencies_ids = mla_constituency_ids + mp_constituency_ids

    items = constituencies_ids.compact.map do |constituency_id|
      get_creative_carousel_item(constituency_id, user_id)
    end.compact

    return nil if items.blank?

    title = "2024 ఎన్నికలు"
    feed_item_id = "#{circle.level}_constituency_status_carousel"
    creative_width = 400
    creative_height = 400
    next_page_url = nil
    analytics_params = {
      feed_item_id: feed_item_id,
      location_circle_id: circle.id,
      location_level: circle.level,
    }

    live_config = {
      refresh_interval: carousel_refresh_interval,
      polling_url: "/election_tickers/live_location_constituency_status?circle_id=#{circle.id}",
    } if counting_time?

    PosterCreative.build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:, creative_width:, creative_height:, live_config:)
  end

  # Users constituency winners congratulation carousel
  def user_constituency_congrats_carousel(user)
    constituency_ids = [user.mla_constituency_id, user.mp_constituency_id].compact

    creatives, next_page_url = PosterCreative.congrats_creatives(creative_circle_ids: constituency_ids)

    items = []
    creatives.each do |creative|
      # only circle id sent to get creatives will be picked on poster_creative_circles.pluck(:circle_id)
      leader_circle_id = creative.poster_creative_circles.where.not(circle_id: creative.creative_circle_id).pluck(:circle_id)
      require_poster_params = true

      if user.has_badge_role?
        party_circle_id = CirclesRelation.where(first_circle_id: leader_circle_id, relation: :Leader2Party).first&.second_circle_id

        if party_circle_id.present?
          require_poster_params = false unless coalited?(party_circle_id, user.affiliated_party_circle_id)
        end
      end

      items << creative.get_json(
        category_kind: :congrats,
        circle_id: leader_circle_id.first,
        require_poster_params: require_poster_params
      )
    end

    return if items.blank?

    title = "మీ నియోజకవర్గ విజేతలు"
    feed_item_id = "user_constituency_congrats_carousel"
    analytics_params = { kind: :congrats, constituency_ids: constituency_ids }
    next_page_url = nil

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url:)
  end

  # User joined party assembly winners congratulation carousel
  def user_joined_party_mla_congrats_carousel(user)
    carousels = []
    affiliated_circle_ids = if user.affiliated_party_circle_id.present?
                              [user.affiliated_party_circle_id]
                            else
                              user.get_user_joined_party_circle_ids
                            end

    mla_leader_ids = CirclesRelation.where(relation: :MLA).select(:second_circle_id)

    affiliated_circles = Circle.where(id: affiliated_circle_ids)
    affiliated_circles.each do |circle|
      carousel = get_party_winners_congrats_carousel(user:, circle: circle, leader_ids: mla_leader_ids, level: :MLA)
      carousels << carousel if carousel.present?
    end

    carousels
  end

  def party_mla_congrats_next_page_carousel(user:, circle_id:, offset:, count: Constants.creatives_count)
    mla_leader_ids = CirclesRelation.where(relation: :MLA).select(:second_circle_id)
    circle = Circle.find circle_id
    get_party_winners_congrats_carousel(user:, circle:, leader_ids: mla_leader_ids, level: :MLA, offset:, count:)
  end

  # User joined party parliament winners congratulation carousel
  def user_joined_party_mp_congrats_carousel(user)
    carousels = []
    affiliated_circle_ids = if user.affiliated_party_circle_id.present?
                              [user.affiliated_party_circle_id]
                            else
                              user.get_user_joined_party_circle_ids
                            end

    mp_leader_ids = CirclesRelation.where(relation: :MP).select(:second_circle_id)

    affiliated_circles = Circle.where(id: affiliated_circle_ids)
    affiliated_circles.each do |circle|
      carousel = get_party_winners_congrats_carousel(user:, circle:, leader_ids: mp_leader_ids, level: :MP)
      carousels << carousel if carousel.present?
    end

    carousels
  end

  def party_mp_congrats_next_page_carousel(user:, circle_id:, offset:, count: Constants.creatives_count)
    mp_leader_ids = CirclesRelation.where(relation: :MP).select(:second_circle_id)
    circle = Circle.find circle_id
    get_party_winners_congrats_carousel(user:, circle:, leader_ids: mp_leader_ids, level: :MP, offset:, count:)
  end

  def get_party_winners_congrats_carousel(user:, circle:, leader_ids:, level:, offset: 0, count: Constants.creatives_count)
    circle_id = circle.id
    party_related_leader_circle_ids = CirclesRelation.where(relation: :Leader2Party, second_circle_id: circle.id)
                                                     .where(first_circle_id: leader_ids)
                                                     .pluck(:first_circle_id)
    creatives, next_page_url = PosterCreative.congrats_creatives(creative_circle_ids: party_related_leader_circle_ids, circle_id: circle_id, offset: offset, count: count)

    items = []
    creatives.each do |creative|
      require_poster_params = true
      creative_circle_ids = creative.poster_creative_circles.pluck(:circle_id)

      if user.has_badge_role?
        require_poster_params = false unless coalited?(circle_id, user.affiliated_party_circle_id)
      end

      items << creative.get_json(
        category_kind: :congrats,
        circle_id: creative_circle_ids.first,
        require_poster_params: require_poster_params
      )
    end

    return if items.blank?

    if level == :MLA
      title = "#{circle.name} అసెంబ్లీ విజేతలు"
      feed_item_id = "party_mla_congrats_carousel_#{circle_id}"
      next_page_url = next_page_url + "&level=MLA" if next_page_url.present?
    elsif level == :MP
      title = "#{circle.name} పార్లమెంటు విజేతలు"
      feed_item_id = "party_mp_congrats_carousel_#{circle_id}"
      next_page_url = next_page_url + "&level=MP" if next_page_url.present?
    end

    analytics_params = { kind: :congrats, circle_id: circle_id }

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url:)
  end

  def user_district_assembly_winners_congrats_carousel(user:, district_id:, offset: 0, count: Constants.creatives_count)
    district = Circle.find(district_id)
    mandal_ids = district.get_all_child_circle_ids_with_given_level(:mandal)
    mla_constituency_ids = CirclesRelation.where(first_circle_id: mandal_ids, relation: :Mandal2MLA).pluck(:second_circle_id)
    mla_constituency_ids.uniq!

    creatives, next_page_url = PosterCreative.congrats_creatives(creative_circle_ids: mla_constituency_ids, circle_id: district_id, offset: offset, count: count)

    items = []
    creatives.each do |creative|
      # only circle id sent to get creatives will be picked on poster_creative_circles.pluck(:circle_id)
      leader_circle_id = creative.poster_creative_circles.where.not(circle_id: creative.creative_circle_id).pluck(:circle_id)
      require_poster_params = true

      if user.has_badge_role?
        party_circle_id = CirclesRelation.where(first_circle_id: leader_circle_id, relation: :Leader2Party).first&.second_circle_id

        if party_circle_id.present?
          require_poster_params = false unless coalited?(party_circle_id, user.affiliated_party_circle_id)
        end
      end

      items << creative.get_json(
        category_kind: :congrats,
        circle_id: leader_circle_id.first,
        require_poster_params: require_poster_params
      )
    end

    return if items.blank?

    title = "మీ జిల్లా అసెంబ్లీ విజేతలు"
    feed_item_id = "user_district_assembly_winners_congrats_carousel"
    analytics_params = { kind: :congrats, district_id: district_id }

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url:)
  end

  def interest_level_constituency_status_carousels (user_id:, circle:)
    case circle.level.to_sym
    when :political_leader
      nil # return leader_constituency_status_carousel(user_id: user_id, circle: circle)
    else
      nil
    end
  end

  def leader_constituency_status_carousel(user_id:, circle:)
    contesting_constituency_circle_ids = CirclesRelation.where(second_circle_id: circle.id, relation: [:MLA_Contestant, :MLA, :MP_Contestant, :MP]).pluck(:first_circle_id)
    return if contesting_constituency_circle_ids.blank?

    items = contesting_constituency_circle_ids.uniq.compact.map do |constituency_id|
      get_creative_carousel_item(constituency_id, user_id)
    end

    return nil if items.blank?

    title = "2024 ఎన్నికలు"
    feed_item_id = "#{circle.level}_constituency_status_carousel"
    creative_width = 400
    creative_height = 400
    next_page_url = nil
    analytics_params = {
      feed_item_id: feed_item_id,
      leader_circle_id: circle.id,
    }

    live_config = {
      refresh_interval: carousel_refresh_interval,
      polling_url: "/election_tickers/live_leader_constituency_status?circle_id=#{circle.id}",
    } if counting_time?

    PosterCreative.build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:, creative_width:, creative_height:, live_config:)
  end

  def party_level_mla_congrats_carousels(user:, circle:)
    mla_leader_ids = CirclesRelation.where(relation: :MLA).pluck(:second_circle_id)

    get_party_winners_congrats_carousel(user:, circle:, leader_ids: mla_leader_ids, level: :MLA)
  end

  def party_level_mp_congrats_carousels(user:, circle:)
    mp_leader_ids = CirclesRelation.where(relation: :MP).pluck(:second_circle_id)

    get_party_winners_congrats_carousel(user:, circle:, leader_ids: mp_leader_ids, level: :MP)
  end

  def leader_level_congrats_carousel(user:, circle:, offset: 0, count: Constants.creatives_count)
    creative_circle_ids = [circle.id]
    creatives, next_page_url = PosterCreative.congrats_creatives(creative_circle_ids: creative_circle_ids, circle_id: circle.id, offset:, count:)

    items = []
    creatives.each do |creative|
      require_poster_params = true

      if user.has_badge_role?
        party_circle_id = CirclesRelation.where(first_circle_id: circle.id, relation: :Leader2Party).first&.second_circle_id
        require_poster_params = false unless coalited?(party_circle_id, user.affiliated_party_circle_id)
      end

      items << creative.get_json(
        category_kind: :congrats,
        circle_id: circle.id,
        require_poster_params: require_poster_params
      )
    end

    return if items.blank?

    title = "2024 ఎన్నికల విజేత"
    feed_item_id = "political_leader_congrats_carousel"
    analytics_params = { kind: :congrats, circle_id: circle.id }

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url:)
  end

  def get_creative_carousel_item(constituency_id, user_id)
    photo_id = get_constituency_status_photo_id(constituency_id)
    return nil if photo_id.blank?

    photo = Photo.find(photo_id.to_i)
    return nil if photo.blank?

    constituency = Circle.find(constituency_id)

    {
      id: photo_id.to_s,
      creative_photo: photo,
      analytics_params: {
        constituency_id: constituency_id,
        constituency_name: constituency&.name_en,
      },
      share_text: "ఎన్నికల ఫలితాలను ప్రజా యాప్ లో లైవ్ గా ఫాలో అవగలరు\n#{share_constituency_status_link(constituency_id)}",
    }
  end

  private

  def share_constituency_status_link(constituency_id)
    data = get_constituency_status_data(constituency_id)
    updated_at = data['updated_at'] if data.present?
    # for making the fallback web url unique for each update, so that users get updated link previews since new singular
    # link will be generated and not returned from cache This affects only users who are sharing the link multiple times
    # and whatsapp / external app is caching the link preview
    updated_at_slug = updated_at.present? ? "&updated_at=#{updated_at}" : ''

    deeplink_uri = URI.parse('praja://buzz.praja.app/feeds/election_feed')
    fallback_web_url = "#{Constants.get_api_host}/election_tickers/constituency_status_link_preview?constituency_id=#{constituency_id}#{updated_at_slug}"

    # singular link with campaign as 'election_results_preview'
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/23t5v')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s,
                                           _fallback_redirect: fallback_web_url })

    link = link_uri.to_s
    link = Singular.shorten_link(link)
  end
end
