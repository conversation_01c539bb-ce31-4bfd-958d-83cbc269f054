class JsonWebToken
  def self.encode(payload)
    JWT.encode(payload, Rails.application.credentials[:jwt_secret_key], 'HS256', { typ: 'JWT' })
  end

  def self.decode(token)
    return JWT.decode(token, Rails.application.credentials[:jwt_secret_key])[0]
  rescue
    return nil
  end

  def self.get_token_to_send_media_service
    self.encode({ service: "ror" })
  end

  def self.get_token_to_send_dm_service
    self.encode({ service: "ror" })
  end

  def self.get_token_for_dm_to_ror_auth
    self.encode({ service: "dm" })
  end

  def self.get_token_for_media_to_ror_auth
    self.encode({ service: "media" })
  end
end
