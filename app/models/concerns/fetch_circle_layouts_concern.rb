module FetchCircleLayoutsConcern
  extend ActiveSupport::Concern

  included do

    def get_circle_layouts_for_non_layout_user(circle:, frames:, common_hash:, user_role:, gradient_circle:)
      circle_layouts = []
      sponsorship_json = circle.sponsorship_json
      frames.each do |circle_frame|
        layout_type = "basic" # as of now we are using basic frames for circle frames so we are sending basic
        frame_id = circle_frame.id
        gold_border = circle_frame.gold_border
        has_shadow_color = circle_frame.has_shadow_color
        identity_type = circle_frame.identity_type
        show_badge_ribbon = circle_frame.override_badge_strip_value(user_role.present?, !custom_role_name.blank?)
        frame_type = circle_frame.frame_type
        frame_font = circle_frame.font
        layout = self.get_layout(layout_type, gold_border, has_shadow_color, gradient_circle, user_role,
                                 common_hash, nil, identity_type, original_layout_type: layout_type)
        layout[:id] = frame_id
        layout[:is_bordered_layout] = false
        layout[:is_locked] = false
        layout[:identity] = layout[:identity].merge(type: identity_type,
                                                    is_user_position_back: false,
                                                    show_badge_ribbon: show_badge_ribbon
        )
        if self.photo&.url.present?
          layout[:identity][:user][:photo_url] = self.photo.url
        else
          layout[:identity][:user][:photo_url] = "https://a-cdn.thecircleapp.in/production/photos/41/08b3490dbb2b7f7e38f3036b92358fd8.jpg"
        end
        layout[:frame_type] = frame_type
        # add font configs for the frame
        layout[:fonts_config] = frame_font.frame_json_data if frame_font.present?
        unless show_badge_ribbon
          layout[:badge_text_color] = layout[:text_color] if layout[:text_color].present?
          layout[:badge_text_color] = 0xff000000 if identity_type == "gold_lettered_user"
        end
        layout[:sponsorship] = sponsorship_json
        layout[:show_praja_logo] = false

        layout.merge!(common_hash)
        circle_layouts << layout
      end
      circle_layouts
    end
  end
end
