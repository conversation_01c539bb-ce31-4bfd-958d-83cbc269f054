module AutoRecommendFrames
  extend ActiveSupport::Concern

  included do
    def generate_auto_recommend_frames

      # get frame ids that suitable based on user's profile
      name_type = self.name_type
      badge_text_type = self.badge_text_type

      # if badge_text_type is nil then don't consider badge_text_type in query
      if badge_text_type.nil?
        frame_ids = FrameRecommendation.where(user_name_type: name_type).pluck(:frame_id)
      else
        frame_ids = FrameRecommendation.where(user_name_type: name_type, badge_text_type: badge_text_type)
                                       .pluck(:frame_id)
      end

      # get all premium frames from the database
      # update the query based on requirements
      # if user affiliated party has slogan icon then we recommend plain_identity_with_party_icon else we should not
      # recommend this frame
      eligible_for_slogan_icon_frame = self.affiliated_party_circle_id.present? &&
        self.affiliated_party_circle.slogan_icon_id.present?
      # if user role has badge description then we recommend party_slogan_identity else we should not recommend this
      # frame
      user_role = self.get_badge_role
      eligible_for_badge_description_frame = user_role.present? && self.get_slogan("party_slogan_identity",
                                                                                   user_role, nil).present?

      premium_frames_hash = Frame.where(id: frame_ids, frame_type: :premium, active: true)
                                 .group_by(&:identity_type)

      # delete plain_identity_with_party_icon frames if user is not eligible for slogan icon frame
      premium_frames_hash.delete("plain_identity_with_party_icon") unless eligible_for_slogan_icon_frame

      # delete party_slogan_identity frames if user is not eligible for badge description frame
      premium_frames_hash.delete("party_slogan_identity") unless eligible_for_badge_description_frame

      # randomly select 20 frames from frames list by maintaining the uniqueness of identity_type and frame_type = premium
      selected_premium_frames = get_recommended_frames(premium_frames_hash, Constants.recommended_premium_frames_count)

      # randomly select 10 frames from frames list by maintaining the uniqueness of identity_type and frame_type = status
      status_frames_hash = Frame.where(id: frame_ids, frame_type: :status, active: true).group_by(&:identity_type)
      selected_status_frames = get_recommended_frames(status_frames_hash, Constants.recommended_status_frames_count)

      selected_premium_frames + selected_status_frames
    end
  end

  # function to add a frame detail to selected_frames if not already included
  def add_unique_frame(selected_frames, frame_detail)
    unless selected_frames.include?(frame_detail)
      selected_frames << frame_detail
    end
  end

  # Function to select frame details based on identities
  def select_frame_details(identities, identity_frame_map, selected_frames, target_count)
    identities.each do |identity|
      frame_detail = identity_frame_map[identity].sample
      frame_detail[:identity_type] = identity
      add_unique_frame(selected_frames, frame_detail)

      break if selected_frames.size >= target_count
    end
  end

  def get_recommended_frames(identity_frame_map, frames_count)

    # Initialize an array for selected frames and shuffle identities for randomness
    selected_frames = []

    shuffled_identities = identity_frame_map.keys.shuffle
    # Select frames
    select_frame_details(shuffled_identities, identity_frame_map, selected_frames, frames_count)

    # If less than frames_count frames were selected, repeat the selection allowing for repeats
    # Ensure that the loop stops if all unique frames have been selected
    unique_frame_details_count = identity_frame_map.values.flatten.uniq.size
    while selected_frames.size < frames_count && selected_frames.size < unique_frame_details_count
      select_frame_details(shuffled_identities.shuffle, identity_frame_map, selected_frames, frames_count)
    end

    selected_frames
  end

  def create_frame_recommendations
    return if UserRecommendedFrame.where(user_id: self.id).exists?
    frames = self.generate_auto_recommend_frames
    # import all frames to user_recommended_frames table with one query
    UserRecommendedFrame.import frames.map { |frame| UserRecommendedFrame.new(user_id: self.id, frame_id: frame[:id]) },
                                on_duplicate_key_ignore: true if frames.present?
  end

  def delete_frame_recommendations
    UserRecommendedFrame.where(user_id: self.id).delete_all
  end

end

