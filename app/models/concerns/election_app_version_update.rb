# frozen_string_literal: true

module ElectionAppVersionUpdate
  IMAGE_URL = "https://a-cdn.thecircleapp.in/600x300/filters:quality(80)/production/admin-media/32/29ca0131-f975-4076-9881-b81c0df8c965.png"

  def update_for_election_live_updates_toast_my_feed(loaded_feed_item_ids)
    return if loaded_feed_item_ids.any? do |id|
      id.present? && id.include?('election_app_version_update_toast')
    end

    if loaded_feed_item_ids.length < 5 && !AppVersionSupport.live_toast_available?
      return update_for_election_live_updates_toast()
    end

    if loaded_feed_item_ids.length >= 5
      return update_for_election_live_updates_toast()
    end
  end

  def update_for_election_live_updates_toast()
    return if Rails.env.test?
    return if AppVersionSupport.live_toast_properly_supported?
    return unless AppVersionSupport.external_deeplink_supported?

    return if Date.today > Date.new(2024, 6, 4)

    cta = @app_os == 'ios' ? Constants.get_app_store_url : Constants.get_play_store_deep_link
    cta_uri = URI.parse(cta)

    external_deeplink_uri = URI.parse('praja://buzz.praja.app/external')
    external_deeplink_uri.query = URI.encode_www_form({ uri: cta_uri.to_s })

    {
      image_url: IMAGE_URL,
      image_height: 300,
      image_width: 600,
      cta_url: external_deeplink_uri.to_s,
      feed_type: 'feed_toast',
      feed_item_id: "election_app_version_update_toast_#{Time.zone.now.to_i}",
      is_removable: false,
      is_shareable: false,
      icon_color: 0xff3250a8,
      cta_text_color: 0xffffffff,
      toast_color: 0xff000000,
      cta_text: "అప్డేట్",
    }
  end
end
