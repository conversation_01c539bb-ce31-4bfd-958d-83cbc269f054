module UserFanRequests
  extend ActiveSupport::Concern

  included do

    def show_fan_poster_request_prompt?
      # fetch the circle ids of the user where the user is an owner
      circle_ids = fetch_circle_ids_of_user_where_user_is_owner

      Circle.where(id: circle_ids).each do |circle|
        circle_id = circle.id
        # Skip to the next circle if the current one is blank
        # or not a political party level or political leader level circle
        next if circle.blank? || !(circle.political_party_level? || circle.political_leader_level?)
        # Skip to the next circle if the owner of circle has already requested premium poster previously
        next if already_requested_for_premium_poster?(circle_id)
        # Skip to the next circle if the owner of circle has already seen the prompt 2 times
        next unless fan_posters_prompt_within_limit?(circle_id)
        # Skip to the next circle if the user fan poster requests are not more than 10
        next unless CirclePremiumInterest.fan_poster_interested_users_count(circle_id) > Constants.min_no_of_users_to_be_interested
        # If all conditions are met for this circle, return true
        return true
      end
      # Return false if none of the circles meet the criteria
      false
    end

    # fetch circles of the user where the user is a owner from user_circle_permission_group
    def fetch_circle_ids_of_user_where_user_is_owner
      UserCirclePermissionGroup.where(user_id: self.id, permission_group_id: Constants.owner_permission_group_id)
                               .pluck(:circle_id)
    end

    def owner_of_circle?(circle_id)
      UserCirclePermissionGroup.where(user_id: self.id, circle_id: circle_id,
                                      permission_group_id: Constants.owner_permission_group_id).exists?
    end

    def circle_id_for_fan_poster_prompt_with_max_interest
      # fetch the circle ids of the user where the user is an owner
      circle_ids = fetch_circle_ids_of_user_where_user_is_owner

      max_interest_circle_id = nil
      max_interest_count = 0

      Circle.where(id: circle_ids).each do |circle|
        next unless circle.political_party_level? || circle.political_leader_level?
        next if already_requested_for_premium_poster?(circle.id)
        next unless fan_posters_prompt_within_limit?(circle.id)

        interested_users_count = CirclePremiumInterest.fan_poster_interested_users_count(circle.id)

        if interested_users_count > max_interest_count && interested_users_count > Constants.min_no_of_users_to_be_interested
          max_interest_count = interested_users_count
          max_interest_circle_id = circle.id
        end
      end

      [max_interest_circle_id, max_interest_count]
    end

    def already_requested_for_premium_poster?(circle_id)
      CirclePremiumInterest.where(circle_id: circle_id, user_id: self.id, key: Constants.owner_premium_interests_key)
                           .exists?
    end

    def fan_posters_prompt_seen_count(circle_id)
      $redis.get(Constants.fan_posters_prompt_seen_count_key(self.id, circle_id)).to_i
    end

    def increment_fan_posters_prompt_seen_count(circle_id)
      $redis.incr(Constants.fan_posters_prompt_seen_count_key(self.id, circle_id))
    end

    # check if the user has seen the prompt 2 times or not
    def fan_posters_prompt_within_limit?(circle_id)
      fan_posters_prompt_seen_count(circle_id) <= Constants.max_no_of_times_to_show_fan_poster_prompt
    end

    def fan_poster_feed_item(exact_users_count, circle)
      circle_json = circle.get_json_v2(self)
      users = CirclePremiumInterest.fan_poster_interested_users(circle, count: Constants.no_of_users_to_be_shown)
      users_count = exact_users_count - users.count
      header = 'మీ అభిమానులు ప్రీమియం పోస్టర్లను స్పాన్సర్ చేయమని రిక్వెస్ట్ చేశారు'
      cta_text = 'మరింత తెలుసుకోండి'
      feed_type = 'fan_requests'
      {
        feed_type: feed_type,
        feed_item_id: Constants.fan_request_feed_item_id,
        header: header,
        users: users,
        users_count: users_count,
        cta_text: cta_text,
        circle_details: circle_json
      }
    end
  end
end
