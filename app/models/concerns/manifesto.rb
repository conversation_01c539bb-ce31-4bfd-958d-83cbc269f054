# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern
  include Elections2024

  included do
    :manifesto_carousels
    :manifesto_carousel_next_page
  end

  def manifesto_carousels(user)
    state_id = user.state_id
    coalitions = state_coalitions(state_id)

    affiliated_party_circle_id = user.affiliated_party_circle_id

    carousels = []
    coalitions = coalitions.sort_by { |coalition| coalition.circle_ids.include?(affiliated_party_circle_id) ? 0 : 1}
    coalitions.each do |coalition|
      carousel = manifesto_carousel(user:, coalition:)
      carousels << carousel if carousel[:items].present?
    end

    carousels
  end

  def manifesto_carousel_next_page(user:, circle_id:, offset:, count: Constants.creatives_count)
    state_id = user.state_id

    coalition = coalition(circle_id)

    manifesto_carousel(user: user, coalition: coalition, offset: offset, count: count)
  end

  def manifesto_carousel(user:, coalition:, offset: 0, count: Constants.creatives_count)
    creatives, next_page_url, offset = PosterCreative.manifesto_creatives(circle_ids: coalition.circle_ids, offset: offset, count: count)

    affiliated_party_circle_id = user.affiliated_party_circle_id
    joined_party_circle_ids = user.get_user_joined_party_circle_ids

    items = []
    creatives.each do |creative|
      creative_circle_ids = creative.poster_creative_circles.pluck(:circle_id)

      preferred_circle_id = creative_circle_ids.find { |circle_id| affiliated_party_circle_id == circle_id or joined_party_circle_ids.include?(circle_id) }
      preferred_circle_id ||= creative_circle_ids.first

      require_poster_params = true
      require_poster_params = false if user.has_badge_role? &&
        coalition.circle_ids.exclude?(user.get_badge_affiliated_party_circle_id)

      items << creative.get_json(
        category_kind: :manifesto,
        circle_id: preferred_circle_id,
        require_poster_params: require_poster_params
      )
    end

    title = "#{coalition.name} మేనిఫెస్టో"
    feed_item_id = "manifesto_carousel_#{coalition.circle_ids.join('_')}"
    analytics_params = { kind: :manifesto, circle_ids: coalition.circle_ids }

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url: next_page_url)
  end
end
