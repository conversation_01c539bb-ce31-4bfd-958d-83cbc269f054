module UserPostersTrial
  extend ActiveSupport::Concern

  included do
    # @deprecated
    def get_trial_period_toast_with_trial_day(user)
      trial_day, trial_duration = get_poster_trial_day_and_duration(user)
      return nil if trial_day.nil?
      toast_json = case trial_day
                   when 1
                     {
                       "feed_type": "feed_toast",
                       "feed_item_id": nil,
                       "header": "🆓 #{trial_duration} రోజుల వరకు ప్రీమియం పోస్టర్స్ ఉచితం",
                       "message": "ఇప్పుడు మీరు పోస్టర్లను ఫ్రీ గా షేర్ చేసుకోవచ్చు!",
                       "toast_color": 0xffE8EAF6,
                       "is_removable": true,
                     }
                   else
                     nil
                   end
      # return toast_json and trial_day
      [toast_json, trial_day]
    end

    def get_poster_trial_day_and_duration(user)
      return nil unless user.is_trial_user?

      trial_start_date, duration = Metadatum.get_user_trial_start_date_and_duration(user)
      [(Time.zone.today - trial_start_date).to_i + 1, duration]
    end
  end
end
