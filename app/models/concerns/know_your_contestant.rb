# frozen_string_literal: true

module KnowYourContestant
  extend ActiveSupport::Concern
  include Elections2024

  # Define a mapping outside the method to avoid redefining it every time the method is called
  CAROUSEL_METHOD_MAPPING = {
    Constants.mla_contestant_carousel_feed_item_id => :get_mla_contestant_carousel,
    Constants.mp_contestant_carousel_feed_item_id => :get_mp_contestant_carousel,
    Constants.district_mla_contestant_carousel_feed_item_id => :get_district_mla_contestant_carousel,
  }.freeze

  included do
    # get the latest creative of each mla contestant and build a contestant carousel feed item
    def get_mla_contestant_carousel(user, send_circle_cta: true)
      return nil if user.nil? || user.mla_constituency_id.nil?
      return nil if Time.zone.now > Time.zone.parse('2024-06-04 11:00:00')

      carousel_type = Constants.user_constituency_mla_contestant_key
      title = send_circle_cta ? 'మీ MLA అభ్యర్థులను తెలుసుకోండి' : 'మీ MLA అభ్యర్థులు'
      feed_item_id = Constants.mla_contestant_carousel_feed_item_id
      build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, send_circle_cta:)
    end

    def get_mp_contestant_carousel(user, send_circle_cta: true)
      return nil if user.nil? || user.mp_constituency_id.nil?
      return nil if Time.zone.now > Time.zone.parse('2024-06-04 11:00:00')

      carousel_type = Constants.user_constituency_mp_contestant_key
      title = send_circle_cta ? 'మీ MP అభ్యర్థులను తెలుసుకోండి' : 'మీ MP అభ్యర్థులు'
      feed_item_id = Constants.mp_contestant_carousel_feed_item_id
      build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, send_circle_cta:)
    end

    def get_district_mla_contestant_carousel(user, send_circle_cta: true)
      return nil if user.nil? || user.district_id.nil?

      carousel_type = Constants.user_district_mla_contestant_key
      title = send_circle_cta ? 'మీ జిల్లా MLA అభ్యర్థులను తెలుసుకోండి' : 'మీ జిల్లా MLA అభ్యర్థులు'
      feed_item_id = Constants.district_mla_contestant_carousel_feed_item_id
      build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, send_circle_cta:)
    end

    def get_party_mla_contestant_carousels(user, send_circle_cta: true)
      return nil if user.nil?

      carousels = []
      affiliated_circle_ids = if user.affiliated_party_circle_id.present?
                                [user.affiliated_party_circle_id]
                              else
                                user.get_user_joined_party_circle_ids
                              end
      affiliated_circle_ids.each do |circle_id|
        carousels << get_party_mla_contestant_carousel(user:, circle_id:, send_circle_cta:)
      end
      carousels
    end

    def get_party_mla_contestant_carousel(user:, circle_id:, send_circle_cta: true)
      circle = Circle.find_by_id(circle_id)
      carousel_type = Constants.party_mla_contestant_key
      title = send_circle_cta ? "#{circle.name} MLA అభ్యర్థులను తెలుసుకోండి" : "#{circle.name} MLA అభ్యర్థులు"
      feed_item_id = "#{circle.name_en}_party_mla_contestant_carousel"
      build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, circle_id:, send_circle_cta:)
    end

    def get_party_mp_contestant_carousels(user, send_circle_cta: true)
      return nil if user.nil?

      carousels = []
      affiliated_circle_ids = if user.affiliated_party_circle_id.present?
                                [user.affiliated_party_circle_id]
                              else
                                user.get_user_joined_party_circle_ids
                              end
      affiliated_circle_ids.each do |circle_id|
        carousels << get_party_mp_contestant_carousel(user:, circle_id:, send_circle_cta:)
      end
      carousels
    end

    def get_party_mp_contestant_carousel(user:, circle_id:, send_circle_cta: true)
      circle = Circle.find_by_id(circle_id)
      carousel_type = Constants.party_mp_contestant_key
      title = send_circle_cta ? "#{circle.name} MP అభ్యర్థులని తెలుసుకోండి" : "#{circle.name} MP అభ్యర్థులు"
      feed_item_id = "#{circle.name_en}_party_mp_contestant_carousel"
      build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, circle_id:, send_circle_cta:)
    end

    def affiliated_leader_with_contestant_ids(carousel_type:, user:, affiliated_party_id: nil)
      contestant_circle_ids = []
      case carousel_type&.to_sym
      when Constants.party_mla_contestant_key
        contestant_circle_ids = CirclesRelation.party_mla_contestants(affiliated_party_id)
      when Constants.party_mp_contestant_key
        contestant_circle_ids = CirclesRelation.party_mp_contestants(affiliated_party_id)
      when Constants.user_district_mla_contestant_key
        contestant_circle_ids = CirclesRelation.mla_contestant_ids_of_a_district(user.district)
      when Constants.user_constituency_mla_contestant_key
        contestant_circle_ids = CirclesRelation.mla_contestant_ids_of_a_mla_const(user.mla_constituency_id)
      when Constants.user_constituency_mp_contestant_key
        contestant_circle_ids = CirclesRelation.mp_contestant_ids_of_a_mp_const(user.mp_constituency_id)
      else
        [[], []]
      end

      # we want to maintain the order of district mla contestants (constituency based)
      # so that same constituency candidates appear together in the district mla carousel
      if carousel_type&.to_sym == Constants.user_district_mla_contestant_key
        affiliated_leader_ids = get_affiliated_leader_ids(leader_ids: contestant_circle_ids, affiliated_party_id: affiliated_party_id)
        [affiliated_leader_ids, contestant_circle_ids]
      else
        re_order_leader_ids_for_kyc(leader_ids: contestant_circle_ids,
                                    affiliated_party_id: affiliated_party_id)
      end
    end

    # get the latest creative of each mp contestant and build a contestant carousel feed item

    # this is to get affiliated party leader id and array of leader ids
    def re_order_leader_ids_for_kyc(leader_ids:, affiliated_party_id:)
      affiliated_leader_ids = []
      if affiliated_party_id.present?
        affiliated_leader_ids = get_affiliated_leader_ids(leader_ids:, affiliated_party_id:)
        # order that party affiliated circle id first then order other leader ids based on their members_count in circle
        leader_ids = Circle.where(id: leader_ids).order(members_count: :desc).pluck(:id)
        leader_ids = leader_ids.partition { |id| id.in?(affiliated_leader_ids) }.flatten
      else
        # sort leader ids based on the their members_count in circle
        leader_ids = Circle.where(id: leader_ids).order(members_count: :desc).pluck(:id)
      end
      [affiliated_leader_ids, leader_ids]
    end

    def get_affiliated_leader_ids(leader_ids:, affiliated_party_id:)
      CirclesRelation.where(first_circle_id: leader_ids,
                            second_circle_id: coalited_circles(affiliated_party_id),
                            relation: :Leader2Party).pluck(:first_circle_id)
    end

    def build_contestant_carousel(user:, title:, feed_item_id:, carousel_type:, circle_id: nil, send_circle_cta: true)
      creative_kind = :contestant
      count = Constants.creatives_count
      affiliated_circle_id = circle_id || user.affiliated_party_circle_id
      affiliated_leader_ids, contestant_ids = affiliated_leader_with_contestant_ids(
        user: user, affiliated_party_id: affiliated_circle_id, carousel_type: carousel_type
      )
      location_ids = fetch_location_ids_based_on_carousel_type(carousel_type: carousel_type, user: user,
                                                               contestant_circle_ids: contestant_ids)
      circle_ids_with_poster_creatives, has_next_page, offset = PosterCreative.latest_creative_of_contestants(
        circle_ids: contestant_ids, location_ids: location_ids,
        sub_query_for_free_users: '', creative_kind: creative_kind
      )
      items = []
      circle_ids = circle_ids_with_poster_creatives.keys
      circle_ids_with_poster_creatives.each do |cid, poster_creative|
        require_poster_params = true
        if user.has_badge_role? && affiliated_leader_ids.exclude?(cid)
          require_poster_params = false
        end
        items << poster_creative.get_json(category_kind: creative_kind, circle_id: cid,
                                          require_poster_params: require_poster_params, send_circle_cta:)
      end
      return nil if items.empty?

      next_page_url = nil
      if has_next_page
        build_next_page_url_params = { user_id: user.id, carousel_type: carousel_type, offset: offset, count: count,
                                       circle_id: circle_id, send_circle_cta: send_circle_cta }
        next_page_url = build_next_page_url(build_next_page_url_params)
      end
      analytics_params = { kind: creative_kind, circle_ids: circle_ids }

      # Note: if send_circle_cta parameter is false, then it's we consider that user is viewing this carousel in
      # posters tab because we are using send_circle_cta logic for poster's tab only
      item_to_carousel_width_ratio = 0.72 if send_circle_cta

      PosterCreative.build_creative_carousel(title: title, items: items,
                                             next_page_url: next_page_url,
                                             analytics_params: analytics_params,
                                             feed_item_id: feed_item_id,
                                             item_to_carousel_width_ratio: item_to_carousel_width_ratio)
    end

    def fetch_location_ids_based_on_carousel_type(carousel_type:, user:, contestant_circle_ids: nil)
      case carousel_type.to_sym
      when Constants.user_constituency_mla_contestant_key
        [user.mla_constituency_id]
      when Constants.user_constituency_mp_contestant_key
        [user.mp_constituency_id]
      when Constants.party_mla_contestant_key
        CirclesRelation.mla_const_of_mla_contestants(contestant_circle_ids)
      when Constants.party_mp_contestant_key
        CirclesRelation.mp_const_of_mp_contestants(contestant_circle_ids)
      when Constants.user_district_mla_contestant_key
        CirclesRelation.mla_const_of_mla_contestants(contestant_circle_ids)
      else
        []
      end
    end

    def build_next_page_url(params)
      user_id = params[:user_id]
      carousel_type = params[:carousel_type]
      offset = params[:offset]
      count = params[:count]
      circle_id = params[:circle_id]
      send_circle_cta = params[:send_circle_cta]
      case carousel_type
      when Constants.user_constituency_mla_contestant_key
        "/creatives?user_id=#{user_id}&carousel_type=#{carousel_type}&offset=#{offset}&count=#{count}&send_circle_cta=#{send_circle_cta}"
      when Constants.user_constituency_mp_contestant_key
        "/creatives?user_id=#{user_id}&carousel_type=#{carousel_type}&offset=#{offset}&count=#{count}&send_circle_cta=#{send_circle_cta}"
      when Constants.party_mla_contestant_key
        "/creatives?user_id=#{user_id}&carousel_type=#{carousel_type}&circle_id=#{circle_id}&offset=#{offset}&count=#{count}&send_circle_cta=#{send_circle_cta}"
      when Constants.party_mp_contestant_key
        "/creatives?user_id=#{user_id}&carousel_type=#{carousel_type}&circle_id=#{circle_id}&offset=#{offset}&count=#{count}&send_circle_cta=#{send_circle_cta}"
      when Constants.user_district_mla_contestant_key
        "/creatives?user_id=#{user_id}&carousel_type=#{carousel_type}&offset=#{offset}&count=#{count}&send_circle_cta=#{send_circle_cta}"
      else
        ''
      end
    end

    def relevant_election_feed_item_for_my_feed(user:)
      # eligible kyc carousel feed item
      viewed_kyc_carousel_ids_with_views = user.viewed_kyc_carousel_ids_with_views

      # check if there any carousel feed item id value is less than 2
      # if yes, then assign the first carousel feed item id
      if viewed_kyc_carousel_ids_with_views.any? { |_k, v| v.to_i < 2 }
        feed_item_id = viewed_kyc_carousel_ids_with_views.select { |_k, v| v.to_i < 2 }.keys.first
      end
      if feed_item_id.nil?
        viewed_kyc_carousel_ids = viewed_kyc_carousel_ids_with_views.keys
        # get feed item id which is not viewed by user from constant values
        feed_item_id = Constants.kyc_carousel_feed_items_for_my_feed.find { |id| !viewed_kyc_carousel_ids.include?(id) }
      end

      carousel_json = fetch_carousel_json(user, feed_item_id) if feed_item_id.present?
      return nil if carousel_json.blank?

      carousel_json
    end

    def fetch_carousel_json(user, feed_item_id)
      return unless feed_item_id.present?

      method = CAROUSEL_METHOD_MAPPING[feed_item_id]
      return if method.nil?

      json_data = send(method, user)

      return if json_data.blank?

      # add more details section to this json_data because this is using in my feed so need to add more details
      # to send user to election feed page
      json_data.merge!({
                         cta_text: 'ఇంకా చూడండి',
                         cta_deeplink: '/feeds/election_feed',
                         cta_description: 'ఎలక్షన్ గురించిన మరిన్ని విశేషాలు మరియు పోస్టర్లు',
                       })
      # Add carousel viewed count only if JSON data is present
      user.add_carousel_viewed_count(feed_item_id)
      json_data
    end

    def update_carousels_view_count_post_election_view(user_id)
      redis_hash_key = Constants.kyc_carousel_views_key(user_id)
      new_values = Constants.kyc_carousel_feed_items_for_my_feed.map { |feed_item_id| [feed_item_id, 2] }.flatten
      $redis.hmset(redis_hash_key, *new_values)
    end
  end
end
