# frozen_string_literal: true

module Elections2024
  extend ActiveSupport::Concern

  class PartyCoalition
    attr_reader :id, :circle_ids, :logo_url, :leaders_image_url, :css_background

    def initialize(id:, circle_ids:, name: nil, css_background: '', logo_url: '', leaders_image_url: '')
      @circle_ids = circle_ids
      @name = name
      @id = id
      @logo_url = logo_url
      @leaders_image_url = leaders_image_url
      @css_background = css_background
    end

    def name
      return @name if @name.present?

      Circle.find_by_id(circle_ids.first)&.name || id
    end
  end

  AP_ASSEMBLY_TICKER_ID = 'ap_assembly'
  TG_PARLIAMENT_TICKER_ID = 'tg_parliament'
  AP_PARLIAMENT_TICKER_ID = 'ap_parliament'
  CENTRE_TICKER_ID = 'centre'
  MAHARASHTRA_ASSEMBLY_TICKER_ID = 'maharashtra_assembly'
  JHARKHAND_ASSEMBLY_TICKER_ID = 'jharkhand_assembly'
  DELHI_ASSEMBLY_TICKER_ID = 'delhi_assembly'

  AP_CIRCLE_ID = 33009
  TG_CIRCLE_ID = 33010

  YCP_CIRCLE_ID = 31403
  TDP_CIRCLE_ID = 31402
  JSP_CIRCLE_ID = 31406
  BJP_AP_CIRCLE_ID = 37788
  INC_AP_CIRCLE_ID = 37967

  BRS_CIRCLE_ID = 31405
  INC_TG_CIRCLE_ID = 31401
  BJP_TG_CIRCLE_ID = 31398
  AIMIM_TG_CIRCLE_ID = 31404

  # Disable Layout/LineLength for the following code
  # rubocop:disable Layout/LineLength
  YCP_COALITION = PartyCoalition.new(
    id: 'ycp',
    circle_ids: [YCP_CIRCLE_ID],
    css_background: 'linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/05f838d6-ffcf-43ab-9645-f69ecb7449b3.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/6d5e224c-8ac6-4fc0-9eba-5ab137b6a952.png',
  )
  PRAJA_GALAM_COALITION = PartyCoalition.new(
    id: 'praja_galam',
    name: 'ప్రజా గళం',
    circle_ids: [TDP_CIRCLE_ID, JSP_CIRCLE_ID, BJP_AP_CIRCLE_ID],
    css_background: 'linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/3482bd68-a020-41b8-bc83-9942a7c88e45.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/15a1b9bf-d490-48b4-8d60-bb50d00513df.png',
  )

  BJP_COALITION = PartyCoalition.new(
    id: 'bjp',
    circle_ids: [BJP_TG_CIRCLE_ID],
    css_background: 'linear-gradient(to top, #FFC225, #FFC77B, #F47216)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/58166d94-b737-4c26-9fd5-fbb11e1ad5ea.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/d8965706-0dc0-4dbd-a470-8c6cf12b340a.png',
  )

  BRS_COALITION = PartyCoalition.new(
    id: 'brs',
    circle_ids: [BRS_CIRCLE_ID],
    css_background: 'linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/d37ac9d3-9cb6-456a-b6e2-1c6252b64fc5.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/4583b9ba-6e7c-45ab-903f-c9491a0ac28e.png',
  )

  INC_COALITION = PartyCoalition.new(
    id: 'inc',
    circle_ids: [INC_TG_CIRCLE_ID],
    css_background: 'linear-gradient(149.88deg, #f37022 4.18%,#e5fff7 52.37%,#0f823f 98.51%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/9b8fb07f-9492-475a-94d4-7728d225364c.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/61212d2d-79bd-4a9f-bc63-ad06f0816005.png',
  )

  INC_AP_COALITION = PartyCoalition.new(
    id: 'inc_ap',
    circle_ids: [INC_AP_CIRCLE_ID],
    css_background: 'linear-gradient(149.88deg, #f37022 4.18%,#e5fff7 52.37%,#0f823f 98.51%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/9b8fb07f-9492-475a-94d4-7728d225364c.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/61212d2d-79bd-4a9f-bc63-ad06f0816005.png',
  )

  AIMIM_COALITION = PartyCoalition.new(
    id: 'aimim',
    circle_ids: [AIMIM_TG_CIRCLE_ID],
    css_background: '#106B48',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/d4d39879-97d0-4ba0-b0d4-fd65352dae44.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/361ec67a-9a31-4e74-b406-73758ad91491.png',
  )

  MAHA_YUTI_COALITION = PartyCoalition.new(
    id: 'maha_yuti',
    circle_ids: [BJP_AP_CIRCLE_ID, BJP_TG_CIRCLE_ID, TDP_CIRCLE_ID, JSP_CIRCLE_ID],
    css_background: 'linear-gradient(110deg, hsla(23, 90%, 54%, 1) 0%, hsla(30, 100%, 60%, 1) 50%, hsla(149, 99%, 33%, 1) 100%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/9719fa24-4ce6-4c78-a3a4-50159a76dd05.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/08e3c614-1d08-4d2a-a587-06ae96f4abfc.png',
  )

  MAHA_VIKAS_AGHADI_COALITION = PartyCoalition.new(
    id: 'maha_vikas_aghadi',
    circle_ids: [INC_AP_CIRCLE_ID, INC_TG_CIRCLE_ID],
    css_background: 'linear-gradient(110deg, hsl(22.9, 90.39%, 55.1%), hsl(21.48, 89.95%, 57.93%) 2.8%, hsl(19.22, 89.13%, 63.3%) 5.34%, hsl(17.35, 88.28%, 69.04%) 7.68%, hsl(15.95, 87.48%, 74.62%) 9.87%, hsl(14.92, 86.75%, 79.86%) 11.96%, hsl(14.16, 86.1%, 84.66%) 14%, hsl(13.59, 85.53%, 88.94%) 16%, hsl(13.16, 85.05%, 92.64%) 18.13%, hsl(12.85, 84.66%, 95.69%) 20.32%, hsl(12.64, 84.37%, 98%) 22.66%, hsl(12.51, 84.19%, 99.47%) 25.2%, hsl(0, 0%, 100%) 28%, hsl(129.85, 10.92%, 99.11%) 31.90%, hsl(129.88, 11.06%, 96.6%) 35.43%, hsl(129.92, 11.29%, 92.69%) 38.70%, hsl(129.98, 11.63%, 87.52%) 41.75%, hsl(130.08, 12.1%, 81.26%) 44.66%, hsl(130.22, 12.74%, 74.03%) 47.5%, hsl(130.45, 13.62%, 65.98%) 50.34%, hsl(130.83, 14.83%, 57.28%) 53.25%, hsl(131.5, 17.83%, 48.15%) 56.3%, hsl(132.87, 29.93%, 38.93%) 59.56%, hsl(136.08, 52.97%, 30.11%) 63.10%, hsl(142.78, 91.53%, 23.14%) 67%, hsl(129.05, 53.41%, 28.87%) 70.3%, hsl(102.4, 41.49%, 31.26%) 73.29%, hsl(75.43, 40.78%, 31.35%) 76.06%, hsl(51.16, 43.79%, 33.63%) 78.64%, hsl(37.89, 50.25%, 38.75%) 81.10%, hsl(30.42, 54.67%, 43.4%) 83.50%, hsl(25.71, 57.79%, 47.51%) 85.90%, hsl(22.56, 62.59%, 51.05%) 88.36%, hsl(20.41, 72.15%, 53.95%) 90.94%, hsl(18.99, 80.21%, 56.14%) 93.70%, hsl(18.15, 85.78%, 57.54%) 96.70%, hsl(17.87, 87.85%, 58.04%))',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/a09c4962-f5fc-452a-97a7-59cc3920368d.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/4baebb85-eb96-4ed4-9f81-ef1ba1fc32d8.png',
  )

  JHARKHAND_INDIA_COALITION = PartyCoalition.new(
    id: 'jharkhand_india',
    circle_ids: [INC_AP_CIRCLE_ID, INC_TG_CIRCLE_ID],
    css_background: 'linear-gradient(149.88deg, #f37022 4.18%,#e5fff7 52.37%,#0f823f 98.51%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/10/ae8197d6-fb41-4d2b-a0ce-0f50ab93c31e.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/fe55bbed-6086-4944-8e91-dfedafe799ea.png',
  )

  JHARKHAND_NDA_COALITION = PartyCoalition.new(
    id: 'jharkhand_nda',
    circle_ids: [BJP_AP_CIRCLE_ID, BJP_TG_CIRCLE_ID],
    css_background: 'linear-gradient(to top, #FFC225, #FFC77B, #F47216)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/10/135a2b45-48cd-4b08-b844-9f70d5913eee.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/f6ddd907-e518-440e-a128-5fa7d76bc870.png',
  )

  DELHI_INDIA_COALITION = PartyCoalition.new(
    id: 'delhi_india',
    name: 'INDIA',
    circle_ids: [INC_AP_CIRCLE_ID, INC_TG_CIRCLE_ID],
    css_background: 'linear-gradient(to right, #f37022 4.18%,#e5fff7 52.37%,#0f823f 98.51%)',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/89e4a656-ff98-46f2-9404-5978b6be9d29.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/9bd7a4df-016c-4939-a1c9-4e977b1996ec.png',
  )

  DELHI_BJP_COALTION = PartyCoalition.new(
    id: 'delhi_bjp',
    name: 'BJP',
    circle_ids: [BJP_AP_CIRCLE_ID, BJP_TG_CIRCLE_ID],
    css_background: '#F47116',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/0c885575-7452-4318-9d1a-1fafd801f059.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/953f5142-06d2-4933-a0ba-3c89744b9832.png',
  )

  DELHI_AAP_COALITION = PartyCoalition.new(
    id: 'delhi_app',
    name: 'AAP',
    circle_ids: [],
    css_background: '#FFFFFF',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/8b828393-b70c-45f6-9399-772bb9959d79.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/37378ab2-cc30-402b-86cc-9ad769633856.png',
  )

  OTHERS_COALITION = PartyCoalition.new(
    id: 'others',
    name: 'ఇతరులు',
    circle_ids: [0],
    css_background: '#717171',
    logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/8dfc8db5-720b-4991-a815-3faa19e90ece.png',
    leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/32/22050d76-1b53-4cb3-8b82-840568f629da.png',
  )
  # rubocop:enable Layout/LineLength

  AP_COALITIONS = [
    YCP_COALITION,
    PRAJA_GALAM_COALITION,
  ].freeze

  AP_PARLIAMENT_COALITIONS = [
    YCP_COALITION,
    PRAJA_GALAM_COALITION,
    INC_AP_COALITION,
  ].freeze

  TG_COALITIONS = [
    BJP_COALITION,
    BRS_COALITION,
    INC_COALITION,
    AIMIM_COALITION,
  ].freeze

  CENTRE_COALITIONS = [
    BJP_COALITION,
    INC_COALITION,
  ].freeze

  MAHARASHTRA_COALITIONS = [
    MAHA_YUTI_COALITION,
    MAHA_VIKAS_AGHADI_COALITION,
  ].freeze

  JHARKHAND_COALITIONS = [
    JHARKHAND_NDA_COALITION,
    JHARKHAND_INDIA_COALITION,
  ].freeze

  DELHI_COALITIONS = [
    DELHI_BJP_COALTION,
    DELHI_AAP_COALITION,
    DELHI_INDIA_COALITION,
  ].freeze

  DEVELOPMENT_COALITIONS = [
    PartyCoalition.new(
      id: 'dev',
      circle_ids: [5],
      css_background: 'linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%)',
      logo_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/d37ac9d3-9cb6-456a-b6e2-1c6252b64fc5.png',
      leaders_image_url: 'https://a-cdn.thecircleapp.in/production/admin-media/40/4583b9ba-6e7c-45ab-903f-c9491a0ac28e.png',
    ),
  ].freeze

  def coalited?(first_circle_id, second_circle_id)
    all_coalitions.each do |coalition|
      return true if coalition.circle_ids.include?(first_circle_id) && coalition.circle_ids.include?(second_circle_id)
    end

    false
  end

  def coalition(circle_id)
    all_coalitions.each do |coalition|
      return coalition if coalition.circle_ids.include?(circle_id)
    end

    nil
  end

  def coalited_circles(circle_id)
    all_coalitions.each do |coalition|
      return coalition.circle_ids if coalition.circle_ids.include?(circle_id)
    end

    [circle_id]
  end

  def state_coalitions(state_circle_id)
    return DEVELOPMENT_COALITIONS if Rails.env.development?

    case state_circle_id
    when AP_CIRCLE_ID
      AP_PARLIAMENT_COALITIONS
    when TG_CIRCLE_ID
      TG_COALITIONS
    else
      []
    end
  end

  def ticker_coalitions(ticker_id)
    case ticker_id
    when AP_ASSEMBLY_TICKER_ID
      AP_COALITIONS
    when TG_PARLIAMENT_TICKER_ID
      TG_COALITIONS
    when AP_PARLIAMENT_TICKER_ID
      AP_PARLIAMENT_COALITIONS
    when CENTRE_TICKER_ID
      CENTRE_COALITIONS
    when MAHARASHTRA_ASSEMBLY_TICKER_ID
      MAHARASHTRA_COALITIONS
    when JHARKHAND_ASSEMBLY_TICKER_ID
      JHARKHAND_COALITIONS
    when DELHI_ASSEMBLY_TICKER_ID
      DELHI_COALITIONS
    else
      []
    end
  end

  def ticker_title(ticker_id)
    case ticker_id
    when AP_ASSEMBLY_TICKER_ID
      'ఆంధ్రప్రదేశ్'
    when TG_PARLIAMENT_TICKER_ID
      'తెలంగాణ'
    when AP_PARLIAMENT_TICKER_ID
      'ఆంధ్రప్రదేశ్'
    when CENTRE_TICKER_ID
      'భారత దేశం'
    when MAHARASHTRA_ASSEMBLY_TICKER_ID
      'మహారాష్ట్ర'
    when JHARKHAND_ASSEMBLY_TICKER_ID
      'ఝార్ఖండ్'
    when DELHI_ASSEMBLY_TICKER_ID
      'ఢిల్లీ'
    else
      ''
    end
  end

  def ticker_sub_title(ticker_id)
    case ticker_id
    when AP_ASSEMBLY_TICKER_ID
      'అసెంబ్లీ ఎన్నికలు 2024'
    when TG_PARLIAMENT_TICKER_ID
      'పార్లమెంటు ఎన్నికలు 2024'
    when AP_PARLIAMENT_TICKER_ID
      'పార్లమెంటు ఎన్నికలు 2024'
    when CENTRE_TICKER_ID
      'పార్లమెంటు ఎన్నికలు 2024'
    when MAHARASHTRA_ASSEMBLY_TICKER_ID
      'అసెంబ్లీ ఎన్నికలు 2024'
    when JHARKHAND_ASSEMBLY_TICKER_ID
      'అసెంబ్లీ ఎన్నికలు 2024'
    when DELHI_ASSEMBLY_TICKER_ID
      'అసెంబ్లీ ఎన్నికలు 2025'
    else
      ''
    end
  end

  def ticker_image_url_key(ticker_id)
    date = Time.zone.now.strftime('%Y-%m-%d')
    "election:ticker-image-url:#{ticker_id}:#{date}"
  end

  def all_ticker_ids
    [AP_ASSEMBLY_TICKER_ID, TG_PARLIAMENT_TICKER_ID, AP_PARLIAMENT_TICKER_ID, CENTRE_TICKER_ID]
  end

  def primary_ticker_by_state(state_id)
    return AP_ASSEMBLY_TICKER_ID if Rails.env.development?

    case state_id
    when AP_CIRCLE_ID
      AP_ASSEMBLY_TICKER_ID
    when TG_CIRCLE_ID
      TG_PARLIAMENT_TICKER_ID
    else
      ''
    end
  end

  def secondary_tickers_by_state(state_id)
    return [CENTRE_TICKER_ID, AP_PARLIAMENT_TICKER_ID, TG_PARLIAMENT_TICKER_ID] if Rails.env.development?

    case state_id
    when AP_CIRCLE_ID
      [AP_PARLIAMENT_TICKER_ID, TG_PARLIAMENT_TICKER_ID]
    when TG_CIRCLE_ID
      [AP_ASSEMBLY_TICKER_ID, AP_PARLIAMENT_TICKER_ID]
    else
      []
    end
  end

  def late_2024_election_tickers
    [MAHARASHTRA_ASSEMBLY_TICKER_ID, JHARKHAND_ASSEMBLY_TICKER_ID]
  end

  def feb_2025_election_tickers
    [DELHI_ASSEMBLY_TICKER_ID]
  end

  def all_coalitions
    Rails.env.development? ? DEVELOPMENT_COALITIONS : [AP_PARLIAMENT_COALITIONS, TG_COALITIONS].flatten
  end

  def get_ticker_data(ticker_id)
    ticker_data = $redis.hget("election:ticker-data", ticker_id)
    ticker_data.present? ? JSON.parse(ticker_data) : {"counts" => {}, "declare_majority_as_winner" => false}
  end

  def set_ticker_data(ticker_id, ticker_data)
    $redis.hset("election:ticker-data", ticker_id, ticker_data.to_json)
  end

  def get_constituency_status_data(constituency_id)
    data = $redis.hget("election:constituency-status-data", constituency_id)
    data.present? ? JSON.parse(data) : nil
  end

  def get_all_constituencies_status_data
    $redis.hgetall("election:constituency-status-data")
  end

  def update_constituency_status_data(constituency_id, data)
    $redis.hset("election:constituency-status-data", constituency_id, data.to_json)
  end

  def get_constituency_status_photo_id(constituency_id)
    $redis.hget("election:constituency-status-photo-id", constituency_id)
  end

  def set_constituency_status_photo_id(constituency_id, photo_id)
    $redis.hset("election:constituency-status-photo-id", constituency_id, photo_id)
  end

  # hash = {
  #     "background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
  #     "background_color_stops" => [0.0, 0.5539, 1.0],
  #     "bg_gradient_begin_x" => 1.0,
  #     "bg_gradient_begin_y" => -1.0,
  #     "bg_gradient_end_x" => 1.0,
  #     "bg_gradient_end_y" => 1.0,
  # }
  # background_color_stops can be nil
  # background_colors can have one or more colors
  def get_css_background(gradients_hash)
    background_colors = gradients_hash["background_colors"]
    background_color_stops = gradients_hash["background_color_stops"]
    bg_gradient_begin_x = gradients_hash["bg_gradient_begin_x"]
    bg_gradient_begin_y = gradients_hash["bg_gradient_begin_y"]
    bg_gradient_end_x = gradients_hash["bg_gradient_end_x"]
    bg_gradient_end_y = gradients_hash["bg_gradient_end_y"]

    angle = Math.atan2(bg_gradient_end_y - bg_gradient_begin_y, bg_gradient_end_x - bg_gradient_begin_x) * 180 / Math::PI
    angle = angle + 360 if angle < 0

    if background_colors.size == 1
      return "##{background_colors[0].to_s(16)[-6..-1]}"
    end

    if background_color_stops.present?
      background_color_stops = background_color_stops.map { |stop| "#{(stop * 100).round(2)}%"}
      colors_part = background_colors.map.with_index do |color, index|
        # convert to hex and take last six chars
        color_hex = color.to_s(16)[-6..-1]
        "##{color_hex} #{background_color_stops[index]}"
      end
    else
      colors_part = background_colors.map do |color|
        color_hex = color.to_s(16)[-6..-1]
        "##{color_hex}"
      end
    end

    "linear-gradient(#{angle}deg, #{colors_part.join(', ')})"
  end

  def get_days_left_as_text
    days_left = get_days_left

    if days_left <= 0
      text = 'మరికొంత సమయంలో ఎన్నికల ఫలితాలు'
    elsif days_left == 1
      text = 'రేపే ఎన్నికల ఫలితాలు'
    else
      text = "#{days_left} రోజుల్లో ఎన్నికల ఫలితాలు"
    end

    text
  end

  def get_days_left
    election_date = Time.zone.parse('2025-02-08')
    (election_date.to_date - Time.zone.now.to_date).to_i
  end

  def counting_time?
    Time.zone.now >= Time.zone.parse('2025-02-08 07:00:00') && Time.zone.now <= Time.zone.parse('2025-02-08 20:00:00')
  end

  def ticker_refresh_interval
    return 30 if AppVersionSupport.live_toast_properly_supported?

    120
  end

  def carousel_refresh_interval
    return 30 if AppVersionSupport.live_toast_properly_supported?

    120
  end

  def get_mp_constituency_ids(state_id)
    data = $redis.hget("state_mp_constituency_ids", state_id)
    return JSON.parse(data) if data.present?

    mandal_ids = Circle.find_by_id(state_id).get_all_child_circle_ids_with_given_level(:mandal)
    mla_constituency_ids = []
    mandal_ids.each_slice(100) do |m_ids|
      mla_constituency_ids |= CirclesRelation.where(first_circle_id: m_ids, relation: :Mandal2MLA).pluck(:second_circle_id)
    end
    mp_constituency_ids = Circle.where(id: mla_constituency_ids).distinct.pluck(:parent_circle_id)
    $redis.hset("state_mp_constituency_ids", state_id, mp_constituency_ids.to_json)

    mp_constituency_ids
  end

  def get_mla_constituency_ids(district_id)
    data = $redis.hget("district_mla_constituency_ids", district_id)
    return JSON.parse(data) if data.present?

    mandal_ids = Circle.find_by_id(district_id).get_all_child_circle_ids_with_given_level(:mandal)
    mla_constituency_ids = []
    mandal_ids.each_slice(100) do |m_ids|
      mla_constituency_ids |= CirclesRelation.where(first_circle_id: m_ids, relation: :Mandal2MLA).pluck(:second_circle_id)
    end
    $redis.hset("district_mla_constituency_ids", district_id, mla_constituency_ids.to_json)

    mla_constituency_ids
  end

  def get_total_seats_count(ticker_id)
    case ticker_id
    when AP_ASSEMBLY_TICKER_ID
      175
    when TG_PARLIAMENT_TICKER_ID
      17
    when AP_PARLIAMENT_TICKER_ID
      25
    when CENTRE_TICKER_ID
      543
    when MAHARASHTRA_ASSEMBLY_TICKER_ID
      288
    when JHARKHAND_ASSEMBLY_TICKER_ID
      81
    when DELHI_ASSEMBLY_TICKER_ID
      70
    else
      0
    end
  end

  def update_ticker_data(ticker_id, params)
    party_data = params[:party_data]
    declare_majority_as_winner = params[:declare_majority_as_winner] == 'on'
    updated_at = Time.zone.now

    total_seats = get_total_seats_count(ticker_id)

    data_hash = {
      "counts" => {},
      "declare_majority_as_winner" => declare_majority_as_winner,
      "updated_at" => updated_at
    }

    party_data.each do |index, data|
      party = data[:party]
      lead_count = data[:lead_count]
      win_count = data[:win_count]

      counts = data_hash["counts"]
      counts[party] = {
        "lead_count" => lead_count.to_i,
        "win_count" => win_count.to_i
      }
    end

    total_lead_count = data_hash["counts"].values.sum { |data| data["lead_count"] }
    total_win_count = data_hash["counts"].values.sum { |data| data["win_count"] }

    if (total_lead_count + total_win_count) <= total_seats
      set_ticker_data(ticker_id, data_hash)
      GenerateElectionTickerImage.perform_async(ticker_id)
      ''
    else
      "Total of lead and win count (#{total_lead_count + total_win_count}) exceeds total seats #{total_seats}"
    end
  end
end
