# frozen_string_literal: true

module ElectionTicker
  extend ActiveSupport::Concern
  include Elections2024

  included do
    :election_ticker_preview
    :election_tickers_my_feed
    :election_ticker_preview_my_feed
    :election_ticker_preview_image_url
  end

  def election_tickers_my_feed(loaded_feed_item_ids)
    ticker_ids = feb_2025_election_tickers
    return [] if ticker_ids.blank?

    return [] if loaded_feed_item_ids.include?("election_ticker_#{ticker_ids.first}")
    return [] if get_days_left.negative?

    if !counting_time? && !@user.is_test_user? && !@user.internal?
      hash_key = 'delhi_2025_election_preview'
      field = @user.id.to_s
      max_count = Constants.max_count_election_ticker_preview_per_week
      ticker_id = ticker_ids.first

      tickers = []
      if $redis.hget(hash_key, field).to_i < max_count
        tickers = [election_ticker_by_ticker_id(ticker_id, @user)]
        $redis.hincrby(hash_key, field, 1)
        $redis.expireat(hash_key, Time.zone.now.end_of_week.to_i)
      end

      return tickers
    else
      tickers = ticker_ids.map { |ticker_id|
        election_ticker_by_ticker_id(ticker_id, @user)
      }
      return tickers
    end
  end

  def election_ticker_preview_my_feed(loaded_feed_item_ids)
    return if loaded_feed_item_ids.include?('election_ticker_preview')

    user = @user
    return if user.blank?

    hash_key = "election_ticker_preview"
    field = user.id.to_s
    max_count = Constants.max_count_election_ticker_preview_per_week

    toast = nil
    if $redis.hget(hash_key, field).to_i < max_count
      toast = election_ticker_preview(user)
      $redis.hincrby(hash_key, field, 1)
      $redis.expireat(hash_key, Time.zone.now.end_of_week.to_i)
    end

    toast
  end

  def election_ticker_preview(user)
    state_id = user.state_id

    primary_state_ticker = primary_ticker_by_state(state_id)
    return nil if primary_state_ticker.blank?

    election_ticker_by_ticker_id(primary_state_ticker, user)
  end

  def secondary_tickers(user)
    state_id = user.state_id

    secondary_ticker_ids = secondary_tickers_by_state(state_id)
    return [] if secondary_ticker_ids.blank?

    secondary_ticker_ids.map { |ticker_id| election_ticker_by_ticker_id(ticker_id, user) }.compact
  end

  def election_ticker_by_ticker_id(ticker_id, user)
    image_url = election_ticker_preview_image_url(ticker_id)
    return nil if image_url.blank?

    live_config = {
      refresh_interval: ticker_refresh_interval,
      polling_url: "/election_tickers/live_ticker?ticker_id=#{ticker_id}",
    } if counting_time? || user.is_test_user? || user.internal?

    {
      image_url: Capture.apply_img_transform(image_url, width: 720, height: 720),
      image_height: 1024,
      image_width: 1024,
      cta_url: 'praja://buzz.praja.app/',
      feed_type: 'feed_toast',
      feed_item_id: "election_ticker_#{ticker_id}",
      is_removable: false,
      share_text: "ఢిల్లీ అసెంబ్లీ ఎన్నికల ఫలితాలను ప్రజా యాప్ లో లైవ్ గా ఫాలో అవగలరు\n#{share_ticker_link(ticker_id)}",
      share_image: false,
      is_shareable: true,
      live_config: live_config,
    }
  end

  def election_ticker_preview_image_url(ticker_id)
    $redis.get(ticker_image_url_key(ticker_id))
  end

  private

  def share_ticker_link(ticker_id)
    data = get_ticker_data(ticker_id)
    updated_at = data['updated_at']
    # for making the fallback web url unique for each update, so that users get updated link previews
    # since new singular link will be generated and not returned from cache
    updated_at_slug = updated_at.present? ? "&updated_at=#{updated_at}" : ''

    deeplink_uri = URI.parse('praja://buzz.praja.app/feeds/election_feed')
    fallback_web_url = "#{Constants.get_api_host}/election_tickers/link_preview?ticker_id=#{ticker_id}#{updated_at_slug}"

    # singular link with campaign as 'election_results_preview'
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/23t5v')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s,
                                           _fallback_redirect: fallback_web_url})

    link = link_uri.to_s
    link = Singular.shorten_link(link)
  end
end
