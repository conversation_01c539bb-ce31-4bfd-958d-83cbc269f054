module UserSearchConcern
  extend ActiveSupport::Concern

  VILLAGE_MATCH_BOOST = 4
  MANDAL_MATCH_BOOST = 1.5
  MLA_CONSTITUENCY_MATCH_BOOST = 1.5
  MP_CONSTITUENCY_MATCH_BOOST = 1.5
  DISTRICT_MATCH_BOOST = 1.5
  STATE_MATCH_BOOST = 1
  PARTY_MATCH_BOOST = 32
  LEADER_MATCH_BOOST = 32
  NAME_MATCH_BOOST = 64
  BADGE_DESCRIPTION_MATCH_BOOST = 64
  PREFIX_NAME_MATCH_BOOST = 8
  MEMBERS_COUNT_BOOST = 0.05

  included do

    # Disabling searchkick callbacks for this model please refer to after_commit block
    searchkick index_name: -> { EsUtil.get_index_name("#{name.tableize}_v2") }, callbacks: false, mappings: {
      "properties": {
        "active": {
          "type": "keyword"
        },
        "affiliated_political_party_id": {
          "type": "keyword"
        },
        "badge": {
          "type": "keyword"
        },
        "badge_grade_level": {
          "type": "rank_feature",
          "positive_score_impact": false
        },
        "badge_location_circle_id": {
          "type": "keyword"
        },
        "circle_ids": {
          "type": "keyword"
        },
        "district_id": {
          "type": "keyword"
        },
        "followers_count": {
          "type": "rank_feature"
        },
        "mandal_id": {
          "type": "keyword"
        },
        "members_count": {
          "type": "long"
        },
        "mla_constituency_id": {
          "type": "keyword"
        },
        "mp_constituency_id": {
          "type": "keyword"
        },
        "name": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "name_en": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "name_te": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "badge_description": {
          type: "search_as_you_type",
          doc_values: false,
          max_shingle_size: 3
        },
        "badge_description_en": {
          type: "search_as_you_type",
          doc_values: false,
          max_shingle_size: 3
        },
        "badge_description_te": {
          type: "search_as_you_type",
          doc_values: false,
          max_shingle_size: 3
        },
        "phone": {
          "type": "search_as_you_type",
          "doc_values": false,
          "max_shingle_size": 3
        },
        "political_party": {
          "type": "keyword"
        },
        "village_id": {
          "type": "keyword"
        },
        "party_circle_id": {
          "type": "keyword"
        },
        "leader_circle_id": {
          "type": "keyword"
        }
      }
    }

    # Disabling searchkick callbacks for this model and not using should_index? method
    # As setting this in should_index? will not index the record during bulk reindexing
    # Inspiration https://github.com/ankane/searchkick/issues/1635#issuecomment-1547195728
    after_commit do
      if active_status? && (saved_change_to_attribute?(:total_followers_count) || saved_change_to_attribute?(:name) || saved_change_to_attribute?(:photo_id))
        reindex(mode: :queue)
      end
    end

  end

  class_methods do
    def search_users(user:, query:, offset:, count:)
      party_match_queries = []
      user.get_user_joined_party_circle_ids.each do |party_id|
        party_match_queries << {
          match: {
            party_circle_id: {
              query: party_id,
              boost: UserSearchConcern::PARTY_MATCH_BOOST
            },
          }
        }
      end

      leader_match_queries = []
      user.get_user_joined_leader_ids.each do |leader_id|
        leader_match_queries << {
          match: {
            leader_circle_id: {
              query: leader_id,
              boost: UserSearchConcern::LEADER_MATCH_BOOST
            },
          }
        }
      end

      users = User.search(
        body: {
          _source: ["id"],
          query: {
            bool: {
              must: [
                {
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        "match_bool_prefix": {
                          "name": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::PREFIX_NAME_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "name_en": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::PREFIX_NAME_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "name_te": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::PREFIX_NAME_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "badge_description": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "badge_description_en": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match_bool_prefix": {
                          "badge_description_te": {
                            "query": query,
                            "minimum_should_match": "100%",
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "match": {
                          "phone": query
                        }
                      },
                      {
                        "match_phrase": {
                          "name_en": {
                            "query": query,
                            "boost": UserSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "name_te": {
                            "query": query,
                            "boost": UserSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "name": {
                            "query": query,
                            "boost": UserSearchConcern::NAME_MATCH_BOOST
                          }
                        }
                      },

                      {
                        "match_phrase": {
                          "badge_description_en": {
                            "query": query,
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "badge_description_te": {
                            "query": query,
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "match_phrase": {
                          "badge_description": {
                            "query": query,
                            "boost": UserSearchConcern::BADGE_DESCRIPTION_MATCH_BOOST
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "name": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "name_en": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "name_te": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },

                      {
                        "fuzzy": {
                          "badge_description": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "badge_description_en": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      },
                      {
                        "fuzzy": {
                          "badge_description_te": {
                            "value": query,
                            "fuzziness": "AUTO:5,10"
                          }
                        }
                      }

                    ]
                  }
                },
              ],
              should: {
                bool: {
                  minimum_should_match: 1,
                  should: [
                    {
                      match: {
                        village_id: {
                          query: user.village_id.to_i,
                          boost: UserSearchConcern::VILLAGE_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mandal_id: {
                          query: user.mandal_id.to_i,
                          boost: UserSearchConcern::MANDAL_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        district_id: {
                          query: user.district_id.to_i,
                          boost: UserSearchConcern::DISTRICT_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mla_constituency_id: {
                          query: user.mla_constituency_id.to_i,
                          boost: UserSearchConcern::MLA_CONSTITUENCY_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        mp_constituency_id: {
                          query: user.mp_constituency_id.to_i,
                          boost: UserSearchConcern::MP_CONSTITUENCY_MATCH_BOOST
                        },
                      }
                    },
                    {
                      match: {
                        state_id: {
                          query: user.state_id.to_i,
                          boost: UserSearchConcern::STATE_MATCH_BOOST
                        },
                      }
                    },
                    {
                      "rank_feature": {
                        "field": "followers_count",
                        "linear": {},
                        "boost": UserSearchConcern::MEMBERS_COUNT_BOOST
                      }
                    },
                  ] + party_match_queries + leader_match_queries,
                },
              },
            }
          },
        },
        page: offset / count,
        per_page: count,
        load: false,
      )

      users.map do |u|
        user_search_info(u.id, user)
      end.compact
    end

    def user_search_info(id, current_user)
      user = User.find(id)
      return unless user.active_status?
      user.photo&.compressed_url!(size: 200)

      {
        type: 'user',
        id: user.id,
        name: user.name,
        phone: user.generate_random_phone,
        photo: user.photo,
        village: User.core_party_user_ids.include?(user.id) ? nil : user.village, # TODO: remove this hack
        followers_count: user.followers_count,
        loggedInUser: user.id == current_user.id,
        follows: user.id == current_user.id ? false : UserFollower.where(user: user, follower: current_user).exists?,
        badge: user.get_badge_role&.get_json,
        avatar_color: user.avatar_color
      }
    end
  end

  def search_data
    additional_data = {
      party_circle_id: get_user_joined_party_circle_ids,
      leader_circle_id: get_user_joined_leader_ids
    }
    data = search_entity_obj.merge(additional_data)
    if data[:followers_count].to_i == 0
      data[:followers_count] = 0.000001
    end
    data.delete(:photo_url) if data[:photo_url].present?
    data.delete('photo_url') if data['photo_url'].present?
    if self.entity_name.present?
      if self.entity_name.name_en.present?
        data[:name_en] = self.entity_name.name_en
      end
      if self.entity_name.name_te.present?
        data[:name_te] = self.entity_name.name_te
      end
    end
    data
  end

end
