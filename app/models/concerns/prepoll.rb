# frozen_string_literal: true

module Prepoll
  extend ActiveSupport::Concern
  include Elections2024

  def prepoll_carousel_my_feed(loaded_feed_item_ids)
    return if loaded_feed_item_ids.any? do |id|
      id.present? && id.include?('prepoll_carousel')
    end

    user = @user
    return if user.blank?

    hash_key = "prepoll_carousel"
    field = user.id.to_s
    max_count = Constants.max_count_prepoll_carousel_per_week

    carousel = nil
    if $redis.hget(hash_key, field).to_i < max_count
      carousel = prepoll_carousel(user)
      $redis.hincrby(hash_key, field, 1)
      $redis.expireat(hash_key, Time.zone.now.end_of_week.to_i)
    end
    return if carousel.present? && carousel[:items].blank?

    carousel
  end

  def prepoll_carousel(user)
    state_id = user.state_id
    state = Circle.find_by_id(state_id)
    return nil unless state.present?

    is_eligible_for_premium_creatives = user.is_eligible_for_premium_creatives?
    sub_query_for_free_users = user.get_sub_query_for_free_users(is_eligible_for_premium_creatives)

    creatives_hash = PosterCreative.category_kind_based_poster_creatives(
      circle_ids: [state_id],
      sub_query_for_free_users: sub_query_for_free_users,
      creative_kind: :prepoll
    )[state_id]

    return nil unless creatives_hash.present?
    poster_creatives, next_page_url = creatives_hash.values_at(:poster_creatives, :next_page_url)
    items = poster_creatives.map do |creative|
      creative.get_json(
        category_kind: :prepoll,
        circle_id: state_id
      )
    end
    analytics_params = { kind: :prepoll, circle_id: state_id, circle_name_en: state.name_en }
    title = "#{state.name} ఎగ్జిట్ పోల్స్"
    feed_item_id = "prepoll_carousel_#{state_id}"

    PosterCreative.build_creative_carousel(title:, items:, feed_item_id:, analytics_params:, next_page_url:)
  end

end
