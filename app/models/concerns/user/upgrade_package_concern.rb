module User::UpgradePackageConcern
  extend ActiveSupport::Concern

  def common_upgrade_package_conditions_met?
    return false if active_subscription.blank?
    return false unless SubscriptionUtils.has_user_subscribed?(id, allow_grace_period: false)
    return false unless has_user_subscribed_to_one_month_plan?
    return false unless last_2_charges_successful_in_first_attempt?(active_subscription.id)
    true
  end

  def upgrade_package_using_offer_conditions_met?
    return false if active_subscription.blank?
    return false unless SubscriptionUtils.has_user_subscribed?(id, allow_grace_period: false)
    return false unless has_user_subscribed_to_one_month_plan?
    true
  end

  def upgrade_package_sheet_metadata_to_be_shown?
    UserMetadatum.exists?(user_id: id, key: Constants.upgrade_package_sheet_key, value: :to_be_shown)
  end

  # mark as shown upgrade package sheet
  def mark_as_shown_upgrade_package_sheet
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.upgrade_package_sheet_key)
    metadatum.value = :shown
    metadatum.save!
  end

  # mark as to be shown upgrade package sheet
  def mark_as_to_be_shown_upgrade_package_sheet
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.upgrade_package_sheet_key)
    metadatum.value = :to_be_shown
    metadatum.save!
  end

  # delete upgrade package sheet key
  def delete_upgrade_package_sheet_key
    UserMetadatum.find_by(user_id: id, key: Constants.upgrade_package_sheet_key)&.destroy
  end

  def last_2_charges_successful_in_first_attempt?(subscription_id)
    last_two_subs_charges = SubscriptionCharge.where(user_id: id, subscription_id:).last(2)

    if is_test_user? || internal?
      return false if last_two_subs_charges.size < 1
      # return false if last charge is not successful
      return false if last_two_subs_charges.last.is_trial_charge? || !last_two_subs_charges.last.success?
    else
      return false if last_two_subs_charges.size < 2
      # return false if last two charges are not successful
      return false if last_two_subs_charges.any? { |charge| charge.is_trial_charge? || !charge.success? }
    end
    true
  end

  def should_show_upgrade_package_nudge?
    # if upgrade package nudge count < 10 then return true else false
    key = Constants.upgrade_package_nudge_count_key(id)
    count = $redis.get(key).to_i
    return true if count < 10
    false
  end

  def delete_upgrade_package_nudge_count
    key = Constants.upgrade_package_nudge_count_key(id)
    $redis.del(key)
  end

  def increment_upgrade_package_nudge_seen_count
    key = Constants.upgrade_package_nudge_count_key(id)
    count = $redis.get(key).to_i
    count > 0 ? $redis.incr(key) : $redis.set(key, 1, ex: 31.days.to_i)
    count
  end

  def has_user_subscribed_to_one_month_plan?
    get_active_user_plan&.plan&.duration_in_months == 1
  end
end
