module User::CohortBasedCampaignConcern
  extend ActiveSupport::Concern

  def user_eligible_1_year_campaign
    # This method will return the eligible campaign details based on the user's cohort
    # get campaign based on user cohort
    cohort = user_cohort
    Campaign.latest_campaign_for_cohort(cohort:, duration_in_months: 12) if cohort.present?
  end

  def user_cohort
    active_subscription = self.active_subscription
    return nil if active_subscription.blank?

    # Get the last two successful charges
    last_two_successful_charges = active_subscription.last_two_successful_charges
    if is_test_user? || internal?
      return nil if last_two_successful_charges.size < 1
    end

    attempt_numbers = last_two_successful_charges.map(&:attempt_number)
    # all these checks has to be done for one monthly subscribed
    # if attempt number is 1 for both charges then user is in cohort 1
    # if attempt number is 1 for one charge and 2 for the other charge then user is in cohort 2
    # if any of the charge has attempt number greater than 2 then user is in cohort 3
    # if atleast one monthly charge is successful then consider that user as cohort 4
    @user_cohort ||= if last_two_successful_charges.size >= 1 && active_subscription.plan.duration_in_months == 1
                       "cohort_4"
                     elsif attempt_numbers.all? { |num| num == 1 } && last_two_successful_charges.size == 2 &&
                       active_subscription.plan.duration_in_months == 1
                       "cohort_1"
                     elsif attempt_numbers.sort == [1, 2] && last_two_successful_charges.size == 2 &&
                       active_subscription.plan.duration_in_months == 1
                       "cohort_2"
                     elsif attempt_numbers.any? { |num| num > 2 } && last_two_successful_charges.size == 2 &&
                       active_subscription.plan.duration_in_months == 1
                       "cohort_3"
                     else
                       nil
                     end
  end

  def get_amount_after_campaign_offer(campaign:, user_cohort:, plan:)
    # This method will return the offer amount for the given campaign and user cohort
    return nil if campaign.blank? || user_cohort.blank?

    # Get the offer amount based on the user cohort and percentage discount and plan amount
    offer_amount = case user_cohort
                   when "cohort_1"
                     plan.total_amount - (campaign.cohort_discount_percentage_for_campaign(
                       cohort: user_cohort, plan_duration_in_months: plan.duration_in_months).to_f / 100) * plan.total_amount
                   when "cohort_2"
                     plan.total_amount - (campaign.cohort_discount_percentage_for_campaign(
                       cohort: user_cohort, plan_duration_in_months: plan.duration_in_months).to_f / 100) * plan.total_amount
                   when "cohort_3"
                     plan.total_amount - (campaign.cohort_discount_percentage_for_campaign(
                       cohort: user_cohort, plan_duration_in_months: plan.duration_in_months).to_f / 100) * plan.total_amount
                   when "cohort_4"
                     plan.total_amount - (campaign.cohort_discount_percentage_for_campaign(
                       cohort: user_cohort, plan_duration_in_months: plan.duration_in_months).to_f / 100) * plan.total_amount
                   else
                     plan.amount
                   end
    offer_amount.to_i
  end

  def has_seen_offer_campaign_already?(campaign_id:)
    UserMetadatum.exists?(user_id: id, key: Constants.campaign_offer_reveal_status_key(campaign_id))
  end
end
