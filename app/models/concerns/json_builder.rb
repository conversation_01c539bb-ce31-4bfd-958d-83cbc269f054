module JsonBuilder
  extend ActiveSupport::Concern

  class_methods do
    def build_creative_carousel(title:, items:, next_page_url:, analytics_params:, feed_item_id:,
                                item_to_carousel_width_ratio: nil, creative_width: 400, creative_height: 500,
                                live_config: nil)
      {
        feed_type: 'creative_carousel',
        feed_item_id: feed_item_id,
        title: title,
        subtitle: nil,
        icon: nil,
        creative_width: creative_width,
        creative_height: creative_height,
        item_to_carousel_width_ratio: item_to_carousel_width_ratio,
        items: items,
        cta_text: nil,
        cta_deeplink: nil,
        cta_description: nil,
        live_config: live_config,
        next_page_url: next_page_url,
        analytics_params: analytics_params,
      }
    end

    def build_poster_carousel(user:, items:, show_more:, params:, enable_help_button: true)
      json = {
        feed_type: 'poster_carousel',
        feed_item_id: "#{Constants.poster_carousel_feed_item_id}_#{Time.zone.now.to_i}",
        items: items,
        show_help: user.is_poster_subscribed && enable_help_button
      }
      if show_more
        # Construct the query string from the params
        query_string = params.map { |key, value| "#{key}=#{value}" }.join('&')

        # Only add the query string if it's not empty
        more_deeplink = "/posters/layout"
        more_deeplink += "?#{query_string}" unless query_string.empty?
        json.merge!(
          more_cta: 'మరిన్ని డిజైన్లు పొందండి',
          more_deeplink: more_deeplink)
      end
      json
    end
  end
end
