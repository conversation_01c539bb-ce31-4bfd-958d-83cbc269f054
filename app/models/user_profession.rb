class UserProfession < ApplicationRecord
  belongs_to :user
  belongs_to :profession
  belongs_to :sub_profession, optional: true
  after_commit :update_floww_lead_score

  # Political leader(excluding <PERSON><PERSON><PERSON><PERSON>) or Community leader
  #
  # This user segmentation was created for optimising premium sales funnel
  def leader?
    Constants.leader_profession_ids.include?(profession_id)
  end

  def update_floww_lead_score
    # just update is enough because if user profession is selected and he is eligible as lead then he is getting added
    # when user profession is created, this function purpose is if user profession is updated then we need to update
    # the lead score
    UpdateLeadScore.set(queue: :critical).perform_async(user_id)
  end
end
