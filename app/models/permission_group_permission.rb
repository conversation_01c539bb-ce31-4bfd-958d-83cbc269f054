class PermissionGroupPermission < ApplicationRecord
  PERMISSION_IDENTIFIER = %i[remove_tag add_tag channel_send_message circle_upload_creative circle_share_poster private_group_send_message delete_message add_admin remove_admin add_sender remove_sender]
  enum permission_identifier: PERMISSION_IDENTIFIER, _suffix: true

  belongs_to :permission_group

  validates :permission_identifier, inclusion: { in: permission_identifiers.keys }, presence: true, allow_blank: false

  after_commit :flush_cache
  CACHE_KEY = 'permission_group_v1'

  def self.get_permissions_of_permission_group(permission_group_id)
    Rails.cache.fetch([CACHE_KEY, permission_group_id], expires_in: 1.month) do
      PermissionGroupPermission.where(permission_group_id: permission_group_id).pluck(:permission_identifier)
    end
  end

  def flush_cache
    Rails.cache.delete([CACHE_KEY, permission_group_id])
  end

end
