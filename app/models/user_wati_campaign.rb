# frozen_string_literal: true

class UserWatiCampaign < ApplicationRecord
  include AASM
  has_paper_trail

  belongs_to :user

  enum campaign_type: {
    trial_activation: "trial_activation",
    subscription_reactivation: "subscription_reactivation"
  }

  enum status: {
    triggered: "triggered",
    sent: "sent",
    delivered: "delivered",
    read: "read",
    replied: "replied",
    failed: "failed"
  }

  aasm column: :status, enum: true do
    state :triggered, initial: true
    state :sent
    state :delivered
    state :read
    state :replied
    state :failed
  end
end
