class Metadatum < ApplicationRecord
  belongs_to :entity, polymorphic: true
  after_commit :update_affiliated_party_on_user
  after_create_commit :send_trial_enabled_notification,
                      :send_trial_ended_event_to_mixpanel,
                      :send_trial_extension_created_event_to_mixpanel,
                      :send_trial_started_event_to_mixpanel_using_callback

  def update_affiliated_party_on_user
    entity.update_affiliated_party_circle_id if entity_type == 'User' && key == Constants.poster_affiliated_party_key
  end

  def self.save_user_trial_data_for_autopay(user, trial_duration)
    Metadatum.where(entity: user,
                    key: Constants.user_poster_trial_duration_key)
             .first_or_create(value: trial_duration)
  end

  # save user poster trial start date and duration in metadatum table in a single transaction
  # def self.save_user_trial_data(user, trial_start_date, poster_trial_duration, admin_user: nil)
  #   ActiveRecord::Base.transaction do
  #     Metadatum.create(entity: user, key: Constants.user_poster_trial_start_date_key, value: trial_start_date)
  #     Metadatum.create(entity: user, key: Constants.user_poster_trial_duration_key, value: poster_trial_duration)
  #   end
  #
  #   premium_pitch = user.premium_pitch
  #   if premium_pitch.present? && premium_pitch.may_enabled_trial?
  #     premium_pitch.enabled_trial!
  #   end
  # end

  # get user trial duration and start date from metadatum table and return as hash
  def self.get_user_trial_start_date_and_duration(user)
    hash = Metadatum.where(entity: user, key: [Constants.user_poster_trial_duration_key, Constants.user_poster_trial_start_date_key])
                    .pluck(:key, :value).to_h
    # return as an array [trial_start_date,  trial_duration ] using hash
    [hash[Constants.user_poster_trial_start_date_key]&.to_date, hash[Constants.user_poster_trial_duration_key].to_i]
  end

  # user can have multiple trial start dates and durations.
  def self.user_trial_start_dates_and_durations(user)
    # return as an array of [trial_start_date,  trial_duration ] using hash
    Metadatum.where(entity: user, key: [Constants.user_poster_trial_duration_key,
                                        Constants.user_poster_trial_start_date_key])
             .order(:id)
             .pluck(:key, :value).group_by(&:first).transform_values { |v| v.map(&:last) }
  end

  # send notification to user when poster trial starts
  # here we are considering poster trial start based on user trial start date is present in metadatum or not
  def send_trial_enabled_notification
    return unless entity.is_a?(User) && key == Constants.user_poster_trial_duration_key && entity.has_premium_layout?
    notification_title = "#{value} రోజులు ఉచిత ట్రయల్ ప్రారంభించండి"
    notification_body = "మీరు పోస్టర్లను ఫ్రీ గా షేర్ చేసుకోవచ్చు!"
    user_id = entity.id
    payload = {
      "title": notification_title,
      "body": notification_body,
      "message_channel": "poster_trial_notification",
      "data": {
        "path": "/premium-experience?source=trial_notification",
      }
    }
    # track mixpanel event
    EventTracker.perform_async(user_id, "poster_trial_notification_sent", { "user_id" => user_id,
                                                                            "trial_duration" => value })

    GarudaNotification.send_user_notification(user_id, payload)

  end

  def send_trial_started_event_to_mixpanel_using_callback
    return unless entity.is_a?(User) && key == Constants.user_poster_trial_start_date_key
    trial_duration = Metadatum.where(entity: entity, key: Constants.user_poster_trial_duration_key).last&.value
    trial_duration = trial_duration.to_i
    return if trial_duration.zero?

    self.class.send_trial_started_event_to_mixpanel(user_id: entity_id, trial_start_date: value.to_date,
                                                    poster_trial_duration: trial_duration, admin_user: nil)
  end

  def self.send_trial_started_event_to_mixpanel(user_id:, trial_start_date:, poster_trial_duration:, admin_user: nil)
    # trial start date is in range of today
    if trial_start_date >= Time.zone.today.beginning_of_day && trial_start_date <= Time.zone.today.end_of_day
      user = User.find_by(id: user_id)
      is_trial_extension_user = user.is_trial_extension_user?
      if is_trial_extension_user
        event_name = "poster_trial_extension_started"
      else
        event_name = "poster_trial_started"
      end
      EventTracker.perform_async(user_id, event_name, {
        "trial_duration" => poster_trial_duration,
        "trial_start_date" => trial_start_date,
        "trial_end_date" => trial_start_date.advance(days: poster_trial_duration.to_i - 1),
        "admin_user_id" => admin_user&.id,
        "admin_user_email" => admin_user&.email
      }.compact)
      SyncMixpanelUser.perform_async(user_id)
    end
  end

  def send_trial_ended_event_to_mixpanel
    return unless entity.is_a?(User) && key == Constants.user_poster_trial_end_date_key
    is_trial_extension_ended = Metadatum.where(entity_type: 'User',
                                               entity_id: entity_id,
                                               key: Constants.user_poster_trial_end_date_key).count > 1
    if is_trial_extension_ended
      event_name = "poster_trial_extension_ended"
    else
      event_name = "poster_trial_ended"
    end

    EventTracker.perform_async(entity_id, event_name, {
      "trial_end_date" => value
    })
    SyncMixpanelUser.perform_async(entity_id)
  end

  def self.get_trial_end_date_incld_extensions(user_id:)
    start_date = Metadatum.where(entity_type: 'User', entity_id: user_id, key: Constants.user_poster_trial_start_date_key).last&.value&.to_date
    return nil unless start_date.present?

    duration = Metadatum.where(entity_type: 'User', entity_id: user_id, key: Constants.user_poster_trial_duration_key).last&.value.to_i
    return nil if duration.zero?

    start_date.advance(days: duration - 1)
  end

  def send_trial_extension_created_event_to_mixpanel
    return unless entity.is_a?(User) && key == Constants.user_poster_trial_start_date_key
    is_trial_extension_user = entity.is_trial_extension_user?
    if is_trial_extension_user
      event_name = "poster_trial_extension_created"
    else
      event_name = "poster_trial_created"
    end
    EventTracker.perform_async(entity_id, event_name, {
      "trial_start_date" => value
    })
  end

  def self.get_user_trail_duration(user_id:)
    Metadatum.where(entity_type: :User, entity_id: user_id, key: Constants.user_poster_trial_duration_key).last&.value.to_i
  end
end
