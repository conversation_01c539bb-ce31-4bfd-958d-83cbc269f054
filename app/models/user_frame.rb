class UserFrame < ApplicationRecord
  belongs_to :user
  belongs_to :frame

  has_paper_trail
  before_save :set_whodunnit

  enum status: {
    active: 'active',
    preview: 'preview',
  }

  scope :active, -> { where(status: :active) }

  def self.frame_type_counts(user_id)
    UserFrame.joins(:frame).where(user_id: user_id).group('frames.frame_type').count
  end

  def self.get_user_frame_types(user_id)
    frame_type_counts(user_id).keys.map(&:to_sym)
  end

  def self.get_user_frame_ids(user_id)
    UserFrame.where(user_id: user_id).pluck(:frame_id)
  end

  private

  def set_whodunnit
    if defined?(current_admin_user) &&current_admin_user.present?
      PaperTrail.request.whodunnit = current_admin_user.id.to_s
    end
  end
end
