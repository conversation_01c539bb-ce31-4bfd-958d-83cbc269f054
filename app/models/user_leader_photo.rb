class UserLeaderPhoto < ApplicationRecord
  has_paper_trail
  belongs_to :creator, polymorphic: true
  belongs_to :user_poster_layout
  belongs_to :photo, polymorphic: true, optional: true
  belongs_to :user, optional: true
  belongs_to :circle, optional: true

  enum header_type: %i[header_1 header_2], _suffix: true
  validates_presence_of :header_type, :priority

  # Protocol generation enabled for comprehensive poster content generation
  after_commit :queue_protocol_generation, on: [:create, :update, :destroy]

  private

  def queue_protocol_generation
    return unless user_poster_layout.entity.is_a?(User)

    # Use 5-second delay to batch multiple changes for the same user
    GenerateProtocolImageWorker.perform_in(5.seconds, user_poster_layout.entity_id)
  end
end
