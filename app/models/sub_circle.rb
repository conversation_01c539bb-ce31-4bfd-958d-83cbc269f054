# frozen_string_literal: true
class SubCircle < Circle
  default_scope { where(circle_type: :sub) }
  enum conversation_type: Constants.conversation_types, _suffix: true
  belongs_to :parent_circle, class_name: 'Circle', foreign_key: 'parent_circle_id'
  has_many :sub_circle_filters, dependent: :destroy

  def self.check_eligibility_to_join_user(role_ids=[], user_location_circle_ids=[], user_purview_location_circle_ids=[], filter_role_ids=[], filter_location_circle_ids=[])
    if filter_location_circle_ids.present? && filter_role_ids.present?
      return (role_ids & filter_role_ids).present? &&
        (( user_location_circle_ids + user_purview_location_circle_ids) &
          filter_location_circle_ids).present?
    elsif filter_location_circle_ids.present?
      return (user_location_circle_ids & filter_location_circle_ids).present?
    elsif filter_role_ids.present?
      return (role_ids & filter_role_ids).present?
    end
    false
  end

end
