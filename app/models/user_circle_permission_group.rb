class UserCirclePermissionGroup < ApplicationRecord
  belongs_to :user
  belongs_to :circle
  belongs_to :permission_group

  validates :user_id, :circle_id, :permission_group_id, numericality: { only_integer: true },
            presence: true, allow_blank: false
  validates_uniqueness_of :circle_id, scope: :permission_group_id,
                          :if => lambda { |ucp| ucp.permission_group_id == Constants.owner_permission_group_id },
                          :message => "Owner already assigned for this circle"
  validate :check_allowed_columns_to_change

  after_commit :create_leader_to_location_relation, :auto_join_owner_in_circle, :send_user_circle_permission_update_to_dm, :flush_cache

  CACHE_KEY = 'user_circle_permission_group_v1'

  def check_allowed_columns_to_change
    if !new_record? && (user_id_changed? || circle_id_changed?)
      errors.add("can't change user or circle")
    end
  end

  def create_leader_to_location_relation
    return unless circle.political_leader_level?

    # Owner assigned to circle then either create new or update existing Leader2Location relation with owner mandal
    # As for now only mandal is considering while creating Leader2Location relation
    if (saved_change_to_id? || saved_change_to_permission_group_id?) && permission_group_id == Constants.owner_permission_group_id
      if circle.first_circle_relations.where(relation: :Leader2Location).exists?
        circle.first_circle_relations.where(relation: :Leader2Location).last.update(second_circle_id: user.mandal_id)
      else
        CirclesRelation.create(first_circle_id: circle_id, second_circle_id: user.mandal_id, relation: :Leader2Location)
      end
    end
  end

  def self.get_user_circle_permission_group_id(user_id, circle_id)
    Rails.cache.fetch([CACHE_KEY, user_id, circle_id], expires_in: 1.month) do
      UserCirclePermissionGroup.where(user_id: user_id, circle_id: circle_id)
                               .pluck(:permission_group_id).first
    end
  end

  def flush_cache
    Rails.cache.delete([CACHE_KEY, user_id, circle_id])
  end

  def send_user_circle_permission_update_to_dm
    if circle.channel_conversation_type?
      DmUtil.send_user_circle_permission_update_to_dm(user_id, circle_id)
    end
  end

  def auto_join_owner_in_circle
    unless UserCircle.where(circle_id: circle_id, user_id: user_id).exists?
      UserCircle.create(circle_id: circle_id, user_id: user_id, source_of_join: :auto)
    end
  end
end
