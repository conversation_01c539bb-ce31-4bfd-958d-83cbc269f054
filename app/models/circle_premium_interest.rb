class CirclePremiumInterest < ApplicationRecord
  belongs_to :circle
  belongs_to :user

  def self.fan_poster_interested_users(circle, count: nil)
    circle_id = circle.id
    affiliated_party_id = circle_id
    circle_relation = nil
    if circle.political_leader_level?
      affiliated_party_id = circle.get_leader_circle_affiliated_party_id
      circle_relation = CirclesRelation.where(second_circle_id: circle_id, relation: [:MLA, :MP, :MLA_Contestant,
                                                                                      :MP_Contestant]).first
    end

    mla_const_circle_id = 0
    mp_const_circle_id = 0

    # for MLA_Contestant circle or MLA get the mla_constituency_id
    # for MP_Contestant circle or <PERSON> get the mp_constituency_id
    if circle_relation.present? && circle_relation.relation.to_sym.in?([:MLA, :MLA_Contestant])
      mla_const_circle_id = circle_relation.first_circle_id
    elsif circle_relation.present? && circle_relation.relation.to_sym.in?([:MP, :MP_Contestant])
      mp_const_circle_id = circle_relation.first_circle_id
    end

    # Sorting priorities:
    # 1. Affiliated party circle ID match.
    # 2. MLA or MP constituency ID match.
    # 3. Grade level (NULLs treated as lowest).

    user_ids = CirclePremiumInterest.where(circle_id: circle_id, key: Constants.user_fan_poster_interests_key)
                                    .joins("INNER JOIN users ON users.id = circle_premium_interests.user_id")
                                    .joins("LEFT JOIN user_roles ON user_roles.user_id = users.id AND
                                        user_roles.primary_role = 1 AND user_roles.active = 1")
                                    .joins("LEFT JOIN roles ON roles.id = user_roles.role_id AND roles.active = 1")
                                    .select("users.id AS user_id")
                                    .order(Arel.sql("
                                      CASE
                                        WHEN users.affiliated_party_circle_id = #{affiliated_party_id} THEN 1
                                        WHEN users.affiliated_party_circle_id IS NULL THEN 2
                                        ELSE 3
                                      END,
                                      CASE
                                        WHEN users.mla_constituency_id = #{mla_const_circle_id} THEN 1
                                        WHEN users.mp_constituency_id = #{mp_const_circle_id} THEN 2
                                        ELSE 3
                                      END,
                                      IF(user_roles.grade_level IS NULL, IF(roles.grade_level IS NULL, 1000,
                                      roles.grade_level), user_roles.grade_level) ASC
                                    "))
                                    .limit(count)
                                    .pluck("users.id")

    User.where(id: user_ids).order(Arel.sql("FIELD(id, #{user_ids.join(', ')})")).map do |u|
      {
        id: u.id,
        name: u.name,
        photo: u.photo,
        hashid: u.hashid,
        badge: u.get_badge_role&.get_json,
        avatar_color: u.avatar_color
      }
    end
  end

  def self.fan_poster_interested_users_count(circle_id)
    self.where(circle_id: circle_id, key: Constants.user_fan_poster_interests_key).count
  end

  def self.has_fan_poster_interest?(circle_id:, user_id:)
    self.where(circle_id: circle_id, user_id: user_id, key: Constants.user_fan_poster_interests_key).exists?
  end
end
