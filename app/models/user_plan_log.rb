class UserPlanLog < ApplicationRecord
  belongs_to :user
  belongs_to :plan
  belongs_to :entity, polymorphic: true
  belongs_to :user_plan, foreign_key: :user_id, primary_key: :user_id, optional: true
  # start_date: exclusive for MRR calculation and inclusive for whether premium active or not
  validates_presence_of :user_id, :plan_id, :start_date, :end_date

  validates :user_id, uniqueness: {
    scope: [:entity_id, :entity_type],
    message: "user plan log already exists for this user and entity"
  }

end
