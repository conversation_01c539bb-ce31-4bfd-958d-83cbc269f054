class AdminUser < ApplicationRecord
  belongs_to :admin_medium, optional: true
  has_many :leader_projects, as: :creator
  has_many :events, as: :creator
  has_many :poster_creatives, as: :creator
  has_many :user_poster_layouts, as: :creator
  has_many :videos, as: :user

  has_paper_trail
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :rememberable, :validatable,
         :omniauthable, :trackable

  ROLE = { admin: 'admin', normal: 'normal', content_creator: 'content_creator', op_executive: 'op_executive', sales_am: 'sales_am',
           relationship_manager: 'relationship_manager', graphic_designer: 'graphic_designer', layout_auditor: 'layout_auditor',
           senior_relationship_manager: 'senior_relationship_manager', support_specialist: 'support_specialist',
           agent_partner: 'agent_partner'
  }
  enum role: ROLE, _suffix: true

  validate :floww_user_id_for_required_roles

  with_options presence: true do
    validates :email, :phone
    validates :uid, uniqueness: { scope: :provider }, if: -> { provider.present? && uid.present? }
  end
  
  def photo_url
    admin_medium&.url
  end

  def floww_user_id_for_required_roles
    if ['relationship_manager', 'senior_relationship_manager', 'sales_am'].include?(role) && floww_user_id.blank?
      errors.add(:floww_user_id, "Floww id must be present for the selected role")
    end
  end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  # Devise override to ignore the password requirement if the user is authenticated
  def password_required?
    return false if provider.present?
    super
  end

  class << self
    def from_omniauth(auth)
      if auth.info.email_verified
        admin_user = where(email: auth.info.email, active: true).first
        admin_user ||= where(auth.slice(:provider, :uid).to_h.merge(active: true)).first
        if admin_user
          admin_user.update(provider: auth.provider, uid: auth.uid, email: auth.info.email)
          return admin_user
        else
          raise 'User not found. Request your manager for access.'
        end
      else
        raise 'Email not verified'
      end
    end
  end
end
