class PosterShare < ApplicationRecord
  belongs_to :poster_creative
  belongs_to :frame, optional: true
  belongs_to :user
  after_commit :index_creative_for_posters_feed, :update_floww_lead_score, on: :create

  enum method: {
    whatsapp: "whatsapp",
    external_share: "external-share",
    download: "download",
    unknown: "unknown"
  }

  private

  def index_creative_for_posters_feed
    if poster_creative.event_id.present?
      IndexCreativesForPostersFeed.perform_async("event_#{poster_creative.event_id}")
    else
      IndexCreativesForPostersFeed.perform_async("creative_#{poster_creative.id}")
    end
  end

  def update_floww_lead_score
    UpdateLeadScore.perform_async(user_id)
  end
end
