class Post < ApplicationRecord
  has_paper_trail
  include Hashid::Rails

  COMMENTS_TYPE = %i[all_users none exclusive_party_users exclusive_party_and_neutral_users]
  enum comments_type: COMMENTS_TYPE, _suffix: true

  belongs_to :user
  belongs_to :parent_post, class_name: 'Post', foreign_key: 'parent_post_id', optional: true
  has_many :post_photos
  has_many :post_videos
  has_many :post_user_tag
  has_many :post_hashtags
  has_many :photos, through: :post_photos
  has_many :videos, through: :post_videos
  has_many :hashtags, through: :post_hashtags
  has_many :likes, class_name: "PostLike"
  has_many :comments, -> { where(active: true) }, class_name: 'PostComment'
  has_many :post_likes, -> { where(active: true) }
  has_many :post_circles, -> { where(active: true) }
  has_many :circles, through: :post_circles
  has_many :metadatum, as: :entity
  has_one :poll
  has_many :reports, as: :reference
  has_many :taggings, as: :taggable

  accepts_nested_attributes_for :post_circles

  attr_accessor :approved_declined_items, :circle_name, :circle_id

  attribute :hashid
  attribute :user_liked
  attribute :user_circled
  attribute :user_seen
  attribute :photos
  attribute :videos
  attribute :likes_count
  attribute :views_count
  attribute :whatsapp_count
  attribute :comments_count
  attribute :opinions_count
  attribute :circle
  attribute :tagged_circles
  attribute :user
  # attribute :poll
  attribute :link
  attribute :parent_post
  attribute :liked_users
  attribute :is_logged_in_user_post
  attribute :is_logged_in_user_forward

  attribute :feed_type
  attribute :feed_item_id
  attribute :feed_type_timestamp
  attribute :feed_reason
  attribute :user_tags
  attribute :hashtags
  attribute :preview_comments
  attribute :post_share_card_gradients
  attribute :post_share_card_text
  attribute :show_post_card_flag
  attribute :enable_comments
  attribute :dm_text_message
  attribute :send_dm_message_notification

  validate :check_post_content_nil
  validate :check_post_content_length_limit, if: -> { errors.blank? }
  validate :check_post_duplicate, if: -> { errors.blank? }
  validate :check_post_photos_and_videos_count, if: -> { errors.blank? }
  validate :check_tagged_circles, if: -> { errors.blank? }

  before_validation :generate_link_preview, :set_has_tagged_circles, :strip_post_content

  after_create_commit :populate_fantom_views, :log_last_post_content, :internal_notification_delay,
                      :parent_post_notification, :set_like_notification_key, :index_for_search_after_create,
                      :fetch_post_tags, :send_media_data_to_media_service, :send_post_to_dm_service
  after_update_commit :index_for_search
  after_update_commit :remove_post_id_from_trending_feed_cache, unless: -> { active }
  after_commit :flush_cache

  before_save :remove_content_from_redis, :if => lambda { |p| !p.active? && p.active_changed? }

  CACHE_KEY = 'post_v63'
  COMMENTS_CACHE_KEY = 'post_comments_v11'

  def self.ransackable_associations(auth_object = nil)
    ["circles", "comments", "likes", "metadatum", "parent_post", "photos", "poll", "post_circles", "post_hashtags", "post_likes", "post_photos", "post_user_tag", "post_videos", "reports", "taggings", "user", "versions", "videos"]
  end

  def generate_link_preview
    urls = URI.extract(self.content.to_s, %w[http https])
    if !urls.nil? && urls.length.positive?
      link = Link.extract_preview_info(urls[0], self.user)
      self.link_id = link.id unless link.nil?
    end
  end

  def set_has_tagged_circles
    self.has_tagged_circles = self.post_circles.present? ? true : false
  end

  def strip_post_content
    self.content = self.content.blank? ? nil : self.content.strip
  end

  def check_post_duplicate
    if new_record? && duplicate?
      errors.add(:content, :invalid, message: "ఈ పోస్ట్ ను మీరు ఇంతకు ముందే పోస్ట్ చేసేసారు!")
    end
  end

  def check_post_content_nil
    unless self.content.present? || self.post_photos.present? || self.post_videos.present?
      errors.add(:base, :invalid, message: "పోస్ట్ ఖాళీ గా ఉండకూడదు!")
    end
  end

  def check_post_content_length_limit
    max_limit_text = 65535
    if self.content.present? && self.content.length > max_limit_text
      errors.add(:content, message: "పోస్ట్ కంటెంట్ ఎక్కువ ఉంది. దయచేసి తగ్గించండి.")
    end
  end

  def check_post_photos_and_videos_count
    if self.post_photos.present? && self.post_videos.present?
      errors.add(:base, :invalid, message: "ఫోటోలు మరియు వీడియో కలిపి జత చెయ్యడం సాధ్యంకాదు!")
    end

    if self.post_photos.present?
      if self.post_photos.size > Photo::MAX_POST_PHOTOS_COUNT
        errors.add(:base, :invalid, message: "#{Photo::MAX_POST_PHOTOS_COUNT} ఫోటోలు మాత్రమే జతచేయగలరు!")
      end
    end

    if self.post_videos.present? && self.post_videos.size > 1
      errors.add(:base, :invalid, message: "ఒక వీడియో మాత్రమే జతచేయగలరు!")
    end
  end

  def check_tagged_circles
    if self.post_circles.length > Circle::MAX_CIRCLES
      errors.add(:post_circles, :invalid, message: "మూడు గ్రూపులను మాత్రమే ఎంచుకోగలరు!")
    end

    tagged_circles = Circle.where(id: self.post_circles.map(&:circle_id))
    level_counts = tagged_circles.map(&:level_before_type_cast).tally # Tallys the collection. Returns a hash where the keys are the elements and the values are numbers of elements
    case true
    when level_counts[1].present? && level_counts[1] > Circle::MAX_VILLAGE_CIRCLES
      errors.add(:post_circles, :invalid, message: "ఒక గ్రామాన్ని మాత్రమే ఎంచుకోగలరు!")
    when level_counts[6].present? && level_counts[6] > Circle::MAX_PARTY_CIRCLES
      errors.add(:post_circles, :invalid, message: "ఒక పొలిటికల్ పార్టీ మాత్రమే ఎంచుకోగలరు!")
    when level_counts[7].present? && level_counts[7] > Circle::MAX_LEADER_CIRCLES
      errors.add(:post_circles, :invalid, message: "రెండు లీడర్ గ్రూపులను మాత్రమే ఎంచుకోగలరు!")
    end
  end

  def parent_post_notification
    return unless parent_post.present? && parent_post.user.id != user.id

    notification_title = user.name + ' మీ పోస్ట్ ని షేర్ చేశారు'
    notification_body = 'పోస్ట్ ను చూడటానికి క్లిక్ చేయండి'
    notification_body = content.truncate(47, separator: ' ') if content.present?

    notification = Notification.create!(description: notification_title, notification_type: :circle, user: parent_post.user, entity_type: 'post', entity_id: id)
    payload = {
      "title": "🔄 #{notification_title}",
      "body": notification_body,
      "message_channel": 'share',
      "data": {
        "path": "/posts/#{id}",
        "circle_notification_id": notification.id.to_s
      }
    }
    GarudaNotification.send_user_notification(parent_post.user_id, payload)
  end

  def set_like_notification_key
    $redis.set("latest_notif_time_#{id}", created_at)
    CheckForLatestNotification.perform_in(2.hours, id, created_at)
  end

  def send_media_data_to_media_service
    # Assumption taken was all photos will be of same media service, similarly for videos
    if post_photos.present?
      SendPostMediaToMediaService.perform_async(id)
    elsif post_videos.present?
      SendPostMediaToMediaService.perform_async(id)
    end
  end

  def send_post_to_dm_service
    send_dm_message_notification = send_dm_message_notification.present? ? send_dm_message_notification : false
    ucp_as_owner = user.get_user_circle_permission_as_owner
    if ucp_as_owner.present? && user.is_eligible_to_send_post_message?
      is_circle_tagged = post_circles.map(&:circle_id).include?(ucp_as_owner.circle_id)
      if is_circle_tagged
        circle = ucp_as_owner.circle
        send_post_msg_hash = {
          post_id: id,
          circle_id: circle.id,
          text: "",
          send_dm_message_notification: send_dm_message_notification
        }
        SendDmPostMessage.perform_async(send_post_msg_hash.to_json) if circle.present? && circle.channel_conversation_type?
      end
    end
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i(badge_user_eq badge_color_eq user_district_id_equals user_role_id_eq user_grade_eq pending_tags_eq)
  end

  def self.user_role_id_eq(value)
    value = 1 if value === true
    value = value.to_i

    joins(user: :user_roles).where("user_roles.role_id = ?", value) if value.present?
  end

  def self.user_grade_eq(value)
    value = 1 if value === true
    value = value.to_i

    joins(user: { user_roles: :role })
      .where("user_roles.grade_level = ? OR (user_roles.grade_level IS NULL AND roles.grade_level = ?)", value, value) if value.present?
  end

  def self.badge_color_eq(value)
    joins(user: { user_roles: :role })
      .where("user_roles.badge_color = ? OR (user_roles.badge_color IS NULL AND roles.badge_color = ?)", value, value) if value.present?
  end

  ActiveRecord::Base.connected_to(role: :reading) do
    def self.badge_user_eq(value)
      if value == 'yes'
        joins(user: :user_roles).where(user_roles: { primary_role: true, active: true })
      elsif value == 'no'
        where.not(
          user_id: User.joins(:user_roles).where(user_roles: { active: true })
        )
      end
    end
  end

  def self.pending_tags_eq(value)
    if value == 'yes'
      joins(:taggings).where(taggings: { status: :pending }).group(:id)
    elsif value == 'no'
      joins(:taggings).where.not(taggings: { status: :pending }).group(:id)
    end
  end

  def self.user_district_id_equals(value)
    joins(:user).where(users: { district_id: value })
  end

  def populate_fantom_views
    TriggerPostViewPopulation.perform_async(id) if created_at > Time.zone.parse('19-07-2022 15:35:00')
  end

  def flush_cache
    Rails.cache.delete([CACHE_KEY, id])
  end

  def self.enable_comments(logged_in_user, post_comments_type, post_party_id_on_comments_type)
    # disable commenting for the users who are blocked form commenting
    return false if logged_in_user.is_blocked_for_commenting?

    logged_in_user_affiliated_party_id = logged_in_user.affiliated_party_circle_id || 0
    case post_comments_type.to_sym
    when :all_users
      true
    when :exclusive_party_users
      post_party_id_on_comments_type == logged_in_user_affiliated_party_id
    when :exclusive_party_and_neutral_users
      [post_party_id_on_comments_type, 0].include? logged_in_user_affiliated_party_id
    else
      false
    end
  end

  def self.get_comments_config(default_comments_type_in_post, is_logged_in_user_post, enable_comments, logged_in_user, post_party_id_on_comments_type)
    available_comment_options = comments_disabled_text = comments_info_header_text = comments_info_body_text = default_comments_type_identifier = nil
    if is_logged_in_user_post
      default_comments_type_identifier = Post.comments_types[default_comments_type_in_post]
      available_comment_options = logged_in_user.available_post_comment_options(false, post_party_id_on_comments_type)
      comments_info_header_text = "కామెంట్ చేయగలుగు వారు"
      comments_info_body_text = "మీ పోస్ట్ అందరికీ కనిపిస్తుంది, కానీ ఎవరు కామెంట్ పెట్టగలరో మీరు పరిమితం చేయవచ్చు."
    end
    comments_disabled_text = "ఈ పోస్ట్ కి కామెంట్ చేయడం నిలిపివేయబడింది" unless enable_comments

    comments_config = {
      comments_info_header_text: comments_info_header_text,
      comments_info_body_text: comments_info_body_text,
      default_comments_type_identifier: default_comments_type_identifier,
      available_comment_options: available_comment_options,
      comments_disabled_text: comments_disabled_text
    }.compact
    comments_config.present? ? comments_config : nil
  end

  def self.get_feed_item(post_id, logged_in_user, app_version = nil, circle = nil)
    if post_id.to_i.to_s != post_id.to_s
      post_id = self.decode_id(post_id)
    else
      post_id = post_id.to_i
    end

    feed_item = Rails.cache.fetch([CACHE_KEY, post_id], expires_in: 36.hours) do
      post_db = Post.includes([:photos, :videos, :post_hashtags, :circles]).where(id: post_id).last
      # for any key value addition, need an update of CACHE_KEY version
      {
        id: post_id,
        hashid: post_db.hashid,
        content: post_db.content,
        created_at: post_db.created_at,
        feed_type: 'circle_posted',
        feed_item_id: post_id.to_s,
        is_poll: false,
        poll: nil,
        user_id: post_db.user_id,
        tagged_circles_ids: post_db.circles.where.not(id: 0).ids,
        parent_post_id: post_db.parent_post_id,
        videos: post_db.get_post_videos,
        photos: post_db.get_post_photos,
        hashtags: post_db.get_post_hashtags,
        link: post_db.link,
        active: post_db.active,
        comments_type: post_db.comments_type,
        party_id_on_comments_type: post_db.party_id_on_comments_type
      }
    end

    post = self.new
    post.id = post_id
    post.created_at = feed_item[:created_at]

    feed_item[:circle_id] = case true
                            when circle.nil?
                              0
                            when (feed_item[:tagged_circles_ids].include? circle.id)
                              circle.id
                            when (feed_item[:tagged_circles_ids] & circle.get_all_child_circle_ids).present?
                              (feed_item[:tagged_circles_ids] & circle.get_all_child_circle_ids).first
                            else
                              0
                            end

    feed_item[:parent_post] = feed_item[:parent_post_id].present? ? self.get_feed_item(feed_item[:parent_post_id], logged_in_user, app_version, circle) : nil

    feed_item[:user] = User.get_feed_item(feed_item[:user_id])
    post.user = feed_item[:user]
    feed_item[:user].loggedInUser = post.user_id == logged_in_user.id
    # feed_item[:user].follows = feed_item[:user_id] == logged_in_user.id ? nil : UserFollower.where(user_id: feed_item[:user_id], follower: logged_in_user).exists?
    feed_item[:user].blocked = false # feed_item[:user_id] == logged_in_user.id ? false : BlockedUser.where(blocked_user_id: feed_item[:user_id], user: logged_in_user).exists?

    feed_item[:user].photo.compressed_url!(size: 100) if feed_item[:user].photo.present?

    feed_item[:circle] = Circle.get_feed_item(feed_item[:circle_id], logged_in_user)
    feed_item[:tagged_circles] = feed_item[:tagged_circles_ids].map do |tci|
      Circle.get_feed_item(tci, logged_in_user)
    end

    feed_item[:is_logged_in_user_post] = post.user_id == logged_in_user.id

    feed_item[:enable_comments] = if feed_item[:is_logged_in_user_post]
                                    true
                                  else
                                    Post.enable_comments(logged_in_user,
                                                         feed_item[:comments_type],
                                                         feed_item[:party_id_on_comments_type])
                                  end
    feed_item[:comments_config] = get_comments_config(feed_item[:comments_type], feed_item[:is_logged_in_user_post], feed_item[:enable_comments], logged_in_user, feed_item[:party_id_on_comments_type])

    feed_item[:user_circled] = false # post.is_circled(logged_in_user)
    feed_item[:user_seen] = post.is_seen(logged_in_user)
    feed_item[:views_count] = nil
    feed_item[:views_count] = post.views_count_display if logged_in_user.is_views_enabled?
    feed_item[:post_share_card_text] = post.post_share_card_text_util(feed_item[:videos].count)
    feed_item[:show_post_card_flag] = Post.show_post_card_flag_util(feed_item)
    feed_item[:photo_upload_tool_tip] = post.photo_upload_tool_tip
    feed_item[:read_more_text] = post.read_more_text

    feed_item[:post_share_card_gradients] = nil
    feed_item[:admin_options] = nil
    if app_version.present?
      feed_item[:post_share_card_gradients] = post.post_share_card_gradients_util(app_version)
      feed_item[:admin_options] = Circle.get_admin_options(logged_in_user, post_id, feed_item[:tagged_circles_ids], app_version)
    end

    if app_version.present?
      if app_version <= Gem::Version.new("2401.05.00")
        feed_item[:photos] = feed_item[:photos].first(3)
      end
    end

    feed_item
  end

  def get_post_hashtags
    self.hashtags.map do |post_hashtag|
      {
        post_id: id,
        hashtag_id: post_hashtag.hashtag_id,
        tag: post_hashtag.tag
      }
    end
  end

  def get_post_comments
    all_comments = Rails.cache.fetch([COMMENTS_CACHE_KEY, id], expires_in: 1.month) do
      self.comments.pluck(:id, :comment, :user_id, :created_at).as_json
    end

    all_comments.map do |c|
      c_user = User.get_feed_item(c[2])
      c_user.loggedInUser = false
      c_user.follows = false
      c_user.blocked = false

      {
        id: c[0],
        comment: c[1],
        user_id: c[2],
        user: c_user,
        created_at: c[3]
      }
    end
  end

  def get_post_videos
    self.videos.map do |video|
      url = video.url
      url = video.url.gsub('https://gpuv.thecircleapp.in', 'https://az-gpuv.thecircleapp.in') if video.gcp? && url.present?
      {
        id: video.id,
        url: url || video.source_url,
        source_url: video.source_url,
        thumbnail_url: video.get_thumbnail_url,
        mode: video.mode == "square" ? "PORTRAIT" : video.mode&.upcase
      }
    end
  end

  def get_post_photos
    # as for now making default alignment as bottom center if post has on poster photo
    has_poster_photo = self.metadatum.where(key: Constants.get_is_poster_photo_key_for_post).exists?
    # alignment using x, y coordinates ( 0.0, 1.0 ) for bottom center and ( 0.0, -1.0 ) for top center
    alignment = has_poster_photo ? [0.0, 1.0] : [0.0, -1.0]

    self.photos.map do |photo|
      {
        id: photo.id,
        orig_url: photo.url,
        url: photo.url,
        placeholder_url: photo.placeholder_url,
        width: photo.width,
        height: photo.height,
        aspect_ratio: photo.aspect_ratio,
        bg_color: photo.bg_color,
        alignment: { x: alignment[0], y: alignment[1] },
      }
    end
  end
  

  def queue_mark_as_seen(seen_user_id)
    # Increase views count on post id
    $redis.hincrby(Constants.post_views_redis_key, id, 1)

    # log user-date level post ids
    user_date_post_views_queue = Constants.user_date_post_views_queue_redis_key(seen_user_id)
    $redis.sadd(user_date_post_views_queue, id)
    $redis.expireat(user_date_post_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

    # Queue for DB insertion
    QueuePostView.perform_async(id, seen_user_id)
  end

  def duplicate?
    return false if content.nil? || content.empty?

    current_unix = Time.zone.now.to_i
    last_posts = $redis.zrangebyscore("last_post_content_#{user_id}", current_unix - 5 * 60, current_unix)
    last_posts&.count&.positive? && last_posts.include?(content)
  end

  def log_last_post_content
    return false if content.nil? || content.empty?

    # set key
    key = "last_post_content_#{user_id}"
    # Add post content to Redis set
    $redis.zadd(key, Time.zone.now.to_i, content)
    # add 5 minutes expiry
    $redis.expire(key, 5 * 60)
  end

  def remove_content_from_redis
    # Remove post content from Redis set
    $redis.zrem("last_post_content_#{user_id}", content)
  end

  def post_share_card_text_util(videos_count)
    name = user.name.strip
    header = name != nil && name.length < 10 && name.length > 0 ? "#{name} ని Praja App ‌లో ఫాలో అవ్వండి" : "Praja App ‌లో నన్ను ఫాలో అవ్వండి"
    video_banner_text = videos_count != 0 ? "పూర్తి వీడియోని Praja App ‌లో చూడండి." : nil
    {
      header: header,
      video_banner_text: video_banner_text,
      photo_upload_tool_tip: self.photo_upload_tool_tip,
      read_more_text: self.read_more_text,
    }
  end

  def post_share_card_text
    self.post_share_card_text_util(videos.count)
  end

  def self.show_post_card_flag_util(post)
    post[:link] == nil || post[:link] == ""
  end

  def read_more_text
    "...Praja App ‌లో మరింత చదవండి"
  end

  def photo_upload_tool_tip
    "మీ ప్రొఫైల్ ఫోటోను జోడించండి"
  end

  def post_share_card_gradients_v1
    circle_id = user.get_badge_affiliated_party_circle_id

    case circle_id
    when 31403
      colors = [0xff122979, 0xff607df4, 0xffeceffb, 0xff367b36]
      stops = [-0.1439, 0.066, 0.4596, 1.1302]
      video_banner_color = 0xff127dff
    when 31402
      colors = [0xffFFFFB9, 0xffEAC055, 0xffFFFF98, 0xffFFFA8E, 0xffEAC055]
      stops = [-0.0206, 0.0243, 0.5183, 0.7685, 1.1277]
      video_banner_color = 0xfff0e07e
    when 31406
      colors = [0xffBE382E, 0xffCD564D, 0xffFEC9C5, 0xffF9E3E2, 0xff000000]
      stops = [-0.0713, 0.1016, 0.4411, 0.5565, 1.3056]
      video_banner_color = 0xffBE382E
    when 31401, 37967
      colors = [0xffF19E4B, 0xffDBD7D4, 0xffF8F8F8, 0xff359B44]
      stops = [0.1754, 0.3301, 0.5827, 0.9901]
      video_banner_color = 0xff3B9E4A
    when 31398, 37788
      colors = [0xffFF6A01, 0xffFFC69E, 0xffFF6A01]
      stops = [0.0365, 0.5088, 0.9635]
      video_banner_color = 0xffFF6B02
    when 31405
      colors = [0xffEC4E9B, 0xffFFCAEC, 0xffEC4E9B]
      stops = [0.365, 0.5104, 0.9323]
      video_banner_color = 0xffEC4E9B
    else
      colors = [0xff3586DC, 0xff56A8EF, 0xff388DEA]
      stops = [-0.0234, 0.549, 1.0233]
      video_banner_color = 0xff3F89FF
    end

    {
      colors: colors, stops: stops, video_banner_color: video_banner_color
    }
  end

  def post_share_card_gradients_v2
    # check below link for directional changes of gradients
    # https://github.com/flutter/flutter/blob/master/packages/flutter/lib/src/painting/alignment.dart
    circle_id = user.get_badge_affiliated_party_circle_id
    case circle_id
    when 31403
      background_colors = [0xff008E46, 0xff22BBB8, 0xff0266B4]
      background_color_stops = [0.0, 0.467, 0.8732]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xff003B6A, 0xff0266B4, 0xff0266B4, 0xff003B6A]
      badge_banner_color_stops = [0.0, 0.3368, 0.6826, 1.0]
      header_text_color = 0xffFFFFFF
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = false
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, -1.0, 1.0, 1.0
    when 31402
      background_colors = [0xffFFED00, 0xffE30613, 0xffE30613, 0xffFFED00]
      background_color_stops = [0.0, 0.3264, 0.6441, 0.8316]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xffF17720, 0xffFFED00, 0xffF17720]
      badge_banner_color_stops = [0.0, 0.4357, 1.0]
      header_text_color = 0xff000000
      footer_text_color = 0xff000000
      badge_text_color = 0xff000000
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    when 31406
      background_colors = [0xffCC0000, 0xffFFB0B0, 0xffFFB0B0, 0xffCC0000]
      background_color_stops = [0.0423, 0.4045, 0.6753, 1.0]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xff540000, 0xffCC0000, 0xffCC0000, 0xff540000]
      badge_banner_color_stops = [0.0, 0.3264, 0.7118, 1.0]
      header_text_color = 0xffFFFFFF
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    when 31401, 37967
      background_colors = [0xff359B44, 0xffFFFCED, 0xffF37022]
      background_color_stops = [0.0763, 0.5295, 1.0]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xff003E1A, 0xff0F823F, 0xff0F823F, 0xff003E1A]
      badge_banner_color_stops = [0.0, 0.2899, 0.6701, 1.0]
      header_text_color = 0xffFFFFFF
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    when 31398, 37788
      background_colors = [0xffFF6A01, 0xffFFC77B, 0xffFFC225]
      background_color_stops = [0.0365, 0.5088, 0.9635]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700]
      badge_banner_color_stops = [0.0, 0.3472, 0.6441, 1.0]
      header_text_color = 0xff000000
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    when 31405
      background_colors = [0xffF47FC5, 0xffED008C]
      background_color_stops = [0.0, 0.3695]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xffAC0066, 0xffED008C, 0xffED008C, 0xffAC0066]
      badge_banner_color_stops = [0.0295, 0.3889, 0.6336, 0.9826]
      header_text_color = 0xffFFFFFF
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    else
      background_colors = [0xff477292, 0xff8EA3B4]
      background_color_stops = [0.0, 1.0]
      video_banner_colors = [0xffE0E0E0, 0xffE0E0E0]
      badge_banner_colors = [0xff477292, 0xff8EA3B4, 0xff477292]
      badge_banner_color_stops = [0.1441, 0.4741, 0.842]
      header_text_color = 0xffFFFFFF
      footer_text_color = 0xffFFFFFF
      badge_text_color = 0xffFFFFFF
      video_banner_text_color = 0xff000000
      direction_is_vertical = true
      gradient_begin_x, gradient_begin_y, gradient_end_x, gradient_end_y = 1.0, 1.0, 1.0, -1.0
    end

    {
      header_text_color: header_text_color,
      footer_text_color: footer_text_color,
      badge_text_color: badge_text_color,
      video_banner_text_color: video_banner_text_color,
      direction_is_vertical: direction_is_vertical,
      gradient_direction: {
        begin_x: gradient_begin_x,
        begin_y: gradient_begin_y,
        end_x: gradient_end_x,
        end_y: gradient_end_y
      },
      upload_tool_tip_color: 0xff606060,
      upload_tool_tip_text_color: 0xffFFFFFF,
      read_more_text_color: 0xff9E9E9E,
      background_gradients: {
        colors: background_colors,
        stops: background_color_stops
      },
      video_banner_gradients: {
        colors: video_banner_colors,
        stops: nil,
      },
      badge_banner_gradients: {
        colors: user.get_badge_role&.role&.show_badge_banner? ? badge_banner_colors : [0x00ffffff, 0x00ffffff],
        stops: user.get_badge_role&.role&.show_badge_banner? ? badge_banner_color_stops : [0, 1]
      }
    }
  end

  def post_share_card_gradients_util(app_version)
    if app_version <= Gem::Version.new('1.13.1')
      post_share_card_gradients_v1
    else
      post_share_card_gradients_v2
    end
  end

  def user_tags
    self.post_user_tag
  end

  def hashtags
    self.post_hashtags
  end

  def likes_count
    likes.count
  end

  def get_liked_user_ids
    likes.pluck(:user_id)
  end

  def whatsapp_count
    $redis.hvals("post_whatsapp_shares_#{self.id}").map { |v| v.to_i }.sum
  end

  def unique_users_whatsapp_count
    $redis.hlen("post_whatsapp_shares_#{self.id}")
  end

  def comments_count
    comments.count
  end

  def opinions_count
    Post.where(parent_post: self, active: true).count + PostBounce.where(post: self, active: true).count
  end

  def circle
    circles.first
  end

  def link
    if !self.link_id.nil? && self.link_id != 0
      Link.find(self.link_id)
    end
  end

  def is_liked(user)
    likes.where(user: user).exists?
  end

  def is_seen(user)
    false
    # $redis.hexists("post_views_#{self.id}", user.id.to_s)
  end

  def get_users_views_count
    begin
      $redis.hget(Constants.post_views_redis_key, id.to_s).to_i
    rescue => exception
      logger.error(exception.message.to_s)
      return 0
    end
  end

  def get_fantom_users_views_count
    begin
      $redis.hvals("post_views_fantom_#{id}").map(&:to_i).sum
    rescue => exception
      logger.error(exception.message.to_s)
      return 0
    end
  end

  def get_total_views_count
    get_users_views_count + get_fantom_users_views_count
  end

  def should_show_views_count?
    created_at >= Time.zone.parse('07-08-2022')
  end

  # nil - wouldn't show up on all
  # not nil - shows the count
  def views_count_display
    return nil unless should_show_views_count?

    views_count = get_total_views_count
    return views_count if views_count.present? # && views_count > 50

    0
  end

  def get_preview_comments
    preview_comments = []
    return preview_comments
    # latest_comment = $redis.get("post_latest_comment_#{self.id}")
    # if !latest_comment.nil?
    #   preview_comments.push(latest_comment)
    # end
    # preview_comments
  end

  def is_circled(user)
    PostBounce.where(post: self, active: true, user: user).exists? || Post.where(parent_post: self, active: true, user: user).exists?
  end

  def get_highest_circle
    self.circles.order(level: :asc).last
  end

  def do_like(user)

    PostLike.create!(post: self, user: user)

    user.user_groups.each do |user_group|
      PopulateTrends.perform_async(self.id, user_group.id)
    end

    if self.user.id != user.id
      $redis.sadd("user_like_for_post_#{self.id}", user.id.to_s)
    end

    if $redis.scard("user_like_for_post_#{self.id}") >= 5
      self.send_like_notification
    end

  end

  def send_like_notification
    post_id = self.id

    if $redis.scard("user_like_for_post_#{post_id}") > 0
      user_id = $redis.srandmember("user_like_for_post_#{post_id}")
      return if user_id.blank?

      user_display = User.find(user_id)
      return if user_display.blank?

      likes_count = self.likes_count - 1
      notification_title = user_display.name
      notification_title += " & #{likes_count} ఇతరులు" if likes_count > 0
      notification_title += " మీ పోస్ట్ ని ట్రెండ్ చేశారు"

      notification_body = "పోస్ట్ ను చూడటానికి క్లిక్ చేయండి"
      notification_body = self.content.truncate(47, separator: ' ') if self.content.present?

      notification = Notification.create!(description: notification_title, notification_type: :like, user: self.user, entity_type: "post", entity_id: self.id)

      payload = {
        "collapse_key": "post-trends-#{self.id}",
        "title": "🔥 " + notification_title,
        "body": notification_body,
        "message_channel": "trend",
        "data": {
          "path": "/posts/#{self.id}",
          "circle_notification_id": notification.id.to_s
        }
      }
      GarudaNotification.send_user_notification(user.id, payload)

      $redis.set("latest_notif_time_#{post_id}", notification.created_at)
      $redis.del("user_like_for_post_#{post_id}")
    end

    latest_notif = $redis.get("latest_notif_time_#{post_id}")

    if ((DateTime.now.to_time.to_i - self.created_at.to_time.to_i).abs <= 48 * 3600)
      CheckForLatestNotification.perform_in(2.hours, post_id, latest_notif)
    end

  end

  def index_for_search
    IndexPostNewV2.perform_async(id)
  end

  def index_for_search_after_create
    IndexPostNewV2.perform_async(id, true)
  end

  # def index_user_for_search
  #   user.index_for_search
  # end

  def fetch_post_tags
    FetchPostTagsAzure.perform_async(self.id) if self.post_photos.present?
    # We are not using photo label detection using google vision api
    # FetchPostTags.perform_async(self.id) if self.post_photos.present?
  end

  def internal_notification_delay
    SendPostInternalNotifications.perform_async(self.id)
  end

  def send_internal_notification
    return if user.get_badge_role.nil?

    user_role = user.get_badge_role

    return unless user_role.check_to_send_internal_notification

    location_id = user_role.purview_circle_id
    location_circle = user_role.purview_circle

    user_ids = case true
               when location_circle.is_village_level?
                 User.where(village_id: location_id).ids

               when location_circle.mandal_level?
                 User.where(mandal_id: location_id).ids

               when location_circle.mla_constituency_level?
                 User.where(mla_constituency_id: location_id).ids

               when location_circle.district_level?
                 User.where(district_id: location_id).ids

               when location_circle.mp_constituency_level?
                 User.where(mp_constituency_id: location_id).ids
               else
                 []
               end
    batch_users_ids(user_ids)
  end

  def batch_users_ids(user_ids)
    user_role = user.get_badge_role

    uids = user_ids.filter_map { |u| u unless u == user_id }

    no_notification_ids = Notification.internal_notifications_count_set(uids)
    notification_user_ids = uids - no_notification_ids

    notification_title = "మీ #{user_role.parent_circle.name} #{user_role.get_role_name} కొత్త పోస్ట్ చేశారు"
    notification_message = "దీనికి సంబంధించి మరిన్ని విషయాలు వెంటనే తెలుసుకోవడానికి ఇక్కడ క్లిక్ చేయండి"

    notification_user_ids.each do |user_id|
      payload = {
        "title": notification_title,
        "body": notification_message,
        "message_channel": "post",
        "data": {
          "path": "/posts/#{id}",
          "is_badge_user_post_notification": true
        }
      }
      GarudaNotification.send_user_notification(user_id, payload)
    end
  end

  def send_internal_notifications_v2
    return if user.get_badge_role.nil?

    user_role = user.get_badge_role

    return unless user_role.check_to_send_internal_notification

    target_user_ids = case true
                      when user_role.role.grade_1?, user_role.role.grade_2?
                        # grade level override on user roles
                        user_role_grade_level_user_ids = UserRole.joins(:user).where(grade_level: [:grade_1, :grade_2], user: { state_id: user.state_id }).pluck(:user_id)
                        # grade level from roles
                        role_grade_level_user_ids = UserRole.joins(:role, :user)
                                                            .where.not(user: { id: user_role_grade_level_user_ids })
                                                            .where(grade_level: nil)
                                                            .where(role: { grade_level: [:grade_1, :grade_2] }, user: { state_id: user.state_id })
                                                            .pluck(:user_id)
                        user_role_grade_level_user_ids + role_grade_level_user_ids
                      else
                        parent_circle = user_role.purview_circle.parent_circle
                        if parent_circle.present?
                          parent_child_circles_ids = parent_circle.child_circles.map(&:id)
                          UserRole.joins(:role).where(primary_circle_id: parent_child_circles_ids, role: { primary_circle_type: :location, grade_level: user_role.role.grade_level }).pluck(:user_id)
                        else
                          []
                        end
                      end
    batch_users_ids(target_user_ids)

  end

  def self.check_tagged_circles(circle_ids)

    return "మూడు గ్రూపులను మాత్రమే ఎంచుకోగలరు." if circle_ids.count > Circle::MAX_CIRCLES

    tagged_circles = Circle.where(id: circle_ids)
    level_counts = tagged_circles.map(&:level_before_type_cast).tally # Tallys the collection. Returns a hash where the keys are the elements and the values are numbers of elements
    message = case true
              when level_counts[1].present? && level_counts[1] > Circle::MAX_VILLAGE_CIRCLES
                "ఒక గ్రామాన్ని మాత్రమే ఎంచుకోగలరు."
              when level_counts[6].present? && level_counts[6] > Circle::MAX_PARTY_CIRCLES
                "ఒక పొలిటికల్ పార్టీ మాత్రమే ఎంచుకోగలరు."
              when level_counts[7].present? && level_counts[7] > Circle::MAX_LEADER_CIRCLES
                "రెండు లీడర్ గ్రూపులను మాత్రమే ఎంచుకోగలరు."
              else
                ""
              end
    return message
  end

  def update_has_tagged_circles
    self.has_tagged_circles = self.post_circles.present? ? true : false
    self.save!(validate: false)
  end

  def remove_post_id_from_trending_feed_cache
    RemovePostIdFromTrendingFeedCache.perform_async(id)
  end

  # @NOTE: This method returns the score and can_retry flag [score, can_retry]
  def get_news_worthy_score
    return [self.news_worthy_score, false] if self.news_worthy_score.present?

    prompt = "On a scale of 1.0 - 5.0, how news-worthy is this content for regional Indian user?" +
      " Analyze all the text, images. Respond only the rating score(float), nothing else."

    user_content = [{ type: "text", text: self.content.to_s }]

    if self.photos.any?
      self.photos.each do |photo|
        user_content << { type: "image_url", image_url: { url: photo.placeholder_url, detail: "low" } }
      end
    end

    if self.videos.any?
      self.videos.each do |video|
        user_content << { type: "image_url", image_url: { url: video.get_thumbnail_url, detail: "low" } }
      end
    end

    begin
      response = fetch_response_from_openai(prompt, user_content)
      if response.body.include?("Timeout while downloading")
        return [nil, true]
      end

      if response.code != "200"
        Honeybadger.notify('OpenAI API request failed while getting the response', context: { post_id: self.id, response: response.body, response_code: response.code })
        return [nil, false]
      end

      parsed_response = JSON.parse(response&.body)
      score = parsed_response.dig("choices", 0, "message", "content")&.to_f
      if score.present?
        self.update(news_worthy_score: score)
      end
      [score, false]
    rescue => e
      Honeybadger.notify('OPENAI API error while parsing the response', context: { post_id: self.id, error: e.message, response: response&.body, response_code: response&.code })
      return [nil, false]
    end
  end

  private

  def fetch_response_from_openai(prompt, user_content)
    uri = URI(Constants.openai_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "Bearer #{Rails.application.credentials[:openai_api_key]}"
    request['Content-Type'] = 'application/json'
    request.body = {
      model: "gpt-4.1-mini",
      max_tokens: 3,
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: user_content }
      ]
    }.to_json
    http.request(request)
  end
end
