class UserPlan < ApplicationRecord
  has_paper_trail

  belongs_to :user
  belongs_to :plan
  has_many :user_plan_logs, foreign_key: :user_id, primary_key: :user_id
  has_many :user_plan_extensions, foreign_key: :user_id, primary_key: :user_id
  after_commit :send_user_to_mixpanel

  def send_user_to_mixpanel
    SyncMixpanelUser.perform_async(user_id)
  end

  def get_new_start_date(grace_period_start_date:)

    new_start_date = nil

    if grace_period_start_date.present?
      grace_period_start_date = Time.zone.parse(grace_period_start_date)
      grace_period_end_date = grace_period_start_date.advance(months: Subscription::GRACE_PERIOD_IN_MONTHS)
      if Time.zone.now < grace_period_end_date
        new_start_date = (end_date - grace_period_start_date).abs >= 1.day ?
                           (grace_period_start_date - 1.day) :
                           end_date
      end
    end

    if new_start_date.blank?
      new_start_date = (Time.zone.now > end_date) ? Time.zone.now : end_date
    end
    new_start_date
  end

end
