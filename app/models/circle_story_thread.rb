class CircleStoryThread < ApplicationRecord
  belongs_to :circle, -> { where(active: true) }

  has_many :user_stories, -> { where(active: true) }

  attribute :circle
  attribute :feed_type
  attribute :feed_type_timestamp
  attribute :stories
  attribute :is_user_added

  # def check_user_added(user, circle)
  #   if user.circles.where(id: circle.id).count > 0
  #     self.user_stories.where(user: user, circle: circle).count > 0
  #   else
  #     true
  #   end
  # end
  #
  # def get_ordered_user_stories(user, circle)
  #   story_circles = [circle] + circle.get_all_child_circles
  #
  #   stories = []
  #   user_added = self.user_stories.where(user: user, circle: story_circles).last
  #   unless user_added.nil?
  #     stories.push(user_added)
  #   end
  #
  #   story_circle_ids = story_circles.map {|s| s.id}
  #
  #   user_followers = UserFollower
  #                        .joins("INNER JOIN user_circles ON user_circles.user_id = user_followers.follower_id")
  #                        .where(follower: user, active: true)
  #                        .where("user_circles.circle_id IN (#{story_circle_ids.join(",")})")
  #                        .map { |f| f.user  }
  #
  #   followers_stories = self.user_stories.where(circle: story_circles, user: user_followers).order(created_at: :desc).map do |follower_story|
  #     follower_story.photo_likes_count = 0
  #     photos_count = 0
  #
  #     follower_story.user_story_photos.each do |user_story_photo|
  #       photos_count += 1
  #       follower_story.photo_likes_count += user_story_photo.likes_count
  #     end
  #
  #     if photos_count > 0
  #       follower_story.photo_likes_count = follower_story.photo_likes_count / photos_count
  #     end
  #
  #     follower_story
  #   end
  #   followers_stories = followers_stories.sort_by{|p| [p.photo_likes_count, p.created_at]}.reverse
  #
  #   other_user_stories = self.user_stories.where(circle: story_circles).where.not(user: [user] + user_followers).order(created_at: :desc).map do |other_user_story|
  #     other_user_story.photo_likes_count = 0
  #     photos_count = 0
  #
  #     other_user_story.user_story_photos.each do |user_story_photo|
  #       photos_count += 1
  #       other_user_story.photo_likes_count += user_story_photo.likes_count
  #     end
  #
  #     if photos_count > 0
  #       other_user_story.photo_likes_count = other_user_story.photo_likes_count / photos_count
  #     end
  #
  #     other_user_story
  #   end
  #   other_user_stories = other_user_stories.sort_by{|p| [p.photo_likes_count, p.created_at]}.reverse
  #
  #   stories + followers_stories + other_user_stories
  # end
end
