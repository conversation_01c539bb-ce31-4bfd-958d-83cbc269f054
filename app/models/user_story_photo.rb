class UserStoryPhoto < ApplicationRecord
  belongs_to :user_story
  belongs_to :photo
  has_many :user_story_photos_views
  has_many :user_story_photo_likes
  has_many :users_viewed, through: :user_story_photos_views, source: :user
  has_many :users_liked, through: :user_story_photo_likes, source: :user

  attribute :photo
  attribute :likes_count
  attribute :views_count
  attribute :is_user_viewed
  attribute :is_user_liked

  def check_user_viewed(user)
    self.user_story_photos_views.where(user: user).count > 0
  end

  def check_user_liked(user)
    self.user_story_photo_likes.where(user: user).count > 0
  end

  def likes_count
    self.users_liked.count
  end

  def views_count
    self.users_viewed.count
  end
end
