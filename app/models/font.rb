class Font < ApplicationRecord
  has_many :frames

  scope :by_ids, -> (font_ids) { where(id: font_ids) }

  # get font json for a frame
  def frame_json_data
    return nil if name_font.blank? || badge_font.blank?
    {
      "name": {
        "font_family": name_font,
        "font_style": "normal",
        "font_weight": "w700"
      },
      "badge": {
        "font_family": badge_font,
        "font_style": "normal",
        "font_weight": "w700"
      }
    }
  end

  # get preload fonts config json for a font
  def self.preload_fonts_config_json_format(font_name:)
    {
      "font_family": font_name,
      "font_style": "normal",
      "font_weight": "w700"
    }
  end

  def self.get_preload_fonts_config
    fonts = Font.all
    # Extract name_font and badge_font, then combine and get unique values
    unique_fonts = (fonts.pluck(:name_font) + fonts.pluck(:badge_font)).uniq

    # get preload fonts config json for each font
    preload_fonts_config = unique_fonts.map do |font_name|
      preload_fonts_config_json_format(font_name: font_name)
    end

    preload_fonts_config
  end
end
