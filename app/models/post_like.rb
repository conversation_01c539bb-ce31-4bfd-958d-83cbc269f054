class PostLike < ApplicationRecord
  belongs_to :post
  belongs_to :user

  validates_uniqueness_of :user_id, scope: :post_id
  after_commit :index_for_search
  after_create_commit :populate_fantom_views, :increase_post_hashtag_likes_count, :mark_for_trend_feedback_eligibility
  after_destroy_commit :decrease_post_hashtag_likes_count
  after_commit :index_post, on: [:create, :destroy]

  def mark_for_trend_feedback_eligibility
    $redis.set(Constants.get_trend_feedback_eligibility_redis_key(user_id), "true", ex: 30.days.to_i)
  end

  def index_for_search
    # user.index_for_search
  end

  def populate_fantom_views
    TriggerPostViewPopulation.perform_async(post_id, id) if user.internal? && post.created_at > Time.zone.parse('19-07-2022 15:35:00')
  end

  def index_post
    IndexPostNewV2.perform_async(post_id)
  end

  def increase_post_hashtag_likes_count
    post.post_hashtags.each do |post_hashtag|
      $redis.hincrby(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, post_hashtag.hashtag_id, 1)
    end
  end
  def decrease_post_hashtag_likes_count
    post.post_hashtags.each do |post_hashtag|
      $redis.hincrby(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, post_hashtag.hashtag_id, -1)
    end
  end

  def self.get_liked_post_ids(post_ids, user_id)
    PostLike.where(post_id: post_ids, user_id: user_id).pluck(:post_id)
  end
end
