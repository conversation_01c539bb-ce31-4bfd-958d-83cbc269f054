class CirclesRelation < ApplicationRecord
  enum relation: { MLA: 'MLA', MP: 'MP', Mandal2MLA: 'Mandal2MLA', Leader2Party: 'Leader2Party',
                   Leader2State: 'Leader2State', Party2State: 'Party2State', MLA_Contestant: 'MLA Contestant',
                   MP_Contestant: 'MP Contestant', <PERSON><PERSON>: 'MLC', MLC_Contestant: 'MLC Contestant',
                   <PERSON><PERSON><PERSON><PERSON><PERSON>: '<PERSON> <PERSON>', Leader2Location: 'Leader2Location', }, _suffix: true
  belongs_to :first_circle, class_name: 'Circle'
  belongs_to :second_circle, class_name: 'Circle'

  validates :first_circle_id, :second_circle_id, numericality: { only_integer: true }, presence: true,
            allow_blank: false
  validates_uniqueness_of :first_circle_id, scope: [:second_circle_id, :relation]
  validates :relation, inclusion: { in: relations.keys }
  validate :validate_circles_are_not_same, :validate_relation_with_circles, if: -> { errors.blank? }
  validate :validate_location_relation, if: -> { errors.blank? }
  validate :unique_leader_to_location_relation, if: -> { errors.blank? }
  attribute :first_circle
  attribute :second_circle
  after_commit :index_for_search
  after_commit :generate_kyc_creative_image
  # after_update_commit :check_and_generate_congratulations_card

  # def check_and_generate_congratulations_card
  #   if relation == "MLA" && active == true &&
  #     (Rails.env.development? || Constants.ap_mla_const_for_suggested_circles.include?(first_circle_id))
  #     GenerateCongratulationsCard.perform_async(id, first_circle_id, second_circle_id)
  #   end
  # end

  def validate_location_relation
    return unless relation.to_sym == :Leader2Location

    errors.add(:first_circle_id, 'first circle level should be political_leader') unless first_circle.political_leader_level?
    errors.add(:second_circle_id, 'second circle level should be location') unless second_circle.location_circle_type?
  end

  def unique_leader_to_location_relation
    return unless relation.to_sym == :Leader2Location

    # As per the discussion keeping the Leader2Location (any level) can only one unique as for now.
    # So only location can be assigned to the leader.
    if CirclesRelation.where(first_circle_id: first_circle_id, relation: :Leader2Location).where.not(id: id).count > 0
      errors.add(:first_circle_id, 'first circle should not have more than one leader to location relations')
    end
  end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def index_for_search
    IndexSearchEntity.perform_async('circle', first_circle_id) if relation.to_sym == :Leader2Party || :Party2State
    IndexSearchEntity.perform_async('circle', second_circle_id) if relation == :MLA || :MP || :MLA_Contestant ||
      :MP_Contestant || :MLC || :MLC_Contestant || :MP_Rajyasabha
  end

  def validate_circles_are_not_same
    errors.add(:first_circle_id, "first circle and second circle should not be same") if first_circle_id == second_circle_id
  end

  def validate_relation_with_circles
    case relation.to_sym
    when :MLA, :MLA_Contestant
      errors.add(:first_circle_id, "first circle level should be mla_constituency") unless first_circle
                                                                                             .mla_constituency_level?
      errors.add(:second_circle_id, "second circle level should be political_leader") unless second_circle
                                                                                               .political_leader_level?
    when :MP, :MP_Contestant
      errors.add(:first_circle_id, "first circle level should be mp_constituency") unless first_circle
                                                                                            .mp_constituency_level?
      errors.add(:second_circle_id, "second circle level should be political_leader") unless second_circle
                                                                                               .political_leader_level?
    when :MLC, :MLC_Contestant, :MP_Rajyasabha
      errors.add(:first_circle_id, "first circle level should be district") unless first_circle.district_level?
      errors.add(:second_circle_id, "second circle level should be political_leader") unless second_circle
                                                                                               .political_leader_level?
    when :Mandal2MLA
      errors.add(:first_circle_id, "first circle level should be mandal") unless first_circle.mandal_level?
      errors.add(:second_circle_id, "second circle level should be mla_constituency") unless second_circle
                                                                                               .mla_constituency_level?
    when :Leader2Party
      errors.add(:first_circle_id, "first circle level should be political_leader") unless first_circle
                                                                                             .political_leader_level?
      errors.add(:second_circle_id, "second circle level should be party") unless second_circle.political_party_level?
    when :Leader2State
      errors.add(:first_circle_id, "first circle level should be political_leader") unless first_circle
                                                                                             .political_leader_level?
      errors.add(:second_circle_id, "second circle level should be state") unless second_circle.state_level?
    when :Party2State
      errors.add(:first_circle_id, "first circle level should be party") unless first_circle.political_party_level?
      errors.add(:second_circle_id, "second circle level should be state") unless second_circle.state_level?
    end
  end

  def self.mla_contestant_ids_of_a_mla_const(mla_constituency_id)
    CirclesRelation.where(first_circle_id: mla_constituency_id, relation: :MLA_Contestant).
      pluck(:second_circle_id)
  end

  def self.mp_contestant_ids_of_a_mp_const(mp_constituency_id, kyc_carousel: false, affiliated_party_id: nil)
    CirclesRelation.where(first_circle_id: mp_constituency_id, relation: :MP_Contestant).
      pluck(:second_circle_id)
  end

  def self.party_mla_contestants(party_id)
    CirclesRelation
      .joins('INNER JOIN circles_relations as cr2 ON circles_relations.first_circle_id = cr2.second_circle_id')
      .where(second_circle_id: party_id, relation: 'Leader2Party')
      .where('cr2.relation = ?', 'MLA Contestant')
      .pluck('circles_relations.first_circle_id')
  end

  def self.party_mp_contestants(party_id)
    CirclesRelation
      .joins('INNER JOIN circles_relations as cr2 ON circles_relations.first_circle_id = cr2.second_circle_id')
      .where(second_circle_id: party_id, relation: 'Leader2Party')
      .where('cr2.relation = ?', 'MP Contestant')
      .pluck('circles_relations.first_circle_id')
  end

  def self.mla_const_of_mla_contestants(mla_contestant_ids)
    CirclesRelation.where(second_circle_id: mla_contestant_ids, relation: :MLA_Contestant).
      pluck(:first_circle_id)
  end

  def self.mp_const_of_mp_contestants(mp_contestant_ids)
    CirclesRelation.where(second_circle_id: mp_contestant_ids, relation: :MP_Contestant).
      pluck(:first_circle_id)
  end

  def self.mla_contestant_ids_of_a_district(district)
    CirclesRelation.joins('JOIN circles_relations as mandal_to_mla ON circles_relations.first_circle_id = mandal_to_mla.second_circle_id')
                   .where('mandal_to_mla.first_circle_id IN (?) AND mandal_to_mla.relation = ?',
                          district.child_circles.select(:id), 'Mandal2MLA')
                   .where(circles_relations: { relation: :MLA_Contestant })
                   .distinct(:second_circle_id)
                   .order('circles_relations.first_circle_id')
                   .pluck('circles_relations.second_circle_id')
  end

  def self.mla_leader_circle_id(mla_constituency_id)
    CirclesRelation.find_by(first_circle_id: mla_constituency_id, relation: :MLA, active: true)&.second_circle_id
  end

  def self.mp_leader_circle_id(mp_constituency_id)
    CirclesRelation.find_by(first_circle_id: mp_constituency_id, relation: :MP, active: true)&.second_circle_id
  end

  def generate_kyc_creative_image
    if relation.to_sym.in?(%i[MLA_Contestant MP_Contestant])
      GenerateKycCreativeImage.perform_async(second_circle_id)
    end
  end

  def self.party_affiliated_circle_ids(party_id:, circle_ids:)
    CirclesRelation.where(second_circle_id: party_id,
                          first_circle_id: circle_ids,
                          relation: :Leader2Party).pluck(:first_circle_id)
  end
end
