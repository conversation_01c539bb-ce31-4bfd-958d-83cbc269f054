class Order < ApplicationRecord
  belongs_to :user
  has_many :order_transactions
  has_many :order_items
  enum status: [:opened, :pending, :successful, :last_transaction_failed, :closed]

  attr_accessor :duration_in_months
  scope :open, -> { where(status: [:opened, :pending, :last_transaction_failed]) }

  validates :user, presence: true, allow_blank: false
  validates :status, presence: true, allow_blank: false

  # validate :premium_package_frames_limit, if: -> { errors.blank? }
  validate :discount_should_not_exceed_maintenance_amount, if: -> { errors.blank? && discount_amount.present? }
  validate :discount_should_not_exceed_total_amount, if: -> { errors.blank? && discount_amount.present? }
  validate :referred_by_user_exists, if: -> { errors.blank? && referred_by.present? }

  after_commit :send_user_to_mixpanel
  after_commit :send_mixpanel_event, if: -> { saved_change_to_status? && successful? }
  after_commit :update_premium_pitch, if: -> { saved_change_to_status? && successful? }

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def send_user_to_mixpanel
    SyncMixpanelUser.perform_in(5.seconds, user_id)
  end

  def update_premium_pitch
    PremiumPitch.find_by(user_id: user_id)&.payment_done!
  end

  def discount_should_not_exceed_maintenance_amount
    if self.discount_amount_changed?
      self.order_items.map do |order_item|
        if order_item.item_type == "Product"
          item_price = ItemPrice.find_by(id: order_item.item_price_id)
          if item_price.present?
            maintenance_price = item_price.maintenance_price
            if self.discount_amount > maintenance_price
              errors.add(:discount_amount, "should not exceed maintenance amount")
            end
          end
        end
      end
    end
  end

  def discount_should_not_exceed_total_amount
    errors.add(:discount_amount, "should not exceed total amount") if discount_amount > total_amount
  end

  def referred_by_user_exists
    errors.add(:referred_by, "user does not exist") unless User.exists?(referred_by)
  end

  def premium_package_frames_limit
    # if order items has item product then run the below code
    parent_order_item = self.order_items.where(item_type: "Product", item_id: Constants.get_poster_product_id).first
    if parent_order_item.present?
      premium_package_frames_count = self.order_items.where(item_type: :Frame, item_id: Frame.get_premium_ids,
                                                            parent_order_item_id: parent_order_item.id).count
      if premium_package_frames_count > Constants.premium_package_frames_count
        errors.add(:order_items, "should not exceed #{Constants.premium_package_frames_count} frames")
      elsif premium_package_frames_count < Constants.premium_package_frames_count
        errors.add(:order_items, "should not be less than #{Constants.premium_package_frames_count} frames")
      end
    end
  end

  def send_mixpanel_event
    duration_in_months = self.order_items.last.duration_in_months
    has_product = self.order_items.where(item_type: "Product", item_id: Constants.get_poster_product_id).exists?
    if has_product
      subscription_type = UserProductSubscription.where(user_id: user_id, item_type: "Product",
                                                        item_id: Constants.get_poster_product_id,
                                                        active: true, source: :orders).count > 1 ? "renewed" : "new"
    else
      subscription_type = "add_on_frame"
    end

    EventTracker.perform_async(user_id, "payment_done_backend", {
      "MRR" => payable_amount / duration_in_months,
      "subscription_type" => subscription_type,
      "duration_in_months" => duration_in_months,
      "payable_amount" => payable_amount,
      "discount_amount" => discount_amount
    })

    # to track revenue in mixpanel
    MixpanelUserRevenueTracker.perform_async(user_id, payable_amount, {
      "$time" => Time.zone.now,
    })
  end
end
