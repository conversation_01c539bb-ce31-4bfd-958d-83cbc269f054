class CirclePackageMapping < ApplicationRecord
  has_paper_trail
  belongs_to :circle
  belongs_to :circle_package

  # Do not perform update operations on circle_packages as the callbacks wrt circle package are on circle 
  validates_presence_of :start_date, :end_date, :circle_id, :circle_package_id
  validate :check_end_date_greater_than_start_date
  before_save :previous_active_circle_package_mapping
  after_commit :update_circle_monthly_usages, :create_circle_monthly_usages, :update_other_circle_package_mappings_wrt_current_mappping, :make_inactive_if_end_date_is_past

  def check_end_date_greater_than_start_date
    if end_date.present? && start_date.present? && end_date <= start_date
      errors.add(:end_date, "can't be less than start date")
    end
  end

  def previous_active_circle_package_mapping
    @previous_active_circle_package_mapping = circle.active_circle_package_mapping if circle.present?
    @previous_active_circle_monthly_usage = circle.circle_monthly_usage if circle.present?
  end

  def update_other_circle_package_mappings_wrt_current_mappping
    active_mapping = circle.active_circle_package_mapping
    if active_mapping.present? && active_mapping.id != id
      if start_date <= Time.zone.today && end_date >= Time.zone.today
        circle.active_circle_package_mapping.update_column(:active, false)
      end
    end
  end

  def get_upgradable_mapping_id
    active_mapping_id = circle.active_circle_package_mapping.id
    circle.existing_circle_package_mappings.where.not(id: active_mapping_id).first&.id
  end

  def make_inactive_if_end_date_is_past
    update_column(:active, false) if end_date < Time.zone.today
  end

  def create_circle_monthly_usages
    # create for the active tenure mappings circle monthly usage
    if previously_new_record? && circle.reload_active_circle_package_mapping&.id == id
      month_end = calculate_month_end(start_date)
      CircleMonthlyUsage.create(circle_id: circle_id,
                                month_start: start_date,
                                month_end: month_end,
                                active: true,
                                channel_message_limit: circle_package.channel_post_msg_limit,
                                channel_message_usage: 0,
                                channel_notification_limit: circle_package.channel_post_msg_notification_limit,
                                channel_notification_usage: 0,
                                fan_posters_limit: circle_package.fan_poster_creatives_limit,
                                fan_posters_usage: 0)
    end
  end

  def update_circle_monthly_usages
    # update the circle monthly usages based on the start date, end date updates of the circle package mapping
    if saved_change_to_start_date? && !previously_new_record? && circle.reload_active_circle_package_mapping&.id == id
      month_end = calculate_month_end(start_date)
      CircleMonthlyUsage.create(circle_id: circle_id,
                                month_start: start_date,
                                month_end: month_end,
                                active: true,
                                channel_message_limit: circle_package.channel_post_msg_limit,
                                channel_message_usage: 0,
                                channel_notification_limit: circle_package.channel_post_msg_notification_limit,
                                channel_notification_usage: 0,
                                fan_posters_limit: circle_package.fan_poster_creatives_limit,
                                fan_posters_usage: 0)
    elsif saved_change_to_end_date? && !previously_new_record? && @previous_active_circle_package_mapping&.id == id
      get_circle_current_active_monthly_usage = @previous_active_circle_monthly_usage
      return if get_circle_current_active_monthly_usage.blank?

      if get_circle_current_active_monthly_usage.month_end > end_date
        get_circle_current_active_monthly_usage.update_column(:month_end, end_date)
        get_circle_current_active_monthly_usage.update_column(:active, false) if end_date < Time.zone.today
      end
    end
  end

  def calculate_month_end(start_date)
    # Returns the day before the next period's start date
    (start_date + 1.month) - 1.day
  end
end
