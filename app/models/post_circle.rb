class PostCircle < ApplicationRecord
  belongs_to :post, -> { where(active: true) }
  belongs_to :circle

  attr_accessor :tag_circle
  after_destroy_commit :update_post_data, :send_notification

  def update_post_data
    post.flush_cache
    post.index_for_search
    post.update_has_tagged_circles
  end

  def send_notification
    SendRemoveTagNotification.perform_async(post_id, circle_id)
  end

end
