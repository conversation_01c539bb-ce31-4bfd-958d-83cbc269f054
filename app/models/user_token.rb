class UserToken < ApplicationRecord
  belongs_to :user
  # has_one :refer_user, class_name: 'User', foreign_key: 'refer_user_id'
  has_many :user_token_usages

  before_create :generate_access_token
  after_create_commit :cache_access_token
  after_update_commit :clear_token_cache

  def clear_token_cache
    return if active?
    $redis.hdel('user_tokens', access_token)
  end

  def self.get_user_from_token(access_token)
    user = nil
    user_token = self.includes(:user).joins(:user).merge(User.active).where(access_token: access_token).first

    # user_id = $redis.hget('user_tokens', access_token)&.to_i
    # if user_id.nil?
      user = user_token.user if user_token.present?
      # $redis.hset('user_tokens', access_token, user.id) if user.present?
    # else
    #   user = User.find user_id
    # end

    [user, user_token]
  end

  private

  def generate_access_token
    self.access_token = SecureRandom.uuid.gsub(/\-/,'')
  end

  def cache_access_token
    $redis.hset('user_tokens', access_token, user_id)
  end
end
