class Photo < ApplicationRecord
  belongs_to :user
  has_many :photo_labels
  has_many :poster_creatives, as: :photo_v3
  has_many :circles, as: :slogan_icon

  enum service: { aws: 'aws', gcp: 'gcp', azure: 'azure' }

  SUPPORTED_IMAGE_FORMATS = %w[.jpg .jpeg .png .webp]
  MAX_POST_PHOTOS_COUNT = 10

  # serve post photos with new domain and old domains
  # old = "https://cdn.thecircleapp.in/some/path"
  # new = "https://grup-cdn.thecircleapp.in/some/path"
  PHOTO_REGEX = %r{^https?://(?:cdn|grup|az-ip-cdn|az-cdn)\.thecircleapp\.in/}

  PRAJA_DOMAINS = ["thecircleapp.in", "praja.buzz"]

  # separating the data attribute for both media services data and ror data
  attribute :aspect_ratio
  attribute :bg_color
  attribute :ms_data
  attr_accessor :ms_data
  attribute :blob_data
  attr_accessor :blob_data
  attribute :placeholder_url, :string

  before_create :upload_new, if: -> { self.blob_data.present? }
  before_create :set_photo_data, if: -> { self.ms_data.present? }
  before_create :set_path, :set_bucket
  after_create_commit :save_dimensions

  validate :presence_of_service, if: -> { self.service.blank? }
  validate :presence_of_url, if: -> { errors.blank? && self.ms_data.present? }
  validate :presence_of_path, if: -> { errors.blank? && self.ms_data.present? }

  def host
    if self.azure?
      'https://a-az-cdn.thecircleapp.in'
    elsif self.gcp?
      'https://a-gc-cdn.thecircleapp.in'
    else
      'https://a-cdn.thecircleapp.in'
    end
  end

  def url
    @url ||= "#{host}/#{path}"
  end

  def placeholder_url
    compressed_url(size: 512)
  end

  # pass either size or width and height
  # fit: true will fit the image in the given width and height
  # fit: false will crop the image to the given width and height (cover)
  def compressed_url(size: 120, quality: 80, fit: true, width: nil, height: nil)
    w = width || size
    h = height || size

    if Photo::PRAJA_DOMAINS.any? { |domain| attributes['url'].include?(domain) }
      "#{host}/#{fit ? 'fit-in/' : ''}#{w}x#{h}/filters:quality(#{quality})/#{path}"
    else
      attributes['url']
    end
  end

  def compressed_url!(size: 120, quality: 80, fit: true, width: nil, height: nil)
    @url = compressed_url(size:, quality:, fit:, width:, height:)
  end

  def presence_of_service
    raise 'Photo service is blank'
  end

  def presence_of_url
    if (new_record? && ms_data[:cdn_url].blank?) || (!new_record? && self.url.blank?)
      raise 'Photo url is blank'
    end
  end

  def presence_of_path
    if (new_record? && ms_data[:path].blank?) || (!new_record? && self.path.blank?)
      raise 'Photo path is blank'
    end
  end

  def set_bucket

    if self.bucket.blank? && ms_data.is_a?(Hash) && ms_data[:bucket].present?
      self.bucket = ms_data[:bucket]
    end

    if self.bucket.blank?
      case self.service
      when 'azure'
        self.bucket = Constants.azure_buckets_source
      when 'gcp'
        self.bucket = Constants.gcp_buckets_source
      when 'aws'
        self.bucket = Constants.aws_buckets_source
      end
    end

  end

  def set_path
    if self.path.blank? && ms_data.is_a?(Hash) && ms_data[:path].present?
      self.path = ms_data[:path]
    end

    if self.path.blank? && attributes['url'].present?
      self.path = URI.parse(attributes['url']).path.gsub(%r{^/|/$}, '')
    end
  end

  def set_photo_data
    self.url = ms_data[:cdn_url]
    self.path = ms_data[:path] if ms_data[:path].present?
    self.height = ms_data[:height]
    self.width = ms_data[:width]
  end

  def upload_new
    return if blob_data.nil?

    begin
      resource = Aws::S3::Resource.new(
        region: 'ap-south-1',
        credentials: Aws::Credentials.new(
          Rails.application.credentials[:aws_access_key_id],
          Rails.application.credentials[:aws_secret_access_key]
        ))

      file_extension = File.extname(blob_data.original_filename)
      hashed_file_name = SecureRandom.uuid + file_extension.downcase

      s3_object_path = "#{Rails.env}/photos/#{user.id}/#{hashed_file_name}"

      checksum = Digest::MD5.file(blob_data).base64digest

      object = resource.bucket(Rails.application.credentials[:aws_s3_bucket_name])
                       .object(s3_object_path)
      object.put(body: blob_data, content_md5: checksum)

      if object && !object.exists?
        raise "Image object doesn't exist"
      else
        self.url = "https://a-cdn.thecircleapp.in/#{s3_object_path}"
        self.service = :aws
      end
    rescue => exception
      Honeybadger.notify(exception)
      raise 'ఫోటో అప్‌లోడ్ విఫలమైంది!'
    end
  end

  def save_dimensions
    SavePhotoDimensionsWorker.perform_in(2.seconds, id)
  end

  def to_honeybadger_context
    { photo_id: id }
  end

  def aspect_ratio
    extension = File.extname(url)

    if Photo::SUPPORTED_IMAGE_FORMATS.include? extension
      if width.to_i <= 0 || height.to_i <= 0
        SavePhotoDimensionsWorker.perform_async(id)
        set_aspect_ratio
      else
        (width.to_f / height.to_f).round(2)
      end
    else
      1.0
    end
  end

  def bg_color
    '#000000'
    # if !dominant_dark_color.nil? && dominant_dark_color.present?
    #   return dominant_dark_color
    # end
    #
    # set_dominant_dark_color
  end

  def set_dominant_dark_color
    # colors = Miro::DominantColors.new(self.url)
    # begin
    #   hex_colors = colors.to_hex
    # rescue
    # hex_colors = ["#000000"]
    # end

    # if hex_colors.length > 0
    #   self.dominant_dark_color = hex_colors[[0, hex_colors.length - 2].max]
    #   self.touch
    #   self.save
    # end
    #
    # self.dominant_dark_color
    '#000000'
  end

  def set_aspect_ratio
    begin
      size = FastImage.size(self.url, raise_on_failure: true)

      if size.present? && size[0] > 0 && size[1] > 0
        self.width = size[0].to_f
        self.height = size[1].to_f

        return (size[0].to_f / size[1].to_f).round(2)
      else
        return 1.0
      end
    rescue
      return 1.0
    end
  end

  def set_explicit_labels
    # rekognition = Aws::Rekognition::Client.new(
    #   region: 'ap-south-1',
    #   credentials: Aws::Credentials.new(
    #     Rails.application.credentials[:aws_access_key_id],
    #     Rails.application.credentials[:aws_secret_access_key]
    #   ),
    # )
    #
    # labels = rekognition.detect_moderation_labels({
    #   image: {
    #     s3_object: {
    #       bucket: Rails.application.credentials[:aws_s3_bucket_name],
    #       name: self.url.sub("https://cdn.thecircleapp.in/", ""),
    #     },
    #   },
    #   min_confidence: 50.0,
    # })
    #
    # labels.moderation_labels.each do |label|
    #   begin
    #     self.photo_labels << PhotoLabel.new({
    #         photo: self,
    #         label_type: :explicit,
    #         category: label.name.downcase.tr(" ", "_"),
    #         parent_category: label.parent_name.downcase.tr(" ", "_"),
    #         confidence: label.confidence,
    #     })
    #   rescue => exception
    #     next
    #   end
    # end
    #
    # count = 0
    # self.photo_labels.each do |label|
    #   count += 1 if label.confidence > 95
    # end
    #
    # self.explicit = true if count > 0
    #
    # self.save
  end

  def self.upload(image, user_id)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ),
    )

    file_extension = File.extname(image.original_filename)
    hashed_file_name = SecureRandom.uuid + file_extension.downcase

    s3_object_path = "#{Rails.env}/photos/#{user_id}/#{hashed_file_name}"

    begin
      checksum = Digest::MD5.file(image).base64digest

      resource
        .bucket(Rails.application.credentials[:aws_s3_bucket_name])
        .object(s3_object_path)
        .put(body: image,
             content_md5: checksum)

      photo = self.new(
        url: "https://a-cdn.thecircleapp.in/#{s3_object_path}",
        user_id: user_id,
        service: :aws
      )

      if photo.save
        photo.blob_data = image
        return photo
      else
        return nil
      end
    rescue => exception
      Honeybadger.notify(exception)
      return nil
    end
  end

  def photo_obj
    {
      id: id,
      url: url
    }
  end

  def self.static_replace_photo_url(url, replacing_url)
    extension = File.extname(url)

    if Photo::SUPPORTED_IMAGE_FORMATS.include? extension
      url.gsub!(Photo::PHOTO_REGEX, replacing_url)
    end
    url
  end

  def self.get_compressed_photo_url(photo_url)
    photo_url.gsub('https://cdn.thecircleapp.in/', 'https://l-cdn.praja.buzz/120x120/')
  end

  def get_media_service_callback_url
    if service.to_sym == :azure || service.to_sym == :aws
      Constants.media_service_callbacks_baseurl + "/photos/post-created"
    elsif service.to_sym == :gcp
      "https://media-service-api.thecircleapp.in/photos/post-created"
    end
  end

  def photo_download_url
    s3 = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ))
    object = s3.bucket(self.bucket).object(self.path)
    object.presigned_url(
      :get_object,
      response_content_disposition: "attachment;"
    )
  end
end
