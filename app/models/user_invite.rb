class UserInvite < ApplicationRecord
  belongs_to :user

  before_create :check_and_update_sent_flag
  MIN_PHONE_USER_INVITE_COUNT = 3
  SPECIFIC_DISTRICT_IDS = [13286]
  MIN_PHONE_USER_INVITE_COUNT_OF_A_SPECIFIC_DISTRICT = 5

  def index_for_contact
    return if self.nil?

    Honeybadger.context({ user_invite_id: self.id })

    phone = self.phone

    nd = UserInvite.where(phone: phone).count

    c = Contact.first_or_create(phone: phone, sent: false, network_density: nd)
    UserInvite.where(phone: phone).where.not(user_id: nil).pluck(:name, :user_id).in_groups_of(100) do |batch|
      batch.compact!
      batch.each do |name, user_id|
        next if name.nil? || user_id.nil?

        begin
          UserContact.create(contact_id: c.id, user_id: user_id, name: name)
        rescue ActiveRecord::RecordNotUnique
          # nothing
        end
      end
    end
  end

  def index_phone
    return
    # uncomment this when we want to index user invites again as of now we are commenting this for the purpose of
    # test coverage

    # return if self.nil?
    #
    # Honeybadger.context({ user_invite_id: self.id })
    #
    # phone = self.phone
    #
    # phone_number_saved_users = UserInvite.where(phone: phone).pluck(:name, :user_id)
    # location_circle_ids_with_duplicate_ids = []
    # names = []
    # user_invitee_ids = []
    #
    # # appends user details to array who saved that phone number
    # phone_number_saved_users.each do |user_details|
    #   # user_details.first is contact name saved by praja_user
    #   name = user_details.first
    #   names << name.downcase
    #   # user_details.last is id of a praja signed up user
    #   user_invitee_ids << user_details.last
    # end
    #
    # # check whether the names had any un_subscribed_word .
    # # If names has un_subscribed_word then we don't index that phone number.
    # has_un_subscribed_word = has_un_subscribed_word(names)
    # return if has_un_subscribed_word
    #
    # user_invitee_list = []
    # User.where(id: user_invitee_ids).find_in_batches(batch_size: 500).each do |batch|
    #   user_invitee_list += batch
    # end
    # user_invitee_list.each do |u|
    #   next if u.internal
    #   location_circle_ids_with_duplicate_ids << u.district_id
    # end
    #
    # return if location_circle_ids_with_duplicate_ids.empty?
    #
    # location_circle_ids = get_location_circle_ids(location_circle_ids_with_duplicate_ids)
    # return if location_circle_ids.empty?
    # # checks whether the phone number user affiliated to any party or not
    # affiliated_to_party = affiliated_party(names)
    #
    # proper_contact_name = get_proper_contact_name(names)
    #
    # badge_user_ids = []
    # badge_users_role_ids = []
    # badge_users_grade_levels = []
    # normal_user_ids = []
    #
    # user_invitee_list.each do |user|
    #   if location_circle_ids.include? user.district_id
    #     user_badge = user.get_badge_role
    #     if user_badge.present?
    #       if user.marketing_consent && !user.internal
    #         badge_user_ids << user.id
    #         badge_users_grade_levels << user_badge.get_readable_grade_level
    #         badge_users_role_ids << user.get_badge_role.role_id
    #       end
    #     elsif user_badge.nil? && !user.internal
    #       normal_user_ids << user.id
    #     end
    #   end
    # end
    #
    # badge_users_role_ids.uniq!
    # badge_users_grade_levels.uniq!
    # body = {
    #   "script": {
    #     "source": '' "
    #             ctx._source.proper_contact_name = params.proper_contact_name;
    #             ctx._source.affiliated_to_party = params.affiliated_to_party;
    #             ctx._source.badge_user_ids = params.badge_user_ids;
    #             ctx._source.badge_users_role_ids = params.badge_users_role_ids;
    #             ctx._source.badge_users_grade_levels = params.badge_users_grade_levels;
    #             ctx._source.location_circle_ids = params.location_circle_ids;
    #             ctx._source.normal_user_ids = params.normal_user_ids;
    #             ctx._source.network_density = params.network_density;
    #         " '',
    #     "params": {
    #       "proper_contact_name": proper_contact_name,
    #       "affiliated_to_party": affiliated_to_party,
    #       "badge_user_ids": badge_user_ids,
    #       "badge_users_role_ids": badge_users_role_ids,
    #       "badge_users_grade_levels": badge_users_grade_levels,
    #       "location_circle_ids": location_circle_ids,
    #       "normal_user_ids": normal_user_ids,
    #       "network_density": badge_user_ids.length + normal_user_ids.length,
    #     }
    #   },
    #   "upsert": {
    #     "id": phone,
    #     "proper_contact_name": proper_contact_name,
    #     "affiliated_to_party": affiliated_to_party,
    #     "badge_user_ids": badge_user_ids,
    #     "badge_users_role_ids": badge_users_role_ids,
    #     "badge_users_grade_levels": badge_users_grade_levels,
    #     "location_circle_ids": location_circle_ids,
    #     "normal_user_ids": normal_user_ids,
    #     "network_density": badge_user_ids.length + normal_user_ids.length,
    #   }
    # }
    #
    # ES_CLIENT.perform_request(
    #   'POST',
    #   "#{EsUtil.get_user_invite_phones_index_v2}/_update/#{phone}?retry_on_conflict=1",
    #   {},
    #   body
    # )
  end

  # returns proper contact name after removing names which contains red words , numbers and names length <= 3
  # it returns nil if there is no proper contact name
  def get_proper_contact_name(names)
    contact_name = nil
    names.each do |name|
      contact_name = filter_contact_name(name)
      return contact_name unless contact_name.nil?
    end
    contact_name
  end

  # Avoid name which has red_words , numbers and length is less than or equal to 3
  def filter_contact_name(name)
    if name.include?('.')
      name = filter_if_name_contains_period(name)
    end
    name = get_name_after_removing_party_name(name)
    # removes special characters and spaces in the name
    name = name.gsub(/[?!@%&#"]/, '').strip
    return nil if RedWord.check_for_names(name) || !name[/\d/].nil? || name.length <= 3 || name.include?("+")
    # returns name after all filters done
    name
  end

  # Extract contact name pre or post . symbol which has more characters
  def filter_if_name_contains_period(name)
    max_len = 0
    result_name = ""
    name.split(".").each do |sub_name|
      if sub_name.length >= max_len
        max_len = sub_name.length
        result_name = sub_name
      end
    end
    result_name
  end

  # checks whether contact name contains any political party name
  def affiliated_party(names)
    affiliated_to_party = nil

    # joining party names using seperator '|'
    party_names = Circle.political_party_keys.join('|')
    party_id = nil
    names.each do |name|
      # it removes digits from the name
      name = name.gsub(/\d+/, "")
      # scan the contact name using regex to find whether there is any party name present
      party_name = name.scan(/\b(?:#{party_names})\b/i)
      # it helps to remove the digits from a party name.
      if party_name.present?
        party_id = Circle.party_name_related_circle_id(party_name.first)
      end
      # checks whether the contact names are affiliate to more than one party
      # if contact names are affiliated to more than one party, it returns null
      unless party_id.nil?
        if affiliated_to_party.nil?
          affiliated_to_party = party_id
        elsif affiliated_to_party != party_id
          affiliated_to_party = nil
          break
        end
      end
    end
    affiliated_to_party
  end

  # removes the party name if contact name contains a political party name
  def get_name_after_removing_party_name(name)
    party_names = Circle.political_party_keys.join('|')
    party_name = name.scan(/\b(?:#{party_names})\b/i)
    if party_name.present?
      name.slice! party_name.first
      return name.strip
    end
    name
  end

  def get_location_circle_ids(location_circle_ids_with_duplicate_ids)
    location_circle_ids = []
    location_circle_ids_with_duplicate_ids.uniq.each do |lc|
      if (SPECIFIC_DISTRICT_IDS.include? lc) && (location_circle_ids_with_duplicate_ids.count(lc) >=
        MIN_PHONE_USER_INVITE_COUNT_OF_A_SPECIFIC_DISTRICT)
        location_circle_ids << lc
      elsif location_circle_ids_with_duplicate_ids.count(lc) >= MIN_PHONE_USER_INVITE_COUNT
        location_circle_ids << lc
      end
    end
    location_circle_ids
  end

  def has_un_subscribed_word(names)
    un_subscribed_words = UnSubscribedWord.all.map(&:word).join('|')
    names.each do |name|
      has_un_subscribed_word = name.scan(/\b(?:#{un_subscribed_words})\b/i)
      return true if has_un_subscribed_word.present?
    end
    false
  end

  private

  def check_and_update_sent_flag
    self.sent = true if UserInvite.where(phone: self.phone, sent: true).exists?
  end

end
