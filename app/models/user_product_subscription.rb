class UserProductSubscription < ApplicationRecord
  belongs_to :order, optional: true
  belongs_to :user
  belongs_to :item, polymorphic: true
  enum source: { orders: "orders", referral: "referral", freebie: "freebie", new_launch: "new_launch",
                 premium_extension: "premium_extension" }

  after_commit :send_user_to_mixpanel
  after_create_commit :send_premium_extension_event_to_mixpanel

  def send_user_to_mixpanel
    SyncMixpanelUser.perform_async(user_id)
  end

  def send_premium_extension_event_to_mixpanel
    return unless (source&.to_sym == :premium_extension && item_type.to_sym == :Product &&
      item_id == Constants.get_poster_product_id)
    EventTracker.perform_async(user_id, "premium_extension_created", {
      "user_id" => user_id,
      "start_date" => start_date,
      "end_date" => end_date,
      "source" => source
    })

    # send premium_extension_started event to mixpanel if it is today only
    if start_date.to_date == Time.zone.today
      EventTracker.perform_async(user_id, "premium_extension_started", {
        "user_id" => user_id,
        "start_date" => start_date,
        "end_date" => end_date,
        "source" => source
      })
    end
  end
end
