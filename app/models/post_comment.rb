class PostComment < ApplicationRecord
  belongs_to :post
  belongs_to :user

  attribute :user

  after_create_commit :set_latest_post_comment,
                      :send_notifications

  after_commit :flush_cache, :index_post

  def flush_cache
    Rails.cache.delete([Post::COMMENTS_CACHE_KEY, post_id])
  end

  private

  def set_latest_post_comment
    $redis.set("post_latest_comment_#{post.id}", comment, ex: (86400 * 15), nx: true) # 15 days
  end

  def index_post
    IndexPostNewV2.perform_async(post.id)
  end

  def send_notifications
    SendNotificationsForComment.perform_async(id)
  end

  def send_notification_for_other_commented_users

    no_of_last_commented_users_to_send_notification = 2 # last two users among all the users who commented on post will get notifications
    PostComment.select(:user_id).where(post: post, active: true).where.not(user: [post.user, user]).group(:user_id)
               .last(no_of_last_commented_users_to_send_notification).each do |post_comment|

      if user.name.present?
        notification_title = user.name + " మీరు కామెంట్ పెట్టిన పోస్ట్ పై కామెంట్ చేశారు"
      else
        notification_title = "మీరు కామెంట్ పెట్టిన పోస్ట్ పై కామెంట్ చేశారు"
      end

      notification_body = comment.truncate(47, separator: ' ')
      notification = Notification.create!(description: notification_title, notification_type: :comment, user_id: post_comment.user_id, entity_type: "post", entity_id: post.id)

      payload = {
        "title": "💬 #{notification_title}",
        "body": notification_body,
        "message_channel": "comment",
        "data": {
          "path": "/posts/#{post.id}",
          "circle_notification_id": notification.id.to_s
        }
      }
      GarudaNotification.send_user_notification(post_comment.user.id, payload)
    end
  end

  def send_notification_for_post_creator
    return if post.user_id == user_id

    notification_title = "#{user.name} మీ పోస్ట్ పై కామెంట్ చేశారు"
    notification_body = comment.truncate(47, separator: ' ')
    notification = Notification.create!(description: notification_title,
                                        notification_type: :comment,
                                        user: post.user,
                                        entity_type: "post",
                                        entity_id: post.id)

    payload = {
      "title": "💬 #{notification_title}",
      "body": notification_body,
      "message_channel": "comment",
      "data": {
        "path": "/posts/#{post.id}",
        "circle_notification_id": notification.id.to_s
      }
    }
    GarudaNotification.send_user_notification(post.user.id, payload)
  end
end
