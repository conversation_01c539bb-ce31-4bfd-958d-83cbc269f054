class RedWord < ApplicationRecord
  validates_uniqueness_of :word
  validates :word, presence: true, allow_blank: false
  after_save_commit :perform_index
  after_destroy_commit :delete_index
  before_validation :normalize_word

  belongs_to :admin_user, optional: true

  def perform_index
    IndexRedWord.perform_async(id, word, is_hate_speech)
  end

  def delete_index
    DeleteRedWordIndex.perform_async(id)
  end

  def normalize_word
    self.word = word.strip.downcase
  end

  def self.check_false_positive_red_words(text, words)
    words.each do |word|
      regexp = "\\b"+word['_source']['red_word']+"\\b"
      regexp = regexp.gsub(" ", '((\\W|_)*)')
      if text.match(regexp)
        return true
      end
    end
    false
  end

  def self.check(text)
    begin
      search_es = ES_CLIENT.search index: EsUtil.get_red_word_index, body: {
        "query": {
          "bool": {
            "must": [
              {
                "match": {
                  "red_word": {
                    "query": text
                  }
                }
              },
              {
                "match": {
                  "is_hate_speech": {
                    "query": true
                  }
                }
              }
            ]
          }
        }
      }
      words_hits = search_es['hits']['hits']
      if words_hits.count > 0
        return RedWord.check_false_positive_red_words(text, words_hits)
      end
    rescue => exception
      logger.error(exception.message.to_s)
      return false
    end
    false
  end

  def self.check_for_names(text)
    begin
      search_es = ES_CLIENT.search index: EsUtil.get_red_word_index, body: {
        "query": {
          "bool": {
            "must": [
              {
                "match": {
                  "red_word": {
                    "query": text
                  }
                }
              }
            ]
          }
        }
      }
      words_hits = search_es['hits']['hits']
      if words_hits.count > 0
        return RedWord.check_false_positive_red_words(text, words_hits)
      end
    rescue => exception
      logger.error(exception.message.to_s)
      return false
    end
    false
  end
end
