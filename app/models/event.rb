class Event < ApplicationRecord
  include J<PERSON><PERSON><PERSON>er
  belongs_to :creator, polymorphic: true
  enum priority: %i[low medium high], _suffix: true

  has_many :poster_creatives, dependent: :destroy
  has_many :video_creatives, dependent: :destroy
  has_many :active_poster_creatives, -> { where(active: true) }, class_name: 'PosterCreative'
  has_many :paid_active_poster_creatives, -> { where(active: true, paid: true) }, class_name: 'PosterCreative'
  has_many :designers, through: :poster_creatives, source: :designer
  has_many :event_circles, dependent: :destroy

  DEFAULT_EVENT_END_TIME = Date.parse("9999-12-31").at_end_of_day.in_time_zone
  attr_accessor :circle_ids
  validates_presence_of :name, :priority, :start_time
  validates :priority, inclusion: { in: Event.priorities.keys }
  validate :end_time_cannot_be_before_start_time, if: -> { errors.blank? }
  before_save :change_end_time_if_nil, if: -> { end_time.blank? }
  after_commit :update_poster_creatives, :index_event_for_posters_feed

  def self.ransackable_associations(auth_object = nil)
    ["creator", "event_circles", "poster_creatives"]
  end

  def end_time_cannot_be_before_start_time
    end_time = self.end_time.present? ? self.end_time : DEFAULT_EVENT_END_TIME
    errors.add(:end_time, "can't be before start time") if end_time < start_time
  end

  def change_end_time_if_nil
    self.end_time = DEFAULT_EVENT_END_TIME
  end

  def get_poster_creative_urls(sub_query_for_free_users)
    poster_creatives = self.poster_creatives.where("start_time < ?", Time.zone.now)
                           .where("end_time > ?", Time.zone.now)
                           .where("poster_creatives.start_time < ?", Time.zone.now)
                           .where(active: true)
                           .where("poster_creatives.end_time > ? #{sub_query_for_free_users} AND poster_creatives.active =
                            TRUE", Time.zone.now)

    [poster_creatives.count, poster_creatives.map { |poster_creative| poster_creative.photo_v2.url }.first(3)]
  end

  def event_poster_creatives(sub_query_for_free_users:, limit:, offset: 0, random: false)
    query = self.poster_creatives
        .includes(:photo_v3)
        .where("start_time < ?", Time.zone.now)
        .where("end_time > ?", Time.zone.now)
        .where("poster_creatives.start_time < ?", Time.zone.now)
        .where(active: true)
        .where("poster_creatives.end_time > ? #{sub_query_for_free_users} AND poster_creatives.active
                            = TRUE", Time.zone.now)
        .where("poster_creatives.photo_v3_id IS NOT NULL")

    if random
      # Get all matching creatives
      all_creatives = query.to_a

      # If there are creatives, select random ones based on limit
      if all_creatives.present?
        # Return random creatives up to the limit
        all_creatives.sample([all_creatives.size, limit].min)
      else
        # No creatives found, return empty array
        []
      end
    else
      # Use the original offset and limit approach
      query.offset(offset).limit(limit)
    end
  end

  # get poster_creatives in order of primary desc and created_at desc
  # and offset and count
  def poster_creatives_with_next_page_url(sub_query_for_free_users:, offset: 0, count: Constants.creatives_count)
    # Adjust the limit to fetch one extra record to check for the presence of a next page.
    # This does not increase the load significantly but allows us to know if more records exist.
    limit = count + 1
    poster_creatives = self.event_poster_creatives(sub_query_for_free_users:, offset:, limit:)

    # Check if we fetched more items than the requested count. If so, there's a next page.
    has_next_page = poster_creatives.size > count

    # If there's a next page, remove the extra item not meant to be returned.
    poster_creatives = poster_creatives.first(count) if has_next_page
    next_page_url = has_next_page ? "/creatives?id=#{id}&offset=#{offset + count}&count=#{count}" : nil
    {
      poster_creatives: poster_creatives,
      next_page_url: next_page_url
    }
  end

  # get events in order of circle level(location,leader,party) and priority desc
  # where event is active and poster is active and poster_creative is not expired and it should include in
  # user affiliated circles
  def self.events_for_posters_tab(user_affiliated_circle_ids:, sub_query_for_free_users:)
    Event.includes(:event_circles, :poster_creatives)
         .joins(event_circles: :circle)
         .where("events.start_time < ?", Time.zone.now)
         .where("events.end_time > ?", Time.zone.now)
         .where("poster_creatives.start_time < ?", Time.zone.now)
         .where("poster_creatives.end_time > ? #{sub_query_for_free_users} AND poster_creatives.active =
                   TRUE", Time.zone.now)
         .where(active: true)
         .where(event_circles: { circle_id: user_affiliated_circle_ids })
         .where.not(poster_creatives: { id: nil })
         .order(Arel.sql("events.priority DESC,
                                  CASE circles.level
                                    WHEN 7 THEN 0
                                    WHEN 6 THEN 1
                                    WHEN 10 THEN 2
                                    ELSE 3
                                  END ASC"))
  end

  def self.fetch_events_data_as_json_v2(user_affiliated_circle_ids:, sub_query_for_free_users:)
    events = events_for_posters_tab(user_affiliated_circle_ids: user_affiliated_circle_ids, sub_query_for_free_users: sub_query_for_free_users)
    return [] if events.blank?

    events.map do |event|
      poster_creatives, next_page_url = event.poster_creatives_with_next_page_url(sub_query_for_free_users: sub_query_for_free_users).values_at(:poster_creatives, :next_page_url)
      items = poster_creatives.map { |poster_creative| poster_creative.get_json(event: event) }
      # Build the creative carousel feed item
      # build feed_item_id from event id
      feed_item_id = "event-#{event.id}"
      Event.build_creative_carousel(title: event.name, items: items, next_page_url: next_page_url,
                                    analytics_params: event.analytics_params, feed_item_id: feed_item_id)
    end
  end

  def analytics_params
    {
      event_name: name,
      event_id: id
    }
  end

  def get_creative_for_wati_campaign
    if Time.zone.now > self.start_time && Time.zone.now < self.end_time
      paid_active_poster_creatives.where(primary: true).first
    else
      creative = paid_active_poster_creatives.where(primary: false).first
      creative = paid_active_poster_creatives.where(primary: true).first if creative.nil?
      creative
    end
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i(event_circle_eq)
  end

  def self.event_circle_eq(value)
    joins(:event_circles).where(event_circles: { circle_id: value })
  end

  def self.upcoming_events(user:, only_one_high_event: false, circle_id_filter: nil, loaded_event_ids: [])
    if circle_id_filter.present?
      user_affiliated_circle_ids = [circle_id_filter]
    else
      user_affiliated_circle_ids = user.affiliated_circle_ids_for_posters_tab
    end

    query = Event.includes(:event_circles, :poster_creatives)
                 .joins(event_circles: :circle)
                 .where("events.start_time > ?", Time.zone.now)
                 .where("poster_creatives.start_time > ?", Time.zone.now)
                 .where(active: true)
                 .where(event_circles: { circle_id: user_affiliated_circle_ids })
                 .where.not(poster_creatives: { id: nil })
                 .where.not(id: loaded_event_ids)

    if only_one_high_event
      query.where(priority: :high).limit(1)
           .order(Arel.sql("CASE circles.level
                                    WHEN 7 THEN 0
                                    WHEN 6 THEN 1
                                    WHEN 10 THEN 2
                                    ELSE 3
                                  END ASC"))
    else
      query.limit(10)
           .order(Arel.sql("events.priority DESC,
                        CASE circles.level
                          WHEN 7 THEN 0
                          WHEN 6 THEN 1
                          WHEN 10 THEN 2
                          ELSE 3
                        END ASC"))
    end
  end

  def self.upcoming_events_including_current(user:, only_high_priority: false, order_by_priority: false)
    user_affiliated_circle_ids = user.affiliated_circle_ids_for_posters_tab
    # add today's events too
    query = Event.includes(:event_circles, :poster_creatives)
                 .joins(event_circles: :circle)
                 .where("events.start_time <= ?", Time.zone.now)
                 .where("poster_creatives.start_time <= ?", Time.zone.now)
                 .where("events.end_time > ?", Time.zone.now)
                 .where("poster_creatives.end_time > ?", Time.zone.now)
                 .where(active: true)
                 .where(priority: only_high_priority ? :high : Event.priorities.values)
                 .where(event_circles: { circle_id: user_affiliated_circle_ids })
                 .where.not(poster_creatives: { id: nil })
                 .limit(10)

    if order_by_priority
      query.order(
        Arel.sql("events.priority DESC,
              events.start_time ASC,
              CASE circles.level
                WHEN 7 THEN 0
                WHEN 6 THEN 1
                WHEN 10 THEN 2
                ELSE 3
              END ASC"))
    else
      query.order(
        Arel.sql("events.start_time ASC,
                events.priority DESC,
                CASE circles.level
                  WHEN 7 THEN 0
                  WHEN 6 THEN 1
                  WHEN 10 THEN 2
                  ELSE 3
                END ASC"))
    end
  end

  def primary_creative
    poster_creatives.find_by(primary: true)
  end

  private

  def update_poster_creatives
    poster_creatives.update_all(active: false) if saved_change_to_active? && !active
  end

  def index_event_for_posters_feed
    #for video_creatives, if the event properties changes, then we need to modify the video_creative properties too if they linked..
    if saved_change_to_start_time? || saved_change_to_end_time? || saved_change_to_active?
      video_creatives.find_each do |vc|
        vc.update_columns(
          start_time: start_time,
          end_time: end_time,
          active: active
        )
        IndexCreativesForPostersFeed.perform_async("video_creative_#{vc.id}")
      end
    end

    if saved_change_to_id? || saved_change_to_priority? || saved_change_to_start_time? || saved_change_to_end_time? ||
      saved_change_to_active?
      IndexCreativesForPostersFeed.perform_async("event_#{id}")
    end
  end
end
