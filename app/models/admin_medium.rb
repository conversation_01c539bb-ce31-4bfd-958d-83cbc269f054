class AdminMedium < ApplicationRecord
  belongs_to :admin_user
  has_many :poster_photos, as: :photo
  has_many :poster_creatives, as: :photo_v2
  has_many :poster_creatives, as: :photo_v3
  has_many :user_leader_photos, as: :photo
  has_many :users, as: :poster_photo
  has_many :users, as: :poster_photo_with_background
  has_many :users, as: :family_frame_photo
  has_many :users, as: :hero_frame_photo
  has_many :circles, as: :slogan_icon

  enum media_type: { photo: 'photo', video: 'video' }
  attribute :blob_data
  attribute :placeholder_url, :string

  validate :check_data_attribute, :check_allowed_extensions
  before_save :set_media_type, :upload

  def host
    'https://a-cdn.thecircleapp.in'
  end

  def path
    URI.parse(attributes['url']).path.gsub(%r{^/|/$}, '')
  end

  def url
    @url ||= "#{host}/#{path}"
  end

  def placeholder_url
    compressed_url(size: 512, quality: 80)
  end

  # pass either size or width and height
  # fit: true will fit the image in the given width and height
  # fit: false will crop the image to the given width and height (cover)
  def compressed_url(size: 120, quality: 80, fit: true, width: nil, height: nil)
    w = width || size
    h = height || size

    "#{host}/#{fit ? 'fit-in/' : ''}#{w}x#{h}/filters:quality(#{quality})/#{path}"
  end

  def compressed_url!(size: 120, quality: 80, fit: true, width: nil, height: nil)
    @url = compressed_url(size:, quality:, fit:, width:, height:)
  end

  def check_data_attribute
    errors.add(:blob_data, message: 'No file data') if new_record? && blob_data.nil?
  end

  def check_allowed_extensions
    return if self.errors.present? || blob_data.nil?

    file_extension = File.extname(blob_data.original_filename)

    unless ['.jpg', '.jpeg', '.png', '.webp', '.mp4'].include? file_extension.downcase
      errors.add(:blob_data, message: 'Not a valid file type')
    end
  end

  def set_media_type
    file_extension = File.extname(blob_data.original_filename)
    type = nil
    if ['.jpg', '.jpeg', '.png', '.webp'].include? file_extension.downcase
      type = :photo
    elsif file_extension == '.mp4'
      type = :video
    end

    self.media_type = type
  end

  def upload
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ),
    )
    file_extension = File.extname(blob_data.original_filename)

    hashed_file_name = SecureRandom.uuid + file_extension.downcase

    s3_object_path = "#{Rails.env}/admin-media/#{admin_user.id}/#{hashed_file_name}"

    checksum = Digest::MD5.file(blob_data).base64digest

    resource
      .bucket(Rails.application.credentials[:aws_s3_bucket_name])
      .object(s3_object_path)
      .put(body: blob_data,
           content_md5: checksum)

    self.url = "https://a-cdn.thecircleapp.in/#{s3_object_path}"
  end

  def photo_download_url
    s3 = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials.aws_access_key_id,
        Rails.application.credentials.aws_secret_access_key
      )
    )
    uri = URI.parse(self.url)
    # Assume that the key is the path without the leading '/'
    key = uri.path[1..-1]
    bucket_name = Rails.application.credentials[:aws_s3_bucket_name]
    object = s3.bucket(bucket_name).object(key)
    object.presigned_url(
      :get_object,
      response_content_disposition: "attachment;"
    )
  end

end
