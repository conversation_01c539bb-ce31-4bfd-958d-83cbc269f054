class GarudaNotification < ApplicationRecord
  include AASM
  has_paper_trail

  belongs_to :admin_user

  enum status: { created: 'created', sending: 'sending', sent: 'sent', failed: 'failed', stopped: 'stopped' }

  validates :title, presence: true
  validates :body, presence: true
  validates :path, presence: true

  validate :validate_filter_flags

  has_one :location_circle, class_name: 'Circle', foreign_key: 'id', primary_key: 'location_circle_id'
  has_one :party_circle, class_name: 'Circle', foreign_key: 'id', primary_key: 'party_circle_id'

  before_save :default_send_at
  after_create_commit :schedule_notification

  attr_readonly :time_taken_to_send, :time_taken_to_send_str

  aasm column: :status, enum: true do
    state :created, initial: true
    state :sending
    state :sent
    state :failed
    state :stopped

    before_all_events :before_all_events

    event :send_notification do
      transitions from: :created, to: :sending
    end

    event :mark_as_sent do
      before do
        self.sent_at = Time.zone.now
      end
      transitions from: [:sending, :sent], to: :sent
    end

    event :mark_as_failed do
      transitions from: [:created, :sending], to: :failed
    end

    event :stop do
      before do
        Sidekiq::ScheduledSet.new.find_job(self.sidekiq_job_id)&.delete if self.sidekiq_job_id.present?
      end
      transitions from: :created, to: :stopped
    end
  end

  def time_taken_to_send
    return nil if self.sent_at.blank?
    self.sent_at.to_i - self.send_at.to_i
  end

  def time_taken_to_send_str
    time_in_sec = self.time_taken_to_send
    return '-' if time_in_sec.blank?

    "#{time_in_sec / 60} min #{time_in_sec % 60} sec"
  end

  def validate_filter_flags
    if self.location_circle_id.blank? && self.party_circle_id.blank?
      errors.add(:location_circle_id, "must select one of location or party circle")
      errors.add(:party_circle_id, "must select one of location or party circle")
    end

    if self.exclusive_party_users && self.party_circle_id.blank?
      errors.add(:party_circle_id, "can't be blank if exclusive_party_users is true")
    end

    if self.party_circle_id.present? && self.location_circle_id.present?
      errors.add(:party_circle_id, "can't be present if location_circle_id is present")
      errors.add(:location_circle_id, "can't be present if party_circle_id is present")
    end
  end

  def default_send_at
    self.send_at = Time.zone.now if self.send_at.blank?
  end

  def schedule_notification
    self.sidekiq_job_id = TriggerGarudaNotification.perform_at(self.send_at, self.id)
    self.save!
  end

  def trigger_notification
    if self.exclusive_party_users && self.party_circle_id.present?
      trigger_exclusive_party_users_notification
    elsif self.party_circle_id.present?
      trigger_party_circle_users_notification
    elsif self.location_circle_id.present? && self.location_circle.district_level?
      trigger_district_circle_users_notification
    elsif self.location_circle_id.present? && self.location_circle.state_level?
      trigger_state_circle_users_notification
    elsif self.location_circle_id.present? && self.location_circle_id == 0
      # disable suggested lists before sending the message
      $redis.set(Constants.disable_suggested_lists_key, true, ex: 5.minutes.to_i)
      trigger_notification_to_all_users
    end
  end

  def self.trigger_notification_for_user_id(user_id, title, body, path)
    uri = URI.parse("#{Constants.get_garuda_base_url}/users/#{user_id}/dispatch")

    payload = {
      "title": title,
      "body": body,
      "data": {
        "path": path,
      }
    }
    resp = Net::HTTP.post(
      uri,
      { payload: payload }.to_json,
      "Content-Type" => "application/json"
    )

    if resp.code.to_i != 200
      raise StandardError.new("Notification sending failed")
    end

    return true
  end

  def trigger_notification_to_all_users
    uri = URI.parse("#{Constants.get_garuda_base_url}/users/all/dispatch")
    send_to_garuda_service(uri)
  end

  def trigger_exclusive_party_users_notification
    uri = URI.parse("#{Constants.get_garuda_base_url}/party/#{self.party_circle_id}/dispatch")
    send_to_garuda_service(uri)
  end

  def trigger_party_circle_users_notification
    uri = URI.parse("#{Constants.get_garuda_base_url}/circle/#{self.party_circle_id}/dispatch")
    send_to_garuda_service(uri)
  end

  def trigger_state_circle_users_notification
    uri = URI.parse("#{Constants.get_garuda_base_url}/state/#{self.location_circle_id}/dispatch")
    send_to_garuda_service(uri)
  end

  def trigger_district_circle_users_notification
    uri = URI.parse("#{Constants.get_garuda_base_url}/district/#{self.location_circle_id}/dispatch")
    send_to_garuda_service(uri)
  end

  def send_to_garuda_service(uri)
    payload = {
      "title": self.title,
      "body": self.body,
      "data": {
        "path": self.path,
      }
    }
    resp = Net::HTTP.post(
      uri,
      { payload: payload }.to_json,
      "Content-Type" => "application/json"
    )

    if resp.code.to_i == 200
      self.notification_id = JSON.parse(resp.body)['notification_id']
      self.sending!
    else
      Honeybadger.notify("Garuda notification failed", context: {
        garuda_notification_id: self.id,
        response_code: resp.code,
        response_body: resp.body
      })
      Rails.logger.warn("Garuda notification failed :: Status Code - #{resp.code} :: Body - #{resp.body}")
      self.mark_as_failed!
    end
  end

  def self.send_channel_notification(circle_id, payload)
    uri = URI.parse("#{Constants.get_garuda_base_url}/circle/#{circle_id}/dispatch")

    response = Net::HTTP.post(uri, payload.to_json, "Content-Type" => "application/json")

    if response.code.to_i == 200
      true
    else
      Honeybadger.notify("Garuda channel notification failed", context: { circle_id: circle_id, payload: payload, response: response })
      false
    end
  end

  def self.trigger_user_notification(user_id, payload)
    uri = URI.parse("#{Constants.get_garuda_base_url}/users/#{user_id}/dispatch")
    response = Net::HTTP.post(uri, payload.to_json, "Content-Type" => "application/json")
    if response.code.to_i == 200
      true
    else
      Honeybadger.notify("Garuda user notification failed", context: { user_id: user_id, payload: payload, response: response })
      false
    end
  end

  def self.send_user_notification(user_id, payload, min_app_version=nil)

    android_payload = {
      "platform": "android",
      "payload": payload,
      "excluded_user_ids": []
    }

    ios_payload = {
      "platform": "ios",
      "payload": payload,
      "excluded_user_ids": []
    }

    if min_app_version.present?
      android_payload[:min_app_version] = min_app_version
      ios_payload[:min_app_version] = min_app_version
    end

    android_notification_call_thread = Thread.new do
      begin
        GarudaNotification.trigger_user_notification(user_id, android_payload)
      rescue => e
        Honeybadger.notify(e, context: { user_id: user_id, android_payload: android_payload })
        throw e
      end
    end

    ios_notification_call_thread = Thread.new do
      begin
        GarudaNotification.trigger_user_notification(user_id, ios_payload)
      rescue => e
        Honeybadger.notify(e, context: { user_id: user_id, ios_payload: ios_payload })
        throw e
      end
    end
    android_notification_call_thread.join
    ios_notification_call_thread.join
  end

  private

  def before_all_events
    self.paper_trail_event = aasm.current_event
  end
end
