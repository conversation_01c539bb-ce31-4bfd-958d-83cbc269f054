# frozen_string_literal: true
class LeaderProject < ApplicationRecord
  belongs_to :user, -> { active }
  belongs_to :user_role, -> { where active: true }
  belongs_to :creator, polymorphic: true

  has_one_attached :photo

  validates :user_id, :user_role_id, numericality: { only_integer: true }, presence: true, allow_blank: false
  validates :title, length: { maximum: 30 }, presence: true
  validates :body, length: { maximum: 150 }, presence: true
  validates :photo, presence: true
  validates :project_date, presence: true

  validate :user_exists?, if: -> { errors.blank? }
  validate :user_role_exists?, if: -> { errors.blank? }
  validate :check_if_user_matched_in_user_role, if: -> { errors.blank? }
  validate :limit_no_of_leader_projects_active, if: -> { errors.blank? }
  validate :validate_project_date_with_user_role_duration, if: -> { errors.blank? }

  def user_exists?
    errors.add(:user_id, "User must exist") if user.blank?
  end

  def user_role_exists?
    errors.add(:user_role_id, "User role must exist") if user_role.blank?
  end

  def check_if_user_matched_in_user_role
    if user_role.user_id != user_id
      errors.add(:user_role_id, "User role is not associated with given user")
    end
  end

  def limit_no_of_leader_projects_active
    if (self.new_record? && self.active) || self.active_changed?(from: false, to: true)
      no_of_active_leader_projects = LeaderProject.where(user_id: user_id, active: true).count
      if no_of_active_leader_projects >= 5
        errors.add(:active, "User already has five active leader projects, can't create more active leader projects records")
      end
    end
  end

  def validate_project_date_with_user_role_duration
    unless self.project_date.between?(self.user_role.start_date, self.user_role.end_date)
      errors.add(:project_date, "Project date doesn't match with given user role duration")
    end
  end

  def photo_url
    "https://l-cdn.praja.buzz/fit-in/512x512/filters:quality(80)/#{photo.key}"
  end

end
