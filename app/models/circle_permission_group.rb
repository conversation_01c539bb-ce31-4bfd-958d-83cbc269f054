class CirclePermissionGroup < ApplicationRecord
  belongs_to :circle, optional: true
  belongs_to :permission_group

  enum circle_type: Circle::CIRCLE_TYPE, _suffix: true
  enum circle_level: Circle::CIRCLE_LEVEL, _suffix: true
  validates :circle_type, inclusion: { in: circle_types.keys }, allow_nil: true
  validates :circle_level, inclusion: { in: circle_levels.keys }, allow_nil: true
  validates :circle_id, numericality: { only_integer: true }, allow_nil: true
  validates :permission_group_id, presence: true, allow_blank: false
  validates :is_user_joined, inclusion: { in: [true, false] }

  after_commit :send_update_to_dm, :flush_cache

  CACHE_KEY = 'circle_permission_group_v1'

  # if circle id is present then circle type and circle level should be null
  validate :circle_id_null_if_circle_type_and_circle_level_present

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def circle_id_null_if_circle_type_and_circle_level_present
    if circle_id.present? && (circle_type.present? || circle_level.present?)
      errors.add(:circle_id, "can't be present if circle type or circle level is present")
    end
  end

  # if circle type and circle level is present then circle id should be null
  validate :circle_type_and_circle_level_present_if_circle_id_null

  def circle_type_and_circle_level_present_if_circle_id_null
    if (circle_type.blank? || circle_level.blank?) && circle_id.blank?
      errors.add(:circle_type, "can't be blank if circle id is blank") if circle_type.blank?
      errors.add(:circle_level, "can't be blank if circle id is blank") if circle_level.blank?
    end
  end

  def self.get_default_circle_permission_group_id(circle_id, circle_type, circle_level, is_user_joined)
    permission_group_id = Rails.cache.fetch([CACHE_KEY, circle_id, is_user_joined], expires_in: 1.month) do
      CirclePermissionGroup.where(circle_id: circle_id, is_user_joined: is_user_joined)
                           .pluck(:permission_group_id).first
    end

    if permission_group_id.blank?
      permission_group_id = Rails.cache.fetch([CACHE_KEY, circle_type, circle_level, is_user_joined], expires_in: 1.month) do
        CirclePermissionGroup.where(circle_type: circle_type, circle_level: circle_level,
                                    is_user_joined: is_user_joined)
                             .pluck(:permission_group_id).first
      end
    end
    permission_group_id
  end

  def flush_cache
    if circle_id.present?
      Rails.cache.delete([CACHE_KEY, circle_id, is_user_joined])
    else
      Rails.cache.delete([CACHE_KEY, circle_type, circle_level, is_user_joined])
    end
  end

  def send_update_to_dm
    if is_user_joined
      circle_ids =
        if circle_id.present? && circle.channel_conversation_type?
          [circle_id]
        elsif circle_type.present? && circle_level.present?
          Circle.where(circle_type: circle_type, level: circle_level, conversation_type: :channel).pluck(:id)
        end

      DmUtil.send_circle_permission_group_update_to_dm(circle_ids)
    end
  end
end
