class Notification < ApplicationRecord
  belongs_to :user

  attribute :entity
  attribute :notification_icon

  NOTIFICATION_ICON_URL = {
    "remove_tag" => "https://a-cdn.thecircleapp.in/production/admin-media/15/b34bc4d75376689bed604015dc80fc8d.png"
  }

  enum notification_type: %i[like comment circle bounce follow new_post mention circle_invite new_post_public new_post_party new_post_leader new_post_location user_badge remove_tag video_poster], _suffix: true

  after_create_commit :update_user_unread_notifications_count

  def update_user_unread_notifications_count
    UpdateUserUnreadNotificationsCount.perform_async(user_id)
  end

  def entity
    return nil if entity_type != 'user'

    user_check = User.find(entity_id)

    follow = UserFollower.where(user_id: entity_id, follower: user).exists?
    user_check.phone = user_check.generate_random_phone
    user_check.follows = follow
    user_check.badge = user_check.get_badge_role&.get_json
    user_check.photo&.compressed_url!(size: 200)

    user_check
  end

  def self.internal_notifications_count_set(user_ids)
    ignore_ids = []

    user_ids.each do |user_id|
      key_name = "notification_count_v1_user_#{user_id}"
      count = $redis.get(key_name).to_i
      case true
      when count == 0
        $redis.set(key_name, "1", ex: 86400)
      when count <= 2
        $redis.incr(key_name)
      else
        ignore_ids << user_id
      end
    end
    return ignore_ids
  end

  def notification_icon
    NOTIFICATION_ICON_URL[notification_type]
  end

end
