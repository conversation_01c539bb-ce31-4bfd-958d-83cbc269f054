class Subscription < ApplicationRecord
  include AASM
  has_paper_trail
  belongs_to :user
  belongs_to :plan
  has_one :user_plan, class_name: '<PERSON><PERSON><PERSON><PERSON>', foreign_key: 'user_id', primary_key: 'user_id'
  has_many :metadatum, as: :entity
  has_many :subscription_charges
  GRACE_PERIOD_IN_MONTHS = 1
  ATTEMPT_NUMBER_TO_CREATE_CONSENT_KEY_FOR_DOWNGRADE = 5
  ATTEMPT_NUMBER_TO_DOWNGRADE = 8
  ATTEMPT_NUMBER_TO_EXPIRE_CONSENT = 8
  MAX_GRACE_PERIOD_GIVEN_COUNT = 3
  TIME_FOR_RAISING_CHARGES_IN_HOURS = 15

  enum payment_gateway: {
    cashfree: 'cashfree',
    juspay: 'juspay',
  }

  # use `cancelled` for the user or merchant cancelled the subscription
  # use `closed` for the subscription is completed or expired
  enum status: {
    created: 'created',
    active: 'active',
    closed: 'closed',
    cancelled: 'cancelled',
    paused: 'paused',
    on_hold: 'on_hold',
  }

  scope :open, -> { where(status: :created) }
  scope :active, -> { where(status: :active) }
  scope :cancellable, -> { where(status: [:active, :on_hold]) }

  aasm column: :status, enum: true do
    state :created, initial: true
    state :active
    state :closed
    state :cancelled
    state :paused
    state :on_hold

    before_all_events :before_all_events

    event :activate do
      transitions from: :created, to: :active, after: [:after_activate, :remove_grace_period, :remove_poster_wati_campaign_count,
                                                       :remove_trial_activation_wati_campaign_count]
    end

    event :close do
      transitions from: :created, to: :closed, after: :mark_subscription_plan_downgrade_consent_as_not_needed
    end

    event :cancel do
      transitions from: [:active, :on_hold, :paused], to: :cancelled, after: [:after_cancel, :remove_grace_period,
                                                                              :remove_upgrade_package_sheet_key,
                                                                              :mark_subscription_plan_downgrade_consent_as_not_needed,
                                                                              :create_poster_wati_campaign_count]
    end

    event :customer_cancel do
      transitions from: [:active, :on_hold, :paused], to: :cancelled, after: [:after_customer_cancel,
                                                                              :remove_grace_period,
                                                                              :remove_upgrade_package_sheet_key,
                                                                              :mark_subscription_plan_downgrade_consent_as_not_needed,
                                                                              :create_poster_wati_campaign_count]
    end

    event :pause do
      transitions from: [:active, :on_hold], to: :paused, after: [:after_pause, :remove_grace_period,
                                                                  :remove_upgrade_package_sheet_key,
                                                                  :mark_subscription_plan_downgrade_consent_as_not_needed,
                                                                  :create_poster_wati_campaign_count]
    end

    event :customer_pause do
      transitions from: [:active, :on_hold], to: :cancelled, after: [:after_customer_paused, :remove_grace_period,
                                                                     :remove_upgrade_package_sheet_key,
                                                                     :mark_subscription_plan_downgrade_consent_as_not_needed]
    end

    event :resume do
      transitions from: :paused, to: :active, after: :remove_grace_period
    end

    event :hold do
      transitions from: :active, to: :on_hold, after: :remove_upgrade_package_sheet_key
    end

    event :unhold do
      transitions from: :on_hold, to: :active, after: [:remove_grace_period,
                                                       :mark_subscription_plan_downgrade_consent_as_not_needed]
    end
  end

  def before_all_events
    self.paper_trail_event = aasm.current_event
  end

  def self.ransackable_associations(auth_object = nil)
    []
  end

  def self.get_open_subscription(plan_id, user_id)
    # Fetch subscription details
    Subscription.where(plan_id: plan_id, user_id: user_id, payment_gateway: :cashfree).open.last
  end

  def self.get_open_juspay_subscription(plan_id, user_id)
    # Fetch subscription details
    Subscription.where(plan_id: plan_id, user_id: user_id, payment_gateway: :juspay)
                .where("created_at > ?", 12.hours.ago)
                .open
                .last
  end

  def self.create_plan_juspay_subscription(plan:, user:)
    pg_id = JuspayPaymentUtils.generate_pg_id
    initial_charge = if user.is_eligible_for_start_trial?
                       1
                     elsif plan.duration_in_months.in?([1, 12])
                       user.get_plan_amount_based_on_duration(plan: plan, duration: plan.duration_in_months)
                     else
                       plan.amount
                     end
    subscription = Subscription.new(plan: plan, user: user, max_amount: plan.amount,
                                    initial_charge: initial_charge, pg_id: pg_id,
                                    payment_gateway: :juspay)

    subscription_charge_pg_id = JuspayPaymentUtils.generate_pg_id

    user_plan = user.user_plan

    payload = {
      order_id: subscription_charge_pg_id,
      amount: initial_charge,
      currency: 'INR',
      'options.create_mandate': "REQUIRED",
      'options.get_client_auth_token': false,
      customer_id: user.get_juspay_customer_id,
      customer_phone: user.phone,
      customer_email: user.email.present? ? user.email : "user-#{user.id}@praja.buzz",
      description: plan.name,
      'mandate.max_amount': plan.amount,
      'mandate.frequency': 'ASPRESENTED',
      'mandate.amount_rule': 'VARIABLE',
      'mandate.start_date': Time.zone.now.to_i,
      'mandate.end_date': (Time.zone.today + 25.years).end_of_day.to_i,
      'mandate.revokable_by_customer': true,
      'mandate.block_funds': 'FALSE',
      'metadata.auto_refund_post_success': initial_charge == 1 && user_plan.blank? ? 'True' : 'False',
      udf1: pg_id,
    }

    response = JuspayPaymentUtils.post('/orders', payload)

    subscription.subscription_charges.build(pg_id: subscription_charge_pg_id,
                                            pg_reference_id: response.dig('id'),
                                            user: user,
                                            amount: initial_charge,
                                            charge_amount: initial_charge,
                                            charge_date: Time.zone.now,
                                            attempt_number: 1,
                                            payment_id: Subscription.generate_payment_id,
                                            status: :sent_to_pg,
                                            pg_json: response)

    subscription.auth_link = response.dig('payment_links', 'mobile')
    subscription.save!

    subscription
  end

  def self.create_plan_subscription(plan:, user:)
    pg_id = JuspayPaymentUtils.generate_pg_id
    initial_charge = if user.is_eligible_for_start_trial?
                       1
                     elsif plan.duration_in_months.in?([1, 12])
                       user.get_plan_amount_based_on_duration(plan: plan, duration: plan.duration_in_months)
                     else
                       plan.amount
                     end
    subscription = Subscription.new(plan: plan, user: user, max_amount: plan.amount,
                                    initial_charge: initial_charge, pg_id: pg_id,
                                    payment_gateway: :cashfree)

    # if user has email send else not
    user.email.present? ? notification_channels = %w[EMAIL SMS] : notification_channels = %w[SMS]

    user_plan = user.user_plan

    payload = {
      subscriptionId: pg_id,
      customerName: user.cashfree_supported_name? ? user.name : "User Name #{user.id}",
      customerPhone: user.phone,
      customerEmail: user.email.present? ? user.email : "user-#{user.id}@praja.buzz",
      returnUrl: "https://prajaapp.sng.link/D3x5b/oynu?_dl=prajaapp%3A%2F%2Fbuzz.praja.app%2Fpayments%2Fsuccess",
      authAmount: initial_charge,
      planInfo: {
        type: "ON_DEMAND",
        planName: plan.name,
        maxAmount: plan.amount,
      },
      refundAuthAmount: initial_charge == 1 && user_plan.blank? ? true : false,
      notificationChannels: notification_channels
    }

    response = CashfreePaymentUtils.cashfree_post_v1('/subscriptions/nonSeamless/subscription', payload)

    subscription.pg_reference_id = response.dig("data", "subReferenceId")
    subscription.auth_link = response.dig('data', 'authLink')
    subscription.pg_json = response

    subscription.save!

    SubscriptionCharge.create(subscription: subscription,
                              user: user,
                              amount: initial_charge,
                              charge_amount: initial_charge,
                              charge_date: Time.zone.now,
                              payment_id: Subscription.generate_payment_id,
                              attempt_number: 1,
                              status: :sent_to_pg)

    # return the subscription
    subscription
  end

  def cancel(reason)
    # save record in metadatum with reason for cancellation
    self.metadatum.create(key: 'cancellation_reason', value: reason)
    self.cancel!
  end

  def mark_subscription_to_take_consent_for_downgrade
    self.metadatum.create(key: Constants.plan_downgrade_consent_key, value: :to_be_shown)
  end

  def mark_subscription_plan_downgrade_consent_as_yes
    self.metadatum.where(key: Constants.plan_downgrade_consent_key).update(value: :yes)
  end

  def mark_subscription_plan_downgrade_consent_as_no
    self.metadatum.where(key: Constants.plan_downgrade_consent_key).update(value: :no)
  end

  def mark_subscription_plan_downgrade_consent_as_expired
    self.metadatum.where(key: Constants.plan_downgrade_consent_key).update(value: :expired)
  end

  def mark_subscription_plan_downgrade_consent_as_not_needed
    self.metadatum.where(key: Constants.plan_downgrade_consent_key).update(value: :not_needed)
  end

  def need_to_get_consent_for_downgrade?
    self.metadatum.where(key: Constants.plan_downgrade_consent_key, value: :to_be_shown).exists?
  end

  def is_plan_downgradable?
    plan.duration_in_months == 12 && self.metadatum.where(key: Constants.plan_downgrade_consent_key, value: :yes).present?
  end

  def after_cancel
    if cashfree?
      # on cancelling here we will call the cashfree api to cancel the subscription
      begin
        CashfreePaymentUtils.cashfree_post_v1("/subscriptions/#{self.pg_reference_id}/cancel", {})
      rescue => e
        Honeybadger.notify(e, context: { subscription_id: self.id, user_id: self.user_id })
      end
    elsif juspay?
      # on cancelling here we will call the juspay api to cancel the subscription
      begin
        JuspayPaymentUtils.post("/mandates/#{self.pg_reference_id}", { command: 'revoke' })
      rescue => e
        Honeybadger.notify(e, context: { subscription_id: self.id, user_id: self.user_id })
      end
    end

    send_cancelled_mixpanel_event(source: 'app')

  end

  def remove_grace_period
    user.metadatum.where(key: Constants.grace_period_given_string).update(key: Constants.grace_period_given_string_expired)
  end

  def after_customer_cancel
    send_cancelled_mixpanel_event(source: 'upi_app')

  end

  def after_pause
    send_paused_mixpanel_event(source: 'backend')

  end

  def after_customer_paused
    self.metadatum.create(key: 'cancellation_reason', value: 'Paused from UPI App')
    if cashfree?
      CashfreePaymentUtils.cashfree_post_v1("/subscriptions/#{self.pg_reference_id}/cancel", {})
    elsif juspay?
      JuspayPaymentUtils.post("/mandates/#{self.pg_reference_id}", { command: 'revoke' })
    end
    send_cancelled_mixpanel_event(source: 'upi_app_pause')

  end

  def after_activate
    self.user.delete_yet_to_start_trial_key
    update_premium_pitch

    Metadatum.create(entity: self,
                     key: Constants.subscription_activated_date_key,
                     value: Time.zone.today.strftime("%Y-%m-%d"))

    if need_to_get_consent_for_downgrade?
      mark_subscription_plan_downgrade_consent_as_not_needed
    end

    duration_in_months = self.plan.duration_in_months

    last_successful_charge_of_previous_subscription = SubscriptionCharge.success_ever.where.not(subscription: self)
                                                                        .where(user: user)
                                                                        .last
    is_upgrade = false
    if last_successful_charge_of_previous_subscription.present?
      is_upgrade = last_successful_charge_of_previous_subscription.subscription.plan.duration_in_months < self.plan.duration_in_months
    end

    # is_trial_setup_charge
    last_successful_charge_of_current_subscription = SubscriptionCharge.success_ever.where(subscription: self).last

    EventTracker.perform_async(user_id, "subscription_activated_backend", {
      "duration_in_months" => duration_in_months,
      "plan_id" => self.plan.id,
      "amount" => self.plan.amount,
      "subscription_id" => self.id,
      "initial_charge" => self.initial_charge,
      "payment_gateway" => self.payment_gateway,
      "is_self_trial_user" => self.user.has_premium_layout? ? false : true,
      "is_upgrade" => is_upgrade,
      "is_trial_setup_charge" => last_successful_charge_of_current_subscription.is_trial_charge?
    })

    # Check for any other subscriptions of the user & close them
    CancelUserOtherSubscriptions.perform_async(self.id)
  end

  def send_paused_mixpanel_event(source:)
    EventTracker.perform_async(user_id, "subscription_paused_backend", {
      "plan_id" => self.plan.id,
      "amount" => self.max_amount,
      "subscription_id" => self.id,
      "is_self_trial_user" => self.user.self_trial_user?,
      "source" => source,
      "payment_gateway" => self.payment_gateway,
    })
  end

  def send_cancelled_mixpanel_event(source:)
    EventTracker.perform_async(user_id, "subscription_cancelled_backend", {
      "plan_id" => self.plan.id,
      "amount" => self.max_amount,
      "subscription_id" => self.id,
      "reason" => self.metadatum.where(key: 'cancellation_reason').last&.value,
      "is_self_trial_user" => self.user.self_trial_user?,
      "source" => source,
      "subscription_activated_date" => self.get_activated_date,
      "payment_gateway" => self.payment_gateway,
    })
  end

  def update_premium_pitch
    if user.has_premium_layout?
      PremiumPitch.find_by(user_id: user_id)&.payment_done!
    else
      lead_source = user.self_trial_user? ? :SELF_TRIAL : :UNKNOWN
      pp = user.find_or_initialize_premium_pitch(source: lead_source)
      if pp.may_mark_as_subscribed_no_layout?
        if user.get_badge_role.present?
          lead_type = "LST_Inbound"
        else
          lead_type = "BLST_Inbound"
        end
        pp.lead_type = lead_type
        pp.mark_as_subscribed_no_layout!
      end
    end
  end

  def last_failed_retry_attempt_number
    subscription_charges.where(status: :failed).last&.attempt_number || 0
  end

  def get_next_retry_charge_date
    subscription_charges.where(status: :created).last&.charge_date if active? || on_hold?
  end

  def set_plan_downgraded_date_key
    self.metadatum.create(key: Constants.plan_downgraded_date_key, value: Time.zone.now)
  end

  def downgraded_date
    self.metadatum.where(key: Constants.plan_downgraded_date_key).last&.value
  end

  # check if the user's last recharge failed for the subscription
  def last_recharge_failed?
    subscription_charge = subscription_charges.last
    if subscription_charge.present?
      if subscription_charge.failed?
        return true
      elsif subscription_charge.created? || subscription_charge.sent_to_pg?
        return subscription_charges.second_to_last&.failed?
      end
      return false
    end
  end

  def get_next_retry_date(current_charge_date:, skip_charge_date_being_old_check: false)
    return unless (active? || on_hold?) && user_plan.present?

    retry_dates = [2, 4, 6, 8, 10, 13, 15, 18, 21, 25, 27, 30]

    ### 1 ###
    # 18 created
    # 19 charge date
    # 20, 7PM failure
    # retry should happen on 25

    ### 2 ###
    # 18 created
    # 19 charge date
    # 20, 4PM failure
    # retry should happen on 21

    ### 3 ###
    # 18 created
    # 19 charge date
    # 19, 8PM failure
    # retry should happen on 21

    ### 4 ###
    # 27 created
    # 28 charge date
    # 29, 8PM failure
    # retry should happen on 2, next month

    ### 5 ###
    # Month has 31 days
    # 28 created
    # 29 charge date
    # 30, 8PM failure
    # retry should happen on 2, next month

    # As payment gateway callbacks are coming after charge date for payment failure, we need to handle those cases here
    callback_difference_in_days = (Time.zone.now.beginning_of_day - current_charge_date.beginning_of_day) / 1.day

    is_charges_for_tomorrow_already_raised = Time.zone.now.hour > TIME_FOR_RAISING_CHARGES_IN_HOURS

    if callback_difference_in_days > 0 && !skip_charge_date_being_old_check
      if is_charges_for_tomorrow_already_raised
        current_charge_date = current_charge_date.advance(days: callback_difference_in_days)
      else
        current_charge_date = current_charge_date.advance(days: callback_difference_in_days - 1)
      end
    end

    last_charge_day = current_charge_date.day + 1
    current_day = Time.zone.now.day
    current_day += 1 if is_charges_for_tomorrow_already_raised

    next_retry_day = retry_dates.find { |day|
      day > ((current_charge_date.month == Time.zone.now.month) && !skip_charge_date_being_old_check ?
               [last_charge_day, current_day].max : last_charge_day) }

    next_charge_date = if next_retry_day.blank?
                         current_charge_date.change(day: retry_dates.first).next_month
                       else
                         current_charge_date.change(day: next_retry_day)
                       end
    # to make sure it won't create the subscription charge if next charge date is less than the time of subscription charge, it is being created
    # [16993, Wed, 02 Oct 2024 18:40:45.199518000 IST +05:30, "refunded", Wed, 02 Oct 2024 18:57:39.878513000 IST +05:30],
    #  [29215, Wed, 16 Oct 2024 00:00:00.000000000 IST +05:30, "failed", Wed, 16 Oct 2024 20:52:06.768433000 IST +05:30],
    #  [30160, Wed, 23 Oct 2024 00:00:00.000000000 IST +05:30, "failed", Fri, 22 Nov 2024 12:47:41.662648000 IST +05:30],
    #  [98034, Fri, 25 Oct 2024 00:00:00.000000000 IST +05:30, "created", Fri, 22 Nov 2024 12:47:41.696890000 IST +05:30]]
    grace_period_start_date = user.metadatum.where(key: Constants.grace_period_given_string).last&.value
    if grace_period_start_date.blank?
      Honeybadger.notify('Grace period start date not found', context: { user_id: user_id, subscription_charge_id: id })
      return
    end

    grace_period_start_date = Time.zone.parse(grace_period_start_date)

    return if next_charge_date >= (grace_period_start_date.advance(months: GRACE_PERIOD_IN_MONTHS) - 3.days)
    return if !skip_charge_date_being_old_check && next_charge_date < Time.zone.now

    return if user_plan.end_date - next_charge_date > 1.day
    next_charge_date
  end

  def get_nth_retry_charge_date(current_charge_date:, n:)
    nth_retry_date = nil
    (1..n).each do |i|
      nth_retry_date = get_next_retry_date(current_charge_date: current_charge_date, skip_charge_date_being_old_check: true)
      if nth_retry_date.present?
        current_charge_date = nth_retry_date
      else
        return
      end
    end
    nth_retry_date.beginning_of_day
  end

  def get_down_gradable_date
    return unless (active? || on_hold?) && user_plan.present? && SubscriptionUtils.is_user_in_grace_period?(user_id)
    first_failed_try_charge = subscription_charges.where(attempt_number: 1, status: :failed).last
    return first_failed_try_charge.present? ? get_nth_retry_charge_date(current_charge_date: first_failed_try_charge.charge_date, n: ATTEMPT_NUMBER_TO_DOWNGRADE + 1) : nil
  end

  def get_days_left_in_grace_period
    grace_period_given_date = Metadatum.where(entity_type: "User", entity_id: user_id, key: Constants.grace_period_given_string).last&.value
    grace_period_end_date = Time.zone.parse(grace_period_given_date).advance(months: GRACE_PERIOD_IN_MONTHS) - 1.days
    Time.zone.now > grace_period_end_date ? 0 : (grace_period_end_date - Time.zone.now).to_i / 1.day
  end

  def get_activated_date
    activated_date = Metadatum.find_by(entity: self, key: Constants.subscription_activated_date_key)&.value
    if activated_date.nil?
      first_successful_charge_ever = self.subscription_charges.success_ever.first
      if first_successful_charge_ever.present?
        activated_date = first_successful_charge_ever.updated_at.strftime("%Y-%m-%d")
      else
        activated_date = self.created_at.strftime("%Y-%m-%d")
      end
    end

    activated_date
  end

  def evaluate_and_apply_grace_period

    return unless on_hold?

    # hold if user has surpassed the grace period
    grace_period_given_date = (user_plan.end_date + 1.day).beginning_of_day
    return if Time.zone.now >= grace_period_given_date.advance(months: GRACE_PERIOD_IN_MONTHS)

    grace_period_given_count = user.metadatum.where(key: Constants.grace_period_given_count_string).last&.value
    if grace_period_given_count.present? && (grace_period_given_count.to_i > MAX_GRACE_PERIOD_GIVEN_COUNT)
      user.metadatum.where(key: Constants.grace_period_given_string).update(key: Constants.grace_period_given_string_expired)
      return
    else
      grace_period_date = user.metadatum.where(key: Constants.grace_period_given_string).last&.value
      if grace_period_date.blank?
        if grace_period_given_count.blank?
          user.metadatum.create(key: Constants.grace_period_given_count_string, value: 1)
        elsif grace_period_given_count.to_i < MAX_GRACE_PERIOD_GIVEN_COUNT
          user.metadatum.where(key: Constants.grace_period_given_count_string).update(value: grace_period_given_count.to_i + 1)
        end
        user.metadatum.create(key: Constants.grace_period_given_string, value: grace_period_given_date)
        EventTracker.perform_async(user.id, 'grace_period_started', {
          "subscription_id" => id,
          "grace_period_given_date" => grace_period_given_date,
          "grace_period_given_count" => (grace_period_given_count&.to_i) || 0 + 1,
          "user_plan_id" => user_plan.id
        })
      end
    end
  end

  def log_downgrade_mixpanel_events(event_name:, next_retry_date:, attempt_number:, extra_params: {})
    EventTracker.perform_async(user_id, event_name, {
      "subscription_id" => id,
      "attempt_number" => attempt_number,
      "downgrade_date" => next_retry_date,
    })
  end

  def handle_charge_payment_failure(last_failed_subscription_charge:)
    last_failed_subscription_charge = last_failed_subscription_charge || subscription_charges.where(status: :failed).last

    if may_hold?
      hold!
    end
    evaluate_and_apply_grace_period

    next_retry_date = get_next_retry_date(current_charge_date: last_failed_subscription_charge.charge_date)
    if next_retry_date.present?

      if last_failed_subscription_charge.attempt_number == ATTEMPT_NUMBER_TO_DOWNGRADE && is_plan_downgradable?
        # downgrade the user plan and subscription plan
        new_plan = Plan.get_plan_based_on_duration(user: user, duration_in_months: 1)
        current_plan = plan
        self.plan = new_plan
        save!
        UserPlan.where(user_id: user_id).last.update!(plan_id: plan_id, amount: new_plan.amount)

        # create the subscription charge with the new plan amount as it is downgraded
        SubscriptionCharge.create(subscription_id: id,
                                  user_id: user_id,
                                  amount: new_plan.amount,
                                  charge_amount: new_plan.amount,
                                  charge_date: next_retry_date.beginning_of_day,
                                  attempt_number: last_failed_subscription_charge.attempt_number + 1,
                                  payment_id: last_failed_subscription_charge.payment_id,
                                  status: :created)

        log_downgrade_mixpanel_events(event_name: 'subscription_plan_downgraded', next_retry_date: next_retry_date, attempt_number: last_failed_subscription_charge.attempt_number + 1, extra_params: { "new_plan_id" => new_plan.id,
                                                                                                                                                                                                        "new_plan_amount" => new_plan.amount,
                                                                                                                                                                                                        "new_plan_duration_in_months" => new_plan.duration_in_months,
                                                                                                                                                                                                        "current_plan_id" => current_plan.id,
                                                                                                                                                                                                        "current_plan_amount" => current_plan.amount,
                                                                                                                                                                                                        "current_plan_duration_in_months" => current_plan.duration_in_months })

      else

        # create or update the consent key for downgrade
        if last_failed_subscription_charge.attempt_number == ATTEMPT_NUMBER_TO_CREATE_CONSENT_KEY_FOR_DOWNGRADE && plan.duration_in_months == 12
          mark_subscription_to_take_consent_for_downgrade
          log_downgrade_mixpanel_events(event_name: 'subscription_plan_downgrade_consent_initiated', next_retry_date: next_retry_date, attempt_number: last_failed_subscription_charge.attempt_number + 1, extra_params: {})

        elsif last_failed_subscription_charge.attempt_number == ATTEMPT_NUMBER_TO_EXPIRE_CONSENT && need_to_get_consent_for_downgrade?
          mark_subscription_plan_downgrade_consent_as_expired
          log_downgrade_mixpanel_events(event_name: 'subscription_plan_downgrade_consent_expired', next_retry_date: next_retry_date, attempt_number: last_failed_subscription_charge.attempt_number + 1)

        end

        # create the subscription charge for the present charge amount without any downgrade as we haven't get the consent for downgrade yet
        SubscriptionCharge.create(subscription_id: id,
                                  user_id: user_id,
                                  amount: plan.amount,
                                  charge_amount: plan.amount,
                                  charge_date: next_retry_date.beginning_of_day,
                                  payment_id: last_failed_subscription_charge.payment_id,
                                  attempt_number: last_failed_subscription_charge.attempt_number + 1,
                                  status: :created)
      end
    else
      mark_subscription_plan_downgrade_consent_as_expired
    end

    if cashfree?
      error_code = last_failed_subscription_charge.pg_json&.dig('cf_reasons')
    elsif juspay?
      error_code = last_failed_subscription_charge.pg_json&.dig('juspay', 'content', 'order', 'txn_detail', 'error_code') || pg_json&.dig('txn_detail', 'error_code')
    end

    unless user.common_upgrade_package_conditions_met?
      user.delete_upgrade_package_sheet_key
    end

    last_failed_subscription_charge.log_payment_mixpanel_event(event_name: 'payment_failed_backend', extra_params: {
      "error_code" => error_code,
    }) unless last_failed_subscription_charge.is_trial_charge?

  end

  def remove_upgrade_package_sheet_key
    user.delete_upgrade_package_sheet_key
  end

  def self.generate_payment_id
    ULID.generate
  end

  def last_two_successful_charges
    # skip trial charge (fetch only last two successful charges other than trial charge)
    subscription_charges.where(status: :success).reject(&:is_trial_charge?).last(2)
  end

  def remove_poster_wati_campaign_count
    user_id = self.user_id
    key = Constants.premium_reactivation_wati_campaign_count
    UserMetadatum.where(user_id: user_id, key: key).destroy_all
  end

  def create_poster_wati_campaign_count
    user_id = self.user_id
    key = Constants.premium_reactivation_wati_campaign_count
    UserMetadatum.create(user_id: user_id, key: key, value: 0)
  end

  def remove_trial_activation_wati_campaign_count
    user_id = self.user_id
    key = Constants.trial_activation_wati_campaign_count
    UserMetadatum.where(user_id: user_id, key: key).destroy_all
  end
end
