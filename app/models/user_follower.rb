class UserFollower < ApplicationRecord
  belongs_to :user
  belongs_to :follower, class_name: 'User'

  scope :active_followers, -> { includes(:follower).where(users: { status: :active }) }
  scope :active_following, -> { includes(:user).where(users: { status: :active }) }

  FOLLOW_TYPES = %i[auto members_tab notifications_page user_following user_followers follow_contacts search post
                    suggested_list user_profile profile view_post_element post_trends_users_page post_page post_forwards
                    trended_users search_page internal]

  enum source_of_follow: FOLLOW_TYPES, _suffix: true

  validates_uniqueness_of :user_id, scope: [:follower_id]
  validate :no_self_follow

  after_commit :update_user_following_and_followers_count
  after_create_commit :send_notification

  def no_self_follow
    if self.user_id == self.follower_id
      errors.add(:follower_id, "can't do self follow")
    end
  end

  def update_user_following_and_followers_count
    UpdateFollowingCount.perform_async(follower.id)
    UpdateFollowersCount.perform_async(user.id)
  end

  def send_notification
    SendFollowNotification.perform_async(id)
  end
end
