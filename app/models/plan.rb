class Plan < ApplicationRecord
  has_many :plan_products, dependent: :destroy

  def self.ransackable_associations(_auth_object = nil)
    %w[plan_products]
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i[]
  end

  # Adding ransackers for these virtual attributes to use in filtering
  ransacker :premium_frames_count do
    Arel.sql("(SELECT COALESCE(SUM(plan_products.quantity), 0) FROM plan_products WHERE plan_products.plan_id = plans.id AND plan_products.product_id = #{Constants.get_premium_frame_product_id})")
  end

  ransacker :status_frames_count do
    Arel.sql("(SELECT COALESCE(SUM(plan_products.quantity), 0) FROM plan_products WHERE plan_products.plan_id = plans.id AND plan_products.product_id = #{Constants.get_status_frame_product_id})")
  end

  # Ransacker for combined frames count
  ransacker :combined_frames_count, formatter: proc { |v| v } do |parent|
    premium_id = Constants.get_premium_frame_product_id
    status_id = Constants.get_status_frame_product_id

    Arel.sql(
      <<-SQL.squish
        (SELECT SUM(CASE WHEN plan_products.product_id = #{premium_id} THEN plan_products.quantity ELSE 0 END) = #{parent.table[:premium_frames_count]} 
         AND SUM(CASE WHEN plan_products.product_id = #{status_id} THEN plan_products.quantity ELSE 0 END) = #{parent.table[:status_frames_count]})
      SQL
    )
  end

  def annual?
    duration_in_months == 12
  end

  def monthly?
    duration_in_months == 1
  end

  def self.system_default_plan
    Plan.find_by(id: Constants.system_default_plan_id)
  end

  def self.get_premium_plans(user:)
    frame_type_counts = UserFrame.frame_type_counts(user.id)
    premium_frames_count = (frame_type_counts['premium'].to_i + frame_type_counts['family_frame_premium'].to_i +
      frame_type_counts['hero_frame_premium'].to_i)
    premium_frames_count = premium_frames_count.positive? ? premium_frames_count : 10
    status_frames_count = frame_type_counts['status'] || 0
    fetch_plans(premium_frames_count, status_frames_count)
  end

  def self.get_plan_based_on_duration(user:, duration_in_months:)
    get_premium_plans(user:).find { |plan| plan.duration_in_months == duration_in_months }
  end

  def self.get_plans_based_on_frames_count(premium_frame_count:, status_frame_count:)
    fetch_plans(premium_frame_count, status_frame_count)
  end

  def premium_frames_count
    plan_products.where(product_id: Constants.get_premium_frame_product_id).sum(:quantity)
  end

  def status_frames_count
    plan_products.where(product_id: Constants.get_status_frame_product_id).sum(:quantity)
  end

  # check if plan exist for given frame count
  def self.plan_exist?(premium_frame_count:, status_frame_count:)
    fetch_plans(premium_frame_count, status_frame_count).present?
  end

  private

  def self.fetch_plans(premium_frame_count, status_frame_count)
    # fetch only 1 and 12 months plans only
    Plan.joins(:plan_products)
        .group('plans.id')
        .where(duration_in_months: [1, 12])
        .having('SUM(CASE WHEN plan_products.product_id = ? THEN plan_products.quantity ELSE 0 END) = ?',
                Constants.get_premium_frame_product_id, premium_frame_count)
        .having('SUM(CASE WHEN plan_products.product_id = ? THEN plan_products.quantity ELSE 0 END) = ?',
                Constants.get_status_frame_product_id, status_frame_count)

  end
end
