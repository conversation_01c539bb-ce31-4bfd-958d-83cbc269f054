class UserPlanExtension < ApplicationRecord
  CANCELLATION_FLOW_EXTENSION_DURATION_IN_DAYS = 15
  belongs_to :user
  belongs_to :user_plan

  validates :duration_in_days, presence: true, numericality: { greater_than: 0 }

  enum reason: {
    cancellation_flow: 'cancellation_flow'
  }, _prefix: true

  after_create :log_extension_mixpanel_event

  def log_extension_mixpanel_event
    EventTracker.perform_async(user_id, "user_plan_extension_created", {
      "duration_in_days" => duration_in_days,
      "reason" => reason,
      "user_plan_id" => user_plan_id,
      "extension_id" => id
    })
  end
end
