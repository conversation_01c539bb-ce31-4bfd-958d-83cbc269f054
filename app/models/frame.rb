class Frame < ApplicationRecord
  enum identifier: { party_curved_identity: "party_curved_identity", party_flat_circle_identity: "party_flat_circle_identity",
                     party_flat_identity: "party_flat_identity", party_white_flat_identity: "party_white_flat_identity",
                     neutral_flat_identity: "neutral_flat_identity", gold_white_flat_identity: "gold_white_flat_identity",
                     gold_curved_identity: "gold_curved_identity", party_neon_curved_identity:
                       "party_neon_curved_identity", party_neon_flat_identity: "party_neon_flat_identity",
                     party_white_overlay_flat_identity: "party_white_overlay_flat_identity", gold_sticker: "gold_sticker",
                     gold_party_tag: "gold_party_tag", cornered_gold_border: "cornered_gold_border", semi_circle: "semi_circle",
                     multi_color: "multi_color", capsule_shiny: "capsule_shiny", capsule_shiny_low_shadow: "capsule_shiny_low_shadow",
                     stroked_border: "stroked_border", slogan_icon_text: "slogan_icon_text", linear_name_and_role: "linear_name_and_role",
                     trapezoidal: "trapezoidal", top_trapezoidal: "top_trapezoidal", bottom_trapezoidal: "bottom_trapezoidal",
                     family_frame: "family_frame", hero_frame: "hero_frame"
  }
  enum frame_type: { premium: "premium", status: "status", circle_free: "circle_free", circle_paid: "circle_paid",
                     basic: "basic", family_frame_premium: "family_frame_premium",
                     hero_frame_premium: "hero_frame_premium" }

  enum identity_type: { flat_user_badge_circle: "flat_user_badge_circle", curved_with_depth: "curved_with_depth",
                        curved: "curved", flat_user_front: "flat_user_front", flat_user_back: "flat_user_back",
                        gold_lettered_user_front: "gold_lettered_user_front", gold_lettered_user_back:
                          "gold_lettered_user_back", flat_user: "flat_user", gold_lettered_user: "gold_lettered_user",
                        glassy_user: "glassy_user", polygonal_profile_identity: "polygonal_profile_identity",
                        party_tag_identity: "party_tag_identity", premium_cornered_party_icon_shiny_identity: "premium_cornered_party_icon_shiny_identity",
                        semi_circular_identity: "semi_circular_identity", multi_color_identity: "multi_color_identity",
                        shiny_identity: "shiny_identity", shiny_identity_with_low_shadow: "shiny_identity_with_low_shadow",
                        stroked_border_identity: "stroked_border_identity", party_slogan_identity_with_party_icon: "party_slogan_identity_with_party_icon",
                        linear_name_and_role_identity: "linear_name_and_role_identity", trapezoidal_identity: "trapezoidal_identity",
                        top_trapezoidal_identity: "top_trapezoidal_identity", bottom_trapezoidal_identity: "bottom_trapezoidal_identity",
                        plain_identity: "plain_identity", premium_cornered_party_icon_gradient_identity: "premium_cornered_party_icon_gradient_identity",
                        basic_transparent_identity: "basic_transparent_identity", basic_no_profile_pic_identity: "basic_no_profile_pic_identity",
                        party_slogan_identity: "party_slogan_identity", plain_identity_with_party_icon: "plain_identity_with_party_icon",

  }
  has_many :item_prices, as: :item
  has_many :circle_frames
  belongs_to :font
  has_one :frame_recommendation

  USER_PHOTO_TYPES = {
    cutout: "cutout",
    with_background: "with_background",
    hero_cutout: "hero_cutout",
    family_cutout: "family_cutout",
  }.freeze

  def self.get_frames_by_ids(frame_ids)
    # get frames in the order of status, premium and then others
    Frame.includes(:font).where(id: frame_ids)
         .order(Arel.sql("CASE WHEN frame_type = 'status' THEN 0 WHEN frame_type = 'premium' THEN 1 ELSE 2 END"))
  end

  # temp hack to get premium frames
  # TODO:: Need to remove this hack once posters 3.0 admin dashboard changes are released
  def self.get_premium_ids
    [1, 2, 10, 3, 4, 5]
  end

  def self.get_neutral_frame
    Frame.where(identifier: :neutral_flat_identity).last
  end

  # use this basic frames for circle frames also
  # If there is a circle layout, send basic frames with the circle layout protocol & is_praja_logo false
  def self.basic_frames
    Frame.includes(:font).where(frame_type: :basic, active: true).order(:id)
  end

  def is_user_free_frame?
    basic?
  end

  def is_user_premium_frame?
    premium? || status? || family_frame_premium? || hero_frame_premium?
  end

  def override_badge_strip_value(has_user_role, has_custom_role_name)
    (has_user_role && has_custom_role_name) ? false : badge_strip
  end

  def user_photo_type
    if frame_type.to_sym == :family_frame_premium
      return USER_PHOTO_TYPES[:family_cutout]
    elsif frame_type.to_sym == :hero_frame_premium
      return USER_PHOTO_TYPES[:hero_cutout]
    end

    if identity_type.to_sym == :flat_user_badge_circle
      return USER_PHOTO_TYPES[:with_background]
    end

    return USER_PHOTO_TYPES[:cutout]
  end
end
