class PosterPhoto < ApplicationRecord
  belongs_to :poster
  belongs_to :photo, polymorphic: true

  validate :presence_of_photos, :presence_of_ring, :validate_photo_orientation, :validate_frame_poster_photo_type,
           :validate_aspect_ratio

  enum photo_orientation: %i[portrait landscape], _suffix: true
  enum leader_photo_ring_color: %i[dark light], _suffix: true

  attribute :blob_data

  def presence_of_photos
    if blob_data.blank?
      poster.errors.add(:poster_photos, :invalid, message: "photos are absent")
    end
  end

  def presence_of_ring
    return if poster.errors.present?

    unless leader_photo_ring_color.present?
      poster.errors.add(:poster_photos, :invalid, message: "leader photo ring color is absent")
    end
  end

  def validate_photo_orientation
    return if poster.errors.present?

    if poster.frame_poster_type? && photo_orientation.blank?
      poster.errors.add(:poster_photos, :invalid, message: "photo orientation is absent")
    end
  end

  def validate_frame_poster_photo_type
    return if poster.errors.present?

    file_name = blob_data.original_filename

    if poster.frame_poster_type? && File.extname(file_name) != '.png'
      poster.errors.add(:poster_photos, :invalid, message: "framed posters should be in png format")
    end
  end

  def validate_aspect_ratio
    return if poster.errors.present?

    aspect_ratio_1 = 630.0 / 818.0
    aspect_ratio_2 = 680.0 / 912.0

    w, h = FastImage.size(blob_data.path)
    unless [aspect_ratio_1, aspect_ratio_2].include? w.to_f / h.to_f
      poster.errors.add(:poster_photos, :invalid, message: "is not in valid aspect ratio")
    end
  end

end
