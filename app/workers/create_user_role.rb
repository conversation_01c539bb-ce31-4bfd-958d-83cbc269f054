class CreateUserRole
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(user_id)
    # commenting it for now as we are not using this worker.

    return if user_id.nil?

    user = User.find user_id

    return if user.nil? || !user.active

    is_eligible, political_party_id, role_id = user.is_eligible_for_user_role

    role = Role.find role_id
    return unless role&.active?

    Honeybadger.context({ user_id_for_creating_user_role: user_id,
                          role_id: role_id,
                          primary_circle_id: political_party_id })

    begin
      if is_eligible
        badge_icon = role.get_badge_icon(political_party_id)

        UserRole.create(user_id: user_id, role_id: role_id,
                        parent_circle_id: political_party_id, primary_role: true, active: true,
                        badge_icon_id: badge_icon.id)

        user.update(marketing_consent: 0)
      end
    rescue ActiveRecord::RecordNotUnique
      #nothing to return
    end
  end
end
