class IndexPostNewV2
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :posts_indexing, retry: 1, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: ->(id, triggered_by_create = false, should_index_hashtag = true) { triggered_by_create ? 10000 : 10 },
      key_suffix: ->(id, triggered_by_create = false, should_index_hashtag = true) { triggered_by_create ? "on_create" : "on_update" }
    },
    threshold: {
      limit: ->(id, triggered_by_create = false, should_index_hashtag = true) { triggered_by_create ? 1_00_000 : 1000 },
      period: 1.minute,
      key_suffix: ->(id, triggered_by_create = false, should_index_hashtag = true) { triggered_by_create ? "on_create" : "on_update" }
    }
  )

  def perform(id, triggered_by_create = false, should_index_hashtag = true)
    return if id.nil?

    Honeybadger.context({ post_id: id })

    post = Post.find_by_id(id)

    return if post.blank? || post.created_at < Constants.no_of_days_post_to_be_indexed.days.ago

    news_worthy_score, can_retry = post.get_news_worthy_score
    IndexPostNewV2.perform_in(1.minute, id) if can_retry
    news_worthy_score = news_worthy_score.to_f

    # Params to index
    hashtag_ids = post.hashtags.map { |ph| ph.hashtag_id }.uniq
    trends_timestamps = post.likes.map { |pl| pl.created_at.to_i * 1000 }
    likes_count = post.likes_count
    comments_count = post.comments_count
    opinions_count = post.opinions_count
    whatsapp_count = post.whatsapp_count
    unique_users_whatsapp_count = post.unique_users_whatsapp_count
    liked_user_ids = post.get_liked_user_ids

    post_circle = post.circle.present? ? post.circle : Circle.find(0)
    tagged_circles = post.circles.where.not(id: 0)
    tagged_village_circle_ids = []
    tagged_mandal_circle_ids = []
    tagged_mla_constituency_circle_ids = []
    tagged_mp_constituency_circle_ids = []
    tagged_district_circle_ids = []
    tagged_state_circle_ids = []
    tagged_interest_circle_ids = []
    is_political_party_tagged = false

    tagged_circles.each do |tc|
      is_political_party_tagged = true if tc.political_party_level? && !is_political_party_tagged
      if tc.location_circle_type?
        tagged_location_circle_id_data = tc.get_location_circle_data
        tagged_village_circle_ids << tagged_location_circle_id_data["village_id"] if tagged_location_circle_id_data["village_id"].present?
        tagged_mandal_circle_ids << tagged_location_circle_id_data["mandal_id"] if tagged_location_circle_id_data["mandal_id"].present?
        tagged_mla_constituency_circle_ids << tagged_location_circle_id_data["mla_constituency_id"] if tagged_location_circle_id_data["mla_constituency_id"].present?
        tagged_mp_constituency_circle_ids << tagged_location_circle_id_data["mp_constituency_id"] if tagged_location_circle_id_data["mp_constituency_id"].present?
        tagged_district_circle_ids << tagged_location_circle_id_data["district_id"] if tagged_location_circle_id_data["district_id"].present?
        tagged_state_circle_ids << tagged_location_circle_id_data["state_id"] if tagged_location_circle_id_data["state_id"].present?
      elsif tc.interest_circle_type?
        tagged_interest_circle_ids << tc.id
      end
    end

    tagged_village_circle_ids.uniq!
    tagged_mandal_circle_ids.uniq!
    tagged_mla_constituency_circle_ids.uniq!
    tagged_mp_constituency_circle_ids.uniq!
    tagged_district_circle_ids.uniq!
    tagged_state_circle_ids.uniq!

    post_user = post.user
    post_user_followers = []
    # don't index followers of users who are owners of a circle and also for praja official account
    is_circle_owner = post_user.is_circle_owner?

    if post_user.id != 41 && !is_circle_owner
      post_user_followers = post_user.followers.pluck(:follower_id)

      post_user_auto_followers = post_user.followers.where(source_of_follow: :auto).pluck(:follower_id)
      post_user_members_tab_followers = post_user.followers.where(source_of_follow: :members_tab).pluck(:follower_id)
    end

    badge_user_role = post_user.get_badge_role
    badge_user_affiliated_circle_id = 0
    if badge_user_role.present?
      badge_user_affiliated_circle_id = badge_user_role.get_badge_user_affiliated_party_circle_id.to_i
    end

    has_poster_photo = false
    if post.taggings.where(tag_id: Constants.post_poster_tag_id).exists?
      has_poster_photo = true
    end

    if !has_poster_photo && post.metadatum.where(key: Constants.get_is_poster_photo_key_for_post).exists?
      has_poster_photo = true
    end

    unless has_poster_photo
      post.photos.each do |photo|
        break if has_poster_photo

        w, h = FastImage.size(photo.url)

        # rounded to 3 decimal places
        photo_aspect_ratio = (w.to_f / h.to_f).round(3)

        # check aspect ratio in between 0.690 and 0.701 (defined based on the posters data)
        if photo_aspect_ratio == 0.67 || (photo_aspect_ratio >= 0.69 && photo_aspect_ratio <= 0.701)
          has_poster_photo = true
        end
      end
    end

    # External urls
    has_external_url = post.link_id.present?

    unless has_external_url
      regex = /(?:(?:https?):\/\/)?[\w\/\-?=%.]+\.[\w\/\-&?=%.]+/
      has_external_url = regex.match(post.content.to_s).present?
    end

    # Post user grade level
    post_user_grade_level = post_user.get_badge_role&.get_readable_grade_level || 1000

    # Post reports count
    reports_count = post.reports.count

    post_photos_count = post.photos.count
    post_videos_count = post.videos.count
    post_first_video_duration = post.videos.first&.duration.to_i
    post_content_length = post.content&.length.to_i

    body = {
      script: {
        source: "" "
                ctx._source.likes_count = params.likes_count;
                ctx._source.comments_count = params.comments_count;
                ctx._source.opinions_count = params.opinions_count;
                ctx._source.whatsapp_count = params.whatsapp_count;
                ctx._source.unique_users_whatsapp_count = params.unique_users_whatsapp_count;
                ctx._source.trends_timestamps = params.trends_timestamps;
                ctx._source.active = params.active;
                ctx._source.badge_user_affiliated_circle_id = params.badge_user_affiliated_circle_id;
                ctx._source.user_village_id = params.user_village_id;
                ctx._source.user_mandal_id = params.user_mandal_id;
                ctx._source.user_mla_constituency_id = params.user_mla_constituency_id;
                ctx._source.user_mp_constituency_id = params.user_mp_constituency_id;
                ctx._source.user_district_id = params.user_district_id;
                ctx._source.user_state_id = params.user_state_id;
                ctx._source.hashtag_ids = params.hashtag_ids;
                ctx._source.is_badge_user = params.is_badge_user;
                ctx._source.liked_user_ids = params.liked_user_ids;
                ctx._source.tagged_circles_ids = params.tagged_circles_ids;
                ctx._source.tagged_interest_circle_ids = params.tagged_interest_circle_ids;
                ctx._source.is_political_party_tagged = params.is_political_party_tagged;
                ctx._source.post_user_followers = params.post_user_followers;
                ctx._source.post_user_auto_followers = params.post_user_auto_followers;
                ctx._source.post_user_members_tab_followers = params.post_user_members_tab_followers;
                ctx._source.tagged_village_circle_ids = params.tagged_village_circle_ids;
                ctx._source.tagged_mandal_circle_ids = params.tagged_mandal_circle_ids;
                ctx._source.tagged_mla_constituency_circle_ids = params.tagged_mla_constituency_circle_ids;
                ctx._source.tagged_mp_constituency_circle_ids = params.tagged_mp_constituency_circle_ids;
                ctx._source.tagged_district_circle_ids = params.tagged_district_circle_ids;
                ctx._source.tagged_state_circle_ids = params.tagged_state_circle_ids;
                ctx._source.has_poster_photo = params.has_poster_photo;
                ctx._source.has_external_url = params.has_external_url;
                ctx._source.post_user_grade_level = params.post_user_grade_level;
                ctx._source.reports_count = params.reports_count;
                ctx._source.post_photos_count = params.post_photos_count;
                ctx._source.post_videos_count = params.post_videos_count;
                ctx._source.post_first_video_duration = params.post_first_video_duration;
                ctx._source.post_content_length = params.post_content_length;
                ctx._source.news_worthy_score = params.news_worthy_score;
            " "",
        params: {
          active: post.active,
          likes_count: likes_count,
          comments_count: comments_count,
          opinions_count: opinions_count,
          whatsapp_count: whatsapp_count,
          unique_users_whatsapp_count: unique_users_whatsapp_count,
          hashtag_ids: hashtag_ids,
          trends_timestamps: trends_timestamps,
          badge_user_affiliated_circle_id: badge_user_affiliated_circle_id,
          user_village_id: post_user.village_id,
          user_mandal_id: post_user.mandal_id,
          user_mla_constituency_id: post_user.mla_constituency_id,
          user_mp_constituency_id: post_user.mp_constituency_id,
          user_district_id: post_user.district_id,
          user_state_id: post_user.state_id,
          is_badge_user: post_user.get_badge_role.present?,
          liked_user_ids: liked_user_ids,
          tagged_circles_ids: tagged_circles.ids,
          tagged_interest_circle_ids: tagged_interest_circle_ids,
          is_political_party_tagged: is_political_party_tagged,
          post_user_followers: post_user_followers,
          post_user_auto_followers: post_user_auto_followers,
          post_user_members_tab_followers: post_user_members_tab_followers,
          tagged_village_circle_ids: tagged_village_circle_ids,
          tagged_mandal_circle_ids: tagged_mandal_circle_ids,
          tagged_mla_constituency_circle_ids: tagged_mla_constituency_circle_ids,
          tagged_mp_constituency_circle_ids: tagged_mp_constituency_circle_ids,
          tagged_district_circle_ids: tagged_district_circle_ids,
          tagged_state_circle_ids: tagged_state_circle_ids,
          has_poster_photo: has_poster_photo,
          has_external_url: has_external_url,
          post_user_grade_level: post_user_grade_level,
          reports_count: reports_count,
          post_photos_count: post_photos_count,
          post_videos_count: post_videos_count,
          post_first_video_duration: post_first_video_duration,
          post_content_length: post_content_length,
          news_worthy_score: news_worthy_score
        }
      },
      upsert: {
        id: id,
        active: post.active,
        likes_count: likes_count,
        comments_count: comments_count,
        opinions_count: opinions_count,
        whatsapp_count: whatsapp_count,
        unique_users_whatsapp_count: unique_users_whatsapp_count,
        hashtag_ids: hashtag_ids,
        trends_timestamps: trends_timestamps,
        parent_post_id: post.parent_post.nil? ? 0 : post.parent_post.id,
        circle_id: post_circle.id,
        circle_level: post_circle.level,
        district_id: post_circle.is_village_level? ? post_circle.parent_circle.parent_circle_id : 0,
        tagged_circles_ids: tagged_circles.ids,
        tagged_interest_circle_ids: tagged_interest_circle_ids,
        is_political_party_tagged: is_political_party_tagged,
        created_at: post.created_at.to_i * 1000,
        user_id: post_user.id,
        user_village_id: post_user.village_id,
        user_mandal_id: post_user.mandal_id,
        user_mla_constituency_id: post_user.mla_constituency_id,
        user_mp_constituency_id: post_user.mp_constituency_id,
        user_district_id: post_user.district_id,
        user_state_id: post_user.state_id,
        is_badge_user: post_user.get_badge_role.present?,
        badge_user_affiliated_circle_id: badge_user_affiliated_circle_id,
        liked_user_ids: liked_user_ids,
        post_user_followers: post_user_followers,
        post_user_auto_followers: post_user_auto_followers,
        post_user_members_tab_followers: post_user_members_tab_followers,
        tagged_village_circle_ids: tagged_village_circle_ids,
        tagged_mandal_circle_ids: tagged_mandal_circle_ids,
        tagged_mla_constituency_circle_ids: tagged_mla_constituency_circle_ids,
        tagged_mp_constituency_circle_ids: tagged_mp_constituency_circle_ids,
        tagged_district_circle_ids: tagged_district_circle_ids,
        tagged_state_circle_ids: tagged_state_circle_ids,
        has_poster_photo: has_poster_photo,
        has_external_url: has_external_url,
        post_user_grade_level: post_user_grade_level,
        reports_count: reports_count,
        post_photos_count: post_photos_count,
        post_videos_count: post_videos_count,
        post_first_video_duration: post_first_video_duration,
        post_content_length: post_content_length,
        news_worthy_score: news_worthy_score
      }
    }
    perform_index(id, post, body)

    hashtag_ids.each { |h_id| IndexHashtag.perform_async(h_id) } if should_index_hashtag
  end

  private

  def perform_index(id, post, body)
    ES_CLIENT.perform_request(
      "POST",
      "#{EsUtil.get_new_posts_index_v2}/_update/#{id}?retry_on_conflict=1" + (post.parent_post.nil? ? "" : "&routing=1"),
      {},
      body
    )
  end
end
