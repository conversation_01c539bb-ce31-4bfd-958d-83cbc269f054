
class FetchPostTags
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(post_id)
    return nil if post_id.blank?

    post = Post.find_by(id: post_id)
    return nil if post.blank?

    Honeybadger.context(post_id: post.id)

    fetched_tags = []

    begin
      # fetch tags from post photos
      post.post_photos.each do |post_photo|
        image_url = post_photo.photo.url
        image_annotator = GOOGLE_CLOUD_VISION.label_detection(image: image_url)
        image_response = image_annotator.responses
        label_response = image_response.first.label_annotations
        label_response_hash = label_response.map(&:to_h)

        # save label json on metadata table entity being post photo
        Metadatum.create(entity: post_photo, key: "photo_label", value: label_response_hash.to_json) if label_response_hash.present?

        label_response_hash.each do |label|
          label_description = label[:description].downcase

          # get label description by comparing with existing tags
          fetched_tags += Constants.cloud_vision_tags.select { |_, values| values.include?(label_description) }.keys
        end
      end

      fetched_tags.uniq!

      # create tagging for the post with pending status
      # TODO: add tagging on individual photos of the post
      begin
        fetched_tags.each do |tag_name|
          tag = Tag.where(identifier: tag_name, tag_type: :post).first
          tagging = Tagging.find_by(taggable: post, tag_id: tag.id)
          if tagging.blank?
            post.taggings.build(taggable: post, tag_id: tag.id, status: :pending)
          end
        end
        post.save!
      rescue
        Honeybadger.notify("Error in create taggings on post: #{post.id} with #{fetched_tags}")
      end
    rescue => e
      Honeybadger.notify("Error #{e} on post: #{post.id}")
    end
  end
end
