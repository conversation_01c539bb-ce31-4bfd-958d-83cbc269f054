# frozen_string_literal: true

class QueueFrameViews
  include Sidekiq::Worker
  sidekiq_options retry: 3, lock: :until_executed, on_conflict: :log

  def perform(user_id, frame_ids_timestamp)
    Honeybadger.context({ user_id: user_id, frame_ids: frame_ids_timestamp })
    return if frame_ids_timestamp.blank? || user_id.blank?

    logger.debug "QueueFrameViews: #{user_id} #{frame_ids_timestamp}"

    $redis.pipelined do |pipeline|
      frame_ids_timestamp.each do |frame_id, timestamp|
        frame_id = frame_id.to_i
        timestamp = timestamp.to_i

        if timestamp == 0
          logger.error "QueueFrameViews: frame_id or timestamp is 0"
          next
        end

        if frame_id == 0
          IncreasePremiumPitchCountWorker.perform_async(user_id)
          next
        end

        # Increase views count on frame_id
        pipeline.hincrby(Constants.frame_views_redis_key, frame_id, 1)

        # log user-date level frame ids
        user_date_frame_views_queue = Constants.user_date_frame_views_queue_redis_key(user_id)
        pipeline.sadd(user_date_frame_views_queue, frame_id)
        pipeline.expireat(user_date_frame_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

        # add to frame views queue
        pipeline.sadd(Constants.frame_views_queue_redis_key,
                      { frame_id: frame_id,
                        user_id: user_id,
                        viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)
      end
    end
  end
end
