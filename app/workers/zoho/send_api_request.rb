# frozen_string_literal: true

module Zoho
  class SendApiRequest
    include Sidekiq::Worker
    include Sidekiq::Throttled::Worker

    sidekiq_options queue: :zoho, retry: 1

    sidekiq_throttle(
      :threshold => { :limit => 5, :period => 1.second }
    )

    # @!method perform(method, url, payload)
    # This method is used to send API request to Zoho
    # @param method [String] HTTP method - POST, PUT, GET, PATCH, DELETE
    # @param url [String] API endpoint
    # @param payload [String] Request payload
    def perform(method, url, payload)
      payload = JSON.parse(payload)
      start_time = Time.zone.now

      ZohoApi.send_api_request(method, url, payload)

      elapsed_time = Time.zone.now - start_time
      Rails.logger.warn("Time taken for Floww send_api_request: #{elapsed_time} seconds")
    end
  end
end
