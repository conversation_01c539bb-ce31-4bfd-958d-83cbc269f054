# frozen_string_literal: true

module Zoho
  class CreateTicket
    include Sidekiq::Worker

    sidekiq_options queue: :zoho

    def perform(user_id, ticket_type, source, data = {})
      user = User.find_by(id: user_id)
      return if user.blank?

      zoho_contact_id = user.get_zoho_contact_id
      zoho_contact_id = user.create_zoho_contact if zoho_contact_id.blank?

      data_str = data.map {|k, v| "#{k.humanize}: #{v.to_s.humanize}"}.join(' || ')

      payload = {
        departmentId: 1087800000000006907,
        contactId: zoho_contact_id,
        subject: "#{ticket_type.humanize} | #{source.humanize}",
        description: "Ticket Type: #{ticket_type.humanize} || Source: #{source.humanize} || #{data_str}",
        phone: user.phone,
        cf: {
          praja_user_id: user.id,
        }
      }

      Zoho::SendApiRequest.perform_async('POST', 'tickets', payload.to_json)
    end
  end
end
