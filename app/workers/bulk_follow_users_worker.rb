class BulkFollowUsersWorker
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 0

  def perform(user_id, following_user_ids, app_version, app_build_number, source_of_follow)
    return if following_user_ids.blank? || user_id.blank?
    return if source_of_follow.present? && UserFollower::FOLLOW_TYPES.exclude?(source_of_follow.to_sym)

    Honeybadger.context({ user_id: user_id, following_user_ids: following_user_ids })

    begin
      user_followers_batch = []

      # create user followers for each contact
      following_user_ids.each do |following_user_id|
        user_follower = UserFollower.new(user_id: following_user_id, follower_id: user_id, source_of_follow: source_of_follow)
        user_followers_batch << user_follower
      end

      # import all the user followers in one go
      UserFollower.import(user_followers_batch, on_duplicate_key_ignore: true, batch_size: 50) if user_followers_batch
                                                                                                    .present?

      # run callbacks based on batch size of 50 which are created in last 10 mins only
      UserFollower.where(user_id: following_user_ids, follower_id: user_id, source_of_follow: source_of_follow).where('created_at > ?', 10.minutes.ago).
        find_each(batch_size: 50) do |user_follower|
        user_follower.send(:update_user_following_and_followers_count)
        user_follower.send(:send_notification)

        EventTracker.perform_async(user_id.to_i, "follow_user",
                                   { "source" => source_of_follow, "follower" => user_follower.follower_id,
                                             "followee" => user_follower.user_id, "$app_version_string" => app_version,
                                             "$app_build_number" => app_build_number })
      end
    rescue Exception => e
      Honeybadger.notify(e)
    end
  end
end
