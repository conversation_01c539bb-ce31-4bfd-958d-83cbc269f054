class SendFollowNotification
  include Sidekiq::Worker
  sidekiq_options queue: :notifications, retry: 0

  def perform(user_follower_id)
    return if user_follower_id.nil?

    uf = nil
    begin
      uf = UserFollower.find user_follower_id
    rescue ActiveRecord::RecordNotFound
      # Ignored
    end

    return if uf.nil? || uf.follower.nil? || uf.user.nil? || !uf.follower.active_status? || !uf.user.active_status?

    notification_title = uf.follower.name + ' మిమ్మల్ని ఫాలో అవుతున్నారు'
    notification_body = uf.follower.name + ' ప్రొఫైల్ ని చూడటానికి క్లిక్ చేయండి'

    notification = Notification.create!(description: notification_title,
                                        notification_type: :follow,
                                        user_id: uf.user_id,
                                        entity_type: 'user',
                                        entity_id: uf.follower_id)

    payload = {
      "title": notification_title,
      "body": notification_body,
      "message_channel": 'follow',
      "data": {
        "path": "/users/#{uf.follower.id}",
        "circle_notification_id": notification.id.to_s
      }
    }

    GarudaNotification.send_user_notification(uf.user_id, payload)

  end
end
