# frozen_string_literal: true

class SendNotificationsForComment
  include Sidekiq::Worker
  sidekiq_options queue: :notifications, retry: 0, backtrace: true

  def perform(post_comment_id)
    post_comment = PostComment.find(post_comment_id)
    return unless post_comment

    post_comment.send(:send_notification_for_post_creator)
    post_comment.send(:send_notification_for_other_commented_users)
  end
end
