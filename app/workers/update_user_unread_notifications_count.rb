# frozen_string_literal: true

class UpdateUserUnreadNotificationsCount
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    return if user_id.blank? || user_id == Constants.praja_account_user_id

    user = User.find_by(id: user_id)
    return if user.blank?

    user.update_column(:unread_notifications_count, Notification.where(user: user, active: true, read: false).count)
  end
end
