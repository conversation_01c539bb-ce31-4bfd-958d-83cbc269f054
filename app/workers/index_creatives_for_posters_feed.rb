class IndexCreativesForPostersFeed
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 1, lock: :until_and_while_executing, on_conflict: :log

  def perform(id)

    # id can be event_xxx or creative_xxx
    # If id is event_xxx, then we need to index all creatives for that event
    # If id is creative_xxx, then we need to index that creative
    # the "event_" is for images like consider it as "event_image_"
    if id.start_with?("event_")
      event_id = id.split("_").last
      event = Event.find(event_id)
      priority = event.priority
      creatives = event.poster_creatives.where(active: true).select(:id, :primary)
      creative_ids = creatives.map(&:id)
      primary_creative_id = creatives.find { |creative| creative.primary }&.id
      circle_ids = event.event_circles.pluck(:circle_id)
      start_date = event.start_time
      end_date = event.end_time
      views_count = PosterCreativeView.where(poster_creative_id: creative_ids).count
      shares_count = PosterShare.where(poster_creative_id: creative_ids).count
      created_at = event.created_at.to_i * 1000
      active = event.active
      format_type = "image"
    elsif id.start_with?("creative_")
      creative_id = id.split("_").last
      creative = PosterCreative.find(creative_id)
      primary_creative_id = creative.id
      creative_ids = [creative.id]
      circle_ids = creative.poster_creative_circles.pluck(:circle_id)
      start_date = creative.start_time
      end_date = creative.end_time
      views_count = PosterCreativeView.where(poster_creative_id: creative_id).count
      shares_count = PosterShare.where(poster_creative_id: creative_id).count
      created_at = creative.created_at.to_i * 1000
      active = creative.active
      format_type = "image"
    elsif id.start_with?("video_creative_")
      creative_id = id.split("_").last
      creative = VideoCreative.find(creative_id)
      primary_creative_id = creative.id
      creative_ids = [creative.id]
      circle_ids = creative.video_creative_circles.pluck(:circle_id)
      start_date = creative.start_time
      end_date = creative.end_time
      views_count = VideoCreativeView.where(video_creative_id: creative_id).count
      shares_count = VideoPosterShare.where(video_creative_id: creative_id).count
      created_at = creative.created_at.to_i * 1000
      active = creative.active
      format_type = "video"
    end
    poster_feed = PosterFeed.new(id: id, created_at: created_at, start_date: start_date, end_date: end_date,
                                 views_count: views_count, shares_count: shares_count, priority: priority,
                                 primary_creative_id: primary_creative_id, creative_ids: creative_ids,
                                 circle_ids: circle_ids, active: active, format_type: format_type)
    poster_feed.reindex
  end
end
