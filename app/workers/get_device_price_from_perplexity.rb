# frozen_string_literal: true

class GetDevicePriceFromPerplexity
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :critical, retry: 1, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: { limit: 3 },
    threshold: { limit: 15, period: 5.second }
  )

  def perform(device_id)
    Honeybadger.context({ device_id: device_id })

    device = Device.find_by(id: device_id)
    return if device.blank?

    api_response = get_device_info_from_perplexity_api(device)

    device.raw_data = api_response
    data = parse_raw_string(api_response['choices'][0]['message']['content'])
    device.name = data.dig('device_marketing_name')
    device.price = data.dig('current_price')
    device.launch_date = data.dig('launch_date')
    device.save!
  end

  private

  def get_device_info_from_perplexity_api(device)
    url = URI("https://api.perplexity.ai/chat/completions")
    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(url)
    request["Content-Type"] = 'application/json'
    request["Authorization"] = "Bearer #{Rails.application.credentials[:perplexity_api_key]}"

    request.body = {
      "model": "llama-3.1-sonar-small-128k-online",
      "messages": [
        {
          "role": "user",
          "content": "What is the Current Indian price of #{device.make} #{device.model} model number mobile? Output should be in a json format, with device model number (input itself, as `device_model_number`), device marketing name (as `device_marketing_name`), current price of the base variant (integer) (as `current_price`), launch date (as `launch_date`) (format YYYY-MM-DD). Do not return any other text or information, except the json."
        }
      ]
    }.to_json

    response = http.request(request)

    JSON.parse(response.body)
  end

  def parse_raw_string(raw_string)
    cleaned_string = raw_string.gsub(/```json\n|```/, '').gsub(/\n/, '')
    JSON.parse(cleaned_string)
  end
end
