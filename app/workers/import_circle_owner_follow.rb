# frozen_string_literal: true
class ImportCircleOwnerFollow
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :low, retry: 0
  sidekiq_throttle(
    :concurrency => { :limit => 1 },
    :threshold => { :limit => 1, :period => 1.second }
  )

  def perform(circle_id, owner_id, created_at_time)
    return if circle_id.blank? || owner_id.blank?

    Honeybadger.context({ circle_id: circle_id, user_id: owner_id })

    UserCircle.where(circle_id: circle_id).where.not(user_id: owner_id).find_in_batches(batch_size: 500).each do |user_circles|
      begin
        user_followers_batch = []

        user_circles.each do |uc|
          user_follower = UserFollower.new(user_id: owner_id, follower_id: uc.user_id, source_of_follow: :auto)
          user_followers_batch << user_follower
        end
        UserFollower.import(user_followers_batch, on_duplicate_key_ignore: true, batch_size: 500) if user_followers_batch.present?

        user_followers_batch.each do |uf|
          uf.run_callbacks(:commit) { true }
        end
        
      rescue Exception => e
        Honeybadger.notify(e)
      end
    end

  end
end
