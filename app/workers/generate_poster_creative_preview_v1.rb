# frozen_string_literal: true

class GeneratePosterCreativePreviewV1
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  sidekiq_options queue: :critical, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: { limit: 5 },
    threshold: { limit: 5, period: 10.seconds }
  )

  def perform(creative_id)
    Honeybadger.context({ creative_id: })
    creative = PosterCreative.find_by_id(creative_id)

    html = generate_html(creative.photo_v3.url)
    uploaded_image = capture_html_as_image(html, '#outer-container')

    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    Metadatum.where(entity: creative, key: 'creative_link_preview_image_url').first_or_create.update(value: uploaded_image['cdn_url'])
  end

  def generate_html(creative_image_url)
    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'poster_creatives', 'poster_creative_gen_preview.html.erb')),
      locals: { creative_image_url: }
    )
  end

end
