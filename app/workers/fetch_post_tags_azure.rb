class FetchPostTagsAzure
  include Sidekiq::Worker

  def perform(post_id)
    return nil if post_id.blank?

    post = Post.find_by(id: post_id)
    return nil if post.blank?

    Honeybadger.context(post_id: post.id)

    has_praja_text = false
    begin
      post.post_photos.each do |post_photo|
        image_url = post_photo.photo.url
        extension = image_url.split('.')[-1]
        next if %w[jpg jpeg png].exclude?(extension)

        url = URI('https://centralindia.api.cognitive.microsoft.com/computervision/imageanalysis:analyze')
        # query params
        url.query = URI.encode_www_form({ 'api-version': '2023-10-01', 'features' => 'read' })
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Post.new(url)
        request['Content-Type'] = 'application/json'
        request['Ocp-Apim-Subscription-Key'] = ENV['AZURE_CLOUD_VISION_SUBSCRIPTION_KEY']
        request.body = { "url": image_url }.to_json
        response = http.request(request)
        response_body = JSON.parse(response.body)

        next unless response_body['readResult'].present? &&
                    response_body['readResult']['blocks'].present? &&
                    response_body['readResult']['blocks'][0].present? &&
                    response_body['readResult']['blocks'][0]['lines'].present?

        texts_result = response_body['readResult']['blocks'][0]['lines']

        texts_result.each do |text_result|
          fetched_text = text_result['text']
          if fetched_text.present? && fetched_text.strip.downcase == 'praja app'
            has_praja_text = true
            break
          end
        end
      end

      if has_praja_text
        begin
          tag = Tag.find_by_id(Constants.post_poster_tag_id)
          post.taggings.build(taggable: post, tag_id: tag.id, status: :approved) if tag.present?
          unless post.metadatum.where(key: Constants.get_is_poster_photo_key_for_post, value: 'true').exists?
            post.metadatum.build(key: Constants.get_is_poster_photo_key_for_post, value: 'true')
            post.save!
          end
        rescue StandardError => e
          Honeybadger.notify('Error in create azure tagging on post', context: { post_id: post.id, error: e })
        end
      end
    rescue StandardError => e
      Honeybadger.notify('Error in fetching text from photo using azure computer vision', context: { post_id: post.id, error: e })
    end
  end
end
