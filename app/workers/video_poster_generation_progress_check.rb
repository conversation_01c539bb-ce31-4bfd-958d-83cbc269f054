# frozen_string_literal: true

class VideoPosterGenerationProgressCheck
    include Sidekiq::Worker
    sidekiq_options queue: :video_posters_generation, retry: 3
  
    def perform(video_poster_id, job_id)
  
      is_video_exists = Aws::S3::Resource.new(
        region: 'ap-south-1',
        credentials: Aws::Credentials.new(
          Rails.application.credentials[:aws_access_key_id],
          Rails.application.credentials[:aws_secret_access_key]
        ),
      ).bucket("circle-app-photos").object("production/video-posters/#{job_id}-video.mp4").exists?
  
      video_poster = UserVideoPoster.find_by(id: video_poster_id)
  
      # update video poster status to completed if video exists and status is not already set to completed
      if is_video_exists && video_poster.may_complete?
        video_poster.complete!
      elsif !is_video_exists && video_poster.may_fail?
        video_poster.fail!("video_not_found")
      end
    end
  end
