# frozen_string_literal: true

class SubCircleCreationUsersJoinHandling
  include Sidekiq::Worker
  sidekiq_options queue: :default, retry: 0

  def perform(sub_circle_id)
    return if sub_circle_id.blank?

    Honeybadger.context({ sub_circle_id: sub_circle_id })

    @sub_circle = SubCircle.find_by(id: sub_circle_id)
    return if @sub_circle.blank?

    filter_role_ids  = SubCircleFilter.where(sub_circle_id: sub_circle_id,filter_key: 'filter_role_id').pluck(:filter_value)
    filter_location_circle_ids  = SubCircleFilter.where(sub_circle_id: sub_circle_id, filter_key: 'filter_location_circle_id').pluck(:filter_value)
    filter_grade_levels  = SubCircleFilter.where(sub_circle_id: sub_circle_id, filter_key: 'filter_grade_level').pluck(:filter_value)
    return if filter_role_ids.blank? && filter_location_circle_ids.blank? && filter_grade_levels.blank?
    @filter_role_ids = filter_role_ids.compact.map(&:to_i)
    @filter_location_circle_ids = filter_location_circle_ids.compact.map(&:to_i)
    @filter_grade_levels = filter_grade_levels.compact.map(&:to_i)
    @parent_circle_id = @sub_circle.parent_circle_id

    if @filter_grade_levels.present? && @filter_role_ids.present?
      user_roles_with_parent_circle_role_ids = UserRole.joins("INNER JOIN roles ON roles.id = user_roles.role_id").where(role_id: @filter_role_ids).where("(user_roles.parent_circle_id = (?) OR roles.parent_circle_id = (?)) AND( user_roles.grade_level IN (?) OR (user_roles.grade_level is NULL AND roles.grade_level IN (?)))", @parent_circle_id, @parent_circle_id, @filter_grade_levels, @filter_grade_levels).distinct.pluck("user_roles.role_id as role_id")

      parent_circle_roles_with_badge_icons = BadgeIconGroup.where(circle_id: @parent_circle_id)
                                                           .joins(badge_icons: :user_roles)
                                                           .joins('LEFT JOIN roles ON roles.id = user_roles.role_id')
                                                           .where('user_roles.role_id IN (?) AND (user_roles.grade_level IN (?) OR (user_roles.grade_level IS NULL AND roles.grade_level IN (?)))',
                                                                  @filter_role_ids, @filter_grade_levels, @filter_grade_levels)
                                                           .distinct
                                                           .pluck('user_roles.role_id')

      @filter_role_ids = user_roles_with_parent_circle_role_ids + parent_circle_roles_with_badge_icons
      @filter_role_ids.compact.uniq!

    elsif @filter_grade_levels.present?

      user_roles_with_parent_circle_role_ids = UserRole.joins("INNER JOIN roles ON roles.id = user_roles.role_id").where("(user_roles.parent_circle_id = (?) OR roles.parent_circle_id = (?)) AND( user_roles.grade_level IN (?) OR (user_roles.grade_level is NULL AND roles.grade_level IN (?)))", @parent_circle_id, @parent_circle_id, @filter_grade_levels, @filter_grade_levels).distinct.pluck("user_roles.role_id as role_id")

      parent_circle_roles_with_badge_icons = BadgeIconGroup.where(circle_id: @parent_circle_id)
                                                           .joins(badge_icons: :user_roles)
                                                           .joins('LEFT JOIN roles ON roles.id = user_roles.role_id')
                                                           .where("(user_roles.grade_level IN (?) OR (user_roles.grade_level IS NULL AND roles.grade_level IN (?)))", @filter_grade_levels, @filter_grade_levels)
                                                           .distinct
                                                           .pluck('user_roles.role_id')
      @filter_role_ids = user_roles_with_parent_circle_role_ids + parent_circle_roles_with_badge_icons
      @filter_role_ids.compact.uniq!

    elsif @filter_role_ids.present?
      parent_circle_id = @sub_circle.parent_circle_id

      user_roles_with_parent_circle_role_ids = UserRole.where(parent_circle_id: parent_circle_id, role_id: @filter_role_ids).distinct.pluck('role_id')
      role_with_parent_circle_role_ids = Role.where(parent_circle_id: parent_circle_id, id: @filter_role_ids).distinct.pluck('id as role_id')
      parent_circle_roles_with_badge_icons = BadgeIconGroup.where(circle_id: parent_circle_id).joins(badge_icons: :user_roles).where("user_roles.role_id IN (#{@filter_role_ids.join(',')})").distinct.pluck('user_roles.role_id as role_id')

      @filter_role_ids = user_roles_with_parent_circle_role_ids + role_with_parent_circle_role_ids + parent_circle_roles_with_badge_icons
      @filter_role_ids.compact.uniq!


    end

    @filter_grade_levels = Role::GRADE_LEVEL.values if @filter_grade_levels.blank?

    if @filter_role_ids.present? && @filter_location_circle_ids.present?

      roles_without_purview = Role.where(id: @filter_role_ids, has_purview: false)
      grouped_circles = Circle.where(id: @filter_location_circle_ids).group_by(&:level)

      village_level_circles = grouped_circles['village'] || []
      mandal_level_circles = grouped_circles['mandal'] || []
      district_level_circles = grouped_circles['district'] || []
      state_level_circles = grouped_circles['state'] || []
      mp_level_circles = grouped_circles['mp_constituency'] || []
      mla_level_circles = grouped_circles['mla_constituency'] || []
      municipality_level_circles = grouped_circles['municipality'] || []
      corporation_level_circles = grouped_circles['corporation'] || []

      all_locations   = village_level_circles + mandal_level_circles + district_level_circles + state_level_circles + mp_level_circles + mla_level_circles + municipality_level_circles + corporation_level_circles

      #CASE 1: Roles without purview
      roles_without_purview.each do |role|
        all_locations.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, false)
        end
      end

      #CASE 2: Roles with purview
      roles_with_purview = Role.where(id: @filter_role_ids, has_purview: true).group_by(&:purview_level)

      village_level_roles = roles_with_purview['village'] || []
      mandal_level_roles = roles_with_purview['mandal'] || []
      district_level_roles = roles_with_purview['district'] || []
      state_level_roles = roles_with_purview['state'] || []
      mp_level_roles = roles_with_purview['mp_constituency'] || []
      mla_level_roles = roles_with_purview['mla_constituency'] || []
      municipality_level_roles = roles_with_purview['municipality'] || []
      corporation_level_roles = roles_with_purview['corporation'] || []

      #CASE 2.1: Village level roles with purview and location filter
      village_level_roles.each do |role|
        all_locations.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.2: Mandal level roles with purview and location filter
      mandal_level_roles.each do |role|
        state_level_circles + district_level_circles + mandal_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.3: District level roles with purview and location filter
      district_level_roles.each do |role|
        state_level_circles + district_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.4: State level roles with purview and location filter
      state_level_roles.each do |role|
        state_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.5: MP level roles with purview and location filter
      mp_level_roles.each do |role|
        state_level_circles + mp_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.6: MLA level roles with purview and location filter
      mla_level_roles.each do |role|
        state_level_circles + district_level_circles + mp_level_circles + mla_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.7: Municipality level roles with purview and location filter
      municipality_level_roles.each do |role|
        state_level_circles + district_level_circles + municipality_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

      #CASE 2.8: Corporation level roles with purview and location filter
      corporation_level_roles.each do |role|
        state_level_circles + district_level_circles + municipality_level_circles + corporation_level_circles.each do |location|
          SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling.perform_async(@sub_circle, role, location, filter_grade_levels, true)
        end
      end

    elsif @filter_role_ids.present?
          joining_users_with_only_filter_roles
    elsif @filter_location_circle_ids.present?
          joining_users_with_only_filter_locations
    end

  end

  def joining_users_with_only_filter_roles


    selected_user_ids_query = User.joins(:user_circles)
                                  .joins(:user_roles)
                                  .joins('LEFT JOIN roles ON roles.id = user_roles.role_id')
                                  .where(user_circles: { circle_id: @parent_circle_id })
                                  .where(user_roles: { role_id: @filter_role_ids })
                                  .where("(user_roles.grade_level IN (?) OR (user_roles.grade_level IS NULL AND roles.grade_level IN (?)))", @filter_grade_levels, @filter_grade_levels)
                                  .select('DISTINCT users.id as user_id')

    selected_user_ids_query.each_slice(500).each do |user_ids_query|
      user_circle_batches = []
      user_ids_query.each do |record|
        begin
          user_circle_batches << UserCircle.new(user_id: record.user_id, circle_id: @sub_circle.id, source_of_join: :sub_circle)
        rescue => e
          Honeybadger.notify(e, context: { user_id: record.user_id, circle_id: @sub_circle.id })
          next
        end
      end
      UserCircle.import(user_circle_batches, on_duplicate_key_ignore: true, batch_size: 500) if user_circle_batches.present?
    end
    @sub_circle.update_members_count
  end

  def joining_users_with_only_filter_locations
    filter_location_ids_as_string = @filter_location_circle_ids.join(',')

    user_ids  = UserCircle.joins(:user).
                            where(circle_id: @sub_circle.parent_circle_id).
                            where("users.village_id IN (#{filter_location_ids_as_string}) OR users.mandal_id IN (#{filter_location_ids_as_string}) OR users.district_id IN (#{filter_location_ids_as_string}) OR users.state_id IN (#{filter_location_ids_as_string}) OR users.mp_constituency_id IN (#{filter_location_ids_as_string}) OR users.mla_constituency_id IN (#{filter_location_ids_as_string})").
                            select('DISTINCT user_circles.user_id')

    user_ids.each_slice(500).each do |user_ids_query|
      user_circle_batches = []
      user_ids_query.each do |record|
        begin
          user_circle_batches << UserCircle.new(user_id: record.user_id, circle_id: @sub_circle.id, source_of_join: :sub_circle)
        rescue => e
          Honeybadger.notify(e, context: { user_id: record.user_id, circle_id: @sub_circle.id })
          next
        end
      end
      UserCircle.import(user_circle_batches, on_duplicate_key_ignore: true, batch_size: 500) if user_circle_batches.present?
    end
    @sub_circle.update_members_count
  end

end
