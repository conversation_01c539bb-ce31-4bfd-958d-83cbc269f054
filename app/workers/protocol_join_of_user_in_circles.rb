class ProtocolJoinOfUserInCircles
  include Sidekiq::Worker
  sidekiq_options queue: :default, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id, circle_ids)
    return if user_id.blank? || circle_ids.blank?
    Honeybadger.context({ user_id: user_id, circle_ids: circle_ids })

    circle_ids = Circle.where(id: circle_ids, active: true).pluck(:id)
    existing_protocol_circle_ids = UserCircle.where(user_id: user_id, source_of_join: :protocol).pluck(:circle_id)

    to_be_destroyed_circle_ids = existing_protocol_circle_ids - circle_ids
    to_be_created_circle_ids = circle_ids - existing_protocol_circle_ids

    to_be_created_circle_ids.each do |circle_id|
      unless UserCircle.where(user_id: user_id, circle_id: circle_id).exists?
        UserCircle.create(user_id: user_id, circle_id: circle_id, source_of_join: :protocol)
      end
    end

    UserCircle.where(user_id: user_id, circle_id: to_be_destroyed_circle_ids, source_of_join: :protocol).destroy_all
  end
end
