# frozen_string_literal: true

class QueueUserPostViews
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 3, lock: :until_executed, on_conflict: :log

  def perform(user_id, post_ids_timestamp)
    Honeybadger.context({ user_id: user_id, post_ids_timestamp: post_ids_timestamp })
    logger.debug "QueueUserPostViews: #{user_id} #{post_ids_timestamp}"

    $redis.pipelined do |pipeline|
      post_ids_timestamp.each do |post_id, timestamp|
        post_id = post_id.to_i
        timestamp = timestamp.to_i

        if post_id == 0 || timestamp == 0
          logger.error "QueueUserPostViews: post_id or timestamp is 0"
          next
        end

        # Increase views count on post id
        pipeline.hincrby(Constants.post_views_redis_key, post_id, 1)

        # log user-date level post ids
        user_date_post_views_queue = Constants.user_date_post_views_queue_redis_key(user_id)
        pipeline.sadd(user_date_post_views_queue, post_id)
        pipeline.expireat(user_date_post_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

        # add to post views queue
        pipeline.sadd(Constants.post_views_queue_redis_key, { post_id: post_id, user_id: user_id }.to_json)

        # log user post view time only if it doesn't exist
        # alternatively using the post_views hash set
        # pipeline.hsetnx("post_views_#{post_id}", user_id.to_s, timestamp)
      end
    end

  end
end
