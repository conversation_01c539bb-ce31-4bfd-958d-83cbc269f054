class RemovePostIdFromTrendingFeedCache
  include Sidekiq::Worker

  sidekiq_options queue: :critical, retry: 0

  def perform(post_id)
    Honeybadger.context(post_id: post_id)
    return if post_id.blank?
    state_ids = Circle.get_state_ids

    # remove post id from cache if it exists
    state_ids.each do |state_id|
      $redis.zrem("#{Constants.trending_feed_cache_key}_#{state_id}", post_id)
    end
  end
end
