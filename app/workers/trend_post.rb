class TrendPost
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    post_id = args[0].to_i
    Honeybadger.context({ post_id: post_id })

    post = Post.find(post_id)

    user_ids = args[1]

    user_ids.each do |user_id|
      begin
        user = User.find(user_id)
        post.do_like(user)
      rescue
        next
      end
    end
  end
end
