# frozen_string_literal: true

class UpdateAppVersionAndAppBuildNumber
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :low, lock: :until_executed, on_conflict: :log
  sidekiq_throttle(
    :concurrency => { :limit => 20 },
    :threshold => { :limit => 500, :period => 1.minute }
  )

  def perform(user_id, app_version_str, app_build_number)
    return if user_id.blank? || app_version_str.blank? || app_build_number.blank?

    user = User.find_by(id: user_id)
    return unless user

    User.where(id: user_id).update(app_version: app_version_str, app_build_number: app_build_number)
  end
end
