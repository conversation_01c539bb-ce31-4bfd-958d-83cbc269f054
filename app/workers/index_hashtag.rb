class IndexHashtag
  include Sidekiq::Worker
  # include Sidekiq::Throttled::Worker

  sidekiq_options queue: :hashtags_indexing, retry: 1, lock: :until_and_while_executing, on_conflict: :log
  # sidekiq_throttle(
  #   # Allow maximum 2 concurrent jobs of this class at a time.
  #   :concurrency => { :limit => 2 },
  #   # Allow maximum 50 jobs being processed within one minute window.
  #   :threshold => { :limit => 50, :period => 1.minute }
  # )

  def perform(id)
    return if id.blank?

    Honeybadger.context({ hashtag_id: id })

    hashtag = Hashtag.find(id)

    trends_es = hashtag.likes
                       .where("created_at > ?", Hashtag::TRENDING_HASHTAGS_DURATION_IN_HOURS.hours.ago)
                       .map { |hl| hl.created_at.to_i * 1000 }

    circle_ids = []
    post_user_ids = []
    posts_trends_es = []
    post_trends_and_hashtag_position_info = []

    posts_es = hashtag.post_hashtags
                      .includes(post: :post_likes)
                      .where("created_at > ?", Hashtag::TRENDING_HASHTAGS_DURATION_IN_HOURS.hours.ago)
                      .map do |ph|
      p = ph.post
      parsed_hashtags = p.content.scan(/(?:\s|^)(?:#(?!(?:\d+|\S+?_|_\S*?)(?:\s|$)))(\S+)(?=\s|$)/i)
      next if parsed_hashtags.blank?

      downcase_parsed_hashtags = parsed_hashtags.map { |ph| ph[0].downcase }

      hashtag_index = downcase_parsed_hashtags.find_index(hashtag.identifier)
      next if hashtag_index.blank?

      posts_trends = p.post_likes.map do |pl|
        pl.created_at.to_i * 1000
      end

      posts_trends_es += posts_trends
      post_trends_and_hashtag_position_info << {
        "post_id" => p.id,
        "trends_time_stamps" => posts_trends,
        "hashtag_position" => hashtag_index
      }
      circle_ids += p.circles.ids
      post_user_ids << p.user_id

      p.created_at.to_i * 1000
    end

    posts_es = posts_es.compact
    circle_ids.uniq!
    post_user_ids.uniq!

    likes_count = hashtag.get_likes_count
    opinions_count = hashtag.get_opinions_count
    whatsapp_count = hashtag.whatsapp_count

    ES_CLIENT.perform_request(
      "POST",
      "#{EsUtil.get_hashtags_index}/_update/#{hashtag.id}?retry_on_conflict=1",
      {},
      {
        "script": {
          "source": """
                  ctx._source.name = params.name;
                  ctx._source.likes_count = params.likes_count;
                  ctx._source.opinions_count = params.opinions_count;
                  ctx._source.whatsapp_count = params.whatsapp_count;
                  ctx._source.trends = params.trends;
                  ctx._source.posts = params.posts;
                  ctx._source.posts_trends = params.posts_trends;
                  ctx._source.active = params.active;
                  ctx._source.circle_ids = params.circle_ids;
                  ctx._source.post_user_ids = params.post_user_ids;
                  ctx._source.post_trends_and_hashtag_position_info = params.post_trends_and_hashtag_position_info;
              """,
          "params": {
            "name": hashtag.name,
            "trends": trends_es,
            "posts": posts_es,
            "posts_trends": posts_trends_es,
            "active": hashtag.active,
            "likes_count": likes_count,
            "opinions_count": opinions_count,
            "whatsapp_count": whatsapp_count,
            "circle_ids": circle_ids,
            "post_user_ids": post_user_ids,
            "post_trends_and_hashtag_position_info": post_trends_and_hashtag_position_info
          }
        },
        "upsert": {
          "id": id,
          "name": hashtag.name,
          "active": hashtag.active,
          "likes_count": likes_count,
          "opinions_count": opinions_count,
          "whatsapp_count": whatsapp_count,
          "trends": trends_es,
          "posts": posts_es,
          "posts_trends": posts_trends_es,
          "circle_ids": circle_ids,
          "post_user_ids": post_user_ids,
          "created_at": hashtag.created_at.to_i * 1000,
          "post_trends_and_hashtag_position_info": post_trends_and_hashtag_position_info
        }
      }
    )
  end
end
