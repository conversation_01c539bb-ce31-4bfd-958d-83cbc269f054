class UploadPhotoToS3UsingUrl
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(image_id, image_url, user_id)
    user = User.find_by(id: user_id)

    return if user.nil? || user.photo_id != image_id

    Honeybadger.context({ image_url: image_url, user_id: user.id })

    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    )

    uri = URI.parse(image_url)
    response = Net::HTTP.get_response(uri)
    if response.code.to_i != 200
      user.update_column(:photo_id, nil)
    else
      image_file = URI.open(image_url)

      file_name = "true_caller_image_#{user.id}_#{Time.now.to_i}_#{SecureRandom.uuid}"
      local_image_path = File.join(Rails.root, 'tmp', file_name)

      IO.copy_stream(image_file, local_image_path)

      hashed_file_name = Digest::MD5.hexdigest(local_image_path)

      s3_object_path = "#{Rails.env}/photos/#{user.id}/#{hashed_file_name}"

      begin
        resource
          .bucket(Rails.application.credentials[:aws_s3_bucket_name])
          .object(s3_object_path)
          .upload_file(local_image_path)

        photo = Photo.new(
          url: "https://a-cdn.thecircleapp.in/#{s3_object_path}",
          user: user,
          service: :aws
        )

        user.photo = photo.save ? photo : nil
        user.save!
      rescue => exception
        Honeybadger.notify(exception)
        # ensure
        # File.delete(local_image_path)
      end
    end
  end
end
