# frozen_string_literal: true

class QueuePosterCreativeViews
  include Sidekiq::Worker
  sidekiq_options retry: 3, lock: :until_executed, on_conflict: :log

  def perform(user_id, poster_creative_ids_timestamp)
    Honeybadger.context({ user_id: user_id, poster_creative_ids: poster_creative_ids_timestamp })
    logger.debug "QueuePosterCreativeViews: #{user_id} #{poster_creative_ids_timestamp}"

    $redis.pipelined do |pipeline|
      poster_creative_ids_timestamp.each do |poster_creative_id, timestamp|
        poster_creative_id = poster_creative_id.to_i
        timestamp = timestamp.to_i

        if poster_creative_id == 0 || timestamp == 0
          logger.error "QueuePosterCreativeViews: poster_creative_id or timestamp is 0"
          next
        end

        # Increase views count on poster_creative_id
        pipeline.hincrby(Constants.poster_creative_views_redis_key, poster_creative_id, 1)

        # log user-date level poster creative ids
        user_date_poster_creative_views_queue = Constants.user_date_poster_creative_views_queue_redis_key(user_id)
        pipeline.sadd(user_date_poster_creative_views_queue, poster_creative_id)
        pipeline.expireat(user_date_poster_creative_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

        # add to poster creative views queue
        pipeline.sadd(Constants.poster_creative_views_queue_redis_key,
                      { poster_creative_id: poster_creative_id,
                        user_id: user_id,
                        viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S")}.to_json)
      end
    end
  end
end
