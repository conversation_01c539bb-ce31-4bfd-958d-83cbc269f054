# frozen_string_literal: true

class CreateCircleFlow
  include Sidekiq::Worker
  sidekiq_options queue: :default, retry: 0

  def perform(user_id, creating_admin_user_id)
    return if user_id.blank?

    user = User.find_by(id: user_id)
    return if user.blank?

    begin
      name = Transliteration::Text.new(user.name)

      photo1 = Photo.create(url: user.poster_photo_with_background.url,
                            user_id: user.id,
                            service: :aws)

      # Create circle
      circle = Circle.new(
        name: name.transliterate('te'),
        name_en: name.transliterate('en'),
        circle_type: :interest,
        level: :political_leader,
        active: true,
        photo: photo1
      )

      photo2 = Photo.create(url: user.poster_photo.url, user_id: user.id, service: :aws)

      # Create circle photos
      circle.circle_photos.build(photo: photo2, photo_type: :poster)

      circle.save!

      # Create owner permission of user on circle
      circle.user_circle_permission_groups.create(permission_group_id: Constants.owner_permission_group_id, user_id: user.id)

      # IGNORE THIS: Create circle relation (Leader2Location) between user's mandal and circle
      # As assigning owner to circle will create Leader2Location relation between user's mandal and circle
      # circle.first_circle_relations.create(second_circle_id: user.mandal_id, relation: :Leader2Location, active: true)

      user_poster_layout = UserPosterLayout.where(entity: user, active: true).first
      admin_user = AdminUser.find_by(id: creating_admin_user_id)

      if user_poster_layout.blank?
        subject = 'No layout for user on create circle flow'
        body = "No User poster layout was found for user: #{user.id} when creating layout of user's circle: #{circle.id}.\nThe circle has been created. Circle layout is failed to create. Please resolve this ASAP."
        context = { user_id: user.id, circle_id: circle.id }

        send_email_and_alert(receiver_email: admin_user.email, subject: subject, body: body, context: context)
        return
      end

      h1_count = user_poster_layout.h1_count
      h2_count = user_poster_layout.h2_count

      layout_type = "layout_#{h1_count}_#{h2_count}".to_sym
      if UserPosterLayout::LAYOUTS.keys.exclude?(layout_type)
        subject = 'Invalid layout type on create circle flow'
        body = "Invalid layout type: #{layout_type} for user: #{user.id} on creating circle layout for user's circle: #{circle.id}.\nThe circle has been created. Circle layout is failed to create. Please resolve this ASAP."
        context = { user_id: user.id, circle_id: circle.id, layout_type: layout_type }
        send_email_and_alert(receiver_email: admin_user.email, subject: subject, body: body, context: context)
        return
      end

      circle_layout_type = "layout_1_#{h1_count + h2_count}".to_sym
      # skip creating circle layout if circle layout type is not present in supported layouts
      if UserPosterLayout::LAYOUTS.keys.include?(circle_layout_type)

        # Create circle layout
        circle_layout = UserPosterLayout.new(entity: circle,
                                             h1_count: 1,
                                             h2_count: h1_count + h2_count,
                                             active: true)

        circle_layout.user_leader_photos.build(photo: user.poster_photo,
                                               header_type: :header_1,
                                               priority: 1,
                                               circle_id: circle.id,
                                               creator: admin_user)

        user_poster_layout.user_leader_photos.each_with_index do |ulp, index|
          header_type = :header_2
          priority = index + 1
          circle_id = ulp.circle_id
          circle_layout.user_leader_photos.build(photo: ulp.photo,
                                                 header_type: header_type,
                                                 priority: priority,
                                                 circle_id: circle_id,
                                                 creator: admin_user)
        end

        circle_layout.save!
      end

      # Enable channel for circle

      circle.update(conversation_type: :channel)

      # Create metadatum of user with circle
      Metadatum.create(entity: circle, key: Constants.user_with_circle_key, value: user.id)
    rescue StandardError => e
      Honeybadger.notify('Error in create circle flow', context: { user_id: user.id, error: e.message })
    end
  end

  private

  def send_email_and_alert(receiver_email:, subject:, body:, context:)
    UserMailer.send_email(
      receiver_email: receiver_email,
      cc_email: '<EMAIL>',
      bcc_email: nil,
      subject: subject,
      body: body
    ).deliver_now

    Honeybadger.notify(subject, context:)
  end
end
