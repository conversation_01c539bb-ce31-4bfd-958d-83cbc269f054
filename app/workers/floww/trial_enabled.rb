# frozen_string_literal: true

module Floww
  class TrialEnabled
    include Sidekiq::Worker

    def perform(user_id)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      FlowwApi.update_layout_completed_activity(@user.id,
                                                release_build_number: @user.release_build_number,
                                                package_to_pitch: @user.should_pitch_yearly_package? ? 'Yearly' : '')
    end
  end
end
