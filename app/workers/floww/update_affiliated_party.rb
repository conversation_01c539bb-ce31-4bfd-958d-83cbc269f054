
module Floww
  class UpdateAffiliatedParty
    include Sidekiq::Worker

    def perform(user_id)
      Rails.logger.warn("Started updating affiliated party ID for user_id: #{user_id}")

      @user = User.find_by_id(user_id)
      return if @user.blank?

      affiliated_party_id = @user.affiliated_party_circle_id.presence || @user.user_exclusive_political_party.presence || 0

      FlowwApi.update_affiliated_party_activity(user_id, affiliated_party_id: affiliated_party_id)

      Rails.logger.warn("Successfully updated affiliated party ID for user_id: #{user_id}, affiliated_party_id: #{affiliated_party_id}")
    end
  end
end
