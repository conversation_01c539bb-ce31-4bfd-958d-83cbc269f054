# frozen_string_literal: true

module Floww
  class SubscribedNoLayout
    include Sidekiq::Worker

    def perform(user_id, lead_type)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      @user.create_floww_contact if floww_contact_id.blank?

      duration_in_months, _ = SubscriptionUtils.get_subscription_duration_in_months_and_amount(@user.id)

      FlowwApi.update_subscribed_no_layout_activity(
        @user.id,
        lead_type:,
        release_build_number: @user.release_build_number,
        subscribed_package: SubscriptionUtils.get_duration_in_words(duration_in_months),
        lead_score: @user.calculate_lead_score
      )
    end
  end
end
