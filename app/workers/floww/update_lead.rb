# frozen_string_literal: true

module Floww
  class UpdateLead
    include Sidekiq::Worker

    def perform(user_id, calculated_lead_score = 0, new_tag = "")
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      fields_hash = {}

      active_user_poster_layout_id = @user.active_user_poster_layout&.id
      if active_user_poster_layout_id
        fields_hash[:poster_layout_url] = "#{Constants.get_admin_host}#{Rails.application.routes.url_helpers.admin_user_poster_layout_path(active_user_poster_layout_id)}"
      end

      premium_pitch = @user.premium_pitch
      if premium_pitch
        fields_hash[:lead_type] = premium_pitch.lead_type
        fields_hash[:lead_sop] = LeadTypeSop.find_by(lead_type: premium_pitch.lead_type)&.sop || nil
        fields_hash[:lead_score] = calculated_lead_score if calculated_lead_score.positive?
      end

      if @user.should_pitch_yearly_package?
        fields_hash[:package_to_pitch] = 'Yearly'
      end

      # Add dashboard links
      fields_hash[:rm_dashboard] = "https://jathara.thecircleapp.in/create-layout?user_id=#{@user.id}"
      fields_hash[:oe_dashboard] = "#{Constants.get_admin_host}/admin/users/#{@user.hashid}/oe_work_flow"
      fields_hash[:boe_dashboard] = "#{Constants.get_admin_host}/admin/users/#{@user.hashid}/boe_work_flow"
      fields_hash[:new_tag] = new_tag if new_tag.present?

      fields_hash.compact!
      return if fields_hash.blank?

      FlowwApi.update_lead_activity(@user.id, fields_hash:)
    end
  end
end
