# frozen_string_literal: true

module Floww
  class SendApiRequest
    include Sidekiq::Worker
    include Sidekiq::Throttled::Worker

    sidekiq_options queue: :floww

    sidekiq_throttle(
      :threshold => { :limit => 10, :period => 1.second }
    )

    def perform(url, payload)
      payload = JSON.parse(payload)
      start_time = Time.zone.now

      FlowwApi.send_api_request(url, payload)

      elapsed_time = Time.zone.now - start_time
      Rails.logger.warn("Time taken for Floww send_api_request: #{elapsed_time} seconds")
    end
  end
end
