# frozen_string_literal: true

module Floww
  class QualifiedPitch
    include Sidekiq::Worker

    def perform(user_id)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      trial_start_date, trial_duration_in_days = Metadatum.get_user_trial_start_date_and_duration(@user.id)
      trial_remaining_days = trial_duration_in_days - (Date.today - trial_start_date.to_date).to_i

      premium_poster_usage = @user.premium_poster_usage_count_after_trial_enabled

      FlowwApi.update_qualified_pitch_activity(user_id, trial_remaining_days: trial_remaining_days, premium_poster_usage: premium_poster_usage) if floww_contact_id.present?
    end
  end
end
