# frozen_string_literal: true

module Floww
  class LayoutRejected
    include Sidekiq::Worker

    def perform(user_id)
      @user = User.find_by(id: user_id)
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      upl = @user.get_user_poster_layout_including_inactive
      rejection_reason = Metadatum.find_by(entity: upl, key: Constants.layout_rejection_reason)&.value

      FlowwApi.update_layout_reviewed_activity(@user.id, is_approved: false, rejection_reason:)
    end
  end
end
