# frozen_string_literal: true

module Floww
  class TrialLowUsage
    include Sidekiq::Worker

    def perform(user_id)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      premium_poster_shares = @user.premium_poster_usage_count_after_trial_enabled

      trial_extended = @user.is_trial_extension_user?
      trial_start_date, trial_duration_in_days = Metadatum.get_user_trial_start_date_and_duration(@user.id)
      trial_period_left = trial_duration_in_days - (Date.today - trial_start_date.to_date).to_i

      FlowwApi.update_trial_low_usage_activity(@user.id, premium_poster_shares:, trial_extended:, trial_period_left:)
    end
  end
end
