# frozen_string_literal: true

module Floww
  class Payment
    include Sidekiq::Worker

    def perform(user_id)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      return if floww_contact_id.blank?

      subscription_duration_in_months, _ = SubscriptionUtils.get_subscription_duration_in_months_and_amount(@user.id)

      FlowwApi.update_mandate_subscribed_activity(
        @user.id,
        subscribed_package: SubscriptionUtils.get_duration_in_words(subscription_duration_in_months)
      )
    end
  end
end
