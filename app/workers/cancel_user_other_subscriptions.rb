# frozen_string_literal: true

class CancelUserOtherSubscriptions
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(subscription_id)
    s = Subscription.find(subscription_id)
    return if s.blank?

    Subscription.where(user_id: s.user_id).where.not(id: subscription_id).each do |subscription|
      if subscription.may_cancel?
        subscription.cancel('another subscription activated')
      elsif subscription.may_close?
        subscription.close!
      end
    end
  end
end
