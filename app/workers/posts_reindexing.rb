# frozen_string_literal: true
require 'sidekiq-scheduler'

class PostsReindexing
  include Sidekiq::Worker

  def perform
    logger.info('Indexing posts worker running')

    date = (Time.zone.today - 4.days).to_datetime

    Post.where('created_at > ?', date).find_in_batches(batch_size: 500) do |group|
      group.each do |p|
        IndexPostNewV2.perform_async(p.id)
      end
      sleep(1)
    end
  end
end
