# frozen_string_literal: true

class UpdateCircleMembersCount
  # include Sidekiq::Worker
  # include Sidekiq::Throttled::Worker
  #
  # sidekiq_options queue: :low, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  # sidekiq_throttle(
  #   :threshold => { :limit => 100, :period => 1.second }
  # )
  #
  # def perform(circle_id)
  #   circle = Circle.find_by(id: circle_id)
  #   return unless circle
  #
  #   circle.update_members_count
  # end
end
