require 'open-uri'
require 'htmlcsstoimage'

class GenerateInviteCard
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    return
    return unless user_id.present?

    Honeybadger.context({ user_id: user_id })

    @user = User.find user_id

    user_role = @user.get_badge_role

    return unless user_role.present?

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/invite_card.html.erb')),
      locals: {
        user: {
          name: @user.name,
          photo_url: @user.photo&.url,
          badge: {
            description: user_role.get_description,
            icon_url: user_role.get_badge_icon_url,
            badge_banner: user_role.get_badge_color,
            badge_ring: user_role.get_badge_ring
          }
        }
      }
    )

    hcti_client = HTMLCSSToImage.new(
      user_id: Rails.application.credentials[:hcti_user_id],
      api_key: Rails.application.credentials[:hcti_api_key]
    )
    hcti_image = hcti_client.create_image(html_content, google_fonts: 'Noto Serif Telugu')

    if hcti_image.present?
      image_file = URI.open(hcti_image.url)
      if image_file.present?
        upload_to_aws(image_file)
        image_file.close
      end
    end
  end

  def upload_to_aws(data)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ),
    )

    s3_object_path = Rails.env + "/invite-cards/user-#{@user.phone}-#{Time.zone.now.to_i}.png"

    begin
      obj = resource.bucket(Rails.application.credentials[:aws_s3_bucket_name]).object(s3_object_path).put(body: data)
      @user.invite_card = 'https://cdn.thecircleapp.in/' + s3_object_path
      @user.save!
    rescue => exception
      return nil
    end
  end
end

# image = hcti_client.create_image("<div>Hello, world</div>",
#                             css: "div { background-image: url('https://image.shutterstock.com/image-vector/background-multicolor-gradient-colourful-color-260nw-1711154779.jpg'); }",
#                             google_fonts: "Roboto")
#
# image
# => #<HTMLCSSToImage::ApiResponse url="https://hcti.io/v1/image/254b444c-dd82-4cc1-94ef-aa4b3a6870a6", id="254b444c-dd82-4cc1-94ef-aa4b3a6870a6">
#   image.url
# => "https://hcti.io/v1/image/254b444c-dd82-4cc1-94ef-aa4b3a6870a6"

