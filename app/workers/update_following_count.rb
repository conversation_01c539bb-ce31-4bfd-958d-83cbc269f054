class UpdateFollowingCount
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :low, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    :threshold => { :limit => 100, :period => 1.second }
  )
  def perform(user_id)
    user = User.find(user_id)
    total_following_count = UserFollower.active_following.where(follower_id: user_id, active: true).count
    user.update(total_following_count: total_following_count)
  end
end
