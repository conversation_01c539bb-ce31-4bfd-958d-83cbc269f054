# frozen_string_literal: true

class VideoPostersGenerationTrigger
    include Sidekiq::Worker
    sidekiq_options queue: :video_posters_generation, retry: 3
  
    def perform(video_poster_id)
      video_poster = UserVideoPoster.find(video_poster_id)
      return if video_poster.blank?
  
      return unless video_poster.may_process?
      Aws::Lambda::Client.new(
        region: 'ap-south-1',
        credentials: Aws::Credentials.new(
          Rails.application.credentials[:aws_access_key_id],
          Rails.application.credentials[:aws_secret_access_key],
        ),
      ).invoke(
        function_name: 'arn:aws:lambda:ap-south-1:666527360739:function:video-poster-generation-VideoPosterGeneration-GbTiDfKi7i6y',
        invocation_type: 'Event',
        payload: video_poster.lambda_payload.to_json,
      )
      video_poster.process!
      VideoPosterGenerationProgressCheck.perform_at(Time.zone.now + 5.minutes, video_poster_id, video_poster.job_id)
    end
  end
