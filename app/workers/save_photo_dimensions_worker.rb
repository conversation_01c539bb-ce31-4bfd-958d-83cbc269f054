class SavePhotoDimensionsWorker
  include Sidekiq::Worker
  sidekiq_options retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(photo_id)
    return if photo_id.nil?

    Honeybadger.context({ photo_id: photo_id })

    photo = Photo.find(photo_id)

    return if photo.nil? || photo.active == false

    photo.set_aspect_ratio if photo.width.to_i <= 0 || photo.height.to_i <= 0
    photo.dominant_dark_color = '#000000'
    # photo.set_dominant_dark_color if photo.dominant_dark_color == '#000000'
    photo.set_explicit_labels unless photo.explicit
    photo.save!
  end
end
