# frozen_string_literal: true

class SendPostMediaToMediaService
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 1

  def perform(post_id)
    return if post_id.blank?

    Honeybadger.context({ post_id: post_id })

    Rails.logger.info("ROR to Media service")

    post = Post.find_by(id: post_id)
    return if post.blank?

    media_array = []

    if post.photo_ids.present?
      url = nil
      photos = []
      post.photos.each do |photo|
        if photo.path.present?
          photo_obj = {}
          photo_obj[:path] = photo.path
          photo_obj[:metadata] = { photo_id: photo.id }
          photos << photo_obj

          url = photo.get_media_service_callback_url if url.blank?
        end
      end

      if photos.present?
        body = { "user_id": post.user_id, "photos": photos }
        media_array << [url, body]
      end
    end

    if post.video_ids.present?
      url = nil
      videos = []
      post.videos.each do |video|
        if video.path.present?
          video_obj = {}
          video_obj[:path] = video.path
          video_obj[:metadata] = { video_id: video.id }
          videos << video_obj

          url = video.get_media_service_callback_url if url.blank?
        end
      end

      if videos.present?
        body = { "user_id": post.user_id, "videos": videos }
        media_array << [url, body]
      end
    end

    media_array.each do |media|
      url = media[0]
      body = media[1]

      SendDataToMediaService.perform_async(url, body)
    end
  end
end
