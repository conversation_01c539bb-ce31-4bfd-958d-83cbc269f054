# frozen_string_literal: true

# First we need to run GeneratePosterCampaignImage worker to generate the poster image
# Then only we can run Send<PERSON>osterCampaignWati worker to send the poster image to the user via Wati
class SendPosterCampaignWati
  include Sidekiq::Worker
  sidekiq_options queue: :campaign_poster_generation, retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id, campaign_name, template_name)
    return if user_id.blank? ||  campaign_name.blank?
    user = User.find_by_id(user_id)
    return if user.blank?

    user_name = user.name
    phone = user.phone

    # get the poster url from user metadata
    metadata_key = Constants.poster_image_url(campaign_name)

    user_metadata = UserMetadatum.where(user_id: user_id, key: metadata_key)
    poster_url = user_metadata.first&.value

    return if poster_url.blank?

    hash_variables_list = [
      'number' => phone.to_s,
      'name' => user_name,
      'poster_url' => poster_url
    ]

    WatiIntegration.perform_async(template_name, hash_variables_list)
  end
end
