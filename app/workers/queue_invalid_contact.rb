# frozen_string_literal: true

class QueueInvalidContact
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 1, lock: :until_and_while_executing, on_conflict: :log

  def perform(invalid_contact_p, user_id)
    Honeybadger.context({ user_id: user_id, contact: invalid_contact_p })

    user = User.find(user_id)
    return if user.internal?

    invalid_contact = JSON.parse(invalid_contact_p)

    redis_key = "invalid_contacts_queue_v1:#{bid}"
    $redis.sadd(redis_key,
                { invalid_contact: invalid_contact, user_id: user_id }.to_json)
    $redis.expire(redis_key, 2.days.to_i, nx: true)
  end
end
