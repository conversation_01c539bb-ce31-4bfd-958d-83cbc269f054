class SendContactNotification
  include Sidekiq::Worker
  sidekiq_options queue: :notifications, retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    user = User.find_by(id: user_id)
    Honeybadger.context({ user_id: user_id })
    return if user.nil?

    # last login time of user
    last_login_time = user.get_last_login_time
    return if last_login_time.blank?
    # return if last_login_time less than 5 hours
    if Time.zone.now < last_login_time + 5.hours
      SendContactNotification.perform_at(last_login_time + 5.hours, user_id)
      return
    end

    last_contacts_uploaded_at = user.get_last_contacts_uploaded_time
    return if last_contacts_uploaded_at.blank?

    if last_login_time > last_contacts_uploaded_at
      # set redis key to show contacts as false because user has not uploaded contacts after last login
      user.set_show_contacts_screen_value_to_redis(false, 30.days.to_i)
      return
    end

    has_min_contacts_for_user = user.is_user_has_minimum_contacts?
    unless has_min_contacts_for_user
      # set redis key to show contacts as false because user has not have min contacts
      user.set_show_contacts_screen_value_to_redis(false, 30.days.to_i)
      return
    end

    # set redis key to show contacts as true because all conditions are met
    user.set_show_contacts_screen_value_to_redis(true, 90.days.to_i)
    # send notification to user
    send_notification(user_id)
  end

  def send_notification(user_id)
    notification_title = 'మీ పరిచయస్తులను ఫాలో అవ్వండి'
    notification_body = 'ఇక్కడ క్లిక్ చేసి కాంటాక్ట్స్ చూడండి'

    payload = {
      "title": notification_title,
      "body": notification_body,
      "message_channel": "contact_notification",
      "data": {
        "path": "/contacts",
      }
    }

    # track mixpanel event
    EventTracker.perform_async(user_id.to_i, "contact_notification_sent", { "user_id" => user_id })
    GarudaNotification.send_user_notification(user_id, payload)
  end
end
