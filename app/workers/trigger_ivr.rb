class TriggerIvr
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_throttle(
    threshold: { limit: 50 , period: 1.minute }
  )

  def perform(user_id)
    # Find & validate the user & user's phone
    user = User.find_by(id: user_id)
    return if user.nil?
    phone = user.phone
    return if phone.blank?

    # Send initiated event
    EventTracker.perform_async(user_id, "charge_ivr_initiated", {})

    # Generating TwiML
    response = Twilio::TwiML::VoiceResponse.new
    response.gather(num_digits: 1, action: "#{Constants.get_api_host}/users/#{user.hashid}/charge-ivr-menu-selection", timeout: 15) do |g|
      g.play(url: "https://thankful-join-9973.twil.io/m-play-1.wav")
      g.play(url: "https://thankful-join-9973.twil.io/m-play-2.wav")
    end
    twiml = response.to_s
    
    # Trigger call
    client = Twilio::REST::Client.new(
      Rails.application.credentials[:twilio_account_sid],
      Rails.application.credentials[:twilio_auth_token]
    )
    client.calls.create(
      method: 'GET',
      status_callback: "#{Constants.get_api_host}/users/#{user.hashid}/charge-ivr-call-status",
      status_callback_event: %w[initiated ringing answered completed],
      status_callback_method: 'POST',
      to: "+91#{phone}",
      from: '+***********',
      twiml: twiml
    )
  end
end
