# frozen_string_literal: true

class SendOtpJob
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 1, lock: :until_executed, on_conflict: :log

  def perform(user_id, phone, code, otp_count, app_hash)
    return unless Rails.env.production?

    Honeybadger.context({ user_id: user_id, otp_count: otp_count })

    @code = code
    @phone = phone
    @app_hash = app_hash

    case otp_count
    when 3
      send_voice_otp
    when 2
      send_2factor_otp
    else
      # send_textlocal_otp
      send_2factor_otp
    end
  end

  private
  def send_textlocal_otp
    sender_id = 'PRAJAA'
    requested_url = 'https://api.textlocal.in/send/?'
    uri = URI.parse(requested_url)

    message = "<#> #{@code} is your Praja app login / signup OTP.\n#{@app_hash}"

    Net::HTTP.post_form(
      uri,
      'apikey' => Rails.application.credentials[:text_local_api_key],
      'numbers' => "91#{@phone}",
      'message' => message,
      'sender' => sender_id,
      'test' => Rails.env.development?
    )
  end

  def send_2factor_otp
    sender_id = 'PRAJAA'
    requested_url = "https://2factor.in/API/V1/#{Rails.application.credentials[:two_factor_api_key]}/ADDON_SERVICES/SEND/TSMS"
    uri = URI.parse(requested_url)

    Net::HTTP.post_form(
      uri,
      'From' => sender_id,
      'To' => "#{@phone}",
      'TemplateName' => 'Praja OTP (new)',
      'VAR1' => @code.to_s,
      'VAR2' => @app_hash
    )
  end

  def send_voice_otp
    otp_str = CGI.escape(@code.to_s.chars.join(' '))

    ## TWILIO
    client = Twilio::REST::Client.new(
      Rails.application.credentials[:twilio_account_sid],
      Rails.application.credentials[:twilio_auth_token])

    client.calls.create(
      to: "+91#{@phone}",
      from: '+***********',
      url: "https://handler.twilio.com/twiml/EH737971c03cb20b25648eb8a9c2cb8a0c?Otp=#{otp_str}")
  end
end
