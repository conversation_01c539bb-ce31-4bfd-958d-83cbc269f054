# frozen_string_literal: true

class QueueValidContact
  include Sidekiq::Worker
  sidekiq_options queue: :contacts, retry: 1, lock: :until_and_while_executing, on_conflict: :log

  def perform(contact_p, user_id)
    Honeybadger.context({ user_id: user_id, contact: contact_p })

    user = User.find(user_id)
    return if user.internal?

    valid_contact = JSON.parse(contact_p)

    found_user = User.where(phone: valid_contact["phone"]).active.last
    valid_contact['phone_user_id'] = found_user&.id

    redis_key = "valid_contacts_queue_v1:#{bid}"
    $redis.sadd(redis_key,
                { valid_contact: valid_contact, user_id: user_id }.to_json)
    $redis.expire(redis_key, 2.days.to_i, nx: true)
  end
end
