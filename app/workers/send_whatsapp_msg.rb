class SendWhatsappMsg
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  sidekiq_options queue: :wati, retry: 1
  sidekiq_throttle(
    concurrency: {
      limit: 5,
    },
    threshold: {
      limit: 30,
      period: 10.seconds,
    }
  )

  def perform(template_name, broadcast_name, hash_variable)
    wati_end_point = Constants.get_send_message_wati_url

    number = hash_variable["number"]
    raise "Number is not 10 digits" if number.length != 10

    url = URI("#{wati_end_point}?whatsappNumber=91#{number}")
    raise "Unable to form URL" if url.host.nil?

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(url)
    request["Content-Type"] = 'application/json-patch+json'
    request["Authorization"] = Rails.application.credentials[:wati_api_key]

    resulting_body_parameters = {}
    resulting_body_parameters["broadcast_name"] = broadcast_name
    resulting_body_parameters["template_name"] = template_name
    internal_list = []
    hash_variable.each do |key, value|
      internal_list.append({
                             "name": key,
                             "value": value
                           })
    end

    resulting_body_parameters["parameters"] = internal_list
    request.body = resulting_body_parameters.to_json

    response = http.request(request)
    body = JSON.parse(response.body)

    if response.code != "200" || (body["result"] != true && body["validWhatsAppNumber"] == true)
      Honeybadger.notify("WATI API call failed:", context: { response_body: body, response_code: response.code })
    end
  end
end
