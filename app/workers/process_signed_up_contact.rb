class ProcessSignedUpContact
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 1

  def perform(signed_up_user_id)
    Honeybadger.context({ user_id: signed_up_user_id })

    user = User.find(signed_up_user_id)
    return if user.internal?

    found_users = UserInvite.where(phone: user.phone, active: true)
    begin
      found_users.each do |found_user|
        referral_user = User.find(found_user.user_id)
        UserContactSuggestion.create(name: found_user['name'],
                                     phone: found_user['phone'],
                                     phone_user_id: user.id,
                                     user: referral_user)
      end
    rescue ActiveRecord::RecordNotUnique
      return
    end
  end
end
