# frozen_string_literal: true

class SubCircleWithRoleWithGradeLevelsAndLocationCombinationUsersJoinHandling
  include Sidekiq::Worker
  sidekiq_options queue: :default, retry: 0

  def role_with_purview_with_grade_levels_and_location_filter
    child_circles_with_role_purview_level_of_a_given_location = get_all_circles_with_respect_to_location_based_on_role_purview_level

    return if child_circles_with_role_purview_level_of_a_given_location.blank?

    child_circles_with_role_purview_level_of_a_given_location.each_slice(500).each do |location_ids_slice|
      user_ids = UserRole.joins(user: :user_circles)
                         .joins('INNER JOIN roles ON roles.id = role_id')
                         .joins('LEFT JOIN badge_icons ON badge_icons.id = badge_icon_id')
                         .joins('LEFT JOIN badge_icon_groups ON badge_icon_groups.id = badge_icons.badge_icon_group_id')
                         .where("role_id = :role_id AND
                          user_circles.circle_id = :parent_circle_id AND
                          (user_roles.grade_level IN (:grade_levels) OR (user_roles.grade_level IS NULL AND roles.grade_level IN (:grade_levels))) AND
                         (badge_icon_groups.circle_id = :parent_circle_id OR roles.parent_circle_id = :parent_circle_id OR user_roles.parent_circle_id = :parent_circle_id) AND
                         user_roles.purview_circle_id IN (:location_ids)",
                                { role_id: @role.id, parent_circle_id: @parent_circle_id, location_ids: location_ids_slice, grade_levels: @grade_levels })
                         .distinct
                         .select(:user_id)

      user_ids.each_slice(500).each do |user_id_objects|
        user_circle_batches = []
        user_id_objects.each do |user_id_object|
          user_id = user_id_object.user_id
          begin
            user_circle_batches << UserCircle.new(user_id: user_id, circle_id: @sub_circle.id, source_of_join: :sub_circle)
          rescue => e
            Honeybadger.notify(e, context: { user_id: user_id, circle_id: @sub_circle.id })
            next
          end
        end
        UserCircle.import(user_circle_batches, on_duplicate_key_ignore: true, batch_size: 500) if user_circle_batches.present?
      end
    end
    @sub_circle.update_members_count
  end

  def get_all_circles_with_respect_to_location_based_on_role_purview_level
    role_purview_level = @role.purview_level.to_sym
    case role_purview_level
    when :village, :municipality, :corporation, :mandal, :district, :mla_constituency, :mp_constituency, :state
      if role_purview_level == @location.level.to_sym
        [@location.id]
      else
        @location.get_all_child_circle_ids_with_given_level(role_purview_level)
      end
    end
  end

  def role_without_purview_with_grade_levels_and_location_filter

    selected_users_ids_query =  UserRole.joins(user: :user_circles)
                                  .joins('INNER JOIN roles ON roles.id = user_roles.role_id')
                                  .joins('LEFT JOIN badge_icons ON badge_icons.id = user_roles.badge_icon_id')
                                  .joins('LEFT JOIN badge_icon_groups ON badge_icon_groups.id = badge_icons.badge_icon_group_id')
                                  .where("roles.has_purview = false AND user_roles.role_id = :role_id AND
                                          user_circles.circle_id = :parent_circle_id AND
                                           (user_roles.grade_level IN (:grade_levels) OR (user_roles.grade_level IS NULL AND roles.grade_level IN (:grade_levels))) AND
                                          (roles.parent_circle_id = :parent_circle_id OR badge_icon_groups.circle_id = :parent_circle_id OR user_roles.parent_circle_id = :parent_circle_id) AND
                                          (users.village_id = :location_id OR users.mandal_id = :location_id OR users.district_id = :location_id OR
                                          users.state_id = :location_id OR users.mp_constituency_id = :location_id OR users.mla_constituency_id = :location_id)",
                                         { role_id: @role.id, parent_circle_id: @parent_circle_id, location_id: @location.id, grade_levels: @grade_levels })
                                  .distinct
                                  .select(:user_id)

    selected_users_ids_query.each_slice(500).each do |user_ids_query_objects|
      user_circle_batches = []
      user_ids_query_objects.each do |user_id_object|
        begin
          user_circle_batches << UserCircle.new(user_id: user_id_object.user_id, circle_id: @sub_circle.id, source_of_join: :sub_circle)
        rescue => e
          Honeybadger.notify(e, context: { user_id: user_id_object.user_id, circle_id: @sub_circle.id, role_id: @role.id})
          next
        end
      end
      UserCircle.import(user_circle_batches, on_duplicate_key_ignore: true, batch_size: 500) if user_circle_batches.present?
    end
    @sub_circle.update_members_count
  end

  def perform(sub_circle, role, location, grade_levels, is_role_has_purview = false)
    @grade_levels = grade_levels.blank? ? Role::GRADE_LEVEL.values : grade_levels
    @sub_circle = sub_circle
    @role = role
    @location = location

    # Example @role will be the @role of top fan
    return if @sub_circle.blank? || @role.blank? || @location.blank? || @grade_levels.blank?
    Honeybadger.context({ sub_circle_id: @sub_circle.id, role_id: @role.id, location_id: @location.id })

    @parent_circle_id = @sub_circle.parent_circle_id

    if is_role_has_purview
      role_with_purview_with_grade_levels_and_location_filter
    else
      role_without_purview_with_grade_levels_and_location_filter
    end
  end

end
