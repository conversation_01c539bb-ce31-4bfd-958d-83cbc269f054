class GenerateKycCreativeImage
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  sidekiq_options queue: :default, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 10,
    },
    threshold: {
      limit: 30,
      period: 5.second,
    }
  )

  def perform(leader_circle_id)
    Honeybadger.context({ circle_id: leader_circle_id })
    leader_circle = Circle.find_by_id(leader_circle_id)
    return if leader_circle.blank?

    # Inactivate all existing contestant creatives
    PosterCreative
      .joins(:poster_creative_circles)
      .where(creative_kind: :contestant, active: true)
      .where(poster_creative_circles: { circle_id: leader_circle_id }).update_all(active: false)

    party_circle = CirclesRelation.find_by(first_circle_id: leader_circle_id, relation: "Leader2Party")&.second_circle

    leader_constituency_relations = CirclesRelation.where(second_circle_id: leader_circle_id, relation: [:MLA_Contestant, :MP_Contestant], active: true)

    leader_constituency_relations.each do |leader_constituency_relation|
      generate_poster_creative(leader_circle: leader_circle, relation: leader_constituency_relation, party_circle: party_circle)
    end

  end

  private

  def generate_html(name, avatar_url, badge_icon_url, party_id, position, constituency)
    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'poster_creatives', 'kyc_creative_image.html.erb')),
      locals: { name: name, avatar_url: avatar_url, badge_icon_url: badge_icon_url, party_id: party_id, position: position, constituency: constituency }
    )
  end

  def generate_poster_creative(leader_circle:, relation:, party_circle:)
    constituency_circle = relation&.first_circle
    return if constituency_circle.blank?
    avatar_url = leader_circle&.photo&.compressed_url(size: 320)
    return if avatar_url.blank?
    badge_icon_url = BadgeIcon.joins(:badge_icon_group).where("badge_icons.color = 'white'
      AND badge_icon_groups.circle_id = #{party_circle&.id}").first&.admin_medium&.url if party_circle.present?
    name = leader_circle.name
    constituency = constituency_circle.name
    relation_string = relation[:relation] == "MLA_Contestant" ? "MLA" : "MP"
    position = "#{party_circle&.short_name || "Independent"} #{relation_string}"
    html = generate_html(name, avatar_url, badge_icon_url, (party_circle&.id || 0), position, constituency)
    uploaded_image = capture_html_as_image(html, '#outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    creator = User.find_by_id(Constants.praja_account_user_id)

    kind = :contestant

    photo_data = OpenStruct.new(uploaded_image)

    photo = Photo.new(ms_data: photo_data, user: creator, service: photo_data.service)
    photo.set_photo_data
    photo.save!

    poster_creative = PosterCreative.new(creative_kind: kind,
                                         photo_v3: photo,
                                         paid: false,
                                         primary: false,
                                         h1_leader_photo_ring_type: "dark",
                                         h2_leader_photo_ring_type: "dark",
                                         start_time: Time.zone.now,
                                         end_time: Time.zone.parse('13-06-2024').end_of_day,
                                         active: true,
                                         creator: creator)

    poster_creative_circle_ids = [leader_circle.id, constituency_circle.id]
    poster_creative_circle_ids.each do |circle_id|
      poster_creative.poster_creative_circles.build(poster_creative_id: poster_creative.id, circle_id: circle_id)
    end
    poster_creative.save!
  end
end
