# frozen_string_literal: true

class GenerateCongratulationsCard
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 1

  def perform(circles_relation_id, constituency_circle_id, mla_circle_id)
    # constituency = Circle.find_by(id: constituency_circle_id)
    # mla = Circle.find_by(id: mla_circle_id)
    # return if constituency.blank? || mla.blank? || mla.photo.blank?
    #
    # mla_party_photo_url = "https://cdn.thecircleapp.in/production/admin-media/10/47af5d09faadbf4420098e03439b90f1.png"
    # party_badge_url = nil
    #
    # relation = CirclesRelation.where(first_circle_id: mla_circle_id, relation: "Leader2Party").first
    # if relation.present?
    #   mla_party = relation.second_circle
    #   if mla_party.present?
    #     mla_party_photo_url = mla_party.photo.url if mla_party.photo.present?
    #     party_badge_url = BadgeIcon.joins(:badge_icon_group).where(color: :GOLD, badge_icon_groups: { circle_id: mla_party.id }).first&.admin_medium&.url
    #   end
    # end
    #
    # data = {
    #   party_logo_url: mla_party_photo_url,
    #   candidate_image_url: mla.photo.url,
    #   party_badge_url: party_badge_url,
    #   constituency_name: constituency.name,
    #   candidate_name: mla.name,
    # }
    # html_content = ElectionsUtil.get_constituency_winner_html(data)
    # image_url = ElectionsUtil.html_to_image(html_content, "constituency-winners/constituency-#{constituency.id}-#{circles_relation_id}-#{Time.zone.now.to_i}.png")
    #
    # Metadatum.create!(
    #   entity: mla,
    #   key: "constituency_winner_image_url",
    #   value: image_url
    # )
  end
end
