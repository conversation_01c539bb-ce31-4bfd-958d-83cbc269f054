class CreateRenewalOrderForUser
  include Sidekiq::Worker
  sidekiq_options queue: :default, retry: 0

  def perform(user_id)
    return if user_id.blank?

    user = User.find_by(id: user_id)
    return if user.blank?

    begin
      # Create a new order for the user with the help of old successful order
      # Add the order items from the old order to the new order
      old_order = Order.joins(:order_items).where(user: user, status: :successful)
                       .where(order_items: { item_type: 'Product' }).last
      # irrespective of order duration, create a new order for the user with duration of 12 months
      new_order = Order.new(old_order.attributes.except('id', 'created_at', 'updated_at', 'status', 'total_amount',
                                                        'payable_amount', 'discount_amount'))
      duration = 12
      product_item_price = ItemPrice.where(item_type: "Product",
                                           item_id: Constants.get_poster_product_id,
                                           active: true,
                                           duration_in_months: duration).last
      new_order.total_amount = product_item_price.price + product_item_price.maintenance_price
      new_order.payable_amount = new_order.total_amount
      new_order.discount_amount = 0
      new_order.status = :opened
      new_order.save!

      # for order item of product
      new_product_order_item = OrderItem.new(
        item_type: 'Product',
        item_id: Constants.get_poster_product_id,
        item_price_id: product_item_price.id,
        duration_in_months: duration,
        total_item_price: product_item_price.price + product_item_price.maintenance_price,
        order_id: new_order.id
      )
      new_product_order_item.save!

      # for order item of frames
      old_frame_order_items = old_order.order_items.where(item_type: 'Frame')
      frames = Frame.where(id: old_frame_order_items.pluck(:item_id))
      old_frame_order_items.each do |old_frame_order_item|
        new_frame_order_item = OrderItem.new(old_frame_order_item.attributes.except('id', 'created_at', 'updated_at',
                                                                                    'order_id', 'parent_order_item_id',
                                                                                    'duration_in_months',
                                                                                    'total_item_price', 'item_price_id'))
        new_frame_order_item.order_id = new_order.id
        new_frame_order_item.duration_in_months = duration
        # if old_frame_order_item type is status then set parent_order_item_id to nil else for for premium frame
        # it should be new_product_order_item.id

        frame = frames.find { |f| f.id == old_frame_order_item.item_id }
        if frame.status?
          frame_item_price = ItemPrice.where(item_type: 'Frame', item_id: frame.id, active: true,
                                             duration_in_months: duration).last
          new_frame_order_item.parent_order_item_id = nil
          new_frame_order_item.item_price_id = frame_item_price.id
          new_frame_order_item.total_item_price = frame_item_price.price + frame_item_price.maintenance_price
          new_order.total_amount += frame_item_price.price + frame_item_price.maintenance_price
          new_order.payable_amount += frame_item_price.price + frame_item_price.maintenance_price
        else
          new_frame_order_item.parent_order_item_id = new_product_order_item.id
        end
        new_frame_order_item.save!
        new_frame_order_item
      end
      new_order.save!
    rescue => exception
      Honeybadger.notify(exception, context: { user_id: user.id })
    end
  end
end
