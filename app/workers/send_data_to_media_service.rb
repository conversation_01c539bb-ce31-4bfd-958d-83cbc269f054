# frozen_string_literal: true

#req_body is in json format
# body = {
#         "user_id": post_user_id,
#         "photos": post_photo_paths,
#         "metadata": {
#           "photo_ids": post_photo_ids
#         }
#       }
# req_url is a string with media service url
class SendDataToMediaService
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 3

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: {args: msg["args"]})

    Rails.logger.info("SendMediaDataToMediaService retries exhausted")

    videos = msg["args"][1]["videos"]
    if videos.present?
      videos.each do |video_obj|
        video_id = video_obj["metadata"]["video_id"]

        video = Video.find_by(id: video_id)
        video.status = :failed
        video.save!
      end
    end
  end

  def perform(req_url, req_body)
    return if req_url.blank? || req_body.blank?

    Honeybadger.context({ req_url: req_url, req_body: req_body })

    Rails.logger.info("Request to Media service")

    url = URI(req_url)

    http = Net::HTTP.new(url.host, url.port)
    # http.use_ssl = true
    request = Net::HTTP::Post.new(url)

    jwt_token = JsonWebToken.get_token_to_send_media_service

    # request headers
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{jwt_token}"

    # request body
    request.body = req_body.to_json

    # request
    response = http.request(request)

    unless response.code.to_i == 201
      raise "Media Service HTTP request failed with status code #{response.code}"
    end

    response
  end
end
