class SendPosterCreativeWorker
  include Sidekiq::Worker
  sidekiq_options retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(poster_creative_id, send_dm_message_notification = false, dm_text_message = "")
    Rails.logger.warn("SendPosterCreativeWorker - poster_creative_id: #{poster_creative_id},
                       send_dm_message_notification: #{send_dm_message_notification},
                       dm_text_message: #{dm_text_message}")
    return if poster_creative_id.blank?

    poster_creative = PosterCreative.find_by(id: poster_creative_id)
    return if poster_creative.blank?

    return unless poster_creative.active?

    return unless poster_creative.start_time <= Time.zone.now &&  Time.zone.now <= poster_creative.end_time

    # disable suggested lists before sending the message
    $redis.set(Constants.disable_suggested_lists_key, true, ex: 5.minutes.to_i)

    sender_id = poster_creative.creator_type == 'User' ? poster_creative.creator_id : Constants.praja_account_user_id
    circle_ids = poster_creative.poster_creative_circles.pluck(:circle_id)
    circle_ids.each do |circle_id|
      circle = Circle.find_by(id: circle_id)
      if circle.channel_conversation_type? 
        send_message_hash = {
          circle_id: circle_id,
          sender_id: sender_id,
          creative_id: poster_creative.id,
          text: dm_text_message.to_s,
          sent_at: poster_creative.created_at,
          category_kind: poster_creative.creative_kind,
          send_dm_message_notification: send_dm_message_notification
        }

        SendDmPosterCreativeMessage.perform_async(send_message_hash.to_json)
      end
    end
    Metadatum.where(entity: poster_creative, key: Constants.poster_creative_jid_key).delete_all
  end
end
