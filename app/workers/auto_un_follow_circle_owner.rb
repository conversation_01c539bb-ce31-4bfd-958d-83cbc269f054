# frozen_string_literal: true

class AutoUnFollowCircleOwner
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :low, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    :concurrency => { :limit => 5 },
    :threshold => { :limit => 10, :period => 1.second }
  )

  def perform(circle_id, user_id)
    return if circle_id.blank? || user_id.blank?

    circle = Circle.find_by(id: circle_id)
    return unless circle
    return unless circle.check_circle_level_to_perform_auto_actions

    user = User.find_by(id: user_id)
    return unless user

    owner_id = circle.get_owner_id
    UserFollower.where(user_id: owner_id, follower_id: user.id, source_of_follow: :auto).first&.destroy unless owner_id.blank?
  end
end
