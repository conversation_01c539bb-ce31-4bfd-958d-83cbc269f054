class SendRemoveTagNotification
  include Sidekiq::Worker
  sidekiq_options queue: :notifications, retry: 0

  def perform(post_id, circle_id)
    return if post_id.nil? || circle_id.nil?

    Honeybadger.context({ post_id: post_id, circle_id: circle_id })

    post = Post.find post_id
    circle = Circle.find circle_id

    notification_title = "మీ పోస్ట్ పై మీరు ట్యాగ్ చేసిన #{circle.name} సర్కిల్ తీసివేయబడింది"
    notification_body = "పోస్ట్ ను చూడటానికి క్లిక్ చేయండి"
    notification = Notification.create!(description: notification_title,
                                        notification_type: :remove_tag,
                                        user_id: post.user_id,
                                        entity_type: "post",
                                        entity_id: post.id)

    payload = {
      "title": notification_title,
      "body": notification_body,
      "message_channel": "remove_tag",
      "data": {
       "path": "/posts/#{post.id}",
        "circle_notification_id": notification.id.to_s
      }
    }
    GarudaNotification.send_user_notification(post.user_id, payload)
  end
end
