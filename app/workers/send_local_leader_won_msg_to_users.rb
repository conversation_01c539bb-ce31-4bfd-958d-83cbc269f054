class SendLocalLeaderWonMsgToUsers
  include Sidekiq::Worker
  sidekiq_options retry: 0, lock: :until_and_while_executing, on_conflict: :log

  include Elections2024

  def perform(leader_id, leader_role, creative_id)
    leader_circle = Circle.find_by(id: leader_id)
    return if leader_circle.blank? || leader_role.blank? || !leader_circle.political_leader_level? || creative_id.blank?

    leader_affiliated_party_circle = leader_circle.get_circle_affiliated_party_id

    # fetch joined affiliated party badge users of leader circle && fetch the users in batch of 300
    batch_size = 300

    # build singular link
    deeplink_uri = URI.parse("praja://buzz.praja.app/posters/layout?creative_id=#{creative_id}&category_kind=congrats&circle_id=#{leader_circle.id}")

    # singular link with campaign
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/vwtg')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s })

    link = link_uri.to_s
    link = Singular.shorten_link(link)

    pattern = %r{https://prajaapp\.sng\.link/A3x5b/vwtg/(.+)}

    poster_link = ""
    matches = link.match(pattern)
    if matches.present?
      extracted_string = matches[1]
      poster_link = extracted_string
    end

    hash_variables_list = [
      {
        "leader_name" => leader_circle.name,
        "leader_role" => leader_role,
        "poster_link" => poster_link,
      }]

    # Fetch users from user_circle where circle_id is leader_id and user_roles are active
    User.joins(:user_roles, :user_circles)
        .where(user_roles: { active: true })
        .where(user_circles: { circle_id: leader_id })
        .active
        .select(:id, :phone, :affiliated_party_circle_id)
        .distinct
        .find_in_batches(batch_size: batch_size) do |user_batch|

      user_ids = user_batch.map(&:id)
      used_in_last_7_days_user_ids = UserTokenUsage.where(user_id: user_ids)
                                                   .where('user_token_usages.created_at > ?', 7.days.ago)
                                                   .pluck("DISTINCT(user_id)")

      # Check UserTokenUsage in batches for each user batch
      user_ids.each do |user_id|
        hash_variables_list.first["number"] = nil
        # Skip if user is used in last 7 days or user is not affiliated with leader affiliated party circles coalition
        next if used_in_last_7_days_user_ids.include?(user_id)
        next unless coalited?(user_batch.find { |user| user.id == user_id }.affiliated_party_circle_id,
                              leader_affiliated_party_circle)

        hash_variables_list.first["number"] = "#{user_batch.find { |user| user.id == user_id }.phone}"
        # to avoid sending message to user if number is not present
        next if hash_variables_list.first["number"].blank?
        WatiIntegration.perform_async("elections_local_leader_won", hash_variables_list)
      end
      sleep(0.2)
    end
  end
end
