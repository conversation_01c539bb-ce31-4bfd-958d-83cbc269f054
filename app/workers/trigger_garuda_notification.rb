# frozen_string_literal: true

class TriggerGarudaNotification
  include Sidekiq::Worker
  sidekiq_options queue: :notifications, retry: 0, lock: :until_and_while_executing, on_conflict: :reject

  def perform(garuda_notification_id)
    garuda_notification = GarudaNotification.find_by(id: garuda_notification_id)
    return if garuda_notification.blank?

    garuda_notification.trigger_notification
  end
end
