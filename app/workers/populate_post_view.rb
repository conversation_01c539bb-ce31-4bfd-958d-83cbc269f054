class PopulatePostView
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0

  def perform(post_id, total_views_to_populate, post_like_id = nil)
    @post = Post.find post_id
    return if @post.nil?

    @from_time = @post.created_at

    @post_like = nil
    if post_like_id.present?
      @post_like = PostLike.find post_like_id
      return if @post_like.nil?

      @from_time = @post_like.created_at
    end

    views_count = $redis.hincrby("post_views_fantom_#{post_id}", (post_like_id.to_s || '0'), 1)

    return if views_count == total_views_to_populate

    # equation goes to infinite for the last view
    if (views_count + 1) < total_views_to_populate
      equation_duration = (((get_beta) / (Math.log((views_count + 1) / total_views_to_populate.to_f))) * 60).round

      next_view_time_in_sec = [0, (equation_duration - (Time.zone.now - @from_time))].max
    else
      next_view_time_in_sec = 600
    end

    PopulatePostView.perform_in(next_view_time_in_sec.seconds,
                                post_id,
                                total_views_to_populate,
                                post_like_id)
  end

  private

  def get_beta
    (@from_time.hour >= 23 && @from_time <= 5) ? -50 : -10
  end
end
