# frozen_string_literal: true

class LastTrialAttemptSchedulerWorker
  include Sidekiq::Worker
  sidekiq_options lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    # Update the lead of the user in Flow
    UpdateLeadScore.perform_async(user_id)
    return if SubscriptionUtils.has_user_ever_subscribed?(user_id)

    user = User.find_by_id(user_id)

    # Calculate the next run time and update Redis
    last_trial_attempt_time = user.user_last_trial_setup_attempt_time
    $redis.zadd(Constants.update_floww_lead_score_based_on_last_trial_attempt_key,
                (last_trial_attempt_time + 7.days + 1.hour).to_i, user_id)
  end
end
