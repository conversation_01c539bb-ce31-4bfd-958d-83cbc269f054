class ProfileViewWorker
  include Sidekiq::Worker
  sidekiq_options queue: :default, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id, viewer_id, viewed_at = Time.zone.now)
    # if user id and viewer id are same then return
    return if user_id == viewer_id || user_id.blank? || viewer_id.blank? || user_id == Constants.praja_account_user_id
    begin
      # if profile view is already there just update viewed_at else create new record
      profile_view = ProfileView.find_by(user_id: user_id, viewer_id: viewer_id)
      if profile_view.present?
        profile_view.update(viewed_at: viewed_at)
      else
        ProfileView.create(user_id: user_id, viewer_id: viewer_id, viewed_at: viewed_at)
      end
    rescue => e
      Honeybadger.notify(e, context: { user_id: user_id, viewer_id: viewer_id, viewed_at: viewed_at })
    end
  end
end
