require 'net/http'
require 'json'
require 'csv'

class PlaceDetailsUsingGooglePlaceId
  # include Sidekiq::Worker
  # sidekiq_options queue: :low, retry: 0
  #
  # def perform(email)
  #   place_ids_csv_file_path = "" # TODO:: Path to the CSV file
  #   location_place_ids_data = CSV.parse(File.read(place_ids_csv_file_path), headers: true)
  #
  #   headers = %w[village_id village_name mandal_id mandal_name district_id district_name place_id latitude longitude]
  #   api_key = Rails.application.credentials[:google_place_search_api_key]
  #
  #   file_name = 'place_details.csv'
  #   # Open the CSV file in write mode
  #   place_details_csv_file_path = File.join(Rails.root, 'tmp', file_name)
  #
  #   CSV.open(place_details_csv_file_path, 'w', write_headers: true, headers: headers) do |csv|
  #     location_place_ids_data.each do |row|
  #       begin
  #         village = Circle.find(row['circle_id'].to_i)
  #         mandal = village.parent_circle
  #         district = mandal.parent_circle
  #         place_id = row['place_id']
  #         if place_id != 0
  #           url = URI("https://maps.googleapis.com/maps/api/place/details/json?place_id=#{place_id}&fields=geometry&key=#{api_key}")
  #           response = Net::HTTP.get(url)
  #           data = JSON.parse(response)
  #           if data['status'] == 'OK' && !data['result'].empty?
  #             latitude = data['result']['geometry']['location']['lat']
  #             longitude = data['result']['geometry']['location']['lng']
  #             csv << [village.id, village.name_en, mandal.id, mandal.name_en, district.id, district.name_en, place_id,
  #                     latitude, longitude]
  #           else
  #             csv << [village.id, village.name_en, mandal.id, mandal.name_en, district.id, district.name_en, place_id, '', '']
  #           end
  #         else
  #           csv << [village.id, village.name_en, mandal.id, mandal.name_en, district.id, district.name_en, '', '', '']
  #         end
  #       rescue => exception
  #         Honeybadger.notify(exception, context: { circle_id: village.id, circle_name: village.name_en })
  #       end
  #     end
  #   end
  #
  #   upload_csv_to_aws(place_details_csv_file_path, file_name, email)
  # end
  #
  # def upload_csv_to_aws(csv_file, file_name, email)
  #   resource = Aws::S3::Resource.new(
  #     region: 'ap-south-1',
  #     credentials: Aws::Credentials.new(
  #       Rails.application.credentials[:aws_access_key_id],
  #       Rails.application.credentials[:aws_secret_access_key]
  #     ),
  #   )
  #
  #   file_extension = ".csv"
  #   hashed_file_name = Digest::MD5.hexdigest(file_name + '_' + Time.now.to_i.to_s) + file_extension
  #
  #   s3_object_path = Rails.env + '/csv_data/finding_place_details_using_place_id/' + hashed_file_name
  #
  #   begin
  #     obj = resource
  #             .bucket(Rails.application.credentials[:aws_s3_files_bucket_name])
  #             .object(s3_object_path)
  #     obj.upload_file(csv_file)
  #
  #     LocationDetailsUsingPlaceIdMailer.location_details_using_place_ids_file('https://cdn.thecircleapp.in/' + s3_object_path, email).deliver_later
  #   rescue
  #     LocationDetailsUsingPlaceIdMailer.location_details_using_place_ids_file(nil, email).deliver_later
  #   end
  # end
end
