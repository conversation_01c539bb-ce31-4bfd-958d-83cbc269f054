class SyncMixpanelUser
  include Sidekiq::Worker
  sidekiq_options queue: :low, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id, calculated_lead_score = 0)
    return if user_id.nil?

    user = User.find_by(id: user_id)
    return if user.nil?

    properties = user.get_mixpanel_hash
    properties[:lead_score] = calculated_lead_score if calculated_lead_score.positive?

    $mixpanel_tracker.people.set(user_id, properties)
  end
end
