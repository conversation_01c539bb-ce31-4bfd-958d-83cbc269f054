# frozen_string_literal: true

class GenerateElectionTickerImage
  include Sidekiq::Worker
  include Capture
  include Elections2024
  include ElectionTicker
  sidekiq_options queue: :critical, retry: 0, lock: :until_and_while_executing, on_conflict: :log

  def perform(ticker_id)
    Honeybadger.context({ ticker_id: ticker_id})
    coalitions = ticker_coalitions(ticker_id)
    return if coalitions.blank?

    title = ticker_title(ticker_id)
    sub_title = ticker_sub_title(ticker_id)
    announcement_text = get_days_left_as_text

    coalitions = coalitions + [OTHERS_COALITION]

    ticker_list = coalitions.map do |coalition|
      {
        logo_url: coalition.logo_url,
        leaders_image_url: coalition.leaders_image_url,
        css_background: coalition.css_background,
      }
    end
    ticker_data = get_ticker_data(ticker_id)
    wons = []
    leads = []
    leads_text = 'ముందంజ'
    wons_text = 'గెలుపు'
    if ticker_data.present?
      wons = coalitions.map do |coalition|
        data = ticker_data["counts"][coalition.id]
        data["win_count"] if data.present?
      end.compact
      leads = coalitions.map do |coalition|
        data = ticker_data["counts"][coalition.id]
        data["lead_count"] if data.present?
      end.compact
      time = ticker_data["updated_at"]
      time = Time.parse(time).in_time_zone('Kolkata').strftime('%l:%M %P') if time.present?
    end

    if wons.all? { |num| num == 0 }
      wons = []
    end

    if leads.all? { |num| num == 0 }
      leads = []
    end

    if wons.present? && ticker_data["declare_majority_as_winner"]
      max_index = index_of_max(wons)
      if max_index.present?
        ticker_list[max_index][:winner] = true
      end
    end

    if wons.present? || leads.present?
      announcement_text = ''
    end

    html = election_ticker_html({title:, sub_title:, announcement_text:, ticker_list:, wons:, leads:, time:, leads_text:, wons_text:})
    uploaded_image = capture_html_as_image(html, '#outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    key = ticker_image_url_key(ticker_id)
    $redis.set(key, uploaded_image['cdn_url'])
    $redis.expire(key, 2.day.to_i)

    # prewarm cache for singular short link
    share_ticker_link(ticker_id)
  end

  private

  def election_ticker_html(data)
    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'election_images', 'election_count_ticker.html.erb')),
      locals: data
    )
  end

  def index_of_max(array)
    # Return nil if the array is empty
    return nil if array.empty?

    # Initialize variables to track the max value and its index
    max_value = array[0]
    max_index = 0

    # Iterate over the array with index
    array.each_with_index do |value, index|
      if value > max_value
        max_value = value
        max_index = index
      end
    end

    max_index
  end
end
