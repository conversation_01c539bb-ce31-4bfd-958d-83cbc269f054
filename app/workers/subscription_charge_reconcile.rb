# frozen_string_literal: true

class SubscriptionChargeReconcile
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  sidekiq_options retry: 3, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    :concurrency => { :limit => 5 },
    :threshold => { :limit => 10, :period => 1.second }
  )

  def perform(subscription_charge_id)
    subscription_charge = SubscriptionCharge.find(subscription_charge_id)
    return if subscription_charge.blank?

    subscription_charge.reconcile

    subscription_charge.reload
    if subscription_charge.sent_to_pg?
      notifier = Slack::Notifier.new(
        "*******************************************************************************"
      )
      notifier.post(
        text: ":warning: *Failed Recon* of charge id #{subscription_charge_id}",
      )
    end
  end
end
