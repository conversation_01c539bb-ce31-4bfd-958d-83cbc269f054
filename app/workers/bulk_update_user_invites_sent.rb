class BulkUpdateUserInvitesSent
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_throttle(
    # Allow maximum 1 concurrent jobs of this class at a time.
    :concurrency => { :limit => 1 },
    # Allow maximum 2 jobs being processed within one minute window.
    :threshold => { :limit => 5, :period => 1.minute }
  )

  def perform(phones_batch)
    return
    delete_phone_records_in_user_invites_index(phones_batch)
    UserInvite.where(phone: phones_batch, sent: false).update_all(sent: true)
  end

  def delete_phone_records_in_user_invites_index(phones_batch)
    return
    ES_CLIENT.perform_request('POST', "#{EsUtil.get_user_invite_phones_index_v2}/_delete_by_query", {}, {
      "query": {
        "terms": {
          "id": phones_batch
        }
      }
    })
  end
end
