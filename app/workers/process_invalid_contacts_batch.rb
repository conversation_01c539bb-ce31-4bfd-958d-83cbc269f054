# frozen_string_literal: true

class ProcessInvalidContactsBatch
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 1

  def on_complete(status, options)
    redis_key = "invalid_contacts_queue_v1:#{status.bid}"

    contacts_batch = $redis.smembers(redis_key)
    invalid_user_invites = []
    contacts_batch.each do |contact|
      contact = JSON.parse(contact)
      invalid_contact = contact['invalid_contact']
      user_id = contact['user_id']

      invalid_user_invites << InvalidUserInvite.new(name: invalid_contact['name'],
                                                   phone: invalid_contact['phone'],
                                                   user_id: user_id)
    end

    InvalidUserInvite.import invalid_user_invites, on_duplicate_key_ignore: true, batch_size: 50 if invalid_user_invites.present?

    $redis.del(redis_key)
  end
end
