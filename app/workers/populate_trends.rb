class Array
  def split_random(n)
    def td(n)
      [take(n),drop(n)]
    end

    def split_random_acc(n,l, acc)
      head,tail = l.td(rand(n)+1)
      return (acc + [head]) if tail==[]
      split_random_acc(n, tail, acc + [head])
    end

    split_random_acc(n,self,[])
  end
end

class PopulateTrends
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    post_id = args[0].to_i
    user_group_id = args[1].to_i
    Honeybadger.context({ post_id: post_id, user_group_id: user_group_id })

    user_group = UserGroup.find(user_group_id)
    max_limit = user_group.max_trends
    min_limit = user_group.min_trends

    random_limit = rand(min_limit..max_limit)

    members = user_group.user_group_members.limit(random_limit)
    member_ids = members.map {|m| m.user_id}

    splits = member_ids.split_random(4)

    count = 1
    splits.each do |split|
      random_minutes = rand(10..20)

      # When it is last chunk,
      # execute that chunk next day between 8AM & 12PM
      if count == 4
        random_minutes = ((rand(Time.parse("#{Date.tomorrow.to_s} 02:30 AM").to_i..Time.parse("#{Date.tomorrow.to_s} 06:30 AM").to_i) - Time.now.to_i)/60)
      end

      split.each do |user_id|
        random_seconds = rand(30..90)
        TrendPost.perform_in((count * random_minutes.minutes) + random_seconds.seconds, post_id, [user_id])
      end

      count += 1
    end
  end
end