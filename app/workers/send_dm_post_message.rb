class SendDmPostMessage
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 0

  def perform(send_post_msg_hash)
    return if send_post_msg_hash.blank?

    send_post_msg_hash = JSON.parse(send_post_msg_hash)
    post_id = send_post_msg_hash['post_id']
    circle_id = send_post_msg_hash['circle_id']
    text = send_post_msg_hash['text']
    send_dm_message_notification = send_post_msg_hash['send_dm_message_notification']

    return if post_id.blank? || circle_id.blank?

    Honeybadger.context(post_id: post_id, circle_id: circle_id)

    post = Post.find_by(id: post_id)
    return if post.blank? || !post.active?

    circle = Circle.find_by(id: circle_id)
    return if circle.blank? || !circle.active?

    Rails.logger.warn("Send DM post message - #{post_id}-#{circle_id}")

    req_url = Constants.get_dm_url + "/conversations/send-channel-message"
    req_body = {
      circleId: circle_id.to_s,
      sentAt: post.created_at.iso8601,
      sendNotification: send_dm_message_notification,
      messageData: {
        senderId: post.user_id.to_s,
        text: text,
        attachments: [
          {
            type: 'POST',
            attachmentData: {
              postId: post.id.to_s
            }
          }
        ]
      }
    }

    Rails.logger.warn("Send DM post message - #{post_id}-#{circle_id}, req_body - #{req_body} and req_url - #{req_url}")

    response = DmUtil.post_request_to_dm(req_url, req_body)
    response_body = JSON.parse(response.body)

    if response.code.to_i == 201
      circle.update_post_message_usage
      if send_dm_message_notification
        circle.update_notification_usage
      end
      post.user.update_post_ids_sent_via_dm(post.id)
      Rails.logger.warn("Send DM post message - #{post_id}-#{circle_id}, message_id - #{response_body['messageId']}")
      response_body['success']
    else
      Honeybadger.notify('Error in send channel message api to DM service', context: { req_url: req_url, req_body: req_body, response_body: response_body })
    end
  end
end
