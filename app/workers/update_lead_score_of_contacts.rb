# frozen_string_literal: true

class UpdateLeadScoreOfContacts
  include Sidekiq::Worker
  sidekiq_options lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    # join with metadata to get the user floww contact id
    UserContactSuggestion.joins("INNER JOIN metadata ON metadata.entity_id = user_contact_suggestions.phone_user_id AND
                                 metadata.entity_type = 'User' AND metadata.key = '#{Constants.floww_contact_id_key}'")
                         .select("user_contact_suggestions.phone_user_id, user_contact_suggestions.id")
                         .where(user_id: user_id).find_each(batch_size: 500) do |user_contact_suggestion|
      UpdateLeadScore.perform_async(user_contact_suggestion.phone_user_id) if user_contact_suggestion.present?
    end
  end
end
