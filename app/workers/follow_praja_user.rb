class FollowPrajaUser
  include Sidekiq::Worker
  sidekiq_options retry: 1

  def perform(user_id)
    return if !Rails.env.production? || user_id.nil?

    user = User.find user_id
    return if user.nil? || !user.active_status?

    begin
      # make every user follow Praja official account
      UserFollower.create(user_id: 41, follower_id: user_id, source_of_follow: :auto)
    rescue ActiveRecord::RecordNotUnique
      # do nothing
    end
  end
end
