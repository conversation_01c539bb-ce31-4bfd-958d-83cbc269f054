# frozen_string_literal: true

class GenerateChannelPreview
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  sidekiq_options queue: :default, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 1,
    },
    threshold: {
      limit: 3,
      period: 5.second,
    }
  )

  def perform(circle_id)
    Honeybadger.context({ circle_id: circle_id })
    circle = Circle.find_by_id(circle_id)
    return if circle.blank?

    name = circle.name
    photo_url = circle.photo&.compressed_url(size: 240)
    # use app_icon url as fallback if circle has no photo
    photo_url = 'https://a-cdn.thecircleapp.in/512x512/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg' unless photo_url.present?
    is_official = circle.official?

    html = generate_html(name, photo_url, is_official)
    uploaded_image = capture_html_as_image(html, '#outer-container')

    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    Metadatum.where(entity: circle, key: 'channel_preview_image_url').first_or_create.update(value: uploaded_image['cdn_url'])
  end

  def generate_html(name, photo_url, is_official)
    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'circles', 'channel_preview_image.html.erb')),
      locals: { name:, photo_url:, is_official: }
    )
  end
end
