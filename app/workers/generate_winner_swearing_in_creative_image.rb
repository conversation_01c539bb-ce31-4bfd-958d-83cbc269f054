# frozen_string_literal: true

class GenerateWinnerSwearingInCreativeImage
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  include Elections2024
  sidekiq_options queue: :default, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 10,
    },
    threshold: {
      limit: 30,
      period: 5.second,
    }
  )

  def perform(constituency_circle_id, notify = false)
    Honeybadger.context({ constituency_circle_id: constituency_circle_id })

    constituency_circle = Circle.find_by_id(constituency_circle_id)
    return if constituency_circle.blank?
    location_data = constituency_circle.get_location_circle_data
    state_id = location_data["state_id"]
    state = Circle.find_by_id(state_id)


    if constituency_circle.mla_constituency_level?
      leader_circle_id = CirclesRelation.mla_leader_circle_id(constituency_circle_id)
      region = state.name
      role = '16వ శాసనసభ సభ్యులుగా'
    elsif constituency_circle.mp_constituency_level?
      leader_circle_id = CirclesRelation.mp_leader_circle_id(constituency_circle_id)
      region = 'భారతదేశం'
      role = '18వ లోక్‌సభ సభ్యులుగా'
    end

    return if leader_circle_id.blank?

    leader_circle = Circle.find_by_id(leader_circle_id)
    return if leader_circle.blank?

    # Inactivate all existing contestant creatives
    PosterCreative
      .joins(:poster_creative_circles)
      .where(creative_kind: :congrats, active: true)
      .where(poster_creative_circles: { circle_id: constituency_circle_id }).update_all(active: false)

    party_id = CirclesRelation.find_by(first_circle_id: leader_circle_id, relation: "Leader2Party")&.second_circle_id
    party_circle = Circle.find_by_id(party_id) if party_id.present?

    leader_name = leader_circle.name
    leader_image_url = leader_circle.photo&.compressed_url(size: 512)

    locals = {
      leader_name:, leader_image_url:, party_id:, role:, region:
    }

    html = generate_html(locals)
    uploaded_image = capture_html_as_image(html, '#outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    creator = User.find_by_id(Constants.praja_account_user_id)
    kind = :congrats
    photo_data = OpenStruct.new(uploaded_image)

    photo = Photo.new(ms_data: photo_data, user: creator, service: photo_data.service)
    photo.set_photo_data
    photo.save!

    poster_creative = PosterCreative.new(creative_kind: kind,
                                         photo_v3: photo,
                                         primary: false,
                                         h1_leader_photo_ring_type: "dark",
                                         h2_leader_photo_ring_type: "dark",
                                         start_time: Time.zone.now,
                                         end_time: Time.zone.parse('30-06-2024').end_of_day,
                                         active: true,
                                         creator: creator)
    poster_creative_circle_ids = [constituency_circle_id, leader_circle_id]
    poster_creative_circle_ids.each do |circle_id|
      poster_creative.poster_creative_circles.build(poster_creative_id: poster_creative.id, circle_id: circle_id)
    end
    poster_creative.save!

    if notify
      # Send notification to all leader circle users
      notification_title = "🌠 శుభాకాంక్షలు పోస్టర్"
      notification_body = "#{role} #{name} గారికి శుభాకాంక్షలు తెలపండి"
      path = "/posters/layout?creative_id=#{poster_creative.id}&circle_id=#{leader_circle_id}&category_kind=congrats"
      payload = {
        "title": notification_title,
        "body": notification_body,
        "data": {
          "path": path,
        }
      }
      GarudaNotification.send_channel_notification(leader_circle_id, { payload: payload })
      SendLocalLeaderWonMsgToUsers.perform_async(leader_circle_id, role, poster_creative.id)
    end
  end

  def generate_html(locals)
    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'election_images', 'winner_swearing_in.html.erb')),
      locals: locals
    )
  end
end
