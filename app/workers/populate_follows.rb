class Array
  def split_random(n)
    def td(n)
      [take(n),drop(n)]
    end

    def split_random_acc(n,l, acc)
      head,tail = l.td(rand(n)+1)
      return (acc + [head]) if tail==[]
      split_random_acc(n, tail, acc + [head])
    end

    split_random_acc(n,self,[])
  end
end

class PopulateFollows
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    followed_user_id = args[0].to_i
    user_group_id = args[1].to_i
    Honeybadger.context({ followed_user_id: followed_user_id, user_group_id: user_group_id })

    user_group = UserGroup.find(user_group_id)

    random_limit = rand(4..8)

    members = user_group.user_group_members.limit(random_limit)
    member_ids = members.map {|m| m.user_id}

    splits = member_ids.split_random(3)

    count = 1
    splits.each do |split|
      random_minutes = rand(60..144)
      split.each do |user_id|
        random_seconds = rand(900..1800)
        FollowUser.perform_in((count * random_minutes.minutes) + random_seconds.seconds, followed_user_id, [user_id])
      end
      count = count + 1
    end
  end
end