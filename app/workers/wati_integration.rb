class WatiIntegration
  include Sidekiq::Worker
  sidekiq_options retry: 1

  def perform(template_name, hash_variables_list)
    hash_variables_list.each do |hash_variable|
      date = Time.new
      date = date.day.to_s + "/" + date.month.to_s + "/" + date.year.to_s
      broadcast_name = template_name + "_" + date
      SendWhatsappMsg.perform_async(template_name, broadcast_name, hash_variable)
    end
  end
end
