class UserRolesUpdateDuration
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0

  #change the path according to the csv used
  def perform
    # user_roles_info = CSV.parse(File.read('./lib/scripts/duration.csv'), headers: true)
    #
    # update_failed_user_role_ids = []
    # user_roles_info.each_slice(1000) do |user_roles_group|
    #   user_roles_group.each do |list|
    #     user_role_id = list["id"].to_i
    #     start_date = list["start_date"]
    #     end_date = list["end_date"]
    #
    #     user_role = UserRole.find_by(id: user_role_id)
    #     if user_role.present?
    #       user_role_update = user_role.update(start_date: start_date, end_date: end_date)
    #
    #       if user_role_update.blank?
    #         update_failed_user_role_ids << user_role_id
    #         $redis.sadd("duration_update_failed_user_role_ids", update_failed_user_role_ids)
    #       end
    #     end
    #   end
    #
    #   sleep 5.seconds
    # end
  end
end
