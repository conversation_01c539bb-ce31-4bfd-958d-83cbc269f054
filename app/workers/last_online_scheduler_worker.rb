# frozen_string_literal: true

class LastOnlineSchedulerWorker
  include Sidekiq::Worker
  sidekiq_options queue: :low, lock: :until_and_while_executing, on_conflict: :log

  def perform(user_id)
    # Update the lead of the user in Flow
    UpdateLeadScore.perform_async(user_id)

    # Calculate the next run time and update Redis
    last_online_time = UserTokenUsage.where(user_id:).last.created_at
    $redis.zadd(Constants.update_floww_lead_score_based_on_last_online_key, (last_online_time + 7.days + 1.hour).to_i,
                user_id)
  end
end
