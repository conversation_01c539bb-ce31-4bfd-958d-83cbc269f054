# frozen_string_literal: true

class ProcessValidContactsBatch
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :contacts, retry: 1
  sidekiq_throttle(
    # Allow maximum 2 concurrent jobs of this class at a time.
    :concurrency => { :limit => 1 },
    # Allow maximum 500 jobs being processed within one minute window.
    :threshold => { :limit => 2, :period => 1.minute }
  )

  def on_complete(status, options)
    redis_key = "valid_contacts_queue_v1:#{status.bid}"

    contacts_batch = $redis.smembers(redis_key)
    user_invites = []
    user_contact_suggestions = []
    contacts_uploaded_user_id = nil
    contacts_batch.each do |contact|
      contact = JSON.parse(contact)
      valid_contact = contact['valid_contact']
      user_id = contact['user_id'].to_i
      contacts_uploaded_user_id = user_id

      if valid_contact['phone_user_id'].nil?
        user_invites << UserInvite.new(name: valid_contact['name'],
                                       phone: valid_contact['phone'],
                                       user_id: user_id,
                                       invited: false)
      else
        user_contact_suggestions << UserContactSuggestion.new(name: valid_contact['name'],
                                                              phone: valid_contact['phone'],
                                                              phone_user_id: valid_contact['phone_user_id'],
                                                              user_id: user_id)
      end
    end

    UserInvite.transaction do
      UserInvite.import user_invites, on_duplicate_key_ignore: true, batch_size: 50 if user_invites.present?
    end

    UserContactSuggestion.import user_contact_suggestions, on_duplicate_key_ignore: true, batch_size: 50 if user_contact_suggestions.present?

    # update floww of contacts uploaded user id
    UpdateLeadScore.perform_async(contacts_uploaded_user_id) if contacts_uploaded_user_id.present?

    $redis.del(redis_key)
  end
end
