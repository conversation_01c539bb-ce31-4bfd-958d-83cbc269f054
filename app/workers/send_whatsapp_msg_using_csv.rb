class SendWhatsappMsgUsingCsv
  include Sidekiq::Worker
  sidekiq_options queue: :wati, retry: 0

  def perform(template_name, broadcast_name, s3_object_path)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ))

    aws_response = resource.bucket(Constants.aws_s3_bucket_name_for_csvs)
                     .object(s3_object_path)
                     .get

    file_content = aws_response.body.read

    wati_end_point = Constants.get_send_message_wati_csv_url

    url = URI("#{wati_end_point}?template_name=#{template_name}&broadcast_name=#{broadcast_name}")
    raise "Unable to form URL" if url.host.nil?

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(url)

    form_data = [['whatsapp_numbers_csv', file_content, {
      filename: File.basename(s3_object_path),
      content_type: 'text/csv'
    }]]
    request.set_form(form_data, 'multipart/form-data')
    request["Authorization"] = Rails.application.credentials[:wati_api_key]

    response = http.request(request)
    body = JSON.parse(response.body)

    if response.code.to_i != 200 || body["result"] != true
      Honeybadger.notify("WATI CSV message trigger error: #{response.code}", context: {
        response_code: response.code,
        response_body: body
      })
    end
  end
end
