require 'net/http'
require 'json'
require 'csv'

class FindPlaceIdOfLocationCircle
  # include Sidekiq::Worker
  # sidekiq_options queue: :low, retry: 0
  #
  # def perform(email)
  #   file_name = 'place_ids_of_village_circles'
  #   csv_file_path = File.join(Rails.root, 'tmp', file_name + '.csv')
  #   api_key = Rails.application.credentials[:google_place_search_api_key]
  #
  #   headers = %w[village_id village_name_en mandal_id mandal_name_en district_id district_name_en place_id status]
  #   # Open the CSV file in write mode
  #   CSV.open(csv_file_path, 'w', write_headers: true, headers: headers) do |csv|
  #     # add headers to csv file
  #     count = 0
  #     Circle.where(active: true, level: Constants.village_levels).each do |circle|
  #       begin
  #         input = circle.name_en + ',' + circle.parent_circle.parent_circle.name_en.strip + ' district'
  #         url = URI("https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=#{URI.encode(input)}&inputtype=textquery&fields=place_id&key=#{api_key}")
  #         response = Net::HTTP.get(url)
  #         data = JSON.parse(response)
  #         place_id = data["candidates"].map { |hash| hash["place_id"] }.join(" ")
  #         csv << [circle.id, circle.name_en, circle.parent_circle.id, circle.parent_circle.name_en,
  #                 circle.parent_circle.parent_circle.id, circle.parent_circle.parent_circle.name_en,
  #                 place_id, data['status']]
  #       rescue => exception
  #         Honeybadger.notify(exception, context: { circle_id: circle.id, circle_name: circle.name_en })
  #       end
  #       count += 1
  #       sleep 60 if (count % 5000).zero?
  #     end
  #   end
  #
  #   upload_csv_to_aws(csv_file_path, file_name, email)
  # end

  # def upload_csv_to_aws(csv_file, file_name, email)
  #   resource = Aws::S3::Resource.new(
  #     region: 'ap-south-1',
  #     credentials: Aws::Credentials.new(
  #       Rails.application.credentials[:aws_access_key_id],
  #       Rails.application.credentials[:aws_secret_access_key]
  #     ),
  #   )
  #
  #   file_extension = ".csv"
  #   s3_object_path = Rails.env + '/csv_data/locations/' + file_name + '_' + Time.now.to_i.to_s + file_extension
  #
  #   begin
  #     obj = resource
  #             .bucket(Rails.application.credentials[:aws_s3_files_bucket_name])
  #             .object(s3_object_path)
  #     obj.upload_file(csv_file)
  #
  #     LocationPlaceIdMailer.locations_with_place_id_file('https://cdn.thecircleapp.in/' + s3_object_path, email).deliver_later
  #   rescue
  #     LocationPlaceIdMailer.locations_with_place_id_file(nil, email).deliver_later
  #   end
  # end
end
