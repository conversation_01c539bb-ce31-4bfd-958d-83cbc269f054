class FollowUser
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    followed_user_id = args[0].to_i
    Honeybadger.context({ followed_user_id: followed_user_id })

    followed_user = User.find(followed_user_id)

    user_ids = args[1]

    user_ids.each do |user_id|
      begin
        followed_user.follow(user_id, "internal")
      rescue
        next
      end
    end
  end
end