class ProcessUserInvitesForGrowthTool
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_throttle(
    :concurrency => { :limit => 1 },
  )

  def perform(district_id, party_id)
    logger.info("Processing user invites for district_id: #{district_id} and party_id: #{party_id}")
    district = Circle.find_by_id(district_id)
    party = Circle.find_by_id(party_id)
    badge_user_ids_of_constituency = User.active
                                         .where(district_id: district_id, affiliated_party_circle_id: party_id)
                                         .pluck(:id, :phone)

    # uploaded_phone_number, Name, uploader_user_id, uploader_phone_number
    file_name = district.name_en.gsub(" ", "") + '_' + party.name_en.gsub(" ", "") + '_users_contacts'
    csv_file_path = File.join(Rails.root, file_name + '.csv')
    headers = %w[uploaded_phone_number Name uploader_user_id uploader_phone_number]
    count = 0
    CSV.open(csv_file_path, 'w', write_headers: true, headers: headers) do |csv|
      badge_user_ids_of_constituency.each do |user_id, phone|
        # do in batch size of 300
        UserInvite.where(user_id: user_id, sent: false).find_in_batches(batch_size: 500) do |user_invites|
          already_registered_phone_numbers = User.where(phone: user_invites.map(&:phone)).pluck(:phone)
          # insert data into csv if phone number is not already registered
          user_invites.each do |user_invite|
            csv << [user_invite.phone, user_invite.name, user_invite.user_id, phone] unless already_registered_phone_numbers.include?(user_invite.phone)
          end
        end
        count += 1
        sleep(0.5) if count % 100 == 0
      end
    end

    upload_csv_to_aws(csv_file_path, file_name)
  end

  def upload_csv_to_aws(csv_file, file_name)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ),
    )

    file_extension = ".csv"
    # s3://praja-raw-contacts-data/khammam_badge_user_id_contacts.csv
    s3_object_path = file_name + file_extension
    begin
      obj = resource
              .bucket("praja-raw-contacts-data")
              .object(s3_object_path)
      obj.upload_file(csv_file)
      invoke_lambda_function(file_name, file_extension)
    rescue => exception
      Honeybadger.notify(exception, context: { file_name: file_name })
    end
  end

  def invoke_lambda_function(file_name, file_extension)
    client = Aws::Lambda::Client.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    )

    function_name = 'arn:aws:lambda:ap-south-1:666527360739:function:contacts-processor-ContactsProcessor-PX2YBXOac452'
    key = file_name + file_extension
    payload = { key: key }.to_json
    resp = client.invoke({ function_name: function_name, payload: payload, invocation_type: "Event" })
    # you can check if resp.status_code == 202
  end
end
