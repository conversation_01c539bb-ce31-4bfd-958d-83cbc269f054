# frozen_string_literal: true

class IndexSearchEntity
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :low, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    # Allow maximum 5 concurrent jobs of this class at a time.
    # :concurrency => { :limit => 1 },
    # Allow maximum 500 jobs being processed within one minute window.
    :threshold => { :limit => 1000, :period => 1.minute }
  )

  def perform(entity, entity_id)
    Honeybadger.context({ entity: entity, entity_id: entity_id })

    case entity
    when 'user'
      entity_obj = User.active.find_by(id: entity_id)
    when 'circle'
      entity_obj = Circle.find(entity_id)
    else
      return
    end

    return if entity_obj.blank?
    
    default_params = {
      "type": entity,
      "id": entity_id
    }
    params = default_params.merge(entity_obj.search_entity_obj)

    perform_index("#{entity}-#{entity_id}", params)
  end

  private

  def perform_index(search_entity_id, params)
    ES_CLIENT.perform_request(
      'POST',
      "#{EsUtil.get_search_entities_index}/_update/#{search_entity_id}?retry_on_conflict=1",
      {},
      {
        "script": {
          "source": '' "
                ctx._source.name = params.name;
                ctx._source.phone = params.phone;
                ctx._source.name_en = params.name_en;
                ctx._source.photo_url = params.photo_url;
                ctx._source.followers_count = params.followers_count;
                ctx._source.members_count = params.members_count;
                ctx._source.active = params.active;
                ctx._source.circle_ids = params.circle_ids;
                ctx._source.badge = params.badge;
                ctx._source.affiliated_political_party_id = params.affiliated_political_party_id;
                ctx._source.badge_location_circle_id = params.badge_location_circle_id;
                ctx._source.badge_grade_level = params.badge_grade_level;
                ctx._source.village_id = params.village_id;
                ctx._source.mandal_id = params.mandal_id;
                ctx._source.mla_constituency_id = params.mla_constituency_id;
                ctx._source.mp_constituency_id = params.mp_constituency_id;
                ctx._source.district_id = params.district_id;
                ctx._source.state_id = params.state_id;
            " '',
          "params": params
        },
        "upsert": params
      }
    )
  end
end
