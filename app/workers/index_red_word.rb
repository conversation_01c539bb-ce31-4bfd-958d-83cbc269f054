class IndexRedWord
  include Sidekiq::Worker
  sidekiq_options queue: :low, lock: :until_and_while_executing, on_conflict: :reschedule

  def perform(id, word, is_hate_speech)
    perform_index(id, word, is_hate_speech)
  end

  private

  def perform_index(id, word, is_hate_speech)
    ES_CLIENT.perform_request(
      'POST',
      "#{EsUtil.get_red_word_index}/_doc/#{id}",
      {},
      {
        "red_word": word,
        "is_hate_speech": is_hate_speech
      }
    )
  end
end
