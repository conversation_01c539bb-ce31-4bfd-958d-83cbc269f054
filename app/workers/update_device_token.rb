class UpdateDeviceToken
  include Sidekiq::Worker
  sidekiq_options queue: :low, retry: 0

  def perform(device_token, user_id, app_version, app_os, device_id, device_make, device_model)
    return if user_id.blank? || device_token.blank?

    Honeybadger.context({ user_id: user_id, device_token: device_token })

    user = User.find user_id

    return if user.blank?

    user_device_tokens_exist = UserDeviceToken.where(device_token: device_token, user: user, active: true).exists?

    mapped_device = nil
    if device_make.present? && device_model.present?
      mapped_device = Device.find_by(model: device_model)
      mapped_device = Device.create(make: device_make, model: device_model) if mapped_device.blank?
    end

    # If there are entries, update the app version & app os
    if user_device_tokens_exist.present?
      UserDeviceToken.where(device_token: device_token, user: user, active: true)
                     .update_all(app_version: app_version, app_os: app_os,
                                 device_id: device_id, mapped_device_id: mapped_device&.id,
                                 device_make: device_make, device_model: device_model)
    else
      # Disable for all users having this device token
      UserDeviceToken
        .where(device_token: device_token, active: true)
        .delete_all

      UserDeviceToken
        .create(
          device_token: device_token,
          user: user,
          app_version: app_version,
          app_os: app_os,
          device_id: device_id,
          mapped_device_id: mapped_device&.id,
          device_make: device_make,
          device_model: device_model
        )
    end
  end
end
