class SendDmPosterCreativeMessage
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 0

  def perform(send_message_hash)
    send_message_hash = JSON.parse(send_message_hash)
    circle_id = send_message_hash['circle_id']
    sender_id = send_message_hash['sender_id']
    creative_id = send_message_hash['creative_id']
    sent_at = send_message_hash['sent_at']
    text = send_message_hash['text']
    category_kind = send_message_hash['category_kind']
    send_dm_message_notification = send_message_hash['send_dm_message_notification']

    return if circle_id.blank? || sender_id.blank? || creative_id.blank? || sent_at.blank?

    Honeybadger.context(send_message_hash: send_message_hash)
    Rails.logger.warn("Send DM poster creative message - #{creative_id}-#{circle_id}")

    req_url = Constants.get_dm_url + "/conversations/send-channel-message"
    req_body = {
      circleId: circle_id.to_s,
      sentAt: sent_at.to_s,
      sendNotification: send_dm_message_notification,
      messageData: {
        senderId: sender_id.to_s,
        text: text,
        attachments: [
          {
            type: 'CREATE_POSTER',
            attachmentData: {
              params: {
                creative_id: creative_id,
                circle_id: circle_id,
                category_kind: category_kind
              }
            }
          }
        ]
      }
    }

    Rails.logger.warn("Send DM poster creative message - #{creative_id}-#{circle_id}, req_body - #{req_body} and req_url - #{req_url}")

    response = DmUtil.post_request_to_dm(req_url, req_body)
    response_body = JSON.parse(response.body)

    if response.code.to_i == 201
      circle = Circle.find_by(id: circle_id)
      circle.update_fan_poster_message_usage
      if send_dm_message_notification
        circle.update_notification_usage
      end
      Rails.logger.warn("Send DM poster creative message - #{creative_id}-#{circle_id}, message_id - #{response_body['messageId']}")
      response_body['success']
    else
      Honeybadger.notify('Error in send channel message api to DM service', context: { req_url: req_url, req_body: req_body, response_body: response_body })
    end
  end
end
