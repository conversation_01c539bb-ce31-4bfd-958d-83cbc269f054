# frozen_string_literal: true
class UserSubCirclesRevaluation
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options retry: 1, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 1
    },
    threshold: {
      limit: 10_000,
      period: 1.minute,
    }
  )

  def perform(user_id)
    return if user_id.blank?

    user  = User.find_by(id: user_id)
    return if user.blank?

    Honeybadger.context({ user_id: user_id })

    #getting all the parent circles which have sub circles by using parent circle column in sub circles table
    total_parent_circle_ids = SubCircle.group(:parent_circle_id).pluck(:parent_circle_id)
    total_parent_circle_ids.compact!
    total_parent_circle_ids.uniq!

    user_location_circle_ids = [user.village_id, user.mandal_id, user.district_id, user.state_id, user.mp_constituency_id, user.mla_constituency_id]
    user_location_circle_ids.compact!

    #getting all the user circles which have sub circles by using above parent circle ids
    user_circle_with_sub_circle_ids = UserCircle.where(circle_id: total_parent_circle_ids, user_id: user_id).pluck(:circle_id)
    user_circle_with_sub_circle_ids += user_location_circle_ids
    user_circle_with_sub_circle_ids.compact!
    user_circle_with_sub_circle_ids.uniq!

    #getting all the sub circles of the user
    total_sub_circle_ids_user_should_joined = SubCircle.where(parent_circle_id: user_circle_with_sub_circle_ids).pluck(:id)
    total_sub_circle_ids_user_should_joined.compact!

    total_user_circles = UserCircle.where(user_id: user_id).pluck(:circle_id)
    total_user_circles.compact!
    total_user_circles.uniq!

    excluded_user_circles = ExcludedUserCircle.where(user_id: user_id).pluck(:circle_id)
    excluded_user_circles.compact!
    excluded_user_circles.uniq!

    needs_to_be_joined = total_sub_circle_ids_user_should_joined - excluded_user_circles

    needs_to_be_removed = []

    if needs_to_be_joined.present?
      #getting all the roles of a user
      user_roles = UserRole.joins("LEFT JOIN roles ON roles.id = user_roles.role_id").where(user_id: user_id)
      user_purview_location_circle_ids = user_roles.map(&:purview_circle_id)
      user_purview_location_circle_ids.compact!

      #looping through all the sub circles of the user
      total_sub_circle_ids_user_should_joined.each do |sub_circle_id|
        filter_location_circle_ids = SubCircleFilter.where(sub_circle_id: sub_circle_id,filter_key: 'filter_location_circle_id').pluck(:filter_value)&.map(&:to_i)
        filter_role_ids = SubCircleFilter.where(sub_circle_id: sub_circle_id, filter_key: 'filter_role_id').pluck(:filter_value)&.map(&:to_i)
        filter_grade_levels = SubCircleFilter.where(sub_circle_id: sub_circle_id, filter_key: 'filter_grade_level').pluck(:filter_value)&.map(&:to_i)
        filter_grade_levels = filter_grade_levels.present? ? filter_grade_levels : Role::GRADE_LEVEL.values
        if filter_role_ids.blank?
          role_ids_to_be_considered = UserRole.joins("INNER JOIN roles ON roles.id = user_roles.role_id").where(user_id: user_id).where("user_roles.grade_level IN (?) OR (user_roles.grade_level is NULL AND roles.grade_level IN (?))", filter_grade_levels,filter_grade_levels).pluck(:role_id)
          filter_role_ids = role_ids_to_be_considered if role_ids_to_be_considered.present?
        else
          role_ids_to_be_considered = UserRole.joins("INNER JOIN roles ON roles.id = user_roles.role_id").where(user_id: user_id).where(role_id: filter_role_ids).where("user_roles.grade_level IN (?) OR (user_roles.grade_level is NULL AND roles.grade_level IN (?))", filter_grade_levels,filter_grade_levels).pluck(:role_id)
        end
        if SubCircle.check_eligibility_to_join_user(role_ids_to_be_considered, user_location_circle_ids,user_purview_location_circle_ids, filter_role_ids, filter_location_circle_ids)
          begin
            UserCircle.create!(user_id: user_id, circle_id: sub_circle_id)
          rescue => e
            Honeybadger.notify(e, context: { user_id: user_id, circle_id: sub_circle_id })
            next
          end
        else
          needs_to_be_removed << sub_circle_id
        end

      end
    end

    if needs_to_be_removed.present?
      needs_to_be_removed.each do |sub_circle_id|
        UserCircle.where(user_id: user_id, circle_id: sub_circle_id).first&.destroy
      end
    end
  end
end

