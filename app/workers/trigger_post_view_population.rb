class TriggerPostViewPopulation
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 1

  def perform(post_id, post_like_id = nil)
    @post = Post.find post_id
    return if @post.nil?

    @from_time = @post.created_at

    @post_like = nil
    if post_like_id.present?
      @post_like = PostLike.find post_like_id
      return if @post_like.nil?

      @from_time = @post_like.created_at
    end

    fantom_likes_count = PostLike.joins(:user).where(post_id: post_id, active: true, users: {internal: true}).where('post_likes.id <= ?', post_like_id).count

    views_to_populate = views_slabs(fantom_likes_count) * get_alpha

    beta_value = get_beta

    next_view_time_in_sec = 0

    (1..views_to_populate).each do |i|
      if i == 1 && post_like_id.present?
        # Give a view immediately only if it is after a trend, but not on post creation
        next_view_time_in_sec = 1
      elsif i == views_to_populate
        # Last view after 10 min of last but one view
        next_view_time_in_sec += 600
      else
        equation_duration = ((beta_value / Math.log(i / views_to_populate.to_f)) * 60).round
        next_view_time_in_sec = [0, equation_duration].max
      end

      PopulatePostViewV2.perform_in(next_view_time_in_sec.seconds, post_id, post_like_id)
    end
  end

  private

  def get_alpha
    return 1 if @post_like.nil?

    diff = ((@post_like.created_at - @post.created_at)/60.0).minutes

    case true
    when diff < (3 * 60).minutes
      1
    when diff >= (3 * 60).minutes && diff < (6 * 60).minutes
      0.8
    when diff >= (6 * 60).minutes && diff < (9 * 60).minutes
      0.6
    when diff >= (9 * 60).minutes && diff < (12 * 60).minutes
      0.4
    when diff >= (12 * 60).minutes && diff < (24 * 60).minutes
      0.3
    when diff >= (24 * 60).minutes
      0.2
    else
      1
    end
  end

  def get_beta
    case true
    when @from_time.hour >= 23
      -20
    when @from_time.hour == 0
      -30
    when @from_time.hour >= 1 && @from_time.hour < 6
      -50
    when @from_time.hour == 6
      -20
    else
      -10
    end
  end

  def views_slabs(like_number)
    if like_number >= 5
      rand(40..60)
    else
      (5 * like_number) + rand(20..30)
    end
  end
end
