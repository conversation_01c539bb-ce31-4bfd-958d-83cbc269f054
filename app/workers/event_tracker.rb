class EventTracker
  include Sidekiq::Worker
  sidekiq_options queue: :default, retry: 3

  def perform(user_id, event_name, properties)
    send_to_mixpanel(user_id, event_name, properties)
    send_to_firebase(user_id, event_name, properties)
  end

  private

  def send_to_mixpanel(user_id, event_name, properties)
    $mixpanel_tracker.track(user_id, event_name, properties)
  end

  def send_to_firebase(user_id, event_name, properties)
    user_device_token = UserDeviceToken.where(user_id: user_id, active: true).last
    return if user_device_token.blank? || user_device_token.firebase_app_instance_id.blank? || user_device_token.app_os.blank?

    firebase_credentials = {
      api_secret: Rails.application.credentials[:firebase_api_secret],
      android: {
        firebase_app_id: '1:922743680415:android:2e9a2b4b32e33812e3e1aa'
      },
      ios: {
        firebase_app_id: '1:922743680415:ios:942bd707db747d9ee3e1aa'
      }
    }

    user_os = user_device_token.app_os.to_sym
    if firebase_credentials.key?(user_os)
      firebase_app_id = firebase_credentials[user_os][:firebase_app_id]
      api_secret = firebase_credentials[:api_secret]
      properties = properties.merge(user_id: user_id)
      firebase_app_instance_id = user_device_token.firebase_app_instance_id

      uri = URI("#{Constants.get_firebase_url}?firebase_app_id=#{firebase_app_id}&api_secret=#{api_secret}")
      request_body = {
        app_instance_id: firebase_app_instance_id,
        events: [
          {
            name: event_name,
            params: properties
          }
        ]
      }.to_json

      Net::HTTP.post(uri, request_body, 'Content-Type' => 'application/json')
    end
  end
end
