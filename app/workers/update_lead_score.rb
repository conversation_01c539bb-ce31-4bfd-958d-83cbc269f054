# frozen_string_literal: true

class UpdateLeadScore
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :update_lead_score, lock: :until_and_while_executing, on_conflict: :log

  sidekiq_throttle(
    :threshold => { :limit => 10, :period => 1.second }
  )

  def perform(user_id)
    @user = User.find user_id
    return if @user.blank?
    return unless @user.active?

    return if @user.is_poster_subscribed && @user.has_premium_layout?

    calculated_lead_score = @user.calculate_lead_score
    # sync mixpanel user with calculated lead score
    SyncMixpanelUser.perform_async(user_id, calculated_lead_score)

    Floww::UpdateLead.perform_async(user_id, calculated_lead_score)
  end
end
