class GetUserInvitesFromEs
  include Sidekiq::Worker
  sidekiq_options retry: 1, lock: :until_and_while_executing, on_conflict: :log

  def perform(enhanced_count, location_circle_id, email, required_count)
    return
    Honeybadger.context({ enhanced_count: enhanced_count,
                          location_circle_id: location_circle_id,
                          email: email,
                          required_count: required_count })

    generate_excel = Axlsx::Package.new
    sheet1 = generate_excel.workbook.add_worksheet(:name => "Party Affiliated User Invites")

    sheet1.add_row %w(Name PhoneNumber partycircle_namete Party_totalgroupcount Partybadgeusers_groupcount
                    Location_namete Location_totaluserscount Location_badgeuserscount)

    sheet2 = generate_excel.workbook.add_worksheet(:name => "Badge Holder User Invites")

    sheet2.add_row %w(Name PhoneNumber BadgeUser_prajaname BadgeUser_locationid Badgeuser_role
                    Location_namete Location_totaluserscount Location_badgeuserscount
                    BadgeUser_politicalID badgeuserpic_url)
    sheet3 = generate_excel.workbook.add_worksheet(:name => "Normal User Invites")
    sheet3.add_row %w(Name PhoneNumber NormalUser_prajaname X Location_namete
                    Location_totaluserscount Location_badgeuserscount)

    scroll_time = "3h"

    size = enhanced_count > 10000 ? 9000 : enhanced_count

    phone_count = 0

    # To get data from es
    user_invites_es = user_invites_query_with_scroll(location_circle_id, scroll_time, size)
    # we can use this scroll ID with the scroll API to retrieve the next batch of results from es
    # phone_count is the count of numbers that are added in to excel
    scroll_id, phone_count = process_es_response(user_invites_es, required_count, phone_count, location_circle_id,
                                                 sheet1, sheet2, sheet3)

    # if phone_count less than required count we retrieve the next batch of results from es
    while phone_count < required_count
      user_invites_es = user_invites_query(scroll_id, scroll_time)
      break if user_invites_es['hits']['hits'].empty?
      scroll_id, phone_count = process_es_response(user_invites_es, required_count, phone_count, location_circle_id,
                                                   sheet1, sheet2, sheet3)

    end

    # delete scroll id from es
    delete_scroll_id_in_es(scroll_id)

    generate_excel.use_shared_strings = true
    file_name = "user invites data on " + DateTime.now.to_s + ".xlsx"
    file_path = File.join(Rails.root, 'tmp', file_name)
    generate_excel.serialize(file_path)

    # To upload excel to aws s3 and sending generated excel download link to given email
    upload_excel_to_aws(file_path, file_name, email)
    # delete the file from tmp after uploading the excel file to aws
    File.delete(file_path)
    # delete redis key
    $redis.del "user_invitee_limit_check"
  end

  def user_invites_query_with_scroll(location_circle_id, scroll_time, size)
    user_invites_es = ES_CLIENT.search scroll: scroll_time, index: EsUtil.get_user_invite_phones_index_v2, body: {
      "query": {
        "script_score": {
          "query": {
            "bool": {
              "filter": {
                "bool": {
                  "should": [
                    {
                      "terms": {
                        "location_circle_ids": [
                          location_circle_id
                        ]
                      }
                    }
                  ]
                }
              }
            }
          },
          "script": {
            "lang": "painless",
            "source": "" "
                              double score = 0;
                              if (params._source.proper_contact_name != null) {
                                     score = 1;
                              }
                              return score;
                          " ""
          }
        }
      },
      "sort": [
        {
          "_score": {
            "order": "desc"
          }
        },
        {
          "network_density": {
            "order": "desc"
          }
        }
      ],
      "size": size,
      "from": 0
    }
    user_invites_es
  end

  # Retrieves the next batch of results for a scrolling search.
  def user_invites_query(scroll_id, scroll_time)
    user_invites_es = ES_CLIENT.perform_request('GET', "_search/scroll", {}, {
      "scroll": scroll_time,
      "scroll_id": scroll_id
    })
    user_invites_es.body
  end

  # process the data that we get from es request
  def process_es_response(user_invites_es, required_count, phone_count, location_circle_id, sheet1, sheet2, sheet3)
    return [nil, required_count] if user_invites_es['hits']['hits'].empty?
    user_invites_es['hits']['hits'].map do |hit|
      user_invites_es_item = hit['_source']

      Honeybadger.context({ user_invites_es_item: user_invites_es_item })

      phone = user_invites_es_item['id']
      phone_with_91 = "91" + phone.to_s
      proper_contact_name = user_invites_es_item['proper_contact_name']
      affiliated_to_party = user_invites_es_item['affiliated_to_party']
      badge_user_ids = user_invites_es_item['badge_user_ids']
      network_density = user_invites_es_item['network_density']
      normal_user_ids = user_invites_es_item['normal_user_ids']

      next if UserInvite.where(phone: phone, sent: true).exists?

      location_circle = Circle.find(location_circle_id)
      all_users_in_location_circle = location_circle.get_members_count
      badge_users_in_location_circle = location_circle.get_badge_users_count

      if affiliated_to_party.present?
        party_circle = Circle.find(affiliated_to_party)

        sheet1.add_row [proper_contact_name, phone_with_91, party_circle.name,
                        party_circle.get_members_count, party_circle.get_badge_users_count,
                        location_circle.name, all_users_in_location_circle, badge_users_in_location_circle]
        phone_count += 1

      elsif badge_user_ids.length.positive?
        user_invitee = get_user_invitee(badge_user_ids, true)
        location_circle_name = nil
        party_circle_name = nil
        unless user_invitee.nil?
          user_location_circle_id = user_invitee.get_affiliated_location_circle_id
          user_political_party_id = user_invitee.get_badge_affiliated_party_circle_id
          location_circle_name = Circle.find(user_location_circle_id).name if user_location_circle_id != 0
          party_circle_name = Circle.find(user_political_party_id).name if user_political_party_id != 0
          sheet2.add_row [proper_contact_name, phone_with_91, user_invitee.name, location_circle_name,
                          user_invitee.get_badge_role.get_role_name, location_circle.name, all_users_in_location_circle,
                          badge_users_in_location_circle, party_circle_name, user_invitee.invite_card]
          phone_count += 1
        end
      elsif normal_user_ids.length.positive?
        user_invitee = get_user_invitee(normal_user_ids)
        unless user_invitee.nil?
          sheet3.add_row [proper_contact_name, phone_with_91, user_invitee.name, network_density,
                          location_circle.name, location_circle.get_members_count, badge_users_in_location_circle]
          phone_count += 1
        end
      end
      break if phone_count == required_count
    end
    [user_invites_es['_scroll_id'], phone_count]
  end

  # It gives user invitee for badge and normal user affiliated.
  def get_user_invitee(user_ids, is_badge_user = false)
    user_invitee = nil

    if is_badge_user
      badge_users_list = User.find(user_ids)
      badge_users_list.each do |temp_user|
        temp_badge_user_role = temp_user.get_badge_role
        next if temp_badge_user_role.nil?
        begin
          # temp_user.name[/\d/].nil? check is to eliminate names which has digits
          if temp_user.photo.present? && check_limit(temp_user.id) &&
            temp_user.marketing_consent && temp_user.name[/\d/].nil?
            if user_invitee.nil?
              user_invitee = temp_user
            else
              user_invitee = temp_user if user_invitee.get_badge_role.get_readable_grade_level <
                temp_badge_user_role.get_readable_grade_level
            end
          end
        rescue => e
          Honeybadger.notify(e, context: {
            temp_user_id: temp_user.id,
            user_invitee_id: user_invitee&.id,
            user_ids: user_ids,
            is_badge_user: is_badge_user
          })
        end
      end
    else
      user_ids.each do |normal_user_id|
        begin
          normal_user = User.find(normal_user_id)
          if check_limit(normal_user_id) && normal_user.name[/\d/].nil?
            user_invitee = normal_user
            break
          end
        rescue => e
          Honeybadger.notify(e, context: {
            normal_user_id: normal_user_id,
            user_invitee_id: user_invitee&.id,
            user_ids: user_ids,
            is_badge_user: is_badge_user
          })
        end
      end
    end
    $redis.hincrby("user_invitee_limit_check", user_invitee.id, 1) unless user_invitee.nil?
    user_invitee
  end

  # Limit for each user_invitee with 200 invites only .
  def check_limit(user_id)
    return false if $redis.hget("user_invitee_limit_check", user_id).to_i > 200
    true
  end

  # to delete scroll id
  def delete_scroll_id_in_es(scroll_id)
    ES_CLIENT.perform_request('DELETE', "_search/scroll/#{scroll_id}")
  end

  # To upload excel file to aws
  def upload_excel_to_aws(excel_file, file_name, email)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ),
    )

    file_extension = ".xlsx"
    hashed_file_name = Digest::MD5.hexdigest(file_name + '_' + Time.now.to_i.to_s) + file_extension

    s3_object_path = Rails.env + '/growth_tool/excel_data/' + hashed_file_name

    begin
      obj = resource
              .bucket(Rails.application.credentials[:aws_s3_bucket_name])
              .object(s3_object_path)
      obj.upload_file(excel_file)

      GrowthToolMailer.wati_template_file('https://cdn.thecircleapp.in/' + s3_object_path, email).deliver_later
    rescue
      GrowthToolMailer.wati_template_file(nil, email).deliver_later
    end
  end
end

