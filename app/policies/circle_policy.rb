class CirclePolicy < ApplicationPolicy
  attr_reader :user, :circle

  def initialize(user, circle)
    @user = user
    @circle = circle
    @permissions = @user.get_all_circle_permissions(@circle.id)
  end

  def is_user_can_remove_tag?
    @permissions.include?('remove_tag')
  end

  def can_upload_creative?
    @permissions.include?('circle_upload_creative')
  end

  def can_add_tag?
    !@user.is_blocked_for_tagging? && @permissions.include?('add_tag')
  end

  class Scope < Scope
    # NOTE: Be explicit about which records you allow access to!
    # def resolve
    #   scope.all
    # end
  end
end
