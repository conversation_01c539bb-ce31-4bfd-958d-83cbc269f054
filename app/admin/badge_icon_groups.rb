ActiveAdmin.register BadgeIconGroup do
  menu :parent => "Roles"
  includes :circle
  searchable_select_options(name: :badge_icon_group,
                            scope: -> { BadgeIconGroup },
                            text_attribute: :name,
                            display_text: ->(record) {
                              "#{record.name}"
                            })
  # Get the list of all badge icons associated to a circle based on circle name
  searchable_select_options(name: :badge_icons,
                            scope: -> { BadgeIconGroup.joins(:badge_icons).select("badge_icon_groups.*, badge_icons.*") },
                            text_attribute: :name,
                            display_text: ->(record) {
                              "#{record.name} #{record.color}"
                            })

  permit_params :name, :circle_id, :photo_1, :photo_2, :photo_3
  actions :all, except: [:destroy, :edit]

  filter :id
  filter :circle_id, label: "Circle ID"
  filter :name

  index do
    selectable_column
    column :id do |badge_icon_group|
      link_to(badge_icon_group.id, admin_badge_icon_group_path(badge_icon_group))
    end
    column :name
    column :circle
    column :created_at
    column :updated_at

  end
  form multipart: true do |f|
    f.semantic_errors
    f.inputs "Badge Icon Group Details" do
      if f.object.new_record?
        f.input :name
        f.input :circle_id
        f.input :photo_1, as: :file, label: "Photo for GOLD color badge icon"
        f.input :photo_2, as: :file, label: "Photo for SILVER color badge icon"
        f.input :photo_3, as: :file, label: "Photo for WHITE color badge icon"
        # else
        #   f.input :name, input_html: {disabled: true}
        #   f.input :circle_id, input_html: {disabled: true}
        #   f.input :photo_1, as: :file, label: "Photo for GOLD color badge icon"
        #   f.input :photo_2, as: :file, label: "Photo for SILVER color badge icon"
        #   f.input :photo_3, as: :file, label: "Photo for WHITE color badge icon"
      end
    end
    f.actions
  end

  show do
    attributes_table do
      row :name
      row :circle
      row :badge_icons do |group|
        table do
          group.badge_icons.map do |badge_icon|
            tr do
              td badge_icon.color
              td image_tag badge_icon.admin_medium.url, class: 'thumb_size', style: "margin-right: 5px;"
            end
          end
        end
      end
    end
  end

  controller do
    def update
      @badge_icon_group = BadgeIconGroup.find_by_hashid!(params[:id])

      if @badge_icon_group.save
        redirect_to admin_badge_icon_group_path(@badge_icon_group)
      else
        render :edit
      end
    end

    def create
      attrs = permitted_params[:badge_icon_group]

      error = ""
      if attrs[:name].empty?
        error = "Cannot create badge icon group without name"
      end
      if attrs[:photo_1].nil? && attrs[:photo_2].nil? && attrs[:photo_3].nil?
        if error.empty?
          error = "badge icon group should contain at least one badge icon photo."
        else
          error += " and should contain at least one badge icon photo."
        end
      end

      if error.empty?
        @badge_icon_group = BadgeIconGroup.new

        @badge_icon_group.name = attrs[:name]
        @badge_icon_group.circle_id = attrs[:circle_id]

        if @badge_icon_group.save
          unless params[:badge_icon_group][:photo_1].nil?
            @badge_icon_1 = BadgeIcon.new
            @badge_icon_1.color = :GOLD
            @badge_icon_1.badge_icon_group_id = @badge_icon_group.id
            @admin_media_1 = AdminMedium.new(blob_data: params[:badge_icon_group][:photo_1], admin_user: current_admin_user)
            begin
              @admin_media_1.save
              if @admin_media_1.errors.present?
                flash[:error] = "Photo 1 " + @admin_media_1.errors.full_messages.first
              else
                @badge_icon_1.admin_medium_id = @admin_media_1.id
                @badge_icon_1.save
              end
            rescue
              flash[:error] = "Unable to upload photo 1"
            end
          end
          unless params[:badge_icon_group][:photo_2].nil?
            @badge_icon_2 = BadgeIcon.new
            @badge_icon_2.color = :SILVER
            @badge_icon_2.badge_icon_group_id = @badge_icon_group.id

            @admin_media_2 = AdminMedium.new(blob_data: params[:badge_icon_group][:photo_2], admin_user: current_admin_user)
            begin
              @admin_media_2.save
              if @admin_media_2.errors.present?
                flash[:error] = "Photo 2 " + @admin_media_2.errors.full_messages.first
              else
                @badge_icon_2.admin_medium_id = @admin_media_2.id
                @badge_icon_2.save
              end
            rescue
              flash[:error] = "Unable to upload photo 2"
            end
          end
          unless params[:badge_icon_group][:photo_3].nil?
            @badge_icon_3 = BadgeIcon.new
            @badge_icon_3.color = :WHITE
            @badge_icon_3.badge_icon_group_id = @badge_icon_group.id
            @admin_media_3 = AdminMedium.new(blob_data: params[:badge_icon_group][:photo_3], admin_user: current_admin_user)
            begin
              @admin_media_3.save
              if @admin_media_3.errors.present?
                flash[:error] = "Photo 3 " + @admin_media_3.errors.full_messages.first
              else
                @badge_icon_3.admin_medium_id = @admin_media_3.id
                @badge_icon_3.save
              end
            rescue
              flash[:error] = "Unable to upload photo 3"
            end
          end
          redirect_to admin_badge_icon_group_path(@badge_icon_group)
        else
          render :new
        end
      else
        flash[:error] = error
        redirect_to new_admin_badge_icon_group_path
      end
    end
  end
end
