# frozen_string_literal: true
ActiveAdmin.register LeaderProject do
  belongs_to :user

  permit_params :user_id, :user_role_id, :title, :body, :photo, :project_date, :active, :creator_id, :creator_type, :user_role_name, :user_name

  actions :all

  show do
    attributes_table do
      row :id
      row :user
      row :user_role do |leader_project|
        link_to(leader_project.user_role.id, admin_user_role_path(leader_project.user_role))
      end
      row :photo do |leader_project|
        image_tag leader_project.photo_url, class: 'thumb_size' if leader_project.photo.attached?
      end
      row :title
      row :body
      row :project_date
      row :creator do |leader_project|
        link_to(leader_project.creator.email, admin_admin_user_path(leader_project.creator))
      end
      row :creator_type
      row :active
    end
  end

  form :html => { :multipart => true } do |f|
    @user = User.find(f.params["user_id"])

    f.semantic_errors
    f.inputs "Leader Project Details" do
      f.input :user_name, input_html: { disabled: true, value: @user.name }
      if f.object.new_record?
        f.input :user_role, as: :searchable_select, ajax: { resource: UserRole, collection_name: :user_roles, params: {user_id: @user.id} }, input_html: { "data-placeholder": "Search by User role id" }
      else
        f.input :user_role_name, input_html: { disabled: true, value: "#{f.object.user_role_id},   #{f.object.user_role.role.name}"}
      end
      f.input :photo, as: :file, hint: (image_tag(f.object.photo_url, class: 'thumb_size') if f.object.photo.attached?)
      f.input :title
      f.input :body
      f.input :project_date, as: :date_picker
      f.input :creator_id, input_html: { value: current_admin_user.id }, as: :hidden
      f.input :creator_type, input_html: { value: "AdminUser" }, as: :hidden
      f.input :active
    end
    f.actions
  end

  controller do
    def update
      @user = User.find(permitted_params[:user_id])

      attrs = permitted_params[:leader_project]
      @leader_project = LeaderProject.find(params[:id])

      @leader_project.title = attrs[:title]
      @leader_project.body = attrs[:body]
      @leader_project.active = attrs[:active]
      @leader_project.project_date = attrs[:project_date]
      @leader_project.creator_id = attrs[:creator_id]
      @leader_project.creator_type = attrs[:creator_type]

      if attrs[:photo].present?
        key = "#{Rails.env}/leader_projects/#{@leader_project.user_id}/#{Time.now.to_i}_#{attrs[:photo].original_filename}"
        @leader_project.photo.attach(io: attrs[:photo], filename: attrs[:photo].original_filename, key: key )
      end

      if @leader_project.save
        redirect_to admin_user_leader_project_path(@user, @leader_project)
      else
        flash[:error] = @leader_project.errors.full_messages
        render :new
      end
    end

    def create
      @user = User.find(permitted_params[:user_id])

      attrs = permitted_params[:leader_project]
      @leader_project = LeaderProject.new(
        user_id: @user.id,
        user_role_id: attrs[:user_role_id],
        title: attrs[:title],
        body: attrs[:body],
        active: attrs[:active],
        project_date: attrs[:project_date],
        creator_id: attrs[:creator_id],
        creator_type: attrs[:creator_type]

      )

      if attrs[:photo].present?
        key = "#{Rails.env}/leader_projects/#{@user.id}/#{Time.now.to_i}_#{attrs[:photo].original_filename}"
        @leader_project.photo.attach(io: attrs[:photo], filename: attrs[:photo].original_filename, key: key)
      end

      if @leader_project.save
        redirect_to admin_user_leader_project_path(@user, @leader_project)
      else
        flash[:error] = @leader_project.errors.full_messages
        render :new
      end
    end
  end
end
