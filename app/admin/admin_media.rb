ActiveAdmin.register AdminMedium do
  menu :parent => "Admin"
  permit_params :media_file, :admin_user_id, :media_type, :url, :description, :active

  actions :all, except: [:edit, :destroy]

  show do
    attributes_table do
      row :id
      row :media_type
      row :url do |medium|
        link_to(medium.url, medium.url)
      end
      row :media do |medium|
        if medium.photo?
          image_tag medium.url, class: 'thumb_size', style: "margin-right: 5px;"
        else
          video_tag medium.url, controls: true, class: 'thumb_size', style: "margin-right: 5px;"
        end
      end
      row :admin_user
      row :created_at
    end
  end

  index do
    selectable_column
    column :id do |medium|
      link_to(medium.id, admin_admin_medium_path(medium))
    end
    column :media_type do |medium|
      medium.media_type
    end
    column :url do |medium|
      link_to(medium.url, medium.url)
    end
    column :created_at
    column :updated_at
  end

  form multipart: true do |f|
    f.semantic_errors
    f.inputs 'Details' do
      if f.object.new_record?
        f.input :media_file, as: :file, label: 'Media File'
      end
    end
    f.actions
  end

  controller do
    def create
      admin_media = AdminMedium.new(blob_data: params[:admin_medium][:media_file], admin_user: current_admin_user)

      begin
        if admin_media.save
          redirect_to admin_admin_medium_path(admin_media)
        else
          flash[:error] = admin_media.errors.full_messages if admin_media.errors.present?
          redirect_to new_admin_admin_medium_path
        end
      rescue
        flash[:error] = "Unable to upload file"
        redirect_to new_admin_admin_medium_path
      end
    end
  end
end
