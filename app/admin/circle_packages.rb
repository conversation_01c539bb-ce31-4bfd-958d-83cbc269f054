ActiveAdmin.register CirclePackage do
  menu :parent => "Circles"
  permit_params :name, :enable_channel, :enable_fan_posters, :eligibile_for_post_push_notifications, :eligibile_for_wati,
                :fan_poster_creatives_limit, :channel_post_msg_limit, :channel_post_msg_notification_limit

  filter :id, label: "Circle Package ID"
  filter :name, label: "Name"

  actions :all, except: [:edit, :destroy]

  index do
    selectable_column
    id_column
    column :name
    column :created_at
    column :updated_at
    actions
  end

  show do
    attributes_table do
      row :id
      row :name
      row :enable_channel
      row :enable_fan_posters
      row :eligibile_for_post_push_notifications
      row :eligibile_for_wati
      row :fan_poster_creatives_limit
      row :channel_post_msg_limit
      row :channel_post_msg_notification_limit
      row :created_by do |circle_package_entity|
        if circle_package_entity.versions.first.present?
          admin_user = AdminUser.find_by(circle_package_entity.versions.first.whodunnit)
          link_to admin_user.email, admin_admin_user_path(admin_user) if admin_user.present?
        end
      end
      row :last_updated_by do |circle_package_entity|
        if circle_package_entity.versions.last.present?
          admin_user = AdminUser.find_by(circle_package_entity.versions.last.whodunnit)
          link_to admin_user.email, admin_admin_user_path(admin_user) if admin_user.present?
        end
      end
      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs do
      f.input :name, label: "Name", required: true
      f.input :enable_channel, label: "Enable Channel", input_html: { id: 'enable_channel_checkbox' }
      f.input :enable_fan_posters, label: "Enable Fan Posters", wrapper_html: { class: 'no_channel' }, input_html: { id: 'enable_fan_posters_checkbox' }
      f.input :eligibile_for_post_push_notifications, label: "Eligibile for Post Push Notifications"
      f.input :eligibile_for_wati, label: "Eligibile for WATI"
      f.input :fan_poster_creatives_limit, label: "Fan Poster Creatives Limit", wrapper_html: { class: 'no_fan_posters' }
      f.input :channel_post_msg_limit, label: "Channel Post Message Limit", wrapper_html: { class: 'no_channel' }
      f.input :channel_post_msg_notification_limit, label: "Channel Post Message Notification Limit", wrapper_html: { class: 'no_channel' }
    end
    f.actions
  end
end
