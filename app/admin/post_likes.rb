ActiveAdmin.register PostLike do
  belongs_to :post

  permit_params :user

  actions :index, :new

  form do |f|
    @post = Post.find(f.params['post_id'])

    f.semantic_errors
    f.inputs do
      f.input :user,
              label: 'Prime account',
              required: true,
              as: :searchable_select,
              multiple: true,
              ajax: { resource: User, collection_name: :internals_prime, params: { post_id: @post.id } }
    end

    f.actions
  end

  controller do
    def create
      post = Post.find(params['post_id'])
      user_ids = params['post_like']['user_id']

      users = user_ids.map do |user_id|
        next if user_id.empty?

        User.find(user_id)
      end

      begin
        users.each do |user|
          next if user.nil?

          post.do_like(user)
        end
        flash[:notice] = 'Done! Sub-accounts\' trends would start populating.'
      rescue StandardError
        flash[:error] = 'Failed! Unable to add trend.'
      end

      redirect_to admin_post_post_likes_url(post)
    end
  end
end
