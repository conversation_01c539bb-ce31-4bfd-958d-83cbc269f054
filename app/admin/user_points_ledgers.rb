ActiveAdmin.register UserPointsLedger do
  belongs_to :user

  actions :all

  form do |f|
    f.semantic_errors
    f.inputs do
      f.input :points,
              label: 'Points',
              required: true
    end

    f.actions
  end

  controller do
    def create
      @user = User.find(params['user_id'])
      points = params['user_points_ledger']['points'].to_i

      if (@user.get_total_referral_points_earned - @user.get_total_referral_points_paid) >= points
        begin
          UserPointsLedger.create!(user: @user, points: points, action_by: current_admin_user, action: :debit)
          flash[:notice] = "Logged #{points} points as paid!"
        rescue StandardError => e
          flash[:error] = "Failed! Unable to add paid points."
        end

        redirect_to admin_user_path(@user)
      else
        flash[:error] = 'Points paid cannot be more than available points'
        redirect_to new_admin_user_user_points_ledger_path(@user)
      end

    end
  end
end
