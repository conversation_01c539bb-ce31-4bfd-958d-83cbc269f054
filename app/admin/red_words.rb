# frozen_string_literal: true

require 'csv'

ActiveAdmin.register RedWord do
  menu :parent => "Spam Words"
  permit_params :word, :is_hate_speech

  actions :all

  controller do
    def create
      build_resource
      @red_word.admin_user_id = current_admin_user.id
      create!
    end
  end

  index do
    selectable_column
    id_column
    column :word
    column :is_hate_speech
    column :admin_user do |a|
      a.admin_user_id.present? ? a.admin_user : "system"
    end
    column :created_at
    actions
  end

  filter :word
  filter :is_hate_speech
  filter :admin_user_id_null, :as => :boolean, :label => "words by system?"
  filter :admin_user_id

  before_action only: [:do_import] do
    Thread.current['import.admin_user_id'] = current_admin_user.id
  end

  active_admin_import validate: true,
                      csv_options: { col_sep: ',' },
                      ignore: true,
                      after_import: lambda { |importer|
                        RedWord.where(word: importer.values_at('word')).each do |red_word|
                          word = red_word[:word].strip.downcase
                          IndexRedWord.perform_async(red_word[:id], word, red_word[:is_hate_speech])
                        end
                      },
                      before_batch_import: lambda { |importer|
                        words = importer.values_at('word').map { |x| [x, x.strip.downcase] }.to_h
                        importer.batch_replace('word', words)

                        # Add admin user id
                        importer.csv_lines.map { |line| line << Thread.current['import.admin_user_id'] }
                      },
                      template_object: ActiveAdminImport::Model.new(
                        force_encoding: :auto,
                        csv_headers: %w[word is_hate_speech admin_user_id]
                      ),
                      batch_size: 1000

  form multipart: true do |f|
    f.semantic_errors
    f.inputs 'New/Edit RedWord' do
      f.input :word, input_html: { disabled: !f.object.new_record? }
      f.input :is_hate_speech
    end
    f.actions
  end
end
