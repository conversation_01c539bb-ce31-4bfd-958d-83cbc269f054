require "uri"
require "net/http"

ActiveAdmin.register Post do

  controller do
    include MediaServiceUpload
  end

  menu :parent => "Posts"
  permit_params :content, :user_id, :video, :video_thumbnail, :active, :dm_text_message, :send_dm_message_notification, :circle_name, :circle_id,
                post_circle_ids: [], photos: [], tagging_ids: [], approved_declined_items: []

  actions :all, except: [:destroy]

  batch_action :active do |ids|
    batch_action_collection.find(ids).each do |post|
      post.update(active: true)
    end
    redirect_to collection_path, notice: "The posts have been made active!"
  end

  batch_action :in_active do |ids|
    batch_action_collection.find(ids).each do |post|
      post.update(active: false)
    end
    redirect_to collection_path, notice: "The posts have been made inactive!"
  end

  action_item :send_garuda_notification, only: :show do
    link_to "Send Garuda Notification", new_admin_notification_path(path: "praja-app://buzz.praja.app/posts/#{resource.id}")
  end

  action_item :mark_as_probable_premium_user, only: :show do
    unless !resource.user.probable_premium_user? &&
      resource.user.has_premium_layout? &&
      resource.user.premium_pitch.blank?
      link_to "Mark as Probable Premium User", mark_as_probable_premium_user_admin_post_path(resource),
              class: 'activate-button',
              data: { confirm: 'Are you sure you want to add this user as probable premium user' }
    end
  end
  member_action :mark_as_probable_premium_user, method: :get do
    @post = resource
    user = @post.user
    # save user as probable premium user if it's not already present in metadatum
    begin
      Metadatum.find_or_create_by(key: Constants.probable_premium_user_key, entity: user)
      $redis.sadd(Constants.probable_premium_user_key, user.id)
    rescue => e
      return redirect_to admin_post_path(@post), alert: "Error: #{e.message}"
    end
    redirect_to admin_post_path(@post), notice: "User marked as probable premium user"
  end

  action_item :send_to_channel, only: :show do
    if current_admin_user.admin_role? && resource.active?
      user = resource.user
      ucp_as_owner = user.get_user_circle_permission_as_owner
      if ucp_as_owner.present?
        is_circle_tagged = resource.post_circles.map(&:circle_id).include?(ucp_as_owner.circle_id)
        if is_circle_tagged
          circle = ucp_as_owner.circle
          if circle.present? && circle.channel_conversation_type?
            link_to "Send to Channel", send_to_channel_admin_post_path(resource, circle_id: circle.id, circle_name: circle.name)
          end
        end
      end
    end
  end

  member_action :send_to_channel, method: :get do
    @post = resource
    render 'send_post_to_channel', params: { post: { circle_id: params[:circle_id], circle_name: params[:circle_name] } }
  end

  member_action :send_post_to_channel, method: :post do
    attrs = params[:post]

    if resource.id.blank? || attrs[:circle_id].blank?
      return redirect_to admin_post_path(resource), alert: "Post id or circle id is missing"
    end

    send_post_msg_hash = {
      post_id: resource.id,
      circle_id: attrs[:circle_id],
      text: attrs[:dm_text_message],
      send_dm_message_notification: attrs[:send_dm_message_notification].to_i == 1
    }

    SendDmPostMessage.perform_async(send_post_msg_hash.to_json)
    return redirect_to admin_post_path(resource), notice: "Post sent to channel"
  end

  index pagination_total: false do
    selectable_column

    column :id do |post|
      link_to(post.id, admin_post_path(post))
    end
    column :content do |post|
      post.content.truncate(57, separator: ' ') if post.content.present?
    end
    column :user_id do |post|
      link_to(post.user.name, admin_user_path(post.user))
    end
    column :fantom_account do |post|
      u_phone = post.user.phone
      if User.is_internal(u_phone)
        "Yes"
      else
        span :style => 'color: red' do
          "No"
        end
      end
    end
    column :badge_user do |post|
      if post.user.get_badge_role.present?
        span :style => 'color: red' do
          "Yes"
        end
      else
        "No"
      end
    end
    column :circles do |post|
      if post.circles.length > 0
        circles = post.circles
        circles.each do |circle|
          link_to(circle.name, admin_circle_path(circle))
        end
      else
        "Public"
      end
    end
    column :parent_post_id do |post|
      (!post.parent_post_id.nil? && post.parent_post_id > 0) ? link_to("Post ##{post.parent_post_id}", admin_post_path(post.parent_post)) : ""
    end
    column :active
    column :news_worthy_score do |post|
      post.news_worthy_score.present? ? post.news_worthy_score : "Nil"
    end
    column :tags_status do |post|
      if post.taggings.pending.present?
        span :style => 'color: red' do
          "Pending"
        end
      end
    end
    column :created_at
    actions default: true, name: "Actions" do |post|
      link_to 'Add Trends', new_admin_post_post_like_url(post) if current_admin_user.content_creator_role?
    end
  end

  show do |post|
    attributes_table do
      row :id
      row :content
      row :mobile_link do |p|
        link = "https://m.praja.buzz/posts/#{p.hashid}"
        link_to(link, link)
      end
      row :singular_long_link do |p|
        link = "https://prajaapp.sng.link/A3x5b/oe88?_dl=praja%3A%2F%2Fposts/#{p.id}&_ddl=praja%3A%2F%2Fposts/#{p.id}"
        link_to(link, link)
      end
      row :notification_deep_link do |p|
        "praja-app://buzz.praja.app/posts/#{p.id}"
      end
      row :user
      row :parent_post
      row :circles do |p|
        p.circles.map do |circle|
          link_to(circle.name, admin_circle_path(circle))
        end
      end
      row :photos do |p|
        p.photos.map do |photo|
          image_tag photo.url, class: 'thumb_size', style: "margin-right: 5px;" if photo.present?
        end
      end
      row :video do |p|
        p.videos.map do |video|
          video_tag (video.source_url || video.url), controls: true, class: 'thumb_size',
                    style: "margin-right: 5px;" if video.present?
        end
      end
      row :active
      row "Tags" do |p|
        p.taggings.approved.map do |tagging|
          tagging.tag.identifier
        end
      end
      row "Pending Tags" do |p|
        p.taggings.pending.map do |tagging|
          tagging.tag.identifier
        end
      end
      row :is_poll
      row :created_at
      row :updated_at
      row :av_count do |p|
        p.get_users_views_count
      end
      row :at_count do |p|
        p.likes.joins(:user).where(users: { internal: false }).count
      end
      row :iv_count do |p|
        p.get_fantom_users_views_count
      end
      row :it_count do |p|
        p.likes.joins(:user).where(users: { internal: true }).count
      end
      # row :trend_by do |p|
      #   select :trend_by, collection: options_for_select(User.joins("INNER JOIN user_groups ON user_groups.user_id = users.id").active.where("user_groups.active = 1").where("users.internal = 1 OR users.phone LIKE '2%' OR users.phone LIKE '3%' OR users.phone LIKE '4%'"))
      # end
    end

    panel "Comments" do
      table_for PostComment.where(post: post, active: true).order(id: :desc) do
        column("ID") { |pc| link_to(pc.id, admin_post_comment_path(pc)) }
        column("Comment") { |pc| pc.comment.truncate(57, separator: ' ') }
        column("User") { |pc| link_to(pc.user.name, admin_user_path(pc.user)) }
        column("Created at") { |pc| pc.created_at }
      end
    end

    panel "Edit History" do
      table_for PaperTrail::Version.where(item_type: "Post", item_id: post.id).order(id: :desc).limit(10) do
        column("Item") { |v| v.item }
        column("Modified at") { |v| v.created_at }
        column("Admin") do |v|
          if v.whodunnit.nil?
            ""
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    active_admin_comments
  end

  form multipart: true do |f|
    f.semantic_errors
    f.inputs "Post Details" do
      if f.object.new_record?
        f.input :content
        f.input :post_circle_ids, label: 'Post Circle/ Circles', as: :searchable_select, multiple: true, ajax: { resource: Circle, collection_name: :post_circles }, input_html: { "data-placeholder": "Search by Circle Id or Name or Short name" }
        f.input :user, as: :searchable_select, ajax: { resource: User, collection_name: :internals }
        f.input :photos, as: :file, input_html: { multiple: true }
        f.input :video, as: :file
        f.input :video_thumbnail, as: :file
        f.input :tagging_ids,
                label: 'Tagging (optional)',
                as: :searchable_select,
                multiple: true,
                collection: Tag.all.map { |tag| [tag.identifier, tag.id] },
                input_html: { class: 'admin-tagging-select', "data-placeholder": "Search by Tagging Id or Tag" }
      else
        pendinq_taggings = f.object.taggings.pending
        f.input :active
        f.input :tagging_ids,
                label: 'Tagging',
                as: :searchable_select,
                multiple: true,
                collection: Tag.all.map { |tag| [tag.identifier, tag.id] },
                input_html: { class: 'admin-tagging-select', "data-placeholder": "Search by Tagging Id or Tag" },
                include_blank: false,
                selected: f.object.taggings.approved.pluck(:tag_id)
        f.input :approved_declined_items, as: :hidden, input_html: { id: 'approved-declined-items' }
        div class: 'custom-tags-input' do
          div class: 'tag-with-buttons' do
            table class: 'tag-table' do
              if pendinq_taggings.present?
                tr do
                  td class: 'tag-table-column-text' do
                    text_node "AI Generated Tags"
                  end
                end
              end
              f.object.taggings.pending.each do |tagging|
                tr do
                  td class: 'tag-table-column-text' do
                    tagging.tag.identifier
                  end
                  td class: 'tag-table-column' do
                    button type: 'button', class: 'approve-button', value: tagging.tag_id, 'data-tag-id' => tagging.tag_id do
                      text_node "&#10004;".html_safe
                    end
                  end
                  td class: 'tag-table-column' do
                    button type: 'button', class: 'decline-button', value: tagging.tag_id, 'data-tag-id' => tagging.tag_id do
                      text_node "&#10008;".html_safe
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
    f.actions
  end

  controller do
    def scoped_collection
      Post.includes(:circles, user: { user_roles: :role })
    end

    def update
      attrs = permitted_params[:post]
      
      tagging_ids = attrs[:tagging_ids]
      tagging_ids.delete_at(0)
      tagging_ids = tagging_ids.map(&:to_i)

      approved_declined_items_json = params[:post][:approved_declined_items]
      if approved_declined_items_json.present?
        approved_declined_items = JSON.parse(approved_declined_items_json)
      else
        approved_declined_items = { "approved"=>[], "declined"=>[] }
      end

      @post = Post.find_by_hashid!(params[:id])
      @post.active = attrs[:active]

      approved_tag_ids = approved_declined_items["approved"].map(&:to_i)
      declined_tag_ids = approved_declined_items["declined"].map(&:to_i)

      post_taggings = @post.taggings
      existing_post_tag_ids = post_taggings.pluck(:tag_id)
      already_approved_tag_ids = post_taggings.approved.pluck(:tag_id)

      new_tag_ids = approved_tag_ids - existing_post_tag_ids
      approve_tags = approved_tag_ids - new_tag_ids
      delete_tags = already_approved_tag_ids - tagging_ids
      decline_tags = declined_tag_ids + delete_tags

      new_tag_ids.each do |tag_id|
        @post.taggings.build(tag_id: tag_id, status: :approved)
      end

      if @post.save!
        approve_tags.each do |tag_id|
          @post.taggings.find_by(tag_id: tag_id)&.update(status: :approved)
        end

        decline_tags.each do |tag_id|
          @post.taggings.find_by(tag_id: tag_id)&.update(status: :declined)
        end

        redirect_to admin_post_path(@post)
      else
        render :edit
      end
    end

    def create
      attrs = permitted_params[:post]

      attrs[:photos] = attrs[:photos].filter(&:present?)
      attrs[:post_circle_ids] = attrs[:post_circle_ids].filter(&:present?).map(&:to_i)
      attrs[:tagging_ids] = attrs[:tagging_ids].filter(&:present?).map(&:to_i)

      # upload photos to gcp
      if attrs[:photos].present?
        attrs[:media_service_photos] = upload_photos(attrs[:photos], attrs[:user_id])
        if attrs[:media_service_photos].blank?
          return redirect_to new_admin_post_path, alert: "Photos upload to media service failed"
        end
      end

      # upload video & thumbnail to media service
      if attrs[:video].present?
        video_size = attrs[:video].size / (1024.0 * 1024.0)

        if video_size <= 150.0
          attrs[:media_service_videos] = upload_video(attrs[:video], attrs[:video_thumbnail], attrs[:user_id])
          if attrs[:media_service_videos].blank?
            return redirect_to new_admin_post_path, alert: "Video & thumbnail upload to media service failed"
          end
        else
          return redirect_to new_admin_post_path, alert: "Video size cannot be more than 150MB"
        end
      end

      @post = Post.new
      @post.content = attrs[:content]
      @post.user_id = attrs[:user_id]

      # create post_photos
      if attrs[:media_service_photos].present?
        attrs[:media_service_photos].each do |photo_data|
          @post.post_photos.build(photo: Photo.new(ms_data: photo_data, user_id: attrs[:user_id].to_i, service: photo_data[:service]))
        end
      end

      # create post_videos
      if attrs[:media_service_videos].present?
        @post.post_videos.build(video: Video.new(ms_data: attrs[:media_service_videos], user_id: attrs[:user_id].to_i, hash_key: (attrs[:hash_key] || attrs[:file_id]), service: attrs[:media_service_videos][:service]))
      end

      # create post_circles
      circle_ids = []
      circle_ids = attrs[:post_circle_ids] if attrs[:post_circle_ids].present?
      circle_ids.each do |c_id|
        @post.post_circles.build(post: @post, circle: Circle.find_by(id: c_id))
      end

      # parse hashtags from content of post
      parsed_hashtags = attrs[:content].scan(/(?:\s|^)(?:#(?!(?:\d+|\S+?_|_\S*?)(?:\s|$)))(\S+)(?=\s|$)/i)
      parsed_hashtags = parsed_hashtags.map { |h| h[0] }

      # create post_hashtags if new
      parsed_hashtags.each do |hashtag|
        next if @post.post_hashtags.map { |hh| hh.tag }.include?("#" + hashtag)

        h = Hashtag.where(identifier: hashtag.downcase, active: true).first_or_create(name: hashtag)
        @post.post_hashtags.build(post: @post,
                                  hashtag: h,
                                  tag: "#" + hashtag)
      end

      # create taggings
      if attrs[:tagging_ids].present?
        attrs[:tagging_ids].each do |tag_id|
          @post.taggings.build(tag_id: tag_id, status: :approved)
        end
      end

      # add post os type in metadata
      @post.metadatum.build(key: "post_os", value: "admin")

      begin
        @post.save!
        redirect_to admin_post_path(@post)
        return
      rescue => e
        flash[:error] = @post.errors.full_messages.join(" మరియు ") || e.message
        return render :new
      end
    end

    def upload_photos(photos, user_id)
      form_data = photos.map do |photo|
        ['photos', photo]
      end

      photos_response = upload_to_media_service(form_data, user_id, "photos")
      return photos_response["files"] if photos_response.present?
    end

    def upload_video(video, thumbnail, user_id)
      if thumbnail.blank?
        form_data = [["video", video]]
      else
        form_data = [["video", video], ["thumbnail", thumbnail]]
      end
      upload_to_media_service(form_data, user_id, "video")
    end

  end

  filter :id
  # filter :hashid
  # filter :content
  filter :user_id, label: "User ID"
  filter :parent_post_id, label: "Parent Post ID"
  # filter :is_fantom_account, as: :boolean
  # filter :is_poll
  filter :active
  filter :badge_user, label: 'Badge user posts', as: :select, collection: [:yes, :no], filters: [:eq]
  filter :badge_color, label: 'Badge color', as: :select, collection: Role.badge_colors, filters: [:eq]
  filter :user_grade, label: 'User Grade', as: :select, collection: Role.grade_levels, filters: [:eq]
  filter :user_role_id, label: 'User Role', as: :numeric, filters: [:eq]
  filter :user_district_id, label: "User's District ID", as: :numeric, filters: [:equals]
  filter :pending_tags, label: 'Pending Tags', as: :select, collection: [:yes, :no], filters: [:eq]
  filter :created_at
  filter :updated_at
end
