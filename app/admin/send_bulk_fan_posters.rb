ActiveAdmin.register_page "Send Bulk Fan Posters" do
  menu priority: 5, :parent => 'Posters V2',
       if: proc{ role = current_admin_user.role.to_sym.in?([:admin, :sales_am, :op_executive]) }
  page_action :submit_form, method: :post do
    attrs = params[:send_bulk_fan_poster]
    send_dm_message_notification = attrs[:send_dm_message_notification]
    dm_text_message = attrs[:dm_text_message]
    circle_ids = attrs[:circle_ids].compact_blank
    send_to_leaders = attrs[:send_to_leader_circles].to_i == 1

    if attrs[:photo].blank?
      return redirect_to admin_send_bulk_fan_posters_path, alert: "Please upload photo"
    end

    poster_creative = PosterCreative.new(creative_kind: :info,
                                         photo_v3: AdminMedium.new(blob_data: attrs[:photo], admin_user_id: current_admin_user.id),
                                         paid: false,
                                         primary: false,
                                         h1_leader_photo_ring_type: :light,
                                         h2_leader_photo_ring_type: :sticker,
                                         start_time: attrs[:start_time],
                                         end_time: attrs[:end_time],
                                         active: true,
                                         creator: current_admin_user,
                                         send_dm_message: true,
                                         send_dm_message_notification: send_dm_message_notification == '1',
                                         dm_text_message: dm_text_message
    )

    poster_creative_circle_ids = []
    if circle_ids.blank?
      level = send_to_leaders ? :political_leader : :political_party
      poster_creative_circle_ids +=  Circle.where(level: level).pluck(:id)
    else
      if send_to_leaders
        circle_ids.each do |circle_id|
          leader_circle_ids = CirclesRelation.where(second_circle_id: circle_id, relation: :Leader2Party).pluck(:first_circle_id)
          poster_creative_circle_ids += leader_circle_ids
        end
      else
        poster_creative_circle_ids += circle_ids
      end
    end

    poster_creative_circle_ids.each do |circle_id|
      poster_creative.poster_creative_circles.build(poster_creative_id: poster_creative.id, circle_id: circle_id)
    end

    if poster_creative.save
      redirect_to admin_poster_creative_path(poster_creative), notice: "Fan Posters created and scheduled"
    else
      redirect_to admin_send_bulk_fan_posters_path, alert: "Failed to create fan posters"
    end
  end

  content title: 'Send Bulk Fan Posters' do
    panel 'Send Posters' do
      active_admin_form_for :send_bulk_fan_poster, url: admin_send_bulk_fan_posters_submit_form_path, method: :post, html: { multipart: true } do |f|
        f.inputs do
          f.input :photo, as: :file, required: true, hint: 'aspect ratio - 800x1000'
          f.input :send_dm_message_notification, as: :boolean, required: true
          f.input :dm_text_message, as: :string, required: false
          f.input :send_to_leader_circles, label: "Circle level", as: :select, collection: [['Party Circles', 0], ['Leader Circles', 1]], required: true
          f.input :circle_ids, label: 'Party Circle ID', as: :searchable_select, multiple: true, ajax: { resource: Circle, collection_name: :party_circles }, required: false, hint: "Leave blank to send to all circles of above specified level "
          f.input :start_time, as: :string, input_html: { type: 'datetime-local', step: 1 }, label: 'Poster start time', required: false
          f.input :end_time, as: :string, input_html: { type: 'datetime-local', step: 1 }, label: 'Poster end time', required: false
        end
        f.actions do
          f.submit 'Send Bulk Fan Posters'
        end
      end
    end
  end
end
