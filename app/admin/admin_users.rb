ActiveAdmin.register AdminUser do
  menu :parent => "Admin"
  permit_params :name, :phone, :email, :role, :floww_user_id, :admin_medium, :password, :password_confirmation, :active

  searchable_select_options(name: :admin_users,
                            scope: ->  { AdminUser.all
                            },
                            display_text: ->(record) {
                              "#{record.id},#{record.email},"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(id_eq: term, email_cont: term, m: 'or').result
                            end
  )
  searchable_select_options(name: :designers,
                            scope: -> { AdminUser.where(role: :graphic_designer)
                            },
                            display_text: ->(record) {
                              "#{record.id},#{record.name},"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(id_eq: term, email_cont: term, m: 'or').result
                            end
  )
  controller do
    before_action :check_admin_user, only: [:new, :edit, :destroy]

    def current_user
      current_admin_user
    end

    def check_admin_user
      begin
        authorize current_admin_user, :can_manage_admin_users?
      rescue Pundit::NotAuthorizedError
        redirect_to admin_admin_users_path, alert: 'You are not authorized to perform this action.'
      end
    end

    def create
      attrs = permitted_params[:admin_user]
      @admin_user = AdminUser.new(attrs.except(:admin_medium))

      if @admin_user.save
        unless attrs[:admin_medium].nil?
          @admin_medium = AdminMedium.new(blob_data: attrs[:admin_medium], admin_user: current_admin_user)
          if @admin_medium.save
            @admin_user.update(admin_medium: @admin_medium)
            redirect_to admin_admin_user_path(@admin_user), notice: "Admin user and medium created successfully"
          else
            flash[:error] = "Unable to save admin medium: #{@admin_medium.errors.full_messages.join(', ')}"
            render :new
          end
        else
          redirect_to admin_admin_user_path(@admin_user), notice: "Admin user created successfully"
        end
      else
        flash[:error] = "Unable to save admin user: #{@admin_user.errors.full_messages.join(', ')}"
        render :new
      end
    end

    def update
      attrs = permitted_params[:admin_user]

      if attrs[:password].blank?
        attrs.delete(:password)
        attrs.delete(:password_confirmation)
      end

      @admin_user = AdminUser.find(params[:id])
      if attrs[:admin_medium].present?
        @admin_medium = AdminMedium.new(blob_data: attrs[:admin_medium], admin_user: current_admin_user)
        if @admin_medium.save
          @admin_user.update(admin_medium: @admin_medium)
          attrs.delete(:admin_medium)
        else
          render :edit
        end
      end
      if @admin_user.update(attrs)
        redirect_to admin_admin_user_path(@admin_user), notice: 'Admin user was successfully updated.'
      else
        render :edit
      end
    end
  end

  index do
    selectable_column
    id_column
    column :name
    # column :email
    column :role
    column :floww_user_id
    column :active
    column :current_sign_in_at
    column :sign_in_count
    column :created_at
    actions name: "Actions"
  end

  filter :id
  filter :email
  filter :role, as: :select, collection: AdminUser.roles
  filter :active
  filter :current_sign_in_at
  filter :sign_in_count
  filter :created_at

  form do |f|
    f.inputs do
      f.input :name, required: true
      f.input :phone, label: "Phone(WhatsApp Phone)", required: true
      f.input :email
      f.input :role, required: true,
              as: :select,
              collection: AdminUser.roles.keys,
              include_blank: false
      f.input :floww_user_id
      f.input :active
      f.input :admin_medium, as: :file
      f.input :password
      f.input :password_confirmation
    end
    f.actions
  end

  show do
    attributes_table do
      row :name
      row "Phone (Whatsapp Phone)" do |au|
        au.phone
      end
      row :email
      row :role
      row :floww_user_id
      row :admin_medium_id do |am|
        image_tag am.admin_medium.url, class: 'thumb_size' if am.admin_medium.present?
      end
      row :active
      row :current_sign_in_at
      row :sign_in_count
      row :created_at
      row :updated_at
    end

    panel 'Edit History' do
      table_for PaperTrail::Version.where(item_type: 'AdminUser', item_id: admin_user.id).order(id: :desc).limit(5) do
        column('Item') { |v| v.item }
        column('Changes') do |v|
          if v.object_changes
            changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
            filtered_changes = changes.reject { |field, _| field == "created_at" || field == "updated_at" || field == "encrypted_password" }
            filtered_changes.map do |field, values|
              old_value = values[0].nil? ? 'nil' : values[0].to_s
              new_value = values[1].nil? ? 'nil' : values[1].to_s
              "#{field}: #{old_value} -> #{new_value}"
            end.join(', ')
          else
            'No changes recorded'
          end
        end
        column('Modified at') { |v| v.created_at }
        column('Admin') do |v|
          if v.whodunnit.nil?
            ''
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end
  end

end
