require 'csv'
ActiveAdmin.register UserCirclePermissionGroup do
  menu :parent => "Permissions"
  includes :circle, :user, :permission_group
  permit_params :user_id, :circle_id, :permission_group_id

  filter :user_id
  filter :circle_id
  filter :permission_group_id

  #TODO: Disabling edit, delete options for user_circle_permission_groups wrt auto unfollow discussion.Could revisit later.
  actions :all, except: [:edit, :destroy]

  #import user circle permission groups

  action_item :only => :index do
    link_to 'Import User Circle PermissionGroups', :action => 'upload_csv'
  end

  collection_action :upload_csv do
    render "upload_file/upload_csv_file"
  end

  collection_action :import_csv, :method => :post do
    add_user_circle_permission_groups(params[:file])
    redirect_to :action => :index, :notice => "CSV imported successfully!"
  end

  form do |f|
    f.inputs do
      f.input :user_id, required: true, input_html: { disabled: !f.object.new_record? }, label: "User ID"
      f.input :circle_id, required: true, input_html: { disabled: !f.object.new_record? }, label: "Circle ID"
      f.input :permission_group, as: :select, collection: PermissionGroup.all.map { |permission_group| [permission_group.name, permission_group.id] },
              label: "Permission Group", required: true

    end
    f.actions
  end

  controller do
    def add_user_circle_permission_groups(csv_data)
      csv_file = csv_data.read
      CSV::Converters[:blank_to_nil] = lambda do |field|
        field && field.empty? ? nil : field
      end
      csv = CSV.new(csv_file, :headers => true, :header_converters => :symbol, :converters => [:all, :blank_to_nil])
      #converting to an array of hashes
      csv_hash = csv.to_a.map { |row| row.to_hash }
      csv_hash.each do |row|
        user_id = row[:user_id]
        circle_id = row[:circle_id]
        permission_group_id = row[:permission_group_id]

        if user_id.present? && circle_id.present? && permission_group_id.present?
          UserCirclePermissionGroup.create!(user_id: user_id, circle_id: circle_id, permission_group_id: permission_group_id)
        end
      end
    end
  end
end
