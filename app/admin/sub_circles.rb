# frozen_string_literal: true

ActiveAdmin.register SubCircle do

  belongs_to :circle
  actions :all, except: [:destroy]
  permit_params :name, :name_en, :photo, :conversation_type,  :active, filter_role_ids: [], filter_location_circle_ids: [], filter_grade_levels: []

  index do
    selectable_column
    column :id do |circle|
      link_to(circle.id, admin_circle_sub_circle_path(circle.parent_circle, circle))
    end
    column :name
    column :name_en
    column :parent_circle_id do |circle|
      (!circle.parent_circle_id.nil? && circle.parent_circle_id > 0) ? link_to(circle.parent_circle.name, admin_circle_path(circle.parent_circle)) : ""
    end
    column :filter_roles do |circle|
      role_ids = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
        if filter.filter_key == "filter_role_id"
          role = Role.where(id: filter.filter_value).first
          if role.present?
            link_to(role.searchable_select_name_for_sub_circles, admin_role_path(role))
          end
        end
      end
      role_ids.compact.join(",").html_safe
    end

    column :filter_location_circles do |circle|
      location_ids = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
        if filter.filter_key == "filter_location_circle_id"
          location = Circle.where(id: filter.filter_value).first
          place_holder = location.searchable_select_name_for_sub_circles
          if location.present?
            link_to(place_holder, admin_circle_path(location))
          end
        end
      end
      location_ids.compact.join(",").html_safe
    end

    column :filter_grade_levels do |circle|
      grade_levels = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
        if filter.filter_key == "filter_grade_level"

          Role::GRADE_LEVEL.key(filter.filter_value.to_i)
        end
      end
      grade_levels.compact.join(",").html_safe
    end

    column :active
    # column :created_at
    # column :updated_at
  end

  form do |f|
    parent_circle = f.object.parent_circle || Circle.find(params[:circle_id])
    existing_filters = SubCircleFilter.where(sub_circle_id: f.object.id)
    existing_role_ids = []
    existing_location_circle_ids = []
    existing_grade_level_filters = []
    existing_filters.each do |filter|
      if filter.filter_key == "filter_role_id"
        existing_role_ids << filter.filter_value
      elsif filter.filter_key == "filter_grade_level"
        existing_grade_level_filters << filter.filter_value
      elsif filter.filter_key == "filter_location_circle_id"
        existing_location_circle_ids << filter.filter_value
      end
    end
    roles_with_parent_circles = Role.includes(:parent_circle).where(id: existing_role_ids)
    existing_location_circles = Circle.includes(parent_circle: :parent_circle).where(id: existing_location_circle_ids)
    preloaded_roles_data = roles_with_parent_circles.map do |role|
      {
        id: role.id,
        name: role.searchable_select_name_for_sub_circles
      }
    end
    preloaded_circles_data = existing_location_circles.map do|circle|
      {
        id: circle.id,
        name: circle.searchable_select_name_for_sub_circles
      }
    end
    preloaded_grade_levels = existing_grade_level_filters.map do |grade_level|
      {
        id: grade_level,
        name: Role::GRADE_LEVEL.key(grade_level.to_i)
      }
    end
    f.semantic_errors
    f.inputs do
      f.input :name,
              label: 'Name',
              required: true
      f.input :name_en,
              label: 'Name EN',
              required: true
      f.input :photo, as: :file, hint: (image_tag(f.object.photo.url, class: 'thumb_size') if f.object.photo.present?)
      f.input :conversation_type,
              as: :select,
              collection: Circle.conversation_types.keys - ['private_group'],
              include_blank: false,
              default: Circle.conversation_types[:none],
              input_html: { disabled: f.object.new_record? }
      f.input :filter_grade_levels,
              as: :searchable_select,
              collection: Role.grade_levels,
              multiple: true,
              input_html: {
                class: 'filter_grade_levels_field',
                "data-placeholder": "Select Grade Levels",
                "data-preselected-grade-levels": preloaded_grade_levels.to_json
              }
      f.input :filter_role_ids,
              label: 'Filter Roles',
              as: :searchable_select,
              multiple: true,
              ajax: { resource: Role, collection_name: :sub_circle_filter_roles },
              input_html: {
                class: 'filter_role_ids_field',
                "data-placeholder": "Search by Role Id or Name",
                "data-preselected-roles": preloaded_roles_data.to_json
              }
      f.input :filter_location_circle_ids,
              label: 'Filter Location Circles',
              as: :searchable_select,
              multiple: true,
              ajax: { resource: Circle, collection_name: :sub_circle_filter_location_circles },
              input_html: {
                class: 'filter_location_circle_ids_field',
                "data-placeholder": "Search by Circle Id or Name",
                "data-preselected-circles": preloaded_circles_data.to_json
              }

      f.input :active

    end
    f.actions
  end

  controller do
    def update
      ActiveRecord::Base.transaction do
        errors = ""
        attrs = permitted_params[:sub_circle]
        @admin = User.find_by_phone(9999999999)
        @parent_circle = Circle.find_by_hashid!(permitted_params[:circle_id])
        @sub_circle = SubCircle.find_by_hashid!(params[:id])
        # Assign attributes
        @sub_circle.assign_attributes(attrs.slice(:name, :name_en, :conversation_type))
        @sub_circle.photo = Photo.upload(attrs[:photo], @admin) if attrs[:photo].present?
        @sub_circle.parent_circle_id = @parent_circle.id
        @sub_circle.circle_type = :sub
        @sub_circle.level = :sub
        @sub_circle.active = true

        # Clean up filter IDs
        attrs[:filter_location_circle_ids].reject!(&:empty?)
        attrs[:filter_role_ids].reject!(&:empty?)
        attrs[:filter_grade_levels].reject!(&:empty?)


        # Process current filters
        current_filters = SubCircleFilter.where(sub_circle_id: @sub_circle.id).group_by(&:filter_key)
        current_filter_role_ids = current_filters['filter_role_id']&.map(&:filter_value) || []
        current_filter_location_circle_ids = current_filters['filter_location_circle_id']&.map(&:filter_value) || []
        current_filter_grade_levels = current_filters['filter_grade_level']&.map(&:filter_value) || []
        if attrs[:filter_location_circle_ids].blank? && attrs[:filter_role_ids].blank? && attrs[:filter_grade_levels].blank? && current_filter_role_ids.blank? && current_filter_location_circle_ids.blank? && current_filter_grade_levels.blank?
          errors = 'Filters cannot be empty'
        end
        # Determine filters to remove
        circle_ids_to_remove = current_filter_location_circle_ids - attrs[:filter_location_circle_ids].map(&:to_s)
        role_ids_to_remove = current_filter_role_ids - attrs[:filter_role_ids].map(&:to_s)
        grade_levels_to_remove = current_filter_grade_levels - attrs[:filter_grade_levels].map(&:to_s)

        # Remove filters
        @sub_circle.sub_circle_filters.where(filter_key: 'filter_role_id', filter_value: role_ids_to_remove).destroy_all
        @sub_circle.sub_circle_filters.where(filter_key: 'filter_location_circle_id', filter_value: circle_ids_to_remove).destroy_all
        @sub_circle.sub_circle_filters.where(filter_key: 'filter_grade_level', filter_value: grade_levels_to_remove).destroy_all

        # Build new filters
        attrs[:filter_role_ids].map(&:to_s).each do |filter_role_id|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_role_id', filter_value: filter_role_id) unless current_filter_role_ids.include?(filter_role_id)
        end

        attrs[:filter_grade_levels].map(&:to_s).each do |filter_grade_level|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_grade_level', filter_value: filter_grade_level) unless current_filter_grade_levels.include?(filter_grade_level)
        end

        attrs[:filter_location_circle_ids].map(&:to_s).each do |filter_location_circle_id|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_location_circle_id', filter_value: filter_location_circle_id) unless current_filter_location_circle_ids.include?(filter_location_circle_id)
        end


        if errors.blank? && @sub_circle.save
          flash[:notice] = 'Sub Circle updated successfully'
          redirect_to admin_circle_sub_circle_path(@parent_circle, @sub_circle)
        else
          flash[:error] = errors
          redirect_to edit_admin_circle_sub_circle_path(@parent_circle, @sub_circle)
        end
      end

    end
    def create
      attrs = permitted_params[:sub_circle]
      @admin = User.find_by_phone(9999999999)
      @parent_circle = Circle.find_by_hashid!(permitted_params[:circle_id])
      @sub_circle = SubCircle.new
      @sub_circle.name = attrs[:name]
      @sub_circle.name_en = attrs[:name_en]
      @sub_circle.conversation_type = attrs[:conversation_type]
      @sub_circle.photo = Photo.upload(attrs[:photo], @admin) unless attrs[:photo].nil?
      @sub_circle.parent_circle_id = @parent_circle.id
      @sub_circle.circle_type = :sub
      @sub_circle.level = :sub
      @sub_circle.active = true
      @sub_circle.filter_location_circle_ids = attrs[:filter_location_circle_ids]
      @sub_circle.filter_role_ids = attrs[:filter_role_ids]

      if attrs[:filter_location_circle_ids].present?
        attrs[:filter_location_circle_ids].reject(&:empty?).each do |filter_location_circle_id|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_location_circle_id', filter_value: filter_location_circle_id)
        end
      end
      if attrs[:filter_grade_levels].present?
        attrs[:filter_grade_levels].reject(&:empty?).each do |filter_grade_level|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_grade_level', filter_value: filter_grade_level)
        end
      end
      if attrs[:filter_role_ids].present?
        attrs[:filter_role_ids].reject(&:empty?).each do |filter_role_id|
          @sub_circle.sub_circle_filters.build(filter_key: 'filter_role_id', filter_value: filter_role_id)
        end
      end

      error = ""
      if attrs[:filter_location_circle_ids].blank? && attrs[:filter_role_ids].blank? && attrs[:filter_grade_levels].blank?
        error = "filters cannot be empty"
      end

      if !error.present? && @sub_circle.save
        flash[:notice] = 'Sub Circle created successfully'
        redirect_to admin_circle_sub_circle_path(@parent_circle, @sub_circle)
      else
        flash[:error] = error.present? ? error : @sub_circle.errors.full_messages.to_sentence
        redirect_to new_admin_circle_sub_circle_path(@parent_circle.id)
      end
    end
  end

  show do |circle|
    attributes_table do
      row :id
      row :name
      row :name_en
      row :parent_circle
      row :photo do |c|
        image_tag c.photo.url, class: 'thumb_size' if c.photo.present?
      end
      row :conversation_type
      row :active
      row :circle_type
      row :level
      row :conversation_type
      row :active
      row :created_at
      row :updated_at
      row :filter_roles do |circle|
        role_ids = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
          if filter.filter_key == "filter_role_id"
            role = Role.where(id: filter.filter_value).first
            if role.present?
              link_to(role.searchable_select_name_for_sub_circles, admin_role_path(role))
            end
          end
        end
        role_ids.compact.join(",").html_safe
      end
      row :filter_location_circles do |circle|
        location_ids = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
          if filter.filter_key == "filter_location_circle_id"
            location = Circle.where(id: filter.filter_value).first
            place_holder = location.searchable_select_name_for_sub_circles
            if location.present?
              link_to(place_holder, admin_circle_path(location))
            end
          end
        end
        location_ids.compact.join(",").html_safe
      end
      row :filter_grade_levels do |circle|
        grade_levels = SubCircleFilter.where(sub_circle_id: circle.id).map do |filter|
          if filter.filter_key == "filter_grade_level"

            Role::GRADE_LEVEL.key(filter.filter_value.to_i)
          end
        end
        grade_levels.compact.join(",").html_safe
      end
    end

    panel "Edit History" do
      table_for PaperTrail::Version.where(item_type: "Circle", item_id: circle.id).order(id: :desc).limit(10) do
        column("Item") { |v| v.item }
        column("Modified at") { |v| v.created_at }
        column("Admin") do |v|
          if v.whodunnit.nil?
            ""
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    active_admin_comments
  end

  filter :id
  filter :name
  filter :name_en
  filter :parent_circle_id, label: "Parent Circle ID"
  filter :conversation_type, as: :select, collection: Circle.conversation_types
  filter :active
end
