ActiveAdmin.register Report do
  actions :index

  index do
    column :id
    column :reference do |report|
      if report.reference_type == "Post"
        link_to("Post ##{report.reference_id}", admin_post_path(report.reference))
      elsif report.reference_type == "User"
        link_to("User ##{report.reference_id}", admin_user_path(report.reference))
      elsif report.reference_type == "PostComment"
        link_to("PostComment ##{report.reference_id}", admin_post_comment_path(report.reference))
      else
        ""
      end
    end
    column :report_reason
    column :user_id do |report|
      link_to(report.user.name, admin_user_path(report.user))
    end
    column :created_at
  end

  filter :report_reason
  filter :reference_type, label: "Reference Type", as: :select, collection: ["Post", "User"]
  filter :reference_id, label: "Reference ID"
  filter :user_id, label: "User ID"
  filter :created_at
end
