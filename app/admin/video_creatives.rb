ActiveAdmin.register VideoCreative do

  controller do
    include MediaServiceUpload
  end

  menu priority: 6, :parent => 'Posters V2',
       if: proc{ role = current_admin_user.role.to_sym.in?([:admin]) }

  scope :all, default: true

  scope :scheduled do |vc|
    vc.where('active = ? AND start_time > ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :live do |vc|
    vc.where('active = ? AND start_time <= ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :expired do |vc|
    vc.where('active = ? AND end_time < ?', true, Time.zone.now)
  end

  scope :closed do |vc|
    vc.where(active: false)
  end

  actions :all, except: [:destroy]

  index do
    selectable_column
    id_column
    column :event do |vc|
      link_to(vc.event.name, admin_event_path(vc.event)) if vc.event.present?
    end

    column :circles do |vc|
      vc.video_creative_circles.map do |video_creative_circle|
        link_to(video_creative_circle.circle.name, admin_circle_path(video_creative_circle.circle)) if video_creative_circle.present?
      end
    end

    column :start_time
    column :end_time
    column :status do |vcs|
      status = ''
      if vcs.active == false
        status = "<span style='color: #FF0000; font-weight: bold;'>Closed</span>"
      elsif vcs.active == true && vcs.start_time.present? && vcs.end_time.present?
        if vcs.start_time > Time.zone.now && vcs.end_time > Time.zone.now
          status = "<span style='color: #008000; font-weight: bold;'>Scheduled</span>"
        elsif vcs.start_time < Time.zone.now && vcs.end_time > Time.zone.now
          status = "<span style='color: #008000; font-weight: bold;'>Live</span>"
        elsif vcs.end_time < Time.zone.now
          status = "<span style='color: #FFA500; font-weight: bold;'>Expired</span>"
        else
          status = 'Unknown'
        end
      end
      status.html_safe
    end

    column :creator do |vc|
      if vc.creator_type == 'AdminUser' && vc.creator.present?
        link_to vc.creator.email, admin_admin_user_path(vc.creator.id)
      end
    end

    actions name: "Actions"
  end

  show do
    attributes_table do
      row :id
      row :event do |vc|
        vc.event.present? ? link_to(vc.event.name, admin_event_path(vc.event)) : '-'
      end
      row :kind

      row :video do |vc|
        if vc.video.present? && vc.video.source_url.present?
          video_tag vc.video.source_url,
                    controls: true,
                    width: 280,
                    height: 150
        else
          "Video not available"
        end
      end
      row :start_time
      row :end_time
      row :designer
      row :creator do |vc|
        link_to(vc.creator.email, admin_admin_user_path(vc.creator)) if vc.creator.present?
      end
    end
  end


  form :html => {:multipart => true} do |f|
    f.semantic_errors
    f.inputs "Video Creative Details" do
      if f.object.new_record?
        f.input :has_event, as: :boolean, label: 'Has Event', input_html: { checked: true, id: 'has_event_checkbox', data: { if: 'changed', then: 'callback has_event_toggled' } }
        f.input :event_id, as: :searchable_select, ajax: { resource: Event, collection_name: :events }, wrapper_html: { class: 'has_event' }, label: 'Event ID', input_html: { "data-placeholder": "Search by ID or Name.." }
        f.input :kind, as: :select, collection: VideoCreative.kinds.keys
        f.input :video_file, as: :file, label: 'Video File'
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, hint: "If blank & event is present, event's start time will be used"
        f.input :end_time, as: :datetime_picker, hint: "If blank & event is present, event's end time will be used"

        f.input :circle_ids,
                label: 'Circle(s)',
                as: :searchable_select,
                multiple: true, ajax: { resource: Circle, collection_name: :poster_circles },
                input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                wrapper_html: { class: 'no_event' }

        f.input :active
      else
        if f.object.event_id.present?
          f.input :event, input_html: { disabled: true, value: f.object.event&.name }
        end
        f.input :kind, as: :select, collection: VideoCreative.kinds.keys
        f.input :video_file, as: :file, label: 'Video File'
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, input_html: { id: 'video_creative_start_time' }
        f.input :end_time, as: :datetime_picker
        f.input :active, input_html: { id: 'video_creative_active_checkbox' }

        if f.object.event_id.blank?
          f.input :circle_ids,
                  label: 'Circle(s)',
                  as: :searchable_select,
                  multiple: true,
                  ajax: {resource: Circle, collection_name: :poster_circles},
                  input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                  hint: "Existing circles #{f.object.video_creative_circles.map { |vcc| vcc.circle.id }.join(', ')}"
        end
      end
    end
    f.actions
  end


  controller do
    def create
      attrs = params[:video_creative]
      create_another = params[:create_another]

      circle_ids = []
      if attrs[:event_id].present?
        circle_ids = EventCircle.where(event_id: attrs[:event_id]).pluck(:circle_id)
      end

      circle_ids = attrs[:circle_ids].compact_blank unless circle_ids.present?

      if attrs[:video_file].blank?
        redirect_back fallback_location: new_admin_video_creative_path,
                      alert: 'Video file is required'
      end

      if attrs[:video_file].present?
        attrs[:media_service_videos] = upload_video(attrs[:video_file], current_admin_user.id)
        if attrs[:media_service_videos].present?
          video = Video.new(ms_data: attrs[:media_service_videos], user: current_admin_user, service: attrs[:media_service_videos][:service])
          video.save
        else
          redirect_back fallback_location: new_admin_video_creative_path, alert: "Video upload to media service failed"
        end
      end

      if circle_ids.present?
        @video_creative = VideoCreative.new(kind: attrs[:kind],
                                            designer_id: attrs[:designer_id],
                                            start_time: attrs[:start_time],
                                            end_time: attrs[:end_time],
                                            active: attrs[:active],
                                            creator: current_admin_user,
                                            )

        @video_creative.video_id = video.id if video.present?
        @video_creative.event_id = attrs[:event_id] if attrs[:event_id].present?

        circle_ids.each do |circle_id|
          @video_creative.video_creative_circles.build(circle_id: circle_id)
        end

        if @video_creative.save
          flash[:notice] = 'Video Creative created successfully'
          if create_another == 'on'
            redirect_to new_admin_video_creative_path
          else
            redirect_to admin_video_creative_path(@video_creative)
          end
        else
          flash[:error] = @video_creative.errors.full_messages.first
          render :new
        end
      else
        message = 'Please select the event' if attrs[:has_event].to_i == 1 && attrs[:event_id].blank?
        message = 'Please select the circle' if attrs[:has_event].to_i == 0 && circle_ids.blank?
        redirect_to new_admin_video_creative_path, alert: message
      end
    end

    def update
      attrs = params[:video_creative]

      @video_creative = VideoCreative.find(params[:id])
      update_video_creative_attributes(attrs)

      deleting_circle_ids = update_circles(attrs) if @video_creative.event_id.blank?

      if @video_creative.save
        @video_creative.video_creative_circles.where(circle_id: deleting_circle_ids).destroy_all if @video_creative.event_id.blank? && deleting_circle_ids.present?
        redirect_to admin_video_creative_path(@video_creative)
      else
        flash[:error] = @video_creative.errors.full_messages.first
        render :edit
      end
    end

    def upload_video(video, user_id)
      form_data = [["video", video]]
      upload_to_media_service(form_data, user_id, "video")
    end

    private

    def update_video_creative_attributes(attrs)
      @video_creative.assign_attributes(
        kind: attrs[:kind],
        active: attrs[:active],
        start_time: attrs[:start_time],
        end_time: attrs[:end_time],
        designer_id: attrs[:designer_id]
      )
    end

    def update_circles(attrs)
      circle_ids = attrs[:circle_ids].compact_blank.map(&:to_i)
      return [] unless circle_ids.present?
      existing_circle_ids = @video_creative.video_creative_circles.map(&:circle_id)

      new_circle_ids = circle_ids - existing_circle_ids
      deleting_circle_ids = existing_circle_ids - circle_ids

      if new_circle_ids.present?
        new_circle_ids.each do |new_circle_id|
          @video_creative.video_creative_circles.build(circle_id: new_circle_id)
        end
      end

      if new_circle_ids.present? || deleting_circle_ids.present?
        IndexCreativesForPostersFeed.perform_async("video_creative_#{params[:id]}")
      end
      deleting_circle_ids
    end

  end

  filter :kind, as: :select, collection: VideoCreative.kinds
  filter :id, label: 'Video Creative ID'
  filter :start_time
  filter :end_time
  filter :active
  filter :event_id, label: 'Event ID'
  filter :circle_ids, label: 'Circle ID'
  filter :creator_id, label: 'Creator ID'
  filter :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers },
         input_html: { "data-placeholder": "Search by ID or email.." }
  filter :created_at
  filter :updated_at
end
