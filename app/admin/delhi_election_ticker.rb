# frozen_string_literal: true

ActiveAdmin.register_page 'Delhi Assembly Ticker' do
  menu priority: 1, parent: 'Election Ticker'
  ticker_id = Elections2024::DELHI_ASSEMBLY_TICKER_ID

  page_action :submit_form, method: :post do
    notice = update_ticker_data(ticker_id, params)

    redirect_to admin_delhi_assembly_ticker_path, notice.blank? ? { notice: 'Updated successfully' } : { alert: notice }
  end

  content title: 'Delhi Assembly Ticker' do
    extend ElectionTickerPanel

    ticker_panel(ticker_id)
  end

  controller do
    include Elections2024
  end
end
