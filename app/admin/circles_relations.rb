ActiveAdmin.register CirclesRelation do
  menu :parent => "Circles"
  permit_params :first_circle_id, :second_circle_id, :relation

  filter :first_circle_id, label: "First Circle ID"
  filter :second_circle_id, label: "Second Circle ID"
  filter :relation, as: :select, collection: CirclesRelation.relations.keys
  filter :active

  form do |f|
    f.inputs do
      f.input :first_circle_id, label: "First Circle ID", required: true
      f.input :second_circle_id, label: "Second Circle ID", required: true
      f.input :relation, required: true, as: :select
      f.input :active
    end
    f.actions
  end

end
