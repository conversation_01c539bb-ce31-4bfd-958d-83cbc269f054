# frozen_string_literal: true

ActiveAdmin.register Profession do
  menu priority: 1, :parent => "Professions"

  permit_params :name, :name_en, :ordinal, :admin_medium_id

  form do |f|
    f.inputs do
      f.input :name, required: true
      f.input :name_en, required: true
      f.input :ordinal, required: true, min: 1, value: 0
      f.input :admin_medium_id, label: "Admin Medium ID"
    end
    f.actions
  end

  show do
    attributes_table do
      row :id
      row :name
      row :name_en
      row :ordinal
      row :admin_medium do |p|
        image_tag p.admin_medium.url, width: '75px' if p.admin_medium.present?
      end
      row :created_at
      row :updated_at
    end

    panel 'Sub Professions' do
      table_for profession.sub_professions.order(:ordinal, :id) do
        column :id do |sub_profession|
          link_to sub_profession.id, [:admin, sub_profession]
        end
        column :name
        column :name_en
        column :ordinal
        column :created_at
        column :updated_at
      end
    end

    panel 'Edit History' do
      table_for PaperTrail::Version.where(item_type: 'Profession', item_id: resource.id).order(id: :desc).limit(10) do
        column('Item') { |v| v.item }
        column('Changes') do |v|
          if v.object_changes
            changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
            filtered_changes = changes.reject { |field, _| field == "created_at" || field == "updated_at" }
            filtered_changes.map do |field, values|
              old_value = values[0].nil? ? 'nil' : values[0].to_s
              new_value = values[1].nil? ? 'nil' : values[1].to_s
              "#{field}: #{old_value} -> #{new_value}"
            end.join(', ')
          else
            'No changes recorded'
          end
        end
        column('Modified at') { |v| v.created_at }
        column('Admin') do |v|
          if v.whodunnit.nil?
            ''
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end
    active_admin_comments
  end
end
