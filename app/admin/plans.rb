ActiveAdmin.register Plan do
  menu priority: 2, parent: "Subscriptions"

  actions :all, except: [:new, :destroy, :edit]

  index do
    selectable_column
    id_column
    column :name
    column :amount
    column :duration_in_months
    column :premium_frames_count do |plan|
      plan.premium_frames_count || 0
    end
    column :status_frames_count do |plan|
      plan.status_frames_count || 0
    end
    actions
  end

  show do
    attributes_table do
      row :id
      row :name
      row :total_amount
      row :discount_amount
      row :amount
      row :duration_in_months
      row :premium_frames_count do |plan|
        plan.premium_frames_count || 0
      end
      row :status_frames_count do |plan|
        plan.status_frames_count || 0
      end
      row :created_at
      row :updated_at
    end
  end

  filter :id
  filter :amount
  filter :duration_in_months
  # Independent filters for premium and status frames count
  filter :premium_frames_count, as: :numeric, label: 'Premium Frames Count'
  filter :status_frames_count, as: :numeric, label: 'Status Frames Count'
end
