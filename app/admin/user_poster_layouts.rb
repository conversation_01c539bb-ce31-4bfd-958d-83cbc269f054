ActiveAdmin.register UserPosterLayout do
  menu priority: 1, parent: 'Posters V2'
  permit_params :user_id, :user_poster_photo, :user_poster_photo_with_background, :subscription_duration,
                :amount, :se_user_id, :poster_affiliated_party_id, :family_frame_photo, :family_frame_name,
                :custom_role_name, :hero_frame_photo, :quality_score, :h1_background_type, :h2_background_type,
                :quality_score_reason, :update_from_admin_dashboard,
                premium_package_frame_ids: [], status_frame_ids: [], premium_frame_add_on_ids: [],
                user_leader_photos_attributes: [:photo, :header_type, :priority, :user_id, :circle_id]

  actions :all, except: [:destroy]

  collection_action :get_es_circles, method: :get do
    render json: [] and return if params[:term].blank? || params[:entity_id].blank?

    user = User.find_by(id: params[:entity_id].to_i)
    circles = Circle.search_circles_for_protocol(user: user, term: params[:term])
    return nil if circles.empty?
    search_data = circles.map do |circle|
      party_relation = CirclesRelation.find_by(first_circle: circle, relation: 'Leader2Party')
      party_circle = party_relation ? Circle.find_by(id: party_relation.second_circle_id) : nil
      {
        id: circle.id,
        name: circle.name,
        name_en: circle.name_en,
        party_short_name: party_circle&.short_name
      }
    end

    render json: search_data
  end

  collection_action :suggesting_circles_of_user, method: :get do
    if params[:entity_type].blank? || params[:entity_type] == "Circle" || params[:entity_id].blank?
      render json: []
      return
    end

    user_id = params[:entity_id].to_i
    search_data = Circle.suggesting_circles_of_user(user_id)
    render json: search_data
  end

  collection_action :get_circle_poster_photo_urls, method: :get do
    circle = Circle.find_by(id: params[:circle_id])
    return render json: { error: 'Circle not found' }, status: :not_found unless circle.present?
    circle_poster_photos = circle.get_poster_photos
    photo_urls = circle_poster_photos.map(&:photo).map(&:url)
    photo_ids = circle_poster_photos.map(&:photo_id)
    data = {
      circlePhotoUrls: photo_urls.compact,
      circlePhotoIds: photo_ids.compact
    }
    render json: data
  end

  collection_action :get_layout_header_circle_photo_url, method: :get do
    id = params[:id]
    header_type = if params[:header_type] == 'H1'
                    :header_1
                  elsif params[:header_type] == 'H2'
                    :header_2
                  end
    priority = params[:priority].to_i + 1
    return render json: { circle_photo_url: nil } if id.blank? || header_type.blank? || priority.blank?

    user_poster_layout = UserPosterLayout.find_by(id: id)
    return render json: { circle_photo_url: nil } if user_poster_layout.blank?

    user_leader_photos = user_poster_layout.user_leader_photos.where(header_type: header_type).order(:priority)
    user_leader_photo = user_leader_photos.find_by(priority: priority)

    return render json: { circle_photo_url: nil } if user_leader_photo.blank?

    circle_id = user_leader_photo.circle_id
    circle_photo_url = user_leader_photo.photo.url
    circle_photo_id = user_leader_photo.photo_id
    priority_value = priority
    return render json: { circle_id: circle_id, circle_photo_url: circle_photo_url, circle_photo_id: circle_photo_id, priority: priority_value }
  end

  collection_action :my_poster_sections, method: :get do
    app_version_string = if request.headers['X-App-Version'].present?
                           request.headers['X-App-Version']
                                  .gsub('-alpha', '.a')
                                  .gsub('-beta', '.b')
                                  .gsub('+', '.')
                         else
                           '0.3.6' # Version till when app version was not being sent
                         end

    # initialise app version controller object & correct attributes data via AppVersionSupport class
    @app_version = Gem::Version.new(app_version_string)
    AppVersionSupport.new(app_version_string)
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    return render json: { feed_items: [] }, status: :ok if @user_poster_layout.entity.is_a?(Circle)
    @user = @user_poster_layout.entity
    poster_sections = @user.my_poster_sections
    render json: { feed_items: poster_sections }, status: :ok
  end

  collection_action :creative_categories_layouts, method: :get do
    app_version_string = if request.headers['X-App-Version'].present?
                           request.headers['X-App-Version']
                                  .gsub('-alpha', '.a')
                                  .gsub('-beta', '.b')
                                  .gsub('+', '.')
                         else
                           '0.3.6' # Version till when app version was not being sent
                         end

    # initialise app version controller object & correct attributes data via AppVersionSupport class
    @app_version = Gem::Version.new(app_version_string)
    AppVersionSupport.new(app_version_string)
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    return render json: { creatives: [], layouts: [] }, status: :ok if @user_poster_layout.entity.is_a?(Circle)
    @user = @user_poster_layout.entity
    category_id = params[:id].to_i if params[:id].present?
    category_kind = params[:category_kind] if params[:category_kind].present?
    circle_id = params[:circle_id].to_i if params[:circle_id].present?

    poster_creatives = @user.get_poster_creatives(category_id, category_kind, circle_id, from_admin_dashboard: true)
    poster_layouts = []
    unless poster_creatives.blank?
      poster_layouts = @user.get_user_layouts(category_id:, circle_id:, category_kind:,
                                              user_poster_layout_id: @user_poster_layout.id)
    end

    render json: { creatives: poster_creatives, layouts: poster_layouts }, status: :ok
  end

  collection_action :edit_layouts, method: :get do
    app_version_string = if request.headers['X-App-Version'].present?
                           request.headers['X-App-Version']
                                  .gsub('-alpha', '.a')
                                  .gsub('-beta', '.b')
                                  .gsub('+', '.')
                         else
                           '0.3.6' # Version till when app version was not being sent
                         end

    # initialise app version controller object & correct attributes data via AppVersionSupport class
    @app_version = Gem::Version.new(app_version_string)
    AppVersionSupport.new(app_version_string)
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    if @user_poster_layout.entity.is_a?(Circle)
      return render json: { creatives: [], layouts: [], selected_frame_ids: [], max_frames_selection_count: 10,
                            total_amount: 0, payable_amount: 0,
                            discount: 0, referrer_id: 0, },
                    status: :ok
    end
    @user = @user_poster_layout.entity

    existing_total_amount = 0
    existing_discount_amount = 0
    existing_payable_amount = 0
    existing_referrer_id = nil
    existing_duration = 0

    order = Order.left_joins(:order_items).where(user_id: @user.id).select('orders.*, order_items.duration_in_months').open.last
    if order.present?
      existing_total_amount = order.total_amount
      existing_discount_amount = order.discount_amount
      existing_payable_amount = order.payable_amount
      existing_referrer_id = order.referred_by
      existing_duration = order.order_items.last.duration_in_months
    end
    if @user.is_poster_subscribed
      return render json: { creatives: [], layouts: [], selected_frame_ids: [], max_frames_selection_count: 10,
                            total_amount: existing_total_amount, payable_amount: existing_payable_amount,
                            discount: existing_discount_amount, referrer_id: existing_referrer_id,
                            duration: existing_duration, enable_trial_buttons: false },
                    status: :ok
    end
    poster_creatives = []
    affiliated_circle_id = @user.affiliated_party_circle_id.to_i
    poster_layouts = @user.get_user_layouts(circle_id: affiliated_circle_id,
                                            user_poster_layout_id: @user_poster_layout.id, is_from_edit_layouts: true)
    selected_frame_ids = []
    poster_layouts.each do |layout|
      selected_frame_ids << layout[:id] if layout[:is_selected]
    end

    # enable trial buttons only if user has no trial start date and duration in metadatum
    enable_trial_buttons = Metadatum.get_user_trial_start_date_and_duration(@user).first.blank?
    if enable_trial_buttons
      enable_trial_buttons = !(@user.is_no_trial_experiment_user? &&
        PremiumPitch.find_by(user_id: @user.id, lead_type: [:L_Inbound, :BL_Inbound]))
    end

    render json: { creatives: poster_creatives, layouts: poster_layouts, selected_frame_ids: selected_frame_ids,
                   max_frames_selection_count: 10, total_amount: existing_total_amount,
                   payable_amount: existing_payable_amount, discount: existing_discount_amount,
                   referrer_id: existing_referrer_id, duration: existing_duration,
                   enable_trial_buttons: enable_trial_buttons },
           status: :ok
  end

  collection_action :edit_layouts_v2, method: :get do
    app_version_string = if request.headers['X-App-Version'].present?
                           request.headers['X-App-Version']
                                  .gsub('-alpha', '.a')
                                  .gsub('-beta', '.b')
                                  .gsub('+', '.')
                         else
                           '0.3.6' # Version till when app version was not being sent
                         end

    # initialise app version controller object & correct attributes data via AppVersionSupport class
    @app_version = Gem::Version.new(app_version_string)
    AppVersionSupport.new(app_version_string)
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    if @user_poster_layout.entity.is_a?(Circle)
      return render json: { creatives: [], layouts: [], selected_frame_ids: [], max_frames_selection_count: 10,
                            referrer_id: 0, premium_frames: [], status_frames: [],
                            enable_trial_buttons: false },
                    status: :ok
    end

    @user = @user_poster_layout.entity
    existing_referrer_id = UserReferral.find_by(referred_user_id: @user.id)&.user_id
    poster_creatives = []
    affiliated_circle_id = @user.affiliated_party_circle_id.to_i
    poster_layouts = @user.get_user_layouts(circle_id: affiliated_circle_id,
                                            user_poster_layout_id: @user_poster_layout.id, is_from_edit_layouts: true)
    selected_frame_ids = []
    hero_and_family_frame_ids = []
    subscribed_status_frames_count = 0
    subscribed_premium_frames_count = 0
    poster_layouts.each do |layout|
      selected_frame_ids << layout[:id] if layout[:is_selected]
      subscribed_status_frames_count += 1 if layout[:is_selected] && layout[:frame_type] == 'status'
      subscribed_premium_frames_count += 1 if layout[:is_selected] &&
                                              layout[:frame_type].in?(%w[premium family_frame_premium hero_frame_premium])
      hero_and_family_frame_ids << layout[:id] if layout[:frame_type].in?(%w[family_frame_premium hero_frame_premium])
    end

    is_subscribed = @user.is_poster_subscribed
    if is_subscribed
      status_frames_min_max_count = { min: subscribed_status_frames_count, max: subscribed_status_frames_count }
      premium_frames_min_max_count = { min: subscribed_premium_frames_count, max: Constants.max_premium_frames_support_count }
    else
      status_frames_min_max_count = { min: 0, max: Constants.max_status_frames_support_count }
      premium_frames_min_max_count = { min: 0, max: Constants.max_premium_frames_support_count }
    end

    if selected_frame_ids.blank?
      non_removable_frame_ids = hero_and_family_frame_ids
      selected_frame_ids = hero_and_family_frame_ids
    else
      non_removable_frame_ids = hero_and_family_frame_ids & selected_frame_ids
    end

    render json: { creatives: poster_creatives, layouts: poster_layouts, selected_frame_ids: selected_frame_ids,
                   referrer_id: existing_referrer_id, premium_frames: premium_frames_min_max_count,
                   status_frames: status_frames_min_max_count, non_removable_frame_ids: non_removable_frame_ids, },
           status: :ok
  end

  collection_action :get_plans, method: :post do
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    return render json: { plans: [] }, status: :ok unless @user_poster_layout&.entity.is_a?(User)

    @user = @user_poster_layout.entity
    premium_frame_ids_count = params[:premium_frame_ids]&.count || 0
    status_frame_ids_count = params[:status_frame_ids]&.count || 0

    plans = if @user.is_poster_subscribed
              [@user.active_premium_plan]
            else
              plans = Plan.get_plans_based_on_frames_count(premium_frame_count: premium_frame_ids_count,
                                                           status_frame_count: status_frame_ids_count)
              selected_plan = @user.should_pitch_yearly_package? ? plans.find(&:annual?) : plans.find(&:monthly?)
              [selected_plan].compact
            end
    if plans.blank?
      return render json: { plans: [] }, status: :ok
    end
    plans = premium_plans_json(user: @user, plans: plans)

    render json: { plans: plans }, status: :ok
  end

  collection_action :save_layouts_v2, method: :post do
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    premium_frame_ids = params[:premium_frame_ids] || []
    status_frame_ids = params[:status_frame_ids] || []
    referrer_id = params[:referrer_id]
    # selected_plan_id = params[:selected_plan_id].to_i

    @user = @user_poster_layout.entity if @user_poster_layout&.entity.is_a?(User)
    return render json: { success: false, message: 'User not found' }, status: :unprocessable_entity if @user.blank?

    UserReferral.where(referred_user_id: @user.id).first_or_create(user_id: referrer_id) if referrer_id.present?

    # check plan exist or not for given frame count
    unless Plan.plan_exist?(premium_frame_count: premium_frame_ids.count, status_frame_count: status_frame_ids.count)
      return render json: { success: false, message: 'Plan does not exist for given frame count' },
                    status: :unprocessable_entity
    end

    existing_frame_ids = UserFrame.where(user_id: @user.id).pluck(:frame_id)
    new_frame_ids = premium_frame_ids + status_frame_ids
    frames_to_delete = existing_frame_ids - new_frame_ids

    UserFrame.where(user_id: @user.id, frame_id: frames_to_delete).destroy_all

    new_frame_ids.each do |frame_id|
      UserFrame.where(user_id: @user.id, frame_id: frame_id).first_or_create(status: 'active')
    end

    order_message = 'Layout is successfully activated for the user.'

    unless SubscriptionUtils.has_user_ever_subscribed?(@user.id)
      Metadatum.save_user_trial_data_for_autopay(@user, Constants.poster_trial_default_duration)
    end

    # note:: this is no longer required because we are showing default plan based on should_pitch_yearly_package? logic
    # @user.set_default_plan(selected_plan_id) if selected_plan_id.present? && selected_plan_id.positive?

    send_wati_msg = UserMetadatum.where(user: @user, key: Constants.send_wati_msg_key).first
    if send_wati_msg.present?
      @user.send_wati_msg_on_layout_creation
      send_wati_msg.destroy
    end

    if @user_poster_layout.entity.is_a?(User) && @user_poster_layout.entity.layout_approval_enabled?
      @user_poster_layout.review_status = :awaited
      @user_poster_layout.first_activated_at ||= Time.zone.now
      @user_poster_layout.save!
    end

    @user.premium_pitch&.saved_layout if @user.has_premium_layout?

    render json: { success: true, message: order_message }, status: :ok
  rescue StandardError => e
    render json: { success: false, message: e.message }, status: :unprocessable_entity
  end

  # End Point For Save preview and Active for user
  collection_action :save_layouts, method: :post do
    @user_poster_layout = UserPosterLayout.find(params[:user_poster_layout_id])
    premium_package_frame_ids = params[:premium_package_frame_ids] || []
    status_frame_ids = params[:status_frame_ids] || []
    duration = params[:duration].to_i
    total_amount = params[:total_amount].to_i
    discount_amount = params[:discount_amount].to_i
    payable_amount = params[:payable_amount].to_i
    referred_by = params[:referred_by]
    trial_duration = params[:trial_duration].to_i

    if duration.positive? && premium_package_frame_ids.present?
      create_order(@user_poster_layout.entity, premium_package_frame_ids, [],
                   status_frame_ids, duration,
                   total_amount, discount_amount,
                   payable_amount, referred_by)
      @user_poster_layout.active = true
      @user_poster_layout.poster_affiliated_party_id = @user_poster_layout.entity.get_poster_affiliated_party_id
      @user_poster_layout.save!
      # check logic of enable trial only where user poster layout is active , entity is a user, enable trial is true
      # and trial duration is positive
      if @user_poster_layout.entity.is_a?(User) && trial_duration.positive?
        # save trial duration and start date in meta data of user
        # Metadatum.save_user_trial_data(@user_poster_layout.entity, Time.zone.today, trial_duration,
        #                                admin_user: current_admin_user)
      end
      order_message = 'Layout is successfully activated for the user.'
    else
      raise StandardError, 'Duration/Premium Package Frames should be present'
    end

    render json: { success: true, message: order_message }, status: :ok
  rescue StandardError => e
    render json: { success: false, message: e.message }, status: :unprocessable_entity
  end

  member_action :get_amount_by_duration, method: :get do
    selected_premium_frame_addon_ids = params[:selected_premium_frame_addon_ids]
    selected_status_frame_ids = params[:selected_status_frame_ids]
    duration = params[:duration].to_i
    amount = 0

    item_price = ItemPrice.where(item_type: "Product",
                                 item_id: Constants.get_poster_product_id,
                                 active: true,
                                 duration_in_months: duration).last
    amount += item_price&.price.to_i + item_price&.maintenance_price.to_i

    if selected_premium_frame_addon_ids.present?
      premium_frames_amount = ItemPrice.get_frames_price(selected_premium_frame_addon_ids, duration).sum
      if premium_frames_amount.present?
        amount += premium_frames_amount.to_i
      else
        render json: { success: false, message: "Premium frames doesn't have the given duration price assigned" }, status: :ok
        return
      end
    end

    if selected_status_frame_ids.present?
      status_frames_amount = ItemPrice.get_frames_price(selected_status_frame_ids, duration).sum
      if status_frames_amount.present?
        amount += status_frames_amount.to_i
      else
        render json: { success: false, message: "Status frames doesn't have the given duration price assigned" }, status: :ok
        return
      end
    end

    render json: { success: true, amount: amount }, status: :ok

  end

  CONFIG_TO_COLUMN_MAPPING = {
    "frame_type" => "frame_type",
    "golden_frame" => "gold_border",
    "is_neon_frame" => "has_shadow_color",
    "has_footer_party_icon" => "has_footer_party_icon",
    "identity_type" => "identity_type",
    "show_badge_ribbon" => "badge_strip",
    "is_user_position_back" => "user_position_back",
    "enable_outer_frame" => "outer_frame",
    "font" => "font_id",
  }

  # Selection Layout Dashboard APIs
  # API to fetch the layout selection configs
  collection_action :get_layout_selection_configs, method: :get do
    if params[:long_name].blank? || params[:long_role].blank?
      return render json: { success: false, message: 'Invalid Inputs' }, status: :unprocessable_entity
    end

    name_type = params[:long_name] == 'true' ? 'long' : 'short'
    badge_type = params[:long_role] == 'true' ? 'long' : 'short'

    frames = Frame.joins(:frame_recommendation)
                  .where(frame_recommendations: { user_name_type: name_type, badge_text_type: badge_type })
                  .includes(:font)

    frame_configs = frames.map do |frame|
      mapped_attributes = frame.attributes.each_with_object({}) do |(key, value), config|
        new_key = CONFIG_TO_COLUMN_MAPPING.invert[key]
        config[new_key] = value if new_key.present?
      end
      mapped_attributes['font'] = frame.font.present? ? frame.font.frame_json_data : nil
      mapped_attributes['frame_type'] = 'premium'
      mapped_attributes
    end

    render json: { success: true, selected_layout_configs: frame_configs }, status: :ok
  end

  collection_action :save_layout_selection_configs, method: :post do
    begin
      long_name = params[:long_name]
      long_role = params[:long_role]
      selected_layout_configs = params[:selected_layout_configs] || []

      if selected_layout_configs.blank? || long_name.nil? || long_role.nil?
        return render json: { success: false, message: 'Invalid Inputs' }, status: :unprocessable_entity
      end

      # change the frame_type in selected_layout_configs
      selected_layout_configs.each do |config|
        # decide frame_type whether it is premium or status
        config['frame_type'] = get_frame_type(config['golden_frame'], config['is_neon_frame'])
      end

      unique_font_configs = extract_unique_font_configs(layout_configs: selected_layout_configs)
      font_ids_map = generate_font_ids_map(unique_font_configs)
      frame_attributes = generate_frame_attributes(layout_configs: selected_layout_configs, font_ids_map: font_ids_map)

      Frame.import(frame_attributes, on_duplicate_key_ignore: true, batch_size: 100)

      fetched_frame_ids = fetch_frame_ids_in_batches(frame_attributes)
      # create item price of frames for 6 months and 12 months
      ItemPrice.create_item_prices_for_frames(fetched_frame_ids)
      frame_recommendation_objects = generate_frame_recommendation_objects(fetched_frame_ids, long_name, long_role)

      if frame_recommendation_objects.present?
        FrameRecommendation.import(frame_recommendation_objects, on_duplicate_key_ignore: true, batch_size: 100)
      end
      render json: { success: true, message: 'Successfully saved the layout selection configs' }, status: :ok
    rescue StandardError => e
      render json: { success: false, message: e.message }, status: :unprocessable_entity
    end
  end

  member_action :get_maintenance_price, method: :get do
    duration = params[:duration].to_i

    maintenance_price = ItemPrice.where(item_type: "Product",
                                        item_id: Constants.get_poster_product_id,
                                        active: true,
                                        duration_in_months: duration).last&.maintenance_price
    render json: { success: true, maintenance_price: maintenance_price }, status: :ok
  end

  member_action :add_quality_score, method: :get do
    render 'admin/user_poster_layouts/add_quality_score', locals: { user_poster_layout: resource }
  end

  member_action :update_quality_score, method: :post do
    score = params[:quality_score].to_i
    reasons = Array(params[:quality_score_reason])
    other_reason = params[:other_reason]
    if reasons.include?("Other") && other_reason.present?
      reasons.map! { |r| r == "Other" ? "Other: #{other_reason}" : r }
    end
    resource.update_columns(quality_score: score, quality_score_reason: reasons.join(", "))
    redirect_to resource_path, notice: "Quality Score updated Successfully"
  end

  # action_item :edit_open_order, only: :show do
  #   unless resource.entity.is_a?(Circle)
  #     link_to 'Edit Open Order', edit_admin_order_path(resource.entity.open_orders.last) if resource.entity
  #                                                                                                   .open_orders
  #                                                                                                   .present?
  #   end
  # end

  action_item :view_preview, only: :show do
    unless resource.entity.is_a?(Circle)
      link_to 'View Preview', "#{Constants.posters_preview_dashboard_base_url}/#/?user_poster_layout_id=#{resource.id}", target: '_blank'
    end
  end

  action_item :edit_frames, only: :show do
    if resource.entity.is_a?(User)
      link_to 'Edit Frames', "#{Constants.posters_preview_dashboard_base_url}/#/edit_layouts?user_poster_layout_id=#{resource.id}",
              target: '_blank'
    end
  end

  # action_item :activate_for_user, only: :show do
  #   unless resource.entity.is_a?(Circle)
  #     link_to "Activate for User", activate_for_user_admin_user_poster_layout_path(resource), class: 'activate-button', method: :post unless resource.active_status?
  #   end
  # end
  #
  # member_action :activate_for_user, method: :post do
  #   @user_poster_layout = UserPosterLayout.find(params[:id])
  #   @user_poster_layout.status = 'active'
  #   @user_poster_layout.save
  #
  #   redirect_to admin_user_poster_layout_path(@user_poster_layout), notice: 'Layout is successfully activated for the user.'
  # end

  # need to think of extend trial once release of auto pay
  # action_item :extend_trial, only: :show, if: proc { resource.entity.is_a?(User) &&
  #   resource.entity.has_premium_layout? && !resource.entity.is_poster_subscribed &&
  #   !resource.entity.is_trial_extension_user? } do
  #   link_to 'Extend Trial', '#', id: 'extend_trial_link', data: { extend_path: extend_trial_admin_user_poster_layout_path(resource) }
  # end
  #
  # member_action :extend_trial, method: :get do
  #   @user_poster_layout = resource
  # end

  action_item :duplicate, only: :show, if: proc { resource.entity_type == 'User' } do
    link_to 'Duplicate', duplicate_admin_user_poster_layout_path(resource)
  end

  member_action :duplicate, method: :get do
    @user_poster_layout = resource
    render 'duplicate_page'
  end

  member_action :create_duplicate, method: :post do
    @user_poster_layout = UserPosterLayout.find(params[:id])

    attrs = params[:user_poster_layout]
    @user = User.find_by(id: attrs[:entity_id])

    if @user.blank?
      flash[:error] = "User not found"
      redirect_to admin_user_poster_layout_path(@user_poster_layout)
      return
    end

    if attrs[:user_poster_photo].blank? || attrs[:user_poster_photo_with_background].blank?
      flash[:error] = "User Poster Photo (with or without background) can't be blank"
      redirect_to admin_user_poster_layout_path(@user_poster_layout)
      return
    end

    @user.poster_photo = AdminMedium.new(blob_data: attrs[:user_poster_photo], admin_user_id: current_admin_user.id)
    @user.poster_photo_with_background = AdminMedium.new(blob_data: attrs[:user_poster_photo_with_background],
                                                         admin_user_id: current_admin_user.id)

    @duplicate_user_poster_layout = UserPosterLayout.new(entity_type: 'User', entity_id: attrs[:entity_id],
                                                         se_user_id: attrs[:se_user_id],
                                                         h1_count: @user_poster_layout.h1_count,
                                                         h2_count: @user_poster_layout.h2_count)

    @duplicate_user_poster_layout.review_status = :awaited if @user.layout_approval_enabled?

    @duplicate_user_poster_layout.user_leader_photos.build(@user_poster_layout.user_leader_photos.map do |ulp|
      ulp.attributes.except('id', 'user_poster_layout_id', 'created_at', 'updated_at', 'creator').merge(creator: current_admin_user)
    end)

    @duplicate_user_poster_layout.poster_affiliated_party_id = attrs[:poster_affiliated_party_id]

    @duplicate_user_poster_layout.active = true

    if @user.save! && @duplicate_user_poster_layout.save!
      flash[:notice] = "Successfully created duplicate poster layout"
      redirect_to admin_user_poster_layout_path(@duplicate_user_poster_layout)
    else
      flash[:error] = @duplicate_user_poster_layout.errors.full_messages.first
      render 'duplicate_page'
    end
  end

  # return whether user has user role or not
  member_action :user_has_badge, method: :get do
    user_id = params[:user_id].to_i
    user = User.find_by(id: user_id)
    return render json: { success: false, user_has_badge: false, message: 'User not found' }, status: :ok if user.blank?

    user_has_badge = user.has_badge_role?

    render json: { success: true, user_has_badge: user_has_badge }, status: :ok
  end

  index do
    selectable_column
    id_column
    column :entity do |upl|
      case upl.entity_type
      when 'User'
        link_to(upl.entity.name, admin_user_path(upl.entity))
      when 'Circle'
        link_to(upl.entity.name, admin_circle_path(upl.entity))
      end
    end
    column :layout_type do |upl|
      "layout_#{upl.h1_count}_#{upl.h2_count}"
    end
    column :rm_user do |upl|
      rm_user = upl.entity.is_a?(User) ? upl.entity.get_rm_user : nil
      rm_user&.present? ? link_to(rm_user.name, admin_admin_user_path(rm_user)) : '-'
    end
    column :active do |upl|
      status_tag(upl.active ? "active" : "inactive", class: upl.active ? "ok" : "warning")
    end
    column :created_at
    column :updated_at
    unless current_admin_user.layout_auditor_role?
      column :creator do |upl|
        first_creator_id = upl.versions.first&.whodunnit
        creator = AdminUser.find_by(id: first_creator_id)
        creator ? link_to(creator.name, admin_admin_user_path(creator)) : '-'
      end
    end

    actions name: "Actions", default: true do |upl|
      unless upl.entity.is_a?(Circle)
        if current_admin_user.role.to_sym.in?([:admin, :layout_auditor])
          item 'Add Quality Score', 'javascript:void(0);',
               onclick: "openMultipleTabs('#{add_quality_score_admin_user_poster_layout_path(upl)}','#{Constants.posters_preview_dashboard_base_url}/#/?user_poster_layout_id=#{upl.id}')",
               class: "open-multiple-tabs"
        end
        text_node '&nbsp;'.html_safe * 2
        item 'View Preview', "#{Constants.posters_preview_dashboard_base_url}/#/?user_poster_layout_id=#{upl.id}", target: '_blank'
        text_node '&nbsp;'.html_safe * 2
        item 'Edit Frames', "#{Constants.posters_preview_dashboard_base_url}/#/edit_layouts?user_poster_layout_id=#{upl.id}", target: '_blank'
        text_node '&nbsp;'.html_safe * 2
      end
    end
  end

  show do
    attributes_table do
      row :id
      row :entity do |upl|
        case upl.entity_type
        when 'User'
          link_to(upl.entity.name, admin_user_path(upl.entity))
        when 'Circle'
          link_to(upl.entity.name, admin_circle_path(upl.entity))
        end
      end
      if resource.entity.is_a?(User)
        row :user_poster_photo_without_background do |upl|
          image_tag upl.entity.poster_photo.url, class: 'thumb_size', style: "margin-right: 5px;" if upl.entity.poster_photo.present?
        end
        row :user_poster_photo_with_background do |upl|
          image_tag upl.entity.poster_photo_with_background.url, class: 'thumb_size',
                    style: "margin-right: 5px;" if upl.entity.poster_photo_with_background.present?
        end
        row :family_frame_photo do |upl|
          image_tag upl.entity.family_frame_photo.url, class: 'thumb_size',
                    style: "margin-right: 5px;" if upl.entity.family_frame_photo.present?
        end
        row :family_frame_name do |upl|
          upl.entity.family_frame_name
        end
        row :custom_role_name do |upl|
          upl.entity.custom_role_name
        end
        row :hero_frame_photo do |upl|
          image_tag upl.entity.hero_frame_photo.url, class: 'thumb_size',
                    style: "margin-right: 5px;" if upl.entity.hero_frame_photo.present?
        end
      end
      row :active
      row :h1_count
      row :h1_background_type
      row :h1_photos do |upl|
        UserLeaderPhoto.where(user_poster_layout_id: upl.id, header_type: :header_1).order(:priority).map do |ulp|
          image_tag ulp.photo.url, class: 'thumb_size', style: "margin-right: 5px;" if ulp.photo.present?
        end
      end
      row :h1_photos_circles do |upl|
        UserLeaderPhoto.includes(:user, :circle).where(user_poster_layout_id: upl.id, header_type: :header_1).order(:priority).map do |ulp|
          link_to(ulp.circle.name, admin_circle_path(ulp.circle)) if ulp.circle.present?
        end.compact
      end
      row :h2_count
      row :h2_background_type
      row :h2_photos do |upl|
        UserLeaderPhoto.where(user_poster_layout_id: upl.id, header_type: :header_2).order(:priority).map do |ulp|
          image_tag ulp.photo.url, class: 'thumb_size', style: "margin-right: 5px;" if ulp.photo.present?
        end
      end
      row :h2_photos_circles do |upl|
        UserLeaderPhoto.includes(:user, :circle).where(user_poster_layout_id: upl.id, header_type: :header_2).order(:priority).map do |ulp|
          link_to(ulp.circle.name, admin_circle_path(ulp.circle)) if ulp.circle.present?
        end.compact
      end
      unless current_admin_user.layout_auditor_role?
        row :creator do |upl|
          first_creator_id = upl.versions.first&.whodunnit
          creator = AdminUser.find_by(id: first_creator_id)
          creator ? link_to(creator.name, admin_admin_user_path(creator)) : '-'
        end
      end
      # row :se_user
      row :rm_user do |upl|
        rm_user = upl.entity.is_a?(User) ? upl.entity.get_rm_user : nil
        rm_user&.present? ? link_to(rm_user.name, admin_admin_user_path(rm_user)) : '-'
      end
      unless current_admin_user.op_executive_role?
        row :quality_score
        row :quality_score_reason
      end
      row :referred_user do |upl|
        if upl.entity.is_a?(User)
          referred_user_id = UserReferral.find_by(referred_user_id: upl.entity.id)&.user_id
          if referred_user_id.present?
            referred_user = User.find_by(id: referred_user_id)
            referred_user ? link_to(referred_user.name, admin_user_path(referred_user)) : '-'
          end
        else
          '-'
        end
      end
      row :poster_affiliated_party_id do |upl|
        if upl.entity.is_a?(User) && upl.entity.get_poster_affiliated_party_id.present?
          link_to(Circle.find_by(id: upl.entity.get_poster_affiliated_party_id).name, admin_circle_path(upl.entity.get_poster_affiliated_party_id))
        else
          '-'
        end
      end
      if resource.entity.is_a?(User)
        row :trial_user do |upl|
          upl.entity.is_trial_user?
        end
        row :already_trial_extended do |upl|
          upl.entity.is_trial_extension_user?
        end
      end
      row :created_at
      row :updated_at
      # TODO:: only for internal users testing. Need to update this logic later
      if resource.entity.is_a?(User) && resource.entity.layout_approval_enabled?
        row :review_status
      end
    end

    unless current_admin_user.layout_auditor_role?
      panel 'Edit History' do
        table_for PaperTrail::Version.where(item_type: 'UserPosterLayout', item_id: user_poster_layout.id).order(id: :desc).limit(10) do
          column('Item') { |v| v.item }
          column('Changes') do |v|
            if v.object_changes.present?
              changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
              filtered_changes = changes.reject { |field, _| %w[created_at updated_at].include?(field) }
              filtered_changes.map do |field, values|
                old_value = values[0].nil? ? 'nil' : values[0].to_s
                new_value = values[1].nil? ? 'nil' : values[1].to_s
                "#{field}: #{old_value} -> #{new_value}"
              end.join(', ')
            else
              'No changes recorded'
            end
          end
          column('Modified at') { |v| v.created_at }
          column('Admin') do |v|
            if v.whodunnit.nil? || v.whodunnit == 'unknown'
              v.whodunnit.nil? ? '' : "Unknown"
            else
              admin_user = AdminUser.find_by(id: v.whodunnit)
              admin_user ? link_to(admin_user.name, [:admin, admin_user]) : "Admin not found"
            end
          end
        end
      end
      panel "User Leader Photos Versions" do
        user_leader_photos = UserLeaderPhoto.where(user_poster_layout_id: user_poster_layout.id)
        if user_leader_photos.present?
          layout_photo_ids = user_leader_photos.pluck(:id)
          leader_photo_versions = PaperTrail::Version.where(item_type: "UserLeaderPhoto", item_id: layout_photo_ids)
                                                     .order(id: :asc)
          latest_versions_per_photo = leader_photo_versions.group_by(&:item_id)
                                                           .transform_values(&:first)
                                                           .values
          user_leader_photo = UserLeaderPhoto.where(id: latest_versions_per_photo.map(&:item_id)).index_by(&:id)
          circle_ids = user_leader_photo.values.map(&:circle_id).compact.uniq
          circles_map = Circle.where(id: circle_ids).index_by(&:id)
          admin_user_ids = latest_versions_per_photo.map(&:whodunnit).compact.uniq
          admin_users_map = AdminUser.where(id: admin_user_ids).index_by(&:id)
          table_for latest_versions_per_photo do
            column("Protocol ID") { |v| v.item_id }
            column("Photo ID") { |v| user_leader_photo[v.item_id]&.photo_id }
            column("Circle") do |v|
              photo = user_leader_photo[v.item_id]
              if photo&.circle_id.present?
                circle = circles_map[photo.circle_id]
                circle ? link_to(circle.name, [:admin, circle]) : "Circle not found"
              else
                "No Circle"
              end
            end
            column("Header Type") { |v| user_leader_photo[v.item_id]&.header_type }
            column("Priority") { |v| user_leader_photo[v.item_id]&.priority }
            column("Created At") { |v| v.created_at }
            column("Admin") do |v|
              if v.whodunnit.present? && v.whodunnit != 'unknown'
                admin_user = admin_users_map[v.whodunnit.to_i]
                admin_user ? link_to(admin_user.name, [:admin, admin_user]) : "Admin not found"
              else
                "Unknown"
              end
            end
          end
        end
      end
      columns do
        column do
          panel "Poster Photos History" do
            layout_users = User.where(id: user_poster_layout.entity_id, status: :active).includes(
              :poster_photo, :poster_photo_with_background, :family_frame_photo, :hero_frame_photo)
            if layout_users.present?
              layout_user_ids = layout_users.pluck(:id)
              versions = PaperTrail::Version.where(item_type: 'User', item_id: layout_user_ids, tag: "user_poster_photos_changed").order(id: :desc)
              versions_map = {}
              layout_users.each do |u|
                user_layout_data = [
                  { photo_type: 'Poster Photo without BG', admin_medium_id: u.poster_photo_id },
                  { photo_type: 'Poster Photo with BG', admin_medium_id: u.poster_photo_with_background_id },
                  { photo_type: 'Family Frame Photo', admin_medium_id: u.family_frame_photo_id },
                  { photo_type: 'Hero Frame Photo', admin_medium_id: u.hero_frame_photo_id }
                ]
                user_layout_data.each do |uld|
                  if uld[:admin_medium_id].present?
                    latest_version = versions.find { |v| v.object_changes.include?(uld[:admin_medium_id].to_s) && v.item_id == u.id }
                    if latest_version.present?
                      versions_map[uld[:photo_type]] = latest_version
                    end
                  end
                end
              end
              layout_data = layout_users.flat_map do |u|
                [
                  { user_id: u.id, photo_type: 'Poster Photo without BG', admin_medium_id: u.poster_photo_id },
                  { user_id: u.id, photo_type: 'Poster Photo with BG', admin_medium_id: u.poster_photo_with_background_id },
                  { user_id: u.id, photo_type: 'Family Frame Photo', admin_medium_id: u.family_frame_photo_id },
                  { user_id: u.id, photo_type: 'Hero Frame Photo', admin_medium_id: u.hero_frame_photo_id }
                ].reject { |data| data[:admin_medium_id].nil? }
              end
              table_for layout_data do
                column('User Id') { |data| data[:user_id] }
                column('Photo Type') { |data| data[:photo_type] }
                column('Admin Medium Id') { |data| data[:admin_medium_id] }
                column('Created At') do |data|
                  version = versions_map[data[:photo_type]]
                  version&.created_at
                end
                column('Admin') do |data|
                  version = versions_map[data[:photo_type]]
                  if version&.whodunnit.present? && version.whodunnit != 'unknown'
                    admin_user = AdminUser.find_by(id: version.whodunnit)
                    admin_user ? link_to(admin_user.name, [:admin, admin_user]) : "Admin not found"
                  else
                    "Unknown"
                  end
                end
              end
            end
          end
        end
        column do
          panel "Frames" do
            user_frames = UserFrame.where(user_id: user_poster_layout.entity_id, status: :active)
            if user_frames.present?
              frame_ids = user_frames.pluck(:id)
              versions = PaperTrail::Version.where(item_type: 'UserFrame', item_id: frame_ids, event: 'create').order(id: :desc)
              versions_map = versions.each_with_object({}) do |version, map|
                map[version.item_id] ||= version
              end
              table_for user_frames do |frame|
                column('Frame ID') { |frame| frame.frame_id }
                column('Created at') { |frame| frame.created_at }
                column('Admin') do |frame|
                  version = versions_map[frame.id]
                  if version && version.whodunnit.present? && version.whodunnit != 'unknown'
                    admin_user = AdminUser.find_by(id: version.whodunnit)
                    admin_user ? link_to(admin_user.name, [:admin, admin_user]) : "Admin not found"
                  else
                    "Unknown"
                  end
                end
              end
            end
          end
        end
      end
    end
    active_admin_comments
  end

  form html: { multipart: true } do |f|
    if !f.object.new_record? && !f.object.active
      div class: "flash flash_error" do
        "You cannot edit an inactive poster layout. Please use the Internal tool."
      end
      return
    end
    f.inputs 'User Poster Layout' do
      if f.object.new_record?
        f.input :entity_type, as: :select, collection: %w[User Circle],
                selected: 'User',
                input_html: { id: 'entity_type_input' }
        f.input :entity_id,
                label: 'Entity ID',
                hint: 'Enter User ID if entity type is User or Circle ID if entity type is Circle',
                input_html: { id: 'entity_id_input' }
        f.input :user_poster_photo, as: :file, label: 'User Poster Photo (without background)',
                wrapper_html: { class: 'user_poster_photos' }
        f.input :user_poster_photo_with_background, as: :file, wrapper_html: { class: 'user_poster_photos' }
        f.input :family_frame_photo, as: :file, label: 'Family Frame Photo', wrapper_html: { class: 'user_poster_photos' }
        f.input :family_frame_name, label: 'Family Frame Name', wrapper_html: { class: 'user_poster_photos' }
        # f.input :custom_role_name, label: 'Custom Role Name', wrapper_html: { class: 'user_poster_photos custom_role_name' },
        #         input_html: { placeholder: 'Enter Custom Role Name', id: 'user_poster_layout_custom_role_name' }
        f.input :hero_frame_photo, as: :file, label: 'Hero Frame Photo', wrapper_html: { class: 'user_poster_photos' }
        f.input :poster_affiliated_party_id, label: 'Poster Affiliated Party ID', as: :searchable_select, ajax: { resource: Circle, collection_name: :party_circles },
                wrapper_html: { class: 'user_poster_photos' }, input_html: { "data-placeholder": 'Search by party ID or party name..' }
        # f.input :se_user_id, label: 'SE User ID', as: :searchable_select, ajax: { resource: AdminUser, collection_name: :admin_users },
        #         input_html: { "data-placeholder": 'Search by ID or Email..' }

        f.inputs class: 'h1_count_section' do
          f.input :h1_count, as: :number, input_html: { id: 'h1_count_input' }
          f.input :h1_background_type, as: :select, collection: UserPosterLayout.h1_background_types,
                  selected: :transparent
          div id: 'dynamic_buttons_h1', style: 'padding-left: 290px;'
        end

        f.inputs class: 'h2_count_section' do
          f.input :h2_count, as: :number, input_html: { id: 'h2_count_input' }
          f.input :h2_background_type, as: :select, collection: UserPosterLayout.h2_background_types,
                  selected: :transparent
          div id: 'dynamic_buttons_h2', style: 'padding-left: 290px;'
        end
      else
        f.input :id, input_html: { disabled: true, id: 'id_input' }
        f.input :entity_type, input_html: { disabled: true, id: 'entity_type_input' }
        f.input :entity_id, input_html: { disabled: true, id: 'entity_id_input' }
        # show below fields only if entity is user
        unless f.object.entity.is_a?(Circle)
          if f.object.entity.poster_photo&.url.present?
            f.input :user_poster_photo, as: :file, hint: image_tag(f.object.entity.poster_photo.url, class: 'thumb_size'), label: 'User Poster Photo (without background)'
          else
            f.input :user_poster_photo, as: :file, label: 'User Poster Photo (without background)'
          end

          if f.object.entity.poster_photo_with_background&.url.present?
            f.input :user_poster_photo_with_background, as: :file, hint: image_tag(f.object.entity.poster_photo_with_background.url, class: 'thumb_size')
          else
            f.input :user_poster_photo_with_background, as: :file
          end

          if f.object.entity.family_frame_photo&.url.present?
            f.input :family_frame_photo, as: :file, hint: image_tag(f.object.entity.family_frame_photo.url, class: 'thumb_size')
          else
            f.input :family_frame_photo, as: :file
          end
          # show family frame name if entity is user and user has family frame name
          f.input :family_frame_name, input_html: { value: f.object.entity&.family_frame_name }

          # show custom role name if entity is user and user has custom role name
          # f.input :custom_role_name, label: 'Custom Role Name', wrapper_html: { class: 'custom_role_name' },
          #         input_html: { placeholder: 'Enter Custom Role Name', id: 'user_poster_layout_custom_role_name',
          #                       value: f.object.entity&.custom_role_name }

          if f.object.entity.hero_frame_photo&.url.present?
            f.input :hero_frame_photo, as: :file, hint: image_tag(f.object.entity.hero_frame_photo.url, class: 'thumb_size')
          else
            f.input :hero_frame_photo, as: :file
          end
          if f.object.entity.get_poster_affiliated_party_id.present?
            f.input :poster_affiliated_party_id, as: :select, collection: Circle.where(level: :political_party).map { |c| [c.name, c.id] },
                    selected: f.object.entity.get_poster_affiliated_party_id
          else
            f.input :poster_affiliated_party_id, as: :select, collection: Circle.where(level: :political_party).map { |c| [c.name, c.id] }
          end
          # f.input :se_user_id, as: :select, collection: AdminUser.all.map { |u| [u.email, u.id] } if current_admin_user.role.to_sym.in?([:admin, :op_executive])
        end
        f.input :h1_count, as: :number, input_html: { id: 'h1_count_input' }
        f.input :h1_background_type, as: :select, collection: UserPosterLayout.h1_background_types
        f.inputs class: 'h1_count_section' do
          div id: 'dynamic_buttons_h1', style: 'padding-left: 290px;'
        end

        f.input :h2_count, as: :number, input_html: { id: 'h2_count_input' }
        f.input :h2_background_type, as: :select, collection: UserPosterLayout.h2_background_types
        f.inputs class: 'h2_count_section' do
          div id: 'dynamic_buttons_h2', style: 'padding-left: 290px;'
        end
      end
    end
    f.actions do
      f.add_create_another_checkbox
      f.action :submit, label: 'Activate for User', button_html: { class: 'activate-button' }
      # f.action :submit, label: 'Save for Preview', button_html: { class: 'preview-button' }
      f.cancel_link(admin_user_poster_layouts_path)
    end
  end

  controller do
    include PlanHelper
    skip_before_action :verify_authenticity_token, only: [:save_layouts, :save_layout_selection_configs,
                                                          :save_layouts_v2, :get_plans]
    before_action :check_layout_status, only: [:edit, :update]

    def extend_trial
      days = params[:number_of_days].to_i
      # Handle logic based on selected extension days
      user = resource.entity
      begin
        start_date, duration = Metadatum.get_user_trial_start_date_and_duration(user)
        if start_date.present? && duration.present?
          end_date = start_date.advance(days: duration - 1)
          if end_date < Time.zone.today
            start_date = Time.zone.today
          else
            start_date = start_date.advance(days: duration)
          end
        else
          start_date = Time.zone.today
        end
        # Metadatum.save_user_trial_data(user, start_date, days, admin_user: current_admin_user)
        UpdateLeadScore.perform_async(user.id)
        redirect_to admin_user_poster_layout_path(resource), notice: "Successfully extended trial for #{days} days"
      rescue StandardError => e
        Honeybadger.notify(e)
        flash[:error] = e.message
        redirect_to admin_user_poster_layout_path(resource), notice: "Failed to extend trial for #{days} days"
      end
    end

    def get_frame_type(golden_frame, is_neon_frame)
      if golden_frame || is_neon_frame
        'status'
      else
        'premium'
      end
    end

    def generate_font_ids_map(unique_font_configs)
      font_ids_map = {}
      unique_font_configs.each do |(name_font_family, badge_font_family)|
        font = Font.find_or_create_by(name_font: name_font_family, badge_font: badge_font_family)
        font_ids_map[[name_font_family, badge_font_family]] = font.id
      end
      font_ids_map
    end

    def generate_frame_recommendation_objects(fetched_frame_ids, long_name, long_role)
      fetched_frame_ids.map do |frame_id|
        FrameRecommendation.new(frame_id: frame_id, user_name_type: long_name ? 'long' : 'short',
                                badge_text_type: long_role ? 'long' : 'short')
      end
    end

    def generate_frame_attributes(layout_configs:, font_ids_map:)
      layout_configs.map do |config|
        name_font_family = config[:font][:name][:font_family]
        badge_font_family = config[:font][:badge][:font_family]

        font_id = font_ids_map[[name_font_family, badge_font_family]]
        config[:font] = font_id

        transformed_config = config.transform_keys { |key| CONFIG_TO_COLUMN_MAPPING[key] }
        new_config = transformed_config.permit(:frame_type, :identity_type, :gold_border, :user_position_back,
                                               :font_id, :has_shadow_color, :outer_frame, :badge_strip,
                                               :has_footer_party_icon)
        Frame.new(new_config)
      end
    end

    def extract_unique_font_configs(layout_configs:)
      layout_configs.each_with_object(Set.new) do |config, set|
        name_font_family = config[:font][:name][:font_family]
        badge_font_family = config[:font][:badge][:font_family]
        set.add([name_font_family, badge_font_family])
      end
    end

    def fetch_frame_ids_in_batches(frame_attributes, batch_size: 20)
      frame_attributes.each_slice(batch_size).flat_map do |batch|
        query_conditions = generate_batched_query_conditions(batch)
        Frame.where(query_conditions).pluck(:id)
      end
    end

    def generate_batched_query_conditions(batch)
      batch.map do |frame|
        conditions = frame.attributes.slice('frame_type', 'identity_type', 'gold_border', 'user_position_back',
                                            'font_id', 'has_shadow_color', 'outer_frame', 'badge_strip',
                                            'has_footer_party_icon')
        conditions.map { |key, value| "#{key} = #{value.is_a?(String) ? "'#{value}'" : value}" }.join(' AND ')
      end.join(' OR ')
    end

    def create
      attrs = params[:user_poster_layout]
      protocol_join_circle_ids = []

      if attrs[:entity_type] == 'User'
        @user = User.find_by(id: attrs[:entity_id])
        @user.poster_photo = AdminMedium.new(blob_data: attrs[:user_poster_photo], admin_user_id: current_admin_user.id)

        @user.poster_photo_with_background = AdminMedium.new(blob_data: attrs[:user_poster_photo_with_background],
                                                             admin_user_id: current_admin_user.id)
        @user.family_frame_photo = AdminMedium.new(blob_data: attrs[:family_frame_photo],
                                                   admin_user_id: current_admin_user.id)
        @user.family_frame_name = attrs[:family_frame_name] if attrs[:family_frame_name].present?
        @user.custom_role_name = attrs[:custom_role_name] if attrs[:custom_role_name].present?
        @user.hero_frame_photo = AdminMedium.new(blob_data: attrs[:hero_frame_photo], admin_user_id: current_admin_user.id)
        flash[:error] = "User not found" if @user.blank?

      elsif attrs[:entity_type] == 'Circle'
        @circle = Circle.find_by(id: attrs[:entity_id])
        flash[:error] = "Circle not found" if @circle.blank?
      end

      if @user.blank? && @circle.blank?
        flash[:error] = "User/Circle not found"
        redirect_to new_admin_user_poster_layout_path
        return
      else
        if @circle.blank? && (attrs[:user_poster_photo].blank? || attrs[:user_poster_photo_with_background].blank?)
          flash[:error] = "User Poster Photo (with or without background) can't be blank"
          redirect_to new_admin_user_poster_layout_path
          return
        end

        h1_count = attrs[:h1_count].present? ? attrs[:h1_count].to_i : 0
        h2_count = attrs[:h2_count].present? ? attrs[:h2_count].to_i : 0

        h1_parsed_data = h2_parsed_data = []
        if params[:buttons].present?
          h1_parsed_data = params[:buttons][:h1].map { |item| JSON.parse(item) } if params[:buttons][:h1].present?
          h2_parsed_data = params[:buttons][:h2].map { |item| JSON.parse(item) } if params[:buttons][:h2].present?
        end

        if (h1_count != h1_parsed_data.count) || (h2_count != h2_parsed_data.count)
          flash[:error] = "Please select circles for header 1 or header 2"
          redirect_to new_admin_user_poster_layout_path
          return
        end

        @user_poster_layout = UserPosterLayout.new(entity_type: attrs[:entity_type],
                                                   entity_id: attrs[:entity_id],
                                                   se_user_id: attrs[:se_user_id],
                                                   h1_background_type: attrs[:h1_background_type],
                                                   h2_background_type: attrs[:h2_background_type],
                                                   h1_count: h1_count,
                                                   h2_count: h2_count)

        h1_parsed_data.each_with_index do |hash, index|
          circle_id = hash["circle_id"].present? ? hash["circle_id"] : nil
          protocol_join_circle_ids += [circle_id] if circle_id.present?
          photo_id = hash["photo_id"]

          # using Photo model to get photo to handle edits of poster photos in the circle page
          photo = Photo.find_by(id: photo_id)

          @user_poster_layout.user_leader_photos.build(
            photo: photo,
            header_type: :header_1,
            priority: index + 1,
            circle_id: circle_id,
            creator: current_admin_user
          )
        end

        h2_parsed_data.each_with_index do |hash, index|
          circle_id = hash["circle_id"].present? ? hash["circle_id"] : nil
          protocol_join_circle_ids += [circle_id] if circle_id.present?
          photo_id = hash["photo_id"]

          # using Photo model to get photo to handle edits of poster photos in the circle page
          photo = Photo.find_by(id: photo_id)

          @user_poster_layout.user_leader_photos.build(
            photo: photo,
            header_type: :header_2,
            priority: index + 1,
            circle_id: circle_id,
            creator: current_admin_user
          )
        end

        @user_poster_layout.poster_affiliated_party_id = attrs[:poster_affiliated_party_id]
        @user_poster_layout.update_from_admin_dashboard = true
        @user_poster_layout.review_status = :awaited if @user_poster_layout.entity.is_a?(User) && @user_poster_layout.entity.layout_approval_enabled?
        if (@user.present? && @user.save && @user_poster_layout.save) || (@circle.present? && @user_poster_layout.save)
          circle_ids = protocol_join_circle_ids.compact.uniq
          ProtocolJoinOfUserInCircles.perform_async(@user.id, circle_ids) if circle_ids.present? && @user.present?
          if create_another?
            redirect_to new_admin_user_poster_layout_path, notice: 'User Poster Layout was successfully created.'
          else
            redirect_to admin_user_poster_layout_path(@user_poster_layout), notice: 'User Poster Layout was successfully created.'
          end
        else
          flash[:error] = @user_poster_layout.errors.full_messages.first if @user_poster_layout.errors.present?
          flash[:error] = @user.errors.full_messages.first if @user.present? && @user.errors.present?
          flash[:error] = @circle.errors.full_messages.first if @circle.present? && @circle.errors.present?
          render :new
        end
      end
    end

    def update
      attrs = params[:user_poster_layout]

      protocol_join_circle_ids = []
      @user_poster_layout = UserPosterLayout.find(params[:id])

      # Check if the layout is in drafts scope
      if !@user_poster_layout.active
        flash[:error] = "You cannot edit an inactive poster layout. Please use the Internal tool."
        redirect_to admin_user_poster_layout_path(@user_poster_layout) and return
      end

      @user_poster_layout.active = true
      @user_poster_layout.se_user_id = attrs[:se_user_id] if attrs[:se_user_id].present?
      @user_poster_layout.h1_background_type = attrs[:h1_background_type] if attrs[:h1_background_type].present?
      @user_poster_layout.h2_background_type = attrs[:h2_background_type] if attrs[:h2_background_type].present?

      if @user_poster_layout.entity.is_a?(User)
        # if user poster photo changed then update it
        if attrs[:user_poster_photo].present?
          @user_poster_layout.entity.poster_photo = AdminMedium.new(blob_data: attrs[:user_poster_photo], admin_user_id: current_admin_user.id)
        end

        # if user poster photo with background changed then update it
        if attrs[:user_poster_photo_with_background].present?
          @user_poster_layout.entity.poster_photo_with_background = AdminMedium.new(blob_data: attrs[:user_poster_photo_with_background],
                                                                                    admin_user_id: current_admin_user.id)
        end

        # if family frame photo changed then update it
        if attrs[:family_frame_photo].present?
          @user_poster_layout.entity.family_frame_photo = AdminMedium.new(blob_data: attrs[:family_frame_photo],
                                                                          admin_user_id: current_admin_user.id)
        end

        # if hero frame photo changed then update it
        if attrs[:hero_frame_photo].present?
          @user_poster_layout.entity.hero_frame_photo = AdminMedium.new(blob_data: attrs[:hero_frame_photo],
                                                                        admin_user_id: current_admin_user.id)
        end

        # update family frame name
        @user_poster_layout.entity.family_frame_name = attrs[:family_frame_name] if attrs[:family_frame_name].present?

        # update custom role name
        if (@user_poster_layout.entity.custom_role_name.present? && attrs[:custom_role_name].blank?) ||
           attrs[:custom_role_name].present?
          @user_poster_layout.entity.custom_role_name = attrs[:custom_role_name]
        end

        @user_poster_layout.poster_affiliated_party_id = attrs[:poster_affiliated_party_id]
        @user_poster_layout.update_from_admin_dashboard = true
      end

      h1_count = attrs[:h1_count].present? ? attrs[:h1_count].to_i : 0
      h2_count = attrs[:h2_count].present? ? attrs[:h2_count].to_i : 0

      h1_parsed_data = h2_parsed_data = []
      if params[:buttons].present?
        h1_parsed_data = params[:buttons][:h1].map { |item| JSON.parse(item) } if params[:buttons][:h1].present?
        h2_parsed_data = params[:buttons][:h2].map { |item| JSON.parse(item) } if params[:buttons][:h2].present?
      end

      if (h1_count != h1_parsed_data.count) || (h2_count != h2_parsed_data.count)
        flash[:error] = "Please select circles for header 1 or header 2"
        redirect_to new_admin_user_poster_layout_path
        return
      end

      delete_h1_indexes = delete_h2_indexes = []
      if params[:delete_buttons].present?
        delete_h1_indexes = params[:delete_buttons][:h1].map(&:to_i) if params[:delete_buttons][:h1].present?
        delete_h2_indexes = params[:delete_buttons][:h2].map(&:to_i) if params[:delete_buttons][:h2].present?
      end

      delete_h1_indexes.sort.reverse.each do |index|
        h1_parsed_data.delete_at(index) if index < h1_parsed_data.length
      end

      delete_h2_indexes.sort.reverse.each do |index|
        h2_parsed_data.delete_at(index) if index < h2_parsed_data.length
      end

      @user_poster_layout.h1_count = h1_parsed_data.length
      @user_poster_layout.h2_count = h2_parsed_data.length

      old_leader_photos_hash = []
      @user_poster_layout.user_leader_photos.each do |ulp|
        old_leader_photos_hash << { id: ulp.id,
                                    circle_id: ulp.circle_id,
                                    photo_id: ulp.photo_id,
                                    header_type: ulp.header_type,
                                    priority: ulp.priority,
                                    _destroy: '1'
        }
      end

      new_leader_photos_hash = []
      h1_parsed_data.each_with_index do |hash, index|
        circle_id = hash["circle_id"].present? ? hash["circle_id"] : nil
        protocol_join_circle_ids += [circle_id] if circle_id.present?
        photo_id = hash["photo_id"]

        if circle_id.present?
          photo = Photo.find_by(id: photo_id)
        else
          photo = AdminMedium.find_by(id: photo_id)
        end
        next if photo.blank?

        match_old_hash = old_leader_photos_hash.find do |old_hash|
          old_hash[:circle_id] == circle_id &&
            old_hash[:photo_id] == photo_id &&
            old_hash[:header_type] == "header_1" &&
            old_hash[:priority] == index + 1
        end
        if match_old_hash.blank?
          new_leader_photos_hash << {
            photo: photo,
            header_type: :header_1,
            priority: index + 1,
            circle_id: circle_id,
            creator: current_admin_user
          }
        end
        delete_hash = old_leader_photos_hash.find do |hash|
          delete_h1_indexes.include?(index) &&
            hash[:circle_id] == circle_id &&
            hash[:photo_id] == photo_id &&
            hash[:header_type] == "header_1" &&
            hash[:priority] == index + 1
        end
        old_leader_photos_hash.delete(match_old_hash) unless delete_hash
      end

      h2_parsed_data.each_with_index do |hash, index|
        circle_id = hash["circle_id"].present? ? hash["circle_id"] : nil
        protocol_join_circle_ids += [circle_id] if circle_id.present?
        photo_id = hash["photo_id"]

        if circle_id.present?
          # using Photo model to get photo to handle edits of poster photos in the circle page
          photo = Photo.find_by(id: photo_id)
        else
          photo = AdminMedium.find_by(id: photo_id)
        end
        next if photo.blank?

        match_old_hash = old_leader_photos_hash.find do |old_hash|
          old_hash[:circle_id] == circle_id &&
            old_hash[:photo_id] == photo_id &&
            old_hash[:header_type] == "header_2" &&
            old_hash[:priority] == index + 1
        end
        if match_old_hash.blank?
          new_leader_photos_hash << {
            photo: photo,
            header_type: :header_2,
            priority: index + 1,
            circle_id: circle_id,
            creator: current_admin_user
          }
        end
        delete_hash = old_leader_photos_hash.find do |hash|
          delete_h1_indexes.include?(index) &&
            hash[:circle_id] == circle_id &&
            hash[:photo_id] == photo_id &&
            hash[:header_type] == "header_2" &&
            hash[:priority] == index + 1
        end
        old_leader_photos_hash.delete(match_old_hash) unless delete_hash
      end

      user_leader_photos = old_leader_photos_hash + new_leader_photos_hash
      @user_poster_layout.user_leader_photos_attributes = user_leader_photos if user_leader_photos.present?

      review_layout = false
      old_parsed_data = {
        header_1: @user_poster_layout.user_leader_photos.where(header_type: 'header_1').pluck(:circle_id, :photo_id, :priority),
        header_2: @user_poster_layout.user_leader_photos.where(header_type: 'header_2').pluck(:circle_id, :photo_id, :priority)
      }
      new_parsed_data = {
        header_1: h1_parsed_data.map.with_index(1) { |item, index| [item["circle_id"], item["photo_id"], index] },
        header_2: h2_parsed_data.map.with_index(1) { |item, index| [item["circle_id"], item["photo_id"], index] }
      }
      photo_data = {
        user_poster_photo: @user_poster_layout.entity.poster_photo,
        user_poster_photo_with_background: @user_poster_layout.entity.is_a?(User) ? @user_poster_layout.entity.poster_photo_with_background : nil,
        family_frame_photo: @user_poster_layout.entity.is_a?(User) ? @user_poster_layout.entity.family_frame_photo : nil,
        hero_frame_photo: @user_poster_layout.entity.is_a?(User) ? @user_poster_layout.entity.hero_frame_photo : nil
      }
      photo_changed = photo_data.any? do |photo, photo_value|
        attrs[photo].present? && photo_value.present? && attrs[photo] != photo_value
      end
      new_data = old_parsed_data != new_parsed_data
      review_layout = photo_changed || new_data

      @user_poster_layout.review_status = :awaited if review_layout && @user_poster_layout.entity.is_a?(User) && @user_poster_layout.entity.layout_approval_enabled?

      if @user_poster_layout.entity.save && @user_poster_layout.save
        circle_ids = protocol_join_circle_ids.compact.uniq
        if circle_ids.present? && @user_poster_layout.entity.is_a?(User)
          ProtocolJoinOfUserInCircles.perform_async(@user_poster_layout.entity_id, circle_ids)
        end
        redirect_to admin_user_poster_layout_path(@user_poster_layout), notice: 'User Poster Layout was successfully updated.'
      else
        flash[:error] = @user_poster_layout.errors.full_messages.first if @user_poster_layout.errors.present?
        flash[:error] = @user_poster_layout.entity.errors.full_messages.first if @user_poster_layout.entity.errors.present?
        render :edit
      end
    end

    def create_order(user, premium_package_frame_ids, premium_frame_add_on_ids, status_frame_ids,
                     duration_in_months, total_amount, discount_amount, payable_amount, referred_by)
      @order = Order.new(user_id: user.id, status: :opened, total_amount: total_amount,
                         discount_amount: discount_amount, payable_amount: payable_amount, referred_by: referred_by)

      order_items_array = []
      product_frames_order_items = []

      unless user.is_subscribed_to_frame?(premium_package_frame_ids)
        item_price = ItemPrice.where(item_type: "Product", item_id: Constants.get_poster_product_id, duration_in_months: duration_in_months, active: true).last

        if item_price.present?
          order_items_array << { item_type: "Product", item_id: Constants.get_poster_product_id, item_price_id: item_price.id,
                                 duration_in_months: item_price.duration_in_months, total_item_price: item_price.price + item_price.maintenance_price }

          premium_package_frame_ids.each do |frame_id|
            product_frames_order_items << { item_type: "Frame", item_id: frame_id, item_price_id: nil, duration_in_months: item_price.duration_in_months }
          end
        end
      end

      unless user.is_subscribed_to_frame?(premium_frame_add_on_ids)
        premium_frame_add_on_ids.each do |frame_id|
          item_price = ItemPrice.where(item_type: "Frame", item_id: frame_id, duration_in_months: duration_in_months, active: true).last
          if item_price.present?
            order_items_array << { item_type: "Frame", item_id: frame_id, item_price_id: item_price.id,
                                   duration_in_months: item_price.duration_in_months, total_item_price: item_price.price + item_price.maintenance_price }
          end
        end
      end

      unless user.is_subscribed_to_frame?(status_frame_ids)
        status_frame_ids.each do |frame_id|
          item_price = ItemPrice.where(item_type: "Frame", item_id: frame_id, duration_in_months: duration_in_months, active: true).last
          if item_price.present?
            order_items_array << { item_type: "Frame", item_id: frame_id, item_price_id: item_price.id,
                                   duration_in_months: item_price.duration_in_months, total_item_price: item_price.price + item_price.maintenance_price }
          end
        end
      end

      order_items_array.each do |order_item|
        @order_item = @order.order_items.build(order_item)

        if order_item[:item_type] == "Product"
          product_frames_order_items.each do |product_frames_order_item|
            product_frames_order_item[:parent_order_item] = @order_item
            @order.order_items.build(product_frames_order_item)
          end
        end
      end

      if @order.save
        user.orders.where(status: [:opened, :last_transaction_failed]).where.not(id: @order.id).update_all(status: :closed)
        "Order and Order Items created successfully"
      else
        raise StandardError, "Couldn't able to create order due to #{@order.errors.full_messages.first}"
        # "Couldn't able to create order due to " + @order.errors.full_messages.first
      end
    end

    def check_layout_status
      @user_poster_layout = UserPosterLayout.find(params[:id])
      unless @user_poster_layout.active
        flash[:error] = "You cannot edit an inactive poster layout. Please use the Internal tool."
        redirect_to admin_user_poster_layout_path(@user_poster_layout)
      end
    end
  end

  filter :id
  filter :entity_id, label: 'User Id/Circle Id'
  # filter :se_user_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :admin_users }
  filter :active, as: :select, collection: [['Active', true], ['Inactive', false]]
  filter :created_at
  filter :updated_at
end
