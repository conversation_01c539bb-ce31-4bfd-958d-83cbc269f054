require 'csv'
ActiveAdmin.register Role do
  menu :parent => "Roles"
  searchable_select_options(name: :roles,
                            scope: -> { Role.where(active: true) },
                            display_text: ->(record) {
                              "#{record.name}," + "#{record.parent_circle_level}#{record.parent_circle&.name}"
                            },
                            text_attribute: :name,
                            filter: lambda do |term, scope|
                              scope.ransack(m: 'or', id_start: term, name_cont: term).result
                            end
  )
  searchable_select_options(name: :sub_circle_filter_roles,
                            scope: lambda do
                              Role.where(active: true)
                            end,
                            display_text: ->(record) {
                              record.searchable_select_name_for_sub_circles
                            },
                            text_attribute: :name,
                            filter: lambda do |term, scope|
                              scope.ransack(m: 'or', id_start: term, name_cont: term).result
                            end
  )

  permit_params :name, :name_en, :has_badge, :badge_ring, :badge_color, :quota_type, :quota_value, :grade_level,
                :active, :show_badge_banner, :parent_circle_id, :has_free_text,
                :parent_circle_level, :has_purview, :purview_level, :permission_group_id, :has_badge_icon,
                display_name_order: []

  actions :all, except: [:destroy]

  collection_action :name_or_name_en_suggestion, method: :get do
    query = params[:query]
    language = params[:language]

    suggestions = if language == 'telugu'
                    Role.where('name LIKE ?', "%#{query}%").pluck(:name)
                  else
                    Role.where('LOWER(name_en) LIKE ?', "%#{query.downcase}%").pluck(:name_en)
                  end

    render json: suggestions
  end

  # check whether role has any user role
  member_action :check_role_has_user_role, method: :get do
    role_id = params[:role_id].to_i
    role_has_user_role = UserRole.where(role_id: role_id).exists?

    render json: { success: true, role_has_user_role: role_has_user_role }, status: :ok
  end

  filter :id
  filter :name
  filter :name_en
  filter :grade_level, as: :select, collection: Role.grade_levels
  filter :quota_type, as: :select, collection: Role.quota_types
  filter :has_badge
  filter :active
  filter :show_badge_banner
  filter :parent_circle_id
  filter :parent_circle_level
  filter :has_purview
  filter :purview_level
  filter :permission_group_id, as: :select, collection: PermissionGroup.all
  filter :has_badge_icon

  index do
    selectable_column

    column :id do |role|
      link_to(role.id, admin_role_path(role))
    end
    column :name
    column :display_name_order do |role|
      role.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) }
    end
    column :has_badge
    column "Quota" do |role|
      if role.quota_value.present?
        role.quota_type + " - " + role.quota_value.to_s
      else
        role.quota_type
      end
    end
    column :grade_level
    column :parent_circle
    column :parent_circle_level
    column :has_purview
    column :purview_level
    column :active
    actions name: "Actions"
  end

  show do |role|
    attributes_table do
      row :name
      row :name_en
      row :parent_circle
      row :parent_circle_level
      row :has_purview
      row :purview_level
      row :display_name_order do |role|
        role.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) }
      end
      row :has_badge
      row :has_badge_icon
      row :badge_color
      row :badge_ring
      row :show_badge_banner
      row :has_free_text
      row :grade_level
      row :quota_type
      row :quota_value
      row :creator do |role|
        first_creator = role.versions.first
        if first_creator.present?
          creator = AdminUser.find_by_id(first_creator.whodunnit)
          if creator.present?
            link_to(creator.email, admin_user_path(creator))
          else
            '-'
          end
        else
          '-'
        end
      end
      row :active
      row :created_at
      row :updated_at
    end

    panel 'Edit History' do
      table_for PaperTrail::Version.where(item_type: 'Role', item_id: role.id).order(id: :desc).limit(10) do
        column('Item') { |v| v.item }
        column('Changes') do |v|
          if v.object_changes
            changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
            filtered_changes = changes.reject { |field, _| field == "created_at" || field == "updated_at" }
            filtered_changes.map do |field, values|
              old_value = values[0].nil? ? 'nil' : values[0].to_s
              new_value = values[1].nil? ? 'nil' : values[1].to_s
              "#{field}: #{old_value} -> #{new_value}"
            end.join(', ')
          else
            'No changes recorded'
          end
        end
        column('Modified at') { |v| v.created_at }
        column('Admin') do |v|
          if v.whodunnit.nil?
            ''
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end
    active_admin_comments
  end

  form do |f|
    f.semantic_errors
    f.inputs "Role Details" do
      # hide id field
      f.input :id, as: :hidden unless f.object.new_record?
      f.input :name, input_html: { id: 'role_name' }
      f.input :name_en, required: true, input_html: { id: 'role_name_en' }
      # if parent circle id is present, then parent circle level should be hide
      f.input :parent_circle, as: :searchable_select, ajax: { resource: Circle, collection_name: :all_circles },
              input_html: { class: 'role_parent_circle_id can_clear_selection', "data-placeholder": "search by circle id or name" }

      # Parent circle level
      f.input :parent_circle_level, as: :select, collection: Circle.levels.keys, input_html: { class: 'role_parent_circle_level' }
      f.input :parent_circle_level, as: :hidden, input_html: { id: 'hidden_role_parent_circle_level' }

      # Has purview
      f.input :has_purview, as: :boolean, input_html: { data: { if: 'not_checked', then: 'hide', target: '.has_purview' }, class: 'role_has_purview' }
      f.input :has_purview, as: :hidden, input_html: { id: 'hidden_role_has_purview' }

      # Purview level
      f.input :purview_level, as: :select, collection: Circle.levels.keys, wrapper_html: { class: 'has_purview' }, input_html: { class: 'role_purview_level' }
      f.input :purview_level, as: :hidden, input_html: { id: 'hidden_role_purview_level' }

      f.input :has_badge, input_html: { data: { if: 'not_checked', then: 'hide', target: '.has_badge' }, class: 'role_has_badge' }
      f.input :badge_color, as: :select, collection: Role.badge_colors.keys, wrapper_html: { class: 'has_badge' }
      f.input :badge_ring, as: :select, collection: [true, false], wrapper_html: { class: 'has_badge' }
      f.input :show_badge_banner, wrapper_html: { class: 'has_badge' }
      f.input :has_badge_icon, as: :select, collection: [true, false], wrapper_html: { class: 'has_badge' }

      if f.object.new_record?
        f.input :display_name_order,
                label: 'Display Name Order',
                as: :searchable_select,
                multiple: true,
                collection: Role::DISPLAY_NAME.keys,
                input_html: { class: 'role-display-name-select', "data-placeholder": "select them in order of how you want display name" },
                hint: "select display name keys in order of how you want display name"
      else
        # collection should be enum values of selected values of display_name_order followed by not selected values of display_name_order
        f.input :display_name_order,
                label: 'Display Name Order',
                as: :searchable_select,
                multiple: true,
                collection: f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) } +
                  (Role::DISPLAY_NAME.keys - f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) }),
                selected: f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) },
                input_html: { class: 'role-display-name-select', "data-placeholder": "select them in order of how you want display name" },
                hint: "select display name keys in order of how you want display name"
      end

      f.input :has_free_text
      f.input :grade_level, as: :select, collection: Role.grade_levels.keys
      f.input :quota_type, as: :select, collection: Role.quota_types.keys, input_html: { data: { eq: 'no_limit', then: 'hide', target: '.is_no_limit' } }
      f.input :quota_value, wrapper_html: { class: 'is_no_limit' }
      f.input :permission_group_id, as: :select, collection: PermissionGroup.all
      f.input :active
    end
    f.actions
  end

  controller do

    before_action :check_admin_user, only: [:new, :edit, :destroy]

    def current_user
      current_admin_user
    end

    def check_admin_user
      begin
        authorize current_admin_user, :can_manage_roles?
      rescue Pundit::NotAuthorizedError
        redirect_to admin_roles_path, alert: 'You are not authorized to perform this action.'
      end
    end

    def update
      attrs = permitted_params[:role]
      @role = Role.find(params[:id])
      @role.name = attrs[:name].strip
      @role.name_en = attrs[:name_en].strip
      @role.parent_circle_id = attrs[:parent_circle_id]
      @role.parent_circle_level = attrs[:parent_circle_level]
      @role.has_purview = attrs[:has_purview]
      @role.purview_level = attrs[:purview_level]
      @role.grade_level = attrs[:grade_level]
      @role.quota_type = attrs[:quota_type]
      @role.quota_value = attrs[:quota_value]
      @role.has_badge = attrs[:has_badge]
      @role.badge_color = attrs[:badge_color]
      @role.badge_ring = attrs[:badge_ring]
      @role.show_badge_banner = attrs[:show_badge_banner]
      @role.has_badge_icon = attrs[:has_badge_icon]
      @role.has_free_text = attrs[:has_free_text]
      @role.permission_group_id = attrs[:permission_group_id]
      @role.active = attrs[:active]

      display_name_order = attrs[:display_name_order].compact_blank

      display_name_order = display_name_order.present? ? display_name_order.map { |display_name| Role::DISPLAY_NAME[display_name.to_sym] }.join(",") : ''
      @role.display_name_order = display_name_order
      if @role.save
        redirect_to admin_role_path(@role)
      else
        flash[:error] = @role.errors.full_messages.first
        render :edit
      end
    end

    def create
      attrs = permitted_params[:role]
      attrs[:name] = attrs[:name].strip
      attrs[:name_en] = attrs[:name_en].strip
      display_name_order = attrs[:display_name_order].compact_blank

      attrs[:display_name_order] = display_name_order.present? ? display_name_order.map { |display_name| Role::DISPLAY_NAME[display_name.to_sym] }.join(",") : ''
      @role = Role.new(attrs)
      if @role.save
        redirect_to admin_role_path(@role)
      else
        flash[:error] = @role.errors.full_messages.first
        render :new
      end
    end
  end
end
