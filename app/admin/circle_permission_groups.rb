ActiveAdmin.register CirclePermissionGroup do
  menu :parent => "Permissions"
  includes :permission_group
  permit_params :circle_id, :circle_type, :circle_level, :permission_group_id, :is_user_joined
  actions :all, except: [:destroy]
  
  filter :circle_id
  filter :circle_type, as: :select, collection: CirclePermissionGroup.circle_types
  filter :circle_level, as: :select, collection: CirclePermissionGroup.circle_levels
  filter :permission_group
  filter :is_user_joined

  form do |f|
    f.inputs do
      f.input :circle_id, label: "Circle ID", input_html: { disabled: !f.object.new_record? }
      f.input :circle_type, as: :select, collection: CirclePermissionGroup.circle_types.keys,
              input_html: { disabled: !f.object.new_record? }
      f.input :circle_level, as: :select, collection: CirclePermissionGroup.circle_levels.keys,
              input_html: { disabled: !f.object.new_record? }
      f.input :permission_group, as: :select, collection: PermissionGroup.all.map { |permission_group| [permission_group.name, permission_group.id] },
              label: "Permission Group", required: true
      f.input :is_user_joined, as: :select, collection: [true, false]

    end
    f.actions
  end

end
