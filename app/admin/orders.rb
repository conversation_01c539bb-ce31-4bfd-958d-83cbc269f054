ActiveAdmin.register Order do
  menu priority: 4, :parent => "Posters V2"
  permit_params :total_amount, :payable_amount, :discount_amount, :duration_in_months, :status,
                :user_id, :referred_by

  actions :all, except: [:destroy, :new]

  # this code is to avoid edit action to be displayed for orders
  # i want edit action to be dispayed in user poster layouts dashboard for an open order
  config.action_items.delete_if { |item|
    # item is an ActiveAdmin::ActionItem
    item.display_on?(:show)
  }

  config.sort_order = "id_desc"

  filter :id
  filter :user_id
  filter :status, as: :select, collection: Order.statuses
  filter :created_at

  member_action :get_amount_data_of_open_order, method: :get do
    duration = params[:duration].to_i
    user_id = params[:user_id].to_i
    amount = 0

    item_price = ItemPrice.where(item_type: "Product",
                                 item_id: Constants.get_poster_product_id,
                                 active: true,
                                 duration_in_months: duration).last

    maintenance_price = item_price&.maintenance_price.to_i

    amount += item_price&.price.to_i + item_price&.maintenance_price.to_i
    user = User.find_by(id: user_id)
    order = user.orders.open.last
    frame_ids = order.order_items.where(item_type: "Frame").pluck(:item_id)
    selected_status_frame_ids = Frame.where(id: frame_ids, frame_type: :status).pluck(:id)
    if selected_status_frame_ids.present?
      status_frames_amount = ItemPrice.get_frames_price(selected_status_frame_ids, duration).sum
      if status_frames_amount.present?
        amount += status_frames_amount.to_i
      else
        render json: { success: false, message: "Status frames doesn't have the given duration price assigned" }, status: :ok
        return
      end
    end

    render json: { success: true, amount: amount, maintenance_price: maintenance_price }, status: :ok

  end

  index do
    selectable_column
    id_column
    column :user
    column :status do |order|
      status_tag(order.status)
    end
    column :total_amount do |order|
      number_to_currency(order.total_amount, unit: "₹")
    end
    column :discount_amount do |order|
      number_to_currency(order.discount_amount, unit: "₹")
    end
    column :payable_amount do |order|
      number_to_currency(order.payable_amount, unit: "₹")
    end
    column :referred_by do |order|
      referred = User.find_by(id: order.referred_by)
      link_to(referred.name, admin_user_path(referred)) if referred.present?
    end
    column :created_at
    # show only view action
    actions name: "Actions", defaults: false do |order|
      item "View", admin_order_path(order), class: "member_link"
    end
  end

  show do
    attributes_table do
      row :id
      row :user
      row :status do |order|
        status_tag(order.status)
      end
      row :total_amount do |order|
        number_to_currency(order.total_amount, unit: "₹")
      end
      row :discount_amount do |order|
        number_to_currency(order.discount_amount, unit: "₹")
      end
      row :payable_amount do |order|
        number_to_currency(order.payable_amount, unit: "₹")
      end
      row :referred_by do |order|
        referred = User.find_by(id: order.referred_by)
        link_to(referred.name, admin_user_path(referred)) if referred.present?
      end
      row :created_at
    end
    panel "Transactions" do
      table_for order.order_transactions do
        column :id
        column :transaction_id
        column :status do |transaction|
          status_tag(transaction.status)
        end
        column :amount do |transaction|
          number_to_currency(transaction.amount, unit: "₹")
        end
        column :created_at
      end
    end
    panel "Products" do
      table_for order.order_items.order(id: :asc) do
        column :id
        column :item_type do |order_item|
          order_item.item_type
        end
        column :item do |order_item|
          if order_item.item_type == "Product"
            order_item.item.name
          elsif order_item.item_type == "Frame"
            order_item.item.identifier
          end
        end
        column :duration_in_months do |order_item|
          "#{order_item.duration_in_months} months"
        end
        column :total_item_price do |order_item|
          number_to_currency(order_item.total_item_price, unit: "₹")
        end
      end
    end
  end

  # write only update action
  form multipart: true do |f|
    f.semantic_errors
    f.inputs "Order" do
      unless f.object.new_record?
        f.input :user_id, input_html: { disabled: true, id: 'order_user_id_input', value: f.object.user_id }
        f.input :user_id, as: :hidden, input_html: { id: 'hidden_order_user_id_input' }
        f.input :status, input_html: { disabled: true, value: f.object.status }
        f.input :duration_in_months, as: :select, collection: [['6 months', 6], ['12 months', 12]],
                input_html: { id: 'duration_in_months_input' }, selected: f.object.order_items.last.duration_in_months

        f.input :total_amount, input_html: { id: 'total_amount_input', disabled: true, value: f.object.total_amount }
        f.input :total_amount, as: :hidden, input_html: { id: 'hidden_total_amount_input' }
        f.input :discount_amount, input_html: { id: 'discount_amount_input' }
        f.input :payable_amount, input_html: { id: 'payable_amount_input', disabled: true, value: f.object.payable_amount }
        f.input :payable_amount, as: :hidden, input_html: { id: 'hidden_payable_amount_input' }
      end
    end
    f.inputs do
      para "Max Discount Amount: ", id: 'max_discount_amount_display', style: 'color: blue; font-weight: bold; font-size: 15px'
    end
    f.actions
  end

  controller do
    def update
      attrs = permitted_params[:order]

      discount_amount = attrs[:discount_amount].to_i
      total_amount = attrs[:total_amount].to_i
      payable_amount = attrs[:payable_amount].to_i
      order = Order.find(params[:id])
      order.payable_amount = payable_amount
      order.total_amount = total_amount
      order.discount_amount = discount_amount
      order.order_items.each do |order_item|
        order_item.duration_in_months = attrs[:duration_in_months].to_i
        if order_item.item_price_id.present?
          item_price = ItemPrice.where(item: order_item.item, duration_in_months: order_item.duration_in_months).last
          order_item.item_price_id = item_price.id
          order_item.total_item_price = item_price.price + item_price.maintenance_price
        end
        order_item.save
      end
      order.save
      redirect_to admin_order_path(order)
    end
  end
end
