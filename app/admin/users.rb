ActiveAdmin.register User do
  searchable_select_options(name: :internals,
                            scope: -> { User.active.where("internal = 1 OR phone LIKE '2%' OR phone LIKE '3%' OR phone LIKE '4%'") },
                            display_text: ->(record) { "#{record.name} (#{record.phone})" },
                            text_attribute: :name,
                            filter: lambda do |term, scope|
                              scope.ransack(phone_eq: term).result
                            end
  )
  searchable_select_options(name: :internals_prime,
                            scope: lambda do |params|
                              post_id = params['post_id']
                              User
                                .joins('INNER JOIN user_groups ON user_groups.user_id = users.id')
                                .joins("LEFT JOIN post_likes ON post_likes.user_id = users.id AND post_likes.post_id = #{post_id}")
                                .active
                                .where('user_groups.active = 1 AND post_likes.id IS NULL')
                            end,
                            display_text: ->(record) do
                              "#{record.name} (#{record.phone}) (#{record.user_groups.first.name})"
                            end,
                            text_attribute: :name,
                            filter: lambda do |term, scope|
                              scope.ransack(m: 'or', phone_start: term, user_groups_name_cont: term).result
                            end
  )
  searchable_select_options(name: :users,
                            scope: -> {
                              User.active
                            },
                            display_text: ->(record) {
                              "#{record.id},#{record.name},#{record.phone}"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(id_eq: term, phone_eq: term, m: 'or').result
                            end
  )

  # See permitted parameters documentation:
  # https://github.com/activeadmin/activeadmin/blob/master/docs/2-resource-customization.md#setting-up-strong-parameters
  #
  # Uncomment all parameters which should be permitted for assignment
  #
  permit_params :name, :active, :verified, :show_phone, :village_id, :photo, :internal_journalist, :dob, :marketing_consent, :birth_place_id, :education,
                :office_address, :contact_email, :contact_phone, :email, :status

  actions :all, except: [:create, :destroy]

  # member_action :generate_invite_card, method: :get do
  #   GenerateInviteCard.perform_async(resource.id)
  #   redirect_to resource_path, notice: "Triggered!"
  # end

  member_action :create_circle_flow, method: :get do
    resource.create_circle_flow(current_admin_user.id)
    redirect_to resource_path, notice: "Circle Creation Flow Triggered!"
  end

  member_action :block_for_tagging, method: :get do
    resource.block_for_tagging
    redirect_to resource_path, notice: "Blocked for tagging!"
  end

  member_action :block_for_commenting, method: :get do
    resource.block_for_commenting
    redirect_to resource_path, notice: "Blocked for commenting!"
  end

  member_action :unblock_for_tagging, method: :get do
    resource.unblock_for_tagging
    redirect_to resource_path, notice: "Unblocked for tagging!"
  end

  member_action :unblock_for_commenting, method: :get do
    resource.unblock_for_commenting
    redirect_to resource_path, notice: "Unblocked for commenting!"
  end

  member_action :nearest_paid_users, method: :get do
    begin
      @nearest_users = resource.find_nearest_paid_user
      render 'admin/users/nearest_paid_users'
    rescue => e
      redirect_to resource_path, notice: "Error: #{e.message}"
    end
  end

  member_action :sync_mixpanel, method: :get do
    begin
      resource.send_to_mixpanel
      redirect_to resource_path, notice: "User Mixpanel sync has been started."
    rescue StandardError => e
      redirect_to resource_path, notice: "Error: #{e.message}"
    end
  end

  member_action :fresh_lead_to_crm, method: :get do
    @user = User.find(params[:id])
    render 'admin/users/fresh_lead_to_crm'
  end

  member_action :send_fresh_lead_to_crm, method: :post do
    begin
      resource.send_as_fresh_lead_to_crm(params[:lead_type_suffix])
      redirect_to resource_path, notice: "Fresh lead added to Floww CRM!"
    rescue StandardError => e
      redirect_to resource_path, notice: "Error: #{e.message}"
    end
  end

  member_action :ban, method: :put do
    begin
      resource.ban!
      redirect_to resource_path, notice: "Banned!"
    rescue StandardError => e
      redirect_to resource_path, notice: "Error: #{e.message}"
    end
  end

  member_action :unban, method: :put do
    begin
      resource.unban!
      redirect_to resource_path, notice: "Activated!"
    rescue StandardError => e
      redirect_to resource_path, notice: "Error: #{e.message}"
    end
  end

  action_item :add_leader_project, only: :show do
    if UserCirclePermissionGroup.where(user_id: resource.id, permission_group_id: Constants.owner_permission_group_id).exists?
      link_to 'Add Project', new_admin_user_leader_project_path(user)
    end
  end

  # action_item :generate, only: :show do
  #   link_to 'Generate Invite Card', generate_invite_card_admin_user_path(user) if user.present?
  # end

  action_item :create_circle, only: :show do
    if resource.get_badge_role.present? &&
       %i[grade_3 grade_4].include?(resource.get_badge_role.get_grade_level.to_sym) &&
       !Metadatum.where(key: Constants.user_with_circle_key, value: resource.id).exists?
      if resource.has_premium_layout?
        layout = resource.get_user_poster_layout
        layout_type = "layout_1_#{layout.h1_count + layout.h2_count}".to_sym
        if UserPosterLayout::LAYOUTS.keys.include?(layout_type)
          prompt_message = "Creates a circle along with the layout of the user's circle."
        else
          prompt_message = "!! Creates only circle without layout of the user's circle.\nUser's layout is not supporting for circle layout: #{layout_type}.\nCreate a layout for the user's circle manually."
        end
        link_to 'Create Circle', create_circle_flow_admin_user_path(user), data: { confirm: prompt_message }
      else
        link_to 'Create Circle', '#', class: 'disabled-button', onclick: "alert('User poster layout for user is required'); return false;"
      end
    end
  end

  action_item :paid_referral_points, only: :show do
    link_to 'Mark Payment of Referral Points', new_admin_user_user_points_ledger_path(user)
  end

  action_item :nearest_paid_users, only: :show do
    if user.active_status?
      link_to 'Find Nearest Paid Users', nearest_paid_users_admin_user_path(user), class: 'activate-button'
    end
  end

  action_item :sync_mixpanel, only: :show do
    if user.active_status?
      link_to 'Sync Mixpanel', sync_mixpanel_admin_user_path(user)
    end
  end

  action_item :block_for_tagging, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Block for tagging', block_for_tagging_admin_user_path(user), class: 'danger-outline-button' unless user.is_blocked_for_tagging?
    end
  end

  action_item :unblock_for_tagging, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Unblock for tagging', unblock_for_tagging_admin_user_path(user), class: 'activate-outline-button' if user.is_blocked_for_tagging?
    end
  end

  action_item :block_for_commenting, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Block for commenting', block_for_commenting_admin_user_path(user), class: 'danger-outline-button' unless user.is_blocked_for_commenting?
    end
  end

  action_item :unblock_for_commenting, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Unblock for commenting', unblock_for_commenting_admin_user_path(user), class: 'activate-outline-button' if user.is_blocked_for_commenting?
    end
  end

  action_item :fresh_lead_to_crm, only: :show do
    if user.active_status? && user.get_floww_contact_id.blank? && current_admin_user.role.to_sym.in?([:admin, :sales_am])
      link_to 'Add as referral in CRM', fresh_lead_to_crm_admin_user_path(user), class: 'activate-button'
    end
  end

  action_item :ban, only: :show do
    if !user.banned? && user.may_ban? && current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Ban', ban_admin_user_path(user), method: :put, class: 'danger-button', data: { confirm: 'Are you sure you want to ban this user?' }
    end
  end

  action_item :unban, only: :show do
    if user.banned? && user.may_unban? && current_admin_user.role.to_sym.in?([:admin, :content_creator])
      link_to 'Un-ban', unban_admin_user_path(user), method: :put, class: 'activate-button', data: { confirm: 'Are you sure you want to un-ban this user?' }
    end
  end

  index pagination_total: false do
    selectable_column

    column :id do |user|
      link_to(user.id, admin_user_path(user))
    end
    column :name
    column :phone
    column :village do |user|
      user.village.present? ? link_to(user.village.name, admin_circle_path(user.village)) : "-"
    end
    column :mandal do |user|
      user.mandal.present? ? link_to(user.mandal.name, admin_circle_path(user.mandal)) : "-"
    end
    column :district do |user|
      user.district.present? ? link_to(user.district.name, admin_circle_path(user.district)) : "-"
    end
    column :state do |user|
      user.state.present? ? link_to(user.state.name, admin_circle_path(user.state)) : "-"
    end
    column :status do |u|
      status_tag(u.status)
    end
    column :created_at
  end

  show do |user|
    attributes_table do
      row :id
      row :name
      row :phone
      row :badge do |u|
        user_role = u.get_badge_role
        user_role.present? ?
          link_to(user_role.get_description, admin_user_role_path(user_role)) :
          link_to("Add Badge?", new_admin_user_role_path)
      end
      row :village, title: "village, mandal, district, state" do |u|
        u.village.present? ?
          link_to(u.village.name, admin_circle_path(u.village)) + ", " +
            link_to(u.mandal.name, admin_circle_path(u.mandal)) + ", " +
            link_to(u.district.name, admin_circle_path(u.district)) + ", " +
            link_to(u.state.name, admin_circle_path(u.state)) :
          '-'
      end
      row :photo do |u|
        image_tag u.photo.url, class: 'thumb_size' if u.photo.present?
      end
      row :status do |u|
        status_tag(u.status)
      end
      row :email
      row :gender
      row :short_bio
      row :dob
      row :birth_place do |u|
        u.birth_place.present? ? link_to(u.birth_place.name, admin_circle_path(u.birth_place)) : '-'
      end
      row :education
      row :office_address
      row :contact_email
      row :contact_phone
      # row :internal_journalist
      row :marketing_consent
      row :invite_card_url do |u|
        u.invite_card.present? ? link_to(u.invite_card, u.invite_card) : ""
      end
      row :rm_user do |u|
        rm_user = u.get_rm_user
        if rm_user.present?
          link_to(rm_user.name, admin_admin_user_path(rm_user))
        else
          '-'
        end
      end
      # row :total_points_earned do |u|
      #   u.get_total_referral_points_earned
      # end
      # row :total_points_paid do |u|
      #   u.get_total_referral_points_paid
      # end
      row :created_at
      row :updated_at
    end

    if user.user_roles.present?
      panel 'Badges' do
        table_for UserRole.where(user: user, active: true) do
          column('ID') { |ub| link_to(ub.id, admin_user_role_path(ub)) }
          column('Color') { |ub| ub.get_badge_color }
          column('Ring') { |ub| ub.get_badge_ring }
          column('Role name') { |ub| link_to(ub.role.name, admin_role_path(ub.role)) }
          column('Circle ID') do |ub|
            circle = Circle.find_by_id(ub.parent_circle_id || ub.role.parent_circle_id)
            link_to(circle.name, admin_circle_path(circle)) if circle.present?
          end
        end
      end
    end

    if user.leader_projects.present?
      panel 'Leader Projects' do
        table_for user.leader_projects do
          column('ID') { |lp| link_to(lp.id, admin_user_leader_project_path(user, lp)) }
          column('User Role ID') { |lp| link_to(lp.user_role_id, admin_user_role_path(lp.user_role)) }
          column('Role Name') { |lp| lp.user_role.role.name }
          column('Title') { |lp| lp.title }
          column('Body') { |lp| lp.body.truncate(27, separator: ' ') }
          column('Active') { |lp| lp.active }
          column('Project Date') { |lp| lp.project_date }
          column('Creator') { |lp| link_to(lp.creator.email, admin_admin_user_path(lp.creator)) }
          column('Actions') { |lp|
            links = []
            links << link_to("View", admin_user_leader_project_path(user, lp))
            links << link_to("Edit", edit_admin_user_leader_project_path(user, lp))
            links << link_to("Delete", admin_user_leader_project_path(user, lp), :method => :delete, :data => { :confirm => "Are you sure?" })
            links.join(" ").html_safe
          }
        end
      end
    end

    panel 'Joined Circles' do
      table_for UserCircle.joins(:circle).where(user: user, circles: { active: true }).order(id: :desc) do
        column('Circle') { |uc| link_to(uc.circle.name, admin_circle_path(uc.circle)) }
        column('Level') { |uc| uc.circle.level.humanize }
        column('Joined at') { |v| v.created_at }
      end
    end
    columns do
      column do
        panel 'Latest Posts' do
          table_for Post.where(user: user, active: true).order(id: :desc).limit(5) do
            column('Post') { |p| link_to(p.content.present? ? p.content.truncate(27, separator: ' ') : "Post##{p.id}", admin_post_path(p)) }
            column('Posted at') { |v| v.created_at }
          end
        end
      end
      column do
        panel 'Latest Opinions' do
          table_for Post.where(user: user, active: true).where.not(parent_post: nil).order(id: :desc).limit(5) do
            column('Post') { |p| link_to(p.content.present? ? p.content.truncate(27, separator: ' ') : "Post##{p.id}", admin_post_path(p)) }
            column('Post Circle') { |p|
              if p.circles.present?
                p.circles.each do |pc|
                  link_to(pc.name, admin_circle_path(pc))
                end
              else
                'Public'
              end
            }
            column('Opinioned at') { |v| v.created_at }
          end
        end
      end
    end

    columns do
      column do
        panel 'Latest Trends' do
          table_for PostLike.joins(:post).where(user: user, active: true, posts: { active: true }).order("post_likes.id DESC").limit(5) do
            column('Post') { |pl| link_to(pl.post.content.present? ? pl.post.content.truncate(27, separator: ' ') : "Post##{pl.post.id}", admin_post_path(pl.post)) }
            column('Post Circle') { |pl|
              if pl.post.circles.present?
                pl.post.circles.each do |plc|
                  link_to(plc.name, admin_circle_path(plc))
                end
              else
                'Public'
              end
            }
            column('Trended at') { |v| v.created_at }
          end
        end
      end
      column do
        panel 'Latest Comments' do
          table_for PostComment.where(user: user, active: true).order(id: :desc).limit(5) do
            column("ID") { |pc| link_to(pc.id, admin_post_comment_path(pc)) }
            column('Comment') { |pc| pc.comment }
            column('Commented at') { |v| v.created_at }
          end
        end
      end
    end

    panel 'Edit History' do
      table_for PaperTrail::Version.where(item_type: 'User', item_id: user.id).order(id: :desc).limit(10) do
        column('Item') { |v| v.item }
        column('Modified at') { |v| v.created_at }
        column('Admin') do |v|
          if v.whodunnit.nil?
            ''
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    active_admin_comments
  end

  form multipart: true do |f|
    f.semantic_errors
    f.inputs 'User Details' do
      f.input :name #, disabled: true, input_html: {:readonly => true}
      f.input :village_id, as: :searchable_select, ajax: { resource: Circle, collection_name: :villages }
      f.input :photo, as: :file, hint: (image_tag(f.object.photo.url, class: 'thumb_size') if f.object.photo.present?)
      f.input :status, as: :select, collection: User.statuses if current_admin_user.role.to_sym == :admin
      f.input :email, label: 'Email / Login Email'
      f.input :dob, as: :date_picker
      f.input :birth_place_id, as: :searchable_select, ajax: { resource: Circle, collection_name: :villages }
      f.input :education
      f.input :office_address
      f.input :contact_email
      f.input :contact_phone
      f.input :verified
      f.input :show_phone
      f.input :internal_journalist
      f.input :marketing_consent
    end
    f.actions
  end

  controller do
    def update
      attrs = permitted_params[:user]

      @user = User.find_by_hashid!(params[:id])
      @user.name = attrs[:name]
      @user.status = attrs[:status] if attrs[:status].present?
      @user.active = attrs[:active]
      @user.email = attrs[:email].downcase if attrs[:email].present?
      @user.verified = attrs[:verified]
      @user.show_phone = attrs[:show_phone]
      @user.internal_journalist = attrs[:internal_journalist]
      @user.marketing_consent = attrs[:marketing_consent]
      @user.dob = attrs[:dob]
      @user.birth_place_id = attrs[:birth_place_id]
      @user.education = attrs[:education]
      @user.office_address = attrs[:office_address]
      @user.contact_email = attrs[:contact_email]
      @user.contact_phone = attrs[:contact_phone]

      unless attrs[:photo].nil?
        @user.photo = Photo.upload(attrs[:photo], @user.id)
      end

      if !attrs[:village_id].nil? && attrs[:village_id] != '' && attrs[:village_id] != '0'
        @user.circles = @user.circles - @user.get_location_circles
        village_id = attrs[:village_id].to_i
        @user.user_circles.build(user: @user, circle_id: village_id)
        @user.village_id = village_id
        @user.populate_location
      end

      if @user.save
        redirect_to admin_user_path(@user)
      else
        render :edit
      end
    end
  end

  filter :id
  filter :phone, input_html: { minlength: 10, maxlength: 10 }
  filter :name
  filter :status
  filter :verified
  filter :show_phone
  filter :created_at
  filter :updated_at
  filter :internal_journalist
  filter :marketing_consent
end
