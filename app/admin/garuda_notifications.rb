# frozen_string_literal: true

ActiveAdmin.register GarudaNotification, :as => "Notifications" do
  permit_params :title, :body, :path, :send_at, :location_circle_id, :party_circle_id, :exclusive_party_users, :admin_user_id
  menu if: proc{ role = current_admin_user.role.to_sym.in?([:admin, :content_creator, :graphic_designer]) }
  actions :all, except: [:edit, :destroy]

  member_action :stop, method: :put do
    begin
      resource.stop!
      flash[:notice] = "Notification stopped!"
    rescue
      flash[:error] = "Notification could not be stopped!"
    end
    redirect_to admin_notifications_path
  end

  collection_action :send_test_notification, method: :post do
    begin
      GarudaNotification.trigger_notification_for_user_id(
        params[:user_id],
        params[:title],
        params[:body],
        params[:path]
      )
      render json: { success: true, message: "Notification sent!" }
    rescue StandardError => e
      render json: { success: false, message: "#{e}" }
    end
  end

  index do
    selectable_column
    id_column
    column :title
    column :body
    column :send_at
    column :status do |gn|
      status_tag(gn.status)
    end
    column :sent_count
    column "Time taken to send" do |gn|
      gn.time_taken_to_send_str
    end
    column :circle do |gn|
      if gn.location_circle_id.present?
        if gn.location_circle_id == 0
          span style: "text-decoration: underline;" do
            "All Users"
          end
        else
          link_to(gn.location_circle.name + " (#{gn.location_circle.level.titleize})",
                  admin_circle_path(gn.location_circle))
        end
      elsif gn.party_circle_id.present?
        link_to(gn.party_circle.name + " (party) " + (gn.exclusive_party_users ? "(Excl.)" : "(Non-Excl.)"),
              admin_circle_path(gn.party_circle))
      else
        span style: "text-decoration: underline;" do
          "All Users"
        end
      end
    end
    column :admin_user
    actions default: true, name: "Actions" do |gn|
      link_to 'Stop', stop_admin_notification_path(gn), method: :put, data: { confirm: 'Are you sure you want to stop this notification?' } if gn.status == 'created' && Sidekiq::ScheduledSet.new.find_job(gn.sidekiq_job_id).present?
    end
  end

  form do |f|
    f.inputs do
      f.input :title
      f.input :body
      f.input :path, input_html: { value: params[:path] || (params[:garuda_notification].present? ? params[:garuda_notification][:path] : nil)}
      f.input :send_at, as: :datetime_picker
      f.input :location_circle_id,
              label: 'Location Circle (select Public for all users)',
              as: :searchable_select,
              ajax: { resource: Circle, collection_name: :garuda_location_circles },
              input_html: { autocomplete: 'off', class: 'can_clear_selection', "data-placeholder": "Search by ID or name.." }
      f.input :party_circle_id,
              label: 'Party Circle',
              as: :searchable_select,
              ajax: { resource: Circle, collection_name: :garuda_party_circles },
              input_html: { autocomplete: 'off', class: 'can_clear_selection', "data-placeholder": "Search by ID or name.." }
      f.input :exclusive_party_users, label: 'Send to Exclusive Party Users Only', as: :boolean
      f.input :admin_user_id, as: :hidden, input_html: { value: current_admin_user.id }
    end
    f.actions do
      f.action :submit, label: 'Send', button_html: { class: 'activate-button' }
      f.button 'Test', class: 'preview-button notification-test', type: 'button'
      f.cancel_link(admin_notifications_path)
    end
  end

  show do
    attributes_table do
      row :id
      row :notification_id
      row :title
      row :body
      row :path
      row :status do |gn|
        status_tag(gn.status)
      end
      row :sent_count
      row :location_circle
      row :party_circle
      row :exclusive_party_users
      row :send_at
      row :sent_at
      row "Time taken to send" do |gn|
        gn.time_taken_to_send_str
      end
      row :admin_user
      row :created_at
    end
  end
end
