ActiveAdmin.register_page "Dashboard" do
  menu priority: 1, label: proc { I18n.t("active_admin.dashboard") }

  content title: proc { I18n.t("active_admin.dashboard") } do
    # div class: "blank_slate_container", id: "dashboard_default_message" do
    #   span class: "blank_slate" do
    #     span I18n.t("active_admin.dashboard_welcome.welcome")
    #     small I18n.t("active_admin.dashboard_welcome.call_to_action")
    #   end
    # end

    # Here is an example of a simple dashboard with columns and panels.

    columns do
      column do
        panel "Recent Posts" do
          ul do
            Post.where(active: true).order(id: :desc).limit(5).map do |post|
              li link_to(post.content.present? ? post.content.truncate(27, separator: ' ') : "Post##{post.id}", admin_post_path(post))
            end
          end
        end
      end

      column do
        panel "Recent Signed Up Users" do
          ul do
            User.active.order(id: :desc).limit(5).map do |user|
              li link_to(user.name, admin_user_path(user))
            end
          end
        end
      end
    end
    columns do
      column do
        panel "Recently updated content" do
          table_for PaperTrail::Version.order(id: :desc).limit(10) do # Use PaperTrail::Version if this throws an error
            column("Item") { |v| v.item }
            column("Type") { |v| v.item_type.underscore.humanize }
            column("Modified at") { |v| v.created_at }
            column("Admin") do |v|
              if v.whodunnit.nil?
                ""
              elsif v.whodunnit == 'unknown'
                "Unknown"
              else
                link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
              end
            end
          end
        end
      end
    end
  end # content
end
