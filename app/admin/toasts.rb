ActiveAdmin.register Toast do
  permit_params :name, :title, :body, :circle_id, :active, :start_time, :end_time, :admin_user_id

  actions :all, except: [:destroy]

  form do |f|
    f.semantic_errors
    f.inputs "Toast Details" do
      if f.object.new_record?
        f.input :name
        f.input :title
        f.input :body
        f.input :circle_id
        f.input :start_time, as: :datetime_picker
        f.input :end_time, as: :datetime_picker
        f.input :active
      else
        f.input :active
      end
    end
    f.actions
  end

  controller do
    def update
      attrs = permitted_params[:toast]

      @toast = Toast.find(params[:id])
      @toast.active = attrs[:active]

      if @toast.save
        redirect_to admin_toast_path(@toast)
      else
        render :edit
      end
    end

    def create
      attrs = permitted_params[:toast]

      @toast = Toast.new(
        name: attrs[:name],
        title: attrs[:title],
        body: attrs[:body],
        start_time: attrs[:start_time],
        end_time: attrs[:end_time],
        circle_id: attrs[:circle_id],
        admin_user_id: current_admin_user.id
      )

      if @toast.save
        redirect_to admin_toast_path(@toast)
      else
        flash[:error] =  @toast.errors.full_messages if @toast.errors.present? || @toast.poster_photos.map(&:errors).present?
        render :new
      end


    end
  end
end
