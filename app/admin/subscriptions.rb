# frozen_string_literal: true

ActiveAdmin.register Subscription do
  permit_params :user_id, :plan_id, :status, :start_date, :end_date
  menu priority: 1, :parent => "Subscriptions"

  actions :all, except: [:new, :destroy, :edit]

  member_action :pause_subscription, method: :get do
    resource.pause!
    redirect_to resource_path, notice: "Subscription paused!"
  end

  member_action :resume_subscription, method: :get do
    resource.resume!
    redirect_to resource_path, notice: "Subscription resumed!"
  end

  member_action :refund_and_cancel, method: :post do
    subscription_charge = resource.subscription_charges.last
    if subscription_charge.present?
      # allow refund only if last charge is successful & charge should not be auth charge
      if subscription_charge.may_initiate_refund?
        # Initiate a full refund
        subscription_charge.initiate_refund(subscription_charge.amount)
        resource.cancel! if resource.may_cancel?

        # Update the user plan to previous day
        up = UserPlan.where(user_id: resource.user_id, source: "charge-#{subscription_charge.id}").last
        plan_duration = subscription_charge.subscription.plan.duration_in_months
        # subtract plan duration from end date
        up.update!(end_date: up.end_date.advance(months: -plan_duration),
                   source: "charge-refund-#{subscription_charge.id}") if up.present?

        # also handle this case adjust start and end date of user plan logs if user got referral redeem as well as
        # charge success but user asked for charge refund then need to handle the logic of user plan logs start and
        # end dates properly.

        inactivated_upl = UserPlanLog.where(user_id: resource.user_id, entity: subscription_charge)
                                     .update(active: false)
        inactivated_upl_end_date = inactivated_upl.last.end_date
        # pick all next user plan logs and update their start date and end date
        user_plan_logs = UserPlanLog.where(user_id: resource.user_id).where("start_date >= ?",
                                                                            inactivated_upl_end_date.beginning_of_day)
        # update start date and end date of user plan logs
        user_plan_logs.each do |upl|
          upl.update!(start_date: upl.start_date.advance(months: -plan_duration),
                      end_date: upl.end_date.advance(months: -plan_duration))
        end

        redirect_to resource_path, notice: "Last charge refunded & cancelled!"
      else
        redirect_to resource_path, alert: "Last charge is not refundable!"
      end
    else
      redirect_to resource_path, alert: "No charges to refund!"
    end
  end

  member_action :cancel_subscription, method: :post do
    if resource.may_cancel?
      resource.cancel!
      redirect_to resource_path, notice: "Subscription has been canceled successfully!"
    else
      redirect_to resource_path, alert: "Subscription cannot be canceled as it is not active."
    end
  end

  action_item :cancel_subscription, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :sales_am]) && subscription.may_cancel?
      link_to "Cancel Subscription", cancel_subscription_admin_subscription_path(subscription),
              method: :post, class: 'danger-outline-button',
              data: { confirm: "Are you sure you want to cancel the subscription?" }
    end
  end

  action_item :refund_and_cancel, only: :show do
    if current_admin_user.admin_role? && subscription.subscription_charges.present?
      last_charge = subscription.subscription_charges.last
      if last_charge.may_initiate_refund?
        link_to "Refund last charge & cancel",
                refund_and_cancel_admin_subscription_path(subscription),
                method: :post,
                class: 'danger-outline-button',
                data: { confirm: 'Are you sure you want to refund last charge & cancel subscription?' }
      end
    end
  end

  # action_item :pause_subscription, only: :show do
  #   link_to 'Pause',
  #           pause_subscription_admin_subscription_path(subscription),
  #           class: 'danger-outline-button',
  #           data: { confirm: 'Are you sure you want to pause?' } if subscription.may_pause?
  # end
  #
  # action_item :resume_subscription, only: :show do
  #   link_to 'Resume',
  #           resume_subscription_admin_subscription_path(subscription),
  #           class: 'activate-button',
  #           data: { confirm: 'Are you sure you want to resume?' } if subscription.may_resume?
  # end

  index pagination_total: false do
    selectable_column
    id_column
    column :user
    column :plan
    column :payment_gateway
    column :status do |subscription|
      status_tag subscription.status
    end
    column :max_amount
    column :updated_at
    actions
  end

  filter :user_id
  filter :plan_id, as: :select, collection: proc { Plan.all.map { |plan| ["#{plan.name} (#{plan.amount})", plan.id] } }
  filter :status, as: :select, collection: Subscription.statuses

  show do
    attributes_table do
      row :id
      row :pg_id
      row :pg_reference_id do |subscription|
        if subscription.pg_reference_id.present?
          if subscription.juspay?
            link_to subscription.pg_reference_id, "https://portal.juspay.in/mandate-orders/mandate-details/#{subscription.pg_reference_id}", target: "_blank"
          else
            link_to subscription.pg_reference_id, "https://merchant.cashfree.com/subscriptions/subscriptions/all-subscriptions/#{subscription.pg_reference_id}/details?env=prod", target: "_blank"
          end
        end
      end
      row :payment_gateway
      row :user
      row :plan
      row :status do |subscription|
        status_tag subscription.status
      end
      row :max_amount
      row :auth_link
      row :created_at
      row :updated_at
    end

    panel "Subscription Charges" do
      table_for subscription.subscription_charges do
        column :id
        column :amount
        column :charge_amount
        column :refunded_amount
        column :pg_id
        column :status do |subscription_charge|
          status_tag subscription_charge.status
        end
        column :success_at
        column :created_at
        column :updated_at
        column "Refunds" do |subscription_charge|
          if subscription_charge.subscription_charge_refunds.any?
            table_for subscription_charge.subscription_charge_refunds do
              column :id
              column :amount
              column :status do |refund|
                status_tag refund.status
              end
              column :reason
              column :created_at
              column :updated_at
            end
          else
            "No refunds"
          end
        end
      end
    end
  end
end
