ActiveAdmin.register_page "BOE Work Flow" do
  menu false

  # Helper method to get the user for the BOE workflow
  controller.helper_method :get_user_for_boe_workflow

  controller.helper do
    def get_user_for_boe_workflow
      # Access the @user instance variable from the controller
      user = controller.instance_variable_get(:@user)

      # If user is nil, redirect to dashboard
      if user.nil?
        redirect_to admin_dashboard_path, alert: "User not found" and return nil
      end

      user
    end
  end

  # Helper method to set a user role as primary and update premium pitch status
  module User<PERSON>oleHelper
    def self.set_user_role_as_primary(user_role)
      user_role.update(primary_role: true, active: true)
      user_role.user.premium_pitch&.saved_badge
    end
  end

  # Define page actions for the buttons
  page_action :already_user_role_present, method: :post do
    user = User.find_by_hashid(params[:user_id])
    if user.present?
      # Get all user roles (including inactive ones)
      user_roles = UserRole.where(user_id: user.id).order(primary_role: :desc, active: :desc, id: :desc)

      if user_roles.count == 1
        # If only one user role, make it primary and active
        user_role = user_roles.first
        UserRoleHelper.set_user_role_as_primary(user_role)

        flash[:notice] = "Marked Badge as already present"
        render json: { success: true, message: 'Badge updated and set as primary', redirect: true }
      else
        # If multiple user roles, return them to be displayed in a modal
        render json: {
          success: true,
          message: 'Multiple user roles found',
          multiple_roles: true,
          user_roles: user_roles.map { |ur| {
            id: ur.id,
            role_name: ur.role.name,
            badge_text: ur.get_description,
            primary_role: ur.primary_role,
            active: ur.active
          } }
        }
      end
    else
      render json: { success: false, message: 'User not found' }, status: :not_found
    end
  end

  # Define page action to set a specific user role as primary
  page_action :set_primary_user_role, method: :post do
    user_role = UserRole.find_by(id: params[:user_role_id])

    if user_role.present?
      # Update the selected role to be primary and active
      UserRoleHelper.set_user_role_as_primary(user_role)

      flash[:notice] = "Selected Badge has been successfully set as primary"
      render json: { success: true, message: 'Badge updated and set as primary', redirect: true }
    else
      render json: { success: false, message: 'Badge not found' }, status: :not_found
    end
  end

  page_action :incomplete, method: :post do
    user = User.find_by_hashid(params[:user_id])
    if user.present?
      begin
        user_poster_layout = user.get_user_poster_layout_including_inactive
        user.premium_pitch.badge_incomplete! if user.premium_pitch&.may_badge_incomplete?

        # Save the remarks
        remarks = params[:remarks]
        PosterLayoutRemark.create(user_poster_layout: user_poster_layout,
                                  remarks: remarks,
                                  admin_user: current_admin_user)
        flash[:notice] = "Remarks saved and user status updated to RM draft"
        render json: { success: true, message: 'User status updated to RM draft' }
      rescue => e
        flash[:alert] = "Error in saving remarks: #{e.message}"
        render json: { success: false, message: "Error in saving remarks: #{e.message}" }, status: :internal_server_error
      end
    else
      flash[:alert] = "User not found"
      render json: { success: false, message: 'User not found' }, status: :not_found
    end
  end

  controller do
    before_action :set_user, only: [:index]
    before_action :validate_badge_text, only: [:index]

    def index
      # Render the index template directly
      render 'active_admin/page/index'
    end

    # Add a method to handle the user_id parameter from the URL
    def self.route_options
      {
        param: :user_id
      }
    end

    private

    def set_user
      if params[:user_id].present?
        @user = User.active.find_by_hashid(params[:user_id])
        if @user.blank?
          redirect_to admin_dashboard_path, alert: "User not found" and return
        end
      else
        # If no user_id is provided, redirect to dashboard
        redirect_to admin_dashboard_path, alert: "User ID is required" and return
      end
    end

    def validate_badge_text
      # Check if badge text is present
      badge_text = @user.get_badge_free_text_for_rm_layout_creation
      if badge_text.blank?
        # Redirect to dashboard with informative message
        redirect_to admin_dashboard_path, alert: "Badge free text is not present for this user. A badge text must be submitted by an RM user before creating a user role." and return
      end
    end
  end

  content title: "BOE Work Flow" do
    # We need to fetch the user again in the content block
    # Use a helper method to get the user ID to avoid accessing params directly
    user = helpers.get_user_for_boe_workflow
    return if user.nil?

    # Check if BOE work is enabled for this user
    boe_work_enabled = user.boe_work_enabled?

    # Show warning if BOE work is not enabled
    unless boe_work_enabled
      div class: "flash flash_warning" do
        text_node "BOE work is only enabled when premium pitch status is 'badge_setup' or 'badge_setup_no_layout_setup'. Current status: #{user.premium_pitch&.status || 'not set'}."
      end
    end

    # Only show the rest of the content if BOE work is enabled
    if boe_work_enabled
      columns do
        column span: 4 do
          panel "User Information" do
            columns do
              column do
                # User details
                attributes_table_for user do
                  row :id
                  row :name
                  row :primary_badge do |u|
                    user_role = u.get_badge_role
                    user_role.present? ?
                      link_to(user_role.get_description, admin_user_role_path(user_role)) :
                      nil
                  end
                  row :affiliated_party do |u|
                    affiliated_circle_id = u.get_affiliated_circle_id_for_rm_layout_creation
                    if affiliated_circle_id.present?
                      affiliated_circle = Circle.find_by(id: affiliated_circle_id)
                      affiliated_circle.present? ? link_to(affiliated_circle.name, admin_circle_path(affiliated_circle)) : '-'
                    else
                      '-'
                    end
                  end
                  row :location, title: "village, mandal, district, state" do |u|
                    u.village.present? ?
                      link_to(u.village.name, admin_circle_path(u.village)) + ", " +
                        link_to(u.mandal.name, admin_circle_path(u.mandal)) + ", " +
                        link_to(u.district.name, admin_circle_path(u.district)) + ", " +
                        link_to(u.state.name, admin_circle_path(u.state)) :
                      '-'
                  end
                  row :photo do |u|
                    image_tag u.photo.url, class: 'thumb_size' if u.photo.present?
                  end
                  row :rm_user do |u|
                    rm_user = u.get_rm_user
                    if rm_user.present?
                      link_to(rm_user.name, admin_admin_user_path(rm_user))
                    else
                      '-'
                    end
                  end
                end
              end
            end
          end
        end
      end

      columns do
        column do
          panel "Existing User Roles" do
            # Get all active user roles for this user
            user_roles = UserRole.includes(:role, :parent_circle, :purview_circle).where(user_id: user.id)
                                 .order(primary_role: :desc, id: :desc).all

            if user_roles.any?
              table_for user_roles do
                column :id do |user_role|
                  # open user role in new tab
                  link_to(user_role.id, admin_user_role_path(user_role), target: "_blank")
                end
                column :role
                column :parent_circle do |user_role|
                  user_role.parent_circle_id.present? ? user_role.parent_circle : user_role.role.parent_circle
                end
                column :purview_circle
                column :badge_text_on_app do |user_role|
                  status_tag(user_role.get_description, class: "status_tag badge_text")
                end
                column :start_date
                column :end_date
                column :grade_level do |user_role|
                  user_role.grade_level || user_role.role.grade_level
                end
                column :primary_role
                column :verification_status do |user_role|
                  status_tag(user_role.verification_status)
                end
                column :active
                column :created_at
                column :actions do |user_role|
                  span do
                    link_to "View", admin_user_role_path(user_role), class: "member_link"
                    link_to "Edit", edit_admin_user_role_path(user_role), class: "member_link", target: "_blank"
                  end
                end
              end
            else
              div class: "blank-slate" do
                span "No user roles found for this user."
              end
            end
          end
        end
      end

      columns do
        column do
          panel "RM given Badge Text" do
            badge_text = user.get_badge_free_text_for_rm_layout_creation
            div class: "badge-container" do
              div class: "badge-header" do
                div class: "badge-text" do
                  text_node badge_text
                end
                div class: "badge-actions" do
                  div class: "action-buttons", id: "action-buttons" do
                    a "Create Badge", href: "javascript:void(0);", class: "button create-badge-button", id: "create-badge-button", onclick: "handleCreateBadge(); return false;"
                    a "Badge already present", href: "javascript:void(0);", class: "button already-role-button", id: "already-role-button", onclick: "handleAlreadyUserRolePresent(); return false;"
                  end

                  div class: "success-message", id: "success-message", style: "display: none;" do
                    span class: "success-icon" do
                      raw "&#10004;" # Tick mark
                    end
                    span "Badge successfully created!", class: "success-text"
                  end
                end
              end
            end
          end
        end
      end

      # Modal for selecting a primary user role
      div id: "user-roles-modal", class: "modal" do
        div class: "modal-content" do
          div class: "modal-header" do
            h3 "Select Primary User Role"
            span class: "close", onclick: "closeUserRolesModal()" do
              h3 "x"
            end
          end
          div class: "modal-body" do
            div id: "user-roles-list" do
              # This will be populated dynamically with JavaScript
            end
          end
          div class: "modal-footer" do
            a "Make Selected Role Primary", href: "javascript:void(0);", class: "button primary-button", id: "set-primary-role-button", onclick: "setPrimaryUserRole(); return false;"
          end
        end
      end

      columns do
        column do
          panel "Mark as Incomplete" do
            div class: "remarks-container" do
              div class: "remarks-header" do
                h4 "Please provide remarks for marking this as incomplete:"
              end
              div class: "remarks-input-container" do
                div class: "remarks-input" do
                  text_area_tag :remarks, nil, id: "incomplete-remarks", rows: 4, style: "width: 100%;", placeholder: "Enter your remarks here...", onkeyup: "checkRemarksValidity()"
                end
                div class: "remarks-actions" do
                  a "Mark as Incomplete", href: "javascript:void(0);", class: "button incomplete-button", id: "incomplete-button", onclick: "handleIncomplete(); return false;", disabled: true, style: "opacity: 0.5; cursor: not-allowed;"
                end
              end
            end
          end
        end
      end

      # Add JavaScript for all functionality
      script do
        raw <<-JS
        // Check if badge has been created on page load
        document.addEventListener('DOMContentLoaded', function() {
          checkBadgeCreated();
        });

        // Function to check if badge has been created
        function checkBadgeCreated() {
          const userId = '#{user.id}';
          const badgeCreatedKey = userId + '_badge_created';
          const value = localStorage.getItem(badgeCreatedKey);

          // Check if we're coming from a direct page load/refresh (not from badge creation)
          const isPageRefresh = !document.referrer.includes('user_roles');

          if (value && !isPageRefresh) {
            // Only show success message if we're coming directly from user_roles page
            // This happens when a badge was just created
            showSuccessMessage();
          } else {
            // For page refreshes or new visits, always show action buttons
            // and remove any existing localStorage item
            if (value) {
              localStorage.removeItem(badgeCreatedKey);
            }

            const actionButtons = document.getElementById('action-buttons');
            if (actionButtons) {
              actionButtons.style.display = 'flex';
            }
          }
        }

        // Function to show success message and hide buttons
        function showSuccessMessage() {
          // Hide action buttons
          const actionButtons = document.getElementById('action-buttons');
          if (actionButtons) {
            actionButtons.style.display = 'none';
          }

          // Hide remarks panel
          const remarksPanel = document.querySelector('.remarks-container');
          if (remarksPanel) {
            remarksPanel.closest('.panel').style.display = 'none';
          }

          // Show success message with animation
          const successMessage = document.getElementById('success-message');
          if (successMessage) {
            successMessage.style.display = 'flex';

            // Scroll to the success message
            setTimeout(function() {
              successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);
          }

          // Remove the localStorage item after showing the success message
          const userId = '#{user.id}';
          const badgeCreatedKey = userId + '_badge_created';
          localStorage.removeItem(badgeCreatedKey);
        }

        // Function to handle Create Badge button click
        function handleCreateBadge() {

          const userId = '#{user.id}';

          // Open new user role form in a new tab with prefilled user ID
          // We need to pass both user_id and user_role[user_id] to ensure proper prefilling
          // Also pass from_boe_workflow=true to indicate this came from BOE Work Flow
          const badgeCreatedKey = userId + '_badge_created';
          const newUserRoleUrl = '/admin/user_roles/new?user_id=' + userId + '&user_role[user_id]=' + userId + '&from_boe_workflow=true';
          const newTab = window.open(newUserRoleUrl, '_blank');

          // Set up observer to check for badge creation
          if (newTab) {
            // Start polling localStorage for badge creation flag

            const checkInterval = setInterval(function() {
              const value = localStorage.getItem(badgeCreatedKey);
              if (value) {
                clearInterval(checkInterval);
                showSuccessMessage();
                // Note: showSuccessMessage now removes the localStorage item
              }
              // The localStorage will be set by the user_roles create action
              // when the user role is successfully created
            }, 3000); // Check every 3 seconds for feedback
          }
        }

        function handleAlreadyUserRolePresent() {
          // Make an AJAX call to update the user's status
          $.ajax({
            url: '/admin/boe_work_flow/already_user_role_present',
            method: 'POST',
            data: { user_id: '#{user.hashid}' },
            success: function(response) {
              if (response.multiple_roles) {
                // If multiple roles, show the modal with the roles
                showUserRolesModal(response.user_roles);
              } else if (response.redirect) {
                // If single role or process completed, redirect to dashboard
                window.location.href = '/admin/dashboard';
              }
            },
            error: function(xhr, status, error) {
              console.error('Error updating user status:', error);
            }
          });
        }

        function showUserRolesModal(userRoles) {
          // Clear previous content
          const userRolesList = document.getElementById('user-roles-list');
          userRolesList.innerHTML = '';

          // Create radio buttons for each user role
          userRoles.forEach(function(userRole) {
            const roleDiv = document.createElement('div');
            roleDiv.className = 'user-role-item';

            const radioInput = document.createElement('input');
            radioInput.type = 'radio';
            radioInput.name = 'user_role_id';
            radioInput.value = userRole.id;
            radioInput.id = 'user_role_' + userRole.id;

            // Pre-select the primary role if it exists
            if (userRole.primary_role) {
              radioInput.checked = true;
            }

            const label = document.createElement('label');
            label.htmlFor = 'user_role_' + userRole.id;
            label.innerHTML = userRole.badge_text +
                             ' <span class="role-status">' +
                             (userRole.primary_role ? ' (Primary)' : '') +
                             (userRole.active ? '' : ' (Inactive)') +
                             '</span>';

            roleDiv.appendChild(radioInput);
            roleDiv.appendChild(label);
            userRolesList.appendChild(roleDiv);
          });

          // Show the modal
          document.getElementById('user-roles-modal').style.display = 'block';
        }

        function closeUserRolesModal() {
          document.getElementById('user-roles-modal').style.display = 'none';
        }

        function setPrimaryUserRole() {
          // Get the selected user role ID
          const selectedRadio = document.querySelector('input[name="user_role_id"]:checked');

          if (!selectedRadio) {
            alert('Please select a user role');
            return;
          }

          const userRoleId = selectedRadio.value;

          // Make an AJAX call to set the selected role as primary
          $.ajax({
            url: '/admin/boe_work_flow/set_primary_user_role',
            method: 'POST',
            data: { user_role_id: userRoleId },
            success: function(response) {
              if (response.redirect) {
                window.location.href = '/admin/dashboard';
              }
            },
            error: function(xhr, status, error) {
              console.error('Error setting primary user role:', error);
              alert('Error setting primary user role: ' + error);
            }
          });
        }

        function checkRemarksValidity() {
          var remarksTextarea = document.getElementById('incomplete-remarks');
          var incompleteButton = document.getElementById('incomplete-button');

          if (remarksTextarea.value.trim() === '') {
            // Disable the button if remarks are empty
            incompleteButton.setAttribute('disabled', 'disabled');
            incompleteButton.style.opacity = '0.5';
            incompleteButton.style.cursor = 'not-allowed';
          } else {
            // Enable the button if remarks are provided
            incompleteButton.removeAttribute('disabled');
            incompleteButton.style.opacity = '1';
            incompleteButton.style.cursor = 'pointer';
          }
        }

        function handleIncomplete() {
          var remarksTextarea = document.getElementById('incomplete-remarks');
          var remarks = remarksTextarea.value.trim();

          if (remarks === '') {
            // This should never happen as the button is disabled when remarks are empty
            console.error('Remarks cannot be empty');
            return;
          }

          // Make an AJAX call to update the user's status
          $.ajax({
            url: '/admin/boe_work_flow/incomplete',
            method: 'POST',
            data: {
              user_id: '#{user.hashid}',
              remarks: remarks
            },
            success: function(response) {
              window.location.href = '/admin/dashboard';
            },
            error: function(xhr, status, error) {
              console.error('Error updating user status:', error);
            }
          });
        }
        JS
      end
    end # End of if !layout_in_draft && boe_work_enabled block
  end
end
