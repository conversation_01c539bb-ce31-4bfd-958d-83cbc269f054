ActiveAdmin.register PermissionGroup do
  menu :parent => "Permissions"
  permit_params :name, permission_group_permissions_attributes: [:permission_identifier, :id, :_destroy]
  actions :all, except: [:destroy]
  index do
    selectable_column
    id_column
    column "Permission Group Name" do |permission_group|
      permission_group.name
    end
    column :created_at
    column :updated_at
    actions
  end

  show do |permission_group|
    attributes_table do
      row :id
      row "Permission Group Name" do
        permission_group.name
      end
      row "Permissions" do |permission_group|
        table do
          permission_group.permission_group_permissions.map do |permission_group_permission|
            tr do
              td permission_group_permission.permission_identifier
            end
          end
        end
      end
      row :created_at
      row :updated_at
    end
  end

  filter :name, label: "Permission Group Name"

  form :html => { :multipart => true } do |f|
    f.semantic_errors
    f.inputs 'New/Edit Permission Group' do
      f.input :name, label: "Permission Group Name", required: true

      f.has_many :permission_group_permissions, allow_destroy: true, heading: 'Permissions' do |permission_group_permission|
        permission_group_permission.input :permission_identifier, as: :select,
                                          collection: PermissionGroupPermission.permission_identifiers.keys,
                                          label: "Permission", required: true
      end

    end
    f.actions
  end
end

