ActiveAdmin.register PostComment do
  menu :parent => "Posts"
  # See permitted parameters documentation:
  # https://github.com/activeadmin/activeadmin/blob/master/docs/2-resource-customization.md#setting-up-strong-parameters
  #
  # Uncomment all parameters which should be permitted for assignment
  #
  #
  # or
  #
  # permit_params do
  #   permitted = [:content, :dynamic_link, :user_id, :parent_post_id, :active, :link_id, :is_poll, :hashid, :user_liked, :user_circled, :user_seen, :photos, :videos, :likes_count, :whatsapp_count, :comments_count, :opinions_count, :circle, :user, :poll, :link, :parent_post, :bounces, :liked_users, :is_logged_in_user_post, :feed_type, :feed_type_timestamp, :user_tags, :hashtags]
  #   permitted << :other if params[:action] == 'create' && current_user.admin?
  #   permitted
  # end

  permit_params :id, :active

  actions :all, except: [:destroy]

  batch_action :active do |ids|
    batch_action_collection.find(ids).each do |post_comment|
      post_comment.update(active: true)
    end
    redirect_to collection_path, notice: "The post comments have been made active!"
  end

  batch_action :in_active do |ids|
    batch_action_collection.find(ids).each do |post_comment|
      post_comment.update(active: false)
    end
    redirect_to collection_path, notice: "The post comments have been made inactive!"
  end

  index do
    selectable_column

    column :id do |post_comment|
      link_to(post_comment.id, admin_post_comment_path(post_comment))
    end
    column :comment do |post_comment|
      post_comment.comment.truncate(57, separator: ' ')
    end
    column :user_id do |post_comment|
      link_to(post_comment.user.name, admin_user_path(post_comment.user))
    end
    column :fantom_account do |post_comment|
      u_phone = post_comment.user.phone
      if User.is_internal(u_phone)
        "Yes"
      else
        span :style => 'color: red' do
          "No"
        end
      end
    end
    column :post_id do |post_comment|
      (!post_comment.post_id.nil? && post_comment.post_id > 0) ? link_to("Post ##{post_comment.post_id}", admin_post_path(post_comment.post)) : ""
    end
    column :active
    column :created_at
    column :updated_at
    actions
  end

  show do |post_comment|
    attributes_table do
      row :id
      row :comment
      row :user
      row :post
      row :active
      row :created_at
      row :updated_at
    end

    panel "Edit History" do
      table_for PaperTrail::Version.where(item_type: "PostComment", item_id: post_comment.id).order(id: :desc).limit(10) do
        column("Item") { |v| v.item }
        column("Modified at") { |v| v.created_at }
        column("Admin") do |v|
          if v.whodunnit.nil?
            ""
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    active_admin_comments
  end

  form multipart: true do |f|
    f.semantic_errors
    f.inputs "Post Comment Details" do
      if f.object.new_record?
        f.input :comment
        f.input :user
        f.input :post
      else
        f.input :active
      end
    end
    f.actions
  end

  controller do
    def update
      attrs = params[:post_comment]

      @post_comment = PostComment.find(params[:id])
      @post_comment.active = attrs[:active]

      if @post_comment.save
        redirect_to admin_post_comments_path(@post_comment)
      else
        render :edit
      end
    end
  end

  filter :id
  filter :comment
  filter :user_id, label: "User ID"
  filter :post_id, label: "Post ID"
  filter :active
  filter :created_at
  filter :updated_at
end
