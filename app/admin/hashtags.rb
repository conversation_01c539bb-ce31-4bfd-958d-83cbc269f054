ActiveAdmin.register Hashtag do
  menu :parent => "Posts"

  permit_params :name

  actions :all, except: [:new, :destroy]

  index pagination_total: false do
    selectable_column
    column :display_name do |hashtag|
      link_to hashtag.name, admin_hashtag_path(hashtag)
    end
    column :identifier
    column :created_at
    actions
  end

  form do |f|
    f.inputs "Hashtag Details" do
      f.input :name, :label => "Display Name"
    end
    f.actions
  end
end
