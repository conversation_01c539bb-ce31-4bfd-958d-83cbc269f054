class DmUtil

  ALLOWED_REASONS = { from_same_village: :FROM_SAME_VILLAGE, from_contacts: :FROM_CONTACTS,
                      from_interest_circles: :FROM_INTEREST_CIRCLES, from_following: :FROM_FOLLOWING,
                      sender_grade_1_or_grade_2: :SENDER_GRADE_1_OR_GRADE_2 }
  REQUESTING_REASONS = { is_comment_blocked_user: :COMMENTING_BLOCKED,
                         allowed_reasons_not_satisfied: :ALLOWED_REASONS_NOT_SATISFIED,
                         exceeded_max_primary_conversations_with_in_24_hrs: :EXCEEDED_MAX_PRIMARY_CONVERSATIONS_WITH_IN_24_HRS,
                         exceeded_max_primary_conversations_with_in_7_days: :EXCEEDED_MAX_PRIMARY_CONVERSATIONS_WITH_IN_7_DAYS,
                         is_receiver_grade_1_or_grade_2: :RECEIVER_GRADE_1_OR_GRADE_2 }
  BANNING_REASONS = { exceeded_max_other_conversations_with_in_24_hrs: :EXCEEDED_MAX_OTHER_CONVERSATIONS_WITH_IN_24_HRS,
                      exceeded_max_other_conversations_with_in_7_days: :EXCEEDED_MAX_OTHER_CONVERSATIONS_WITH_IN_7_DAYS,
                      is_blocked_by_multiple_users: :Is_BLOCKED_BY_MULTIPLE_USERS }

  AUTH_STATUS = { allowed: :ALLOWED, requested: :REQUESTED, sender_blocked: :SENDER_BLOCKED,
                  receiver_blocked: :RECEIVER_BLOCKED, both_blocked: :BOTH_BLOCKED, banned: :BANNED }
  REJECTING_REASONS = { sender_blocked: :SENDER_BLOCKED, receiver_blocked: :RECEIVER_BLOCKED,
                        both_blocked: :BOTH_BLOCKED }

  NATS_SUBJECT_PREFIX = 'DM.' + ENV['DEPLOYMENT'] + '.'

  def self.authorize_user_to_create_one_to_one_conversation(sender, receiver_id,
                                                            count_of_last_7_days_active_conversations_created = 0,
                                                            count_of_last_24_hrs_active_conversations_created = 0,
                                                            count_of_last_7_days_active_other_conversations_created = 0,
                                                            count_of_last_24_hrs_active_other_conversations_created = 0)
    # fetching user and receiver
    receiver = User.find_by(id: receiver_id)
    return if receiver.blank? || sender.blank?

    sender_id = sender.id

    # checking whether the sender is blocked by the user
    is_sender_blocked = BlockedUser.where(user_id: receiver_id, blocked_user_id: sender_id).exists?

    # checking whether the receiver is blocked by the user
    is_receiver_blocked = BlockedUser.where(user_id: sender_id, blocked_user_id: receiver_id).exists?

    # FIRST MAJOR REJECTING BLOCK
    # major Rejecting block before authorizing user to create one to one conversation
    # It will send response as blocked if user is blocked by receiver or receiver is blocked by user
    # It will create other conversation if user is being blocked

    # returning response if both user and receiver are blocked users
    return { :status => AUTH_STATUS[:both_blocked], :parameters => {
      :rejecting_reasons => REJECTING_REASONS[:both_blocked]
    } } if is_sender_blocked && is_receiver_blocked

    # returning response if user is blocked user
    return { :status => AUTH_STATUS[:sender_blocked], :parameters => {
      :rejecting_reasons => REJECTING_REASONS[:sender_blocked]
    } } if is_sender_blocked

    # returning response if receiver is blocked user
    return { :status => AUTH_STATUS[:receiver_blocked], :parameters => {
      :rejecting_reasons => REJECTING_REASONS[:receiver_blocked]
    } } if is_receiver_blocked

    # GETTING REQUIREMENTS FOR FURTHER AUTH LOGIC

    # getting sender and receiver badge roles
    sender_grade_level = sender.get_badge_role&.get_readable_grade_level

    receiver_grade_level = receiver.get_badge_role&.get_readable_grade_level

    grade_1_2_levels = [UserRole.grade_levels[:grade_1], UserRole.grade_levels[:grade_2]]

    # checking whether the sender is grade 1 or grade 2 user
    is_sender_grade_1_or_grade_2 = grade_1_2_levels.include?(sender_grade_level)

    # checking whether the receiver is grade 1 or grade 2 user
    is_receiver_grade_1_or_grade_2 = grade_1_2_levels.include?(receiver_grade_level)

    # whether the user is following by the receiver.
    is_sender_from_receiver_following = UserFollower.where(follower_id: receiver_id, user_id: sender_id).exists?

    # checking whether the sender is from receiver's contacts
    is_sender_from_receiver_contacts = UserContactSuggestion.where(phone_user_id: sender_id, user_id: receiver_id).exists?

    # getting sender interest circles
    sender_interest_circle_ids = sender.get_user_joined_interest_circle_ids

    # getting receiver interest circles
    receiver_interest_circle_ids = receiver.get_user_joined_interest_circle_ids

    # common interest circles
    have_common_interest_circles_ids = (sender_interest_circle_ids & receiver_interest_circle_ids).present?

    # checking whether the receiver and user are from same village circle,
    is_from_same_village = sender.village_id == receiver.village_id

    # reason string to be returned based on above conditions for the user to create one to one conversation
    allowed_reasons = ''
    allowed_reasons += "#{ALLOWED_REASONS[:from_same_village]}," if is_from_same_village
    allowed_reasons += "#{ALLOWED_REASONS[:from_contacts]}," if is_sender_from_receiver_contacts
    allowed_reasons += "#{ALLOWED_REASONS[:from_interest_circles]}," if have_common_interest_circles_ids.present?
    allowed_reasons += "#{ALLOWED_REASONS[:from_following]}," if is_sender_from_receiver_following
    allowed_reasons += "#{ALLOWED_REASONS[:sender_grade_1_or_grade_2]}," if is_sender_grade_1_or_grade_2
    allowed_response = { :status => AUTH_STATUS[:allowed], :parameters => allowed_reasons.present? ? { :allowing_reasons => allowed_reasons.chop } : {} }

    # MAJOR ALLOWING CONDITIONS BLOCK
    # According to logic we should allow user to create primary conversation if sender is grade I/II or sender is from receiver contacts or sender is from receiver followers
    # checking whether the sender is from receiver's following
    if is_sender_from_receiver_following || is_sender_grade_1_or_grade_2 || is_sender_from_receiver_contacts
      return allowed_response
    end

    # adding moderation logic here
    moderation = OneToOneConvModeration.new(sender_id: sender_id,
                                            is_sender_blocked_for_commenting: sender.is_blocked_for_commenting?,
                                            sender_blocked_by_count: BlockedUser.where(blocked_user_id: sender_id, reason: %w[chat_block chat_block_and_report]).count,
                                            sender_followers_count: UserFollower.where(user_id: sender_id).count,
                                            count_of_last_7_days_active_other_conversations_created: count_of_last_7_days_active_other_conversations_created,
                                            count_of_last_24_hrs_active_other_conversations_created: count_of_last_24_hrs_active_other_conversations_created,
                                            count_of_last_7_days_active_conversations_created: count_of_last_7_days_active_conversations_created,
                                            count_of_last_24_hrs_active_conversations_created: count_of_last_24_hrs_active_conversations_created)

    # checking whether the moderation is requested or banned
    if moderation.banned?
      return { :status => AUTH_STATUS[:banned], :parameters => {
        :banning_reasons => moderation.reason
      } }
    end

    if moderation.requested?
      return { :status => AUTH_STATUS[:requested], :parameters => {
        :requesting_reasons => moderation.reason
      } }
    end

    # if sender and receiver are grade 1 or 2 users, they can msg each other in primary channel, if they both have common interest circles.
    if is_receiver_grade_1_or_grade_2
      return { :status => AUTH_STATUS[:requested], :parameters => {
        :requesting_reasons => REQUESTING_REASONS[:is_receiver_grade_1_or_grade_2]
      } }
    end

    # checking whether the receiver and sender are from same village circle,
    # # or whether the receiver and sender have common interest circles.
    # here it is covering, badge user from my interest circles condition also as we are checking for common interest circles, of users.

    if is_from_same_village || have_common_interest_circles_ids.present?
      return allowed_response
    end

    # returning requested as status if none of the above conditions are satisfied
    return { :status => AUTH_STATUS[:requested], :parameters => {
      :requesting_reasons => REQUESTING_REASONS[:allowed_reasons_not_satisfied]
    } }

  end

  def self.search(value, logged_in_user)
    # need to add custom queries for dm here
    must_match_queries_to_be_added = []
    must_not_match_queries_to_be_added = [
      {
        "terms": {
          "id": [logged_in_user.id]
        }
      }
    ]
    search_es = ES_CLIENT.search index: EsUtil.get_search_entities_index,
                                 body: SearchUtil.search_query(value, logged_in_user, must_match_queries_to_be_added,
                                                               must_not_match_queries_to_be_added)

    search_es['hits']['hits']
  end

  def self.dm_recommended_users_es_query(logged_in_user, followers_ids, following_ids, mutual_following_ids, user_contacts_ids, user_party_circle_ids, excluded_user_ids, count)

    if logged_in_user.mp_constituency_id.present?
      max_filter_query = [{
                            "term": {
                              "mp_constituency_id": logged_in_user.mp_constituency_id
                            }
                          }]
    else
      max_filter_query = [{
                            "term": {
                              "district_id": logged_in_user.district_id
                            }
                          }]
    end

    {
      "fields": %w[id type],
      "_source": false,
      "query": {
        "bool": {
          "must_not": [
            {
              "terms": {
                "id": excluded_user_ids
              }
            }
          ],
          "must": [
            {
              "bool": {
                "filter": [
                  {
                    "term": {
                      "active": true
                    }
                  },
                  {
                    "term": {
                      "type": "user"
                    }
                  }

                ] + max_filter_query,
                "minimum_should_match": 1,
                "should": [
                  {
                    "terms": {
                      "id": followers_ids + following_ids + user_contacts_ids
                    }
                  },
                  {
                    "terms": {
                      "user_party_circle_ids": user_party_circle_ids
                    }
                  },
                  {
                    "term": {
                      "village_id": logged_in_user.village_id
                    }
                  }

                ] + max_filter_query
              }
            }
          ],
          "should": [
            {
              "terms": {
                "id": followers_ids,
                "boost": 10
              }
            },
            {
              "terms": {
                "id": following_ids,
                "boost": 10
              }
            },
            {
              "terms": {
                "id": user_contacts_ids,
                "boost": 50
              }
            },
            {
              "terms": {
                "user_party_circle_ids": user_party_circle_ids,
                "boost": 20
              }
            },
            {
              "bool": {
                "must": [
                  {
                    "term": {
                      "village_id": logged_in_user.village_id
                    }
                  }
                ],
                "boost": 30
              }
            },
            {
              "terms": {
                "id": mutual_following_ids,
                "boost": 50
              }
            },
            {
              "bool": {
                "must": max_filter_query
              }
            }
          ]
        }
      },
      "sort": {
        "_score": "desc",
      },
      "size": count
    }
  end

  def self.blocked_info_callback_to_dm(req_url, req_body)
    return if req_url.blank? || req_body.blank?

    Honeybadger.context({ req_url: req_url, req_body: req_body })

    Rails.logger.info("Request about blocked data to DM service")

    # request
    response = DmUtil.put_request_to_dm(req_url, req_body)

    unless response.code.to_i == 200
      raise "DM Service blocked data HTTP request failed with status code #{response.code}"
    end
    JSON.parse(response.body)['success']
  end

  def self.send_user_join_callback_to_dm_service(circle_id, user_id)
    req_body = get_request_body_for_join_or_unjoin(circle_id, user_id)
    return if req_body.blank?
    req_url = Constants.get_dm_url + "/conversations/join-circle-conversation"

    response = put_request_to_dm(req_url, req_body)
    if response.present?
      response_body = JSON.parse(response.body)

      if response.code.to_i == 200
        response_body['success']
      else
        Honeybadger.notify('Error in sending user join callback to DM service', context: { req_url: req_url, req_body: req_body, response_body: response_body })
      end
    end
  end

  def self.send_user_unjoin_callback_to_dm_service(circle_id, user_id)
    req_body = get_request_body_for_join_or_unjoin(circle_id, user_id)
    return if req_body.blank?
    req_url = Constants.get_dm_url + "/conversations/leave-circle-conversation"

    response = put_request_to_dm(req_url, req_body)
    if response.present?
      response_body = JSON.parse(response.body)

      if response.code.to_i == 200
        response_body['success']
      else
        Honeybadger.notify('Error in sending user unjoin callback to DM service', context: { req_url: req_url, req_body: req_body, response_body: response_body })
      end
    end
  end

  def self.get_request_body_for_join_or_unjoin(circle_id, user_id)
    return if circle_id.blank? || user_id.blank?

    circle = Circle.find_by(id: circle_id)
    return if circle.blank?

    members_count = circle.get_members_count

    {
      userId: user_id.to_s,
      circleId: circle_id.to_s,
      membersCount: members_count
    }
  end

  def self.create_channel_callback_to_dm(circle_id, members_count)
    begin
      return if circle_id.blank? || members_count.blank?
      Rails.logger.info("Request to create or activate channel conversation to DM service")
      req_body = { circleId: circle_id.to_s, membersCount: members_count }
      req_url = Constants.get_dm_url + "/conversations/channel"
      response = DmUtil.post_request_to_dm(req_url, req_body)
      response_body = JSON.parse(response.body)

      if response.code.to_i == 201
        response_body["success"]
      else
        Honeybadger.notify("Error while creating channel conversation on DM service", context: { response_body: response_body })
        false
      end
    rescue => e
      Honeybadger.notify(e)
      false
    end
  end

  def self.create_private_group_callback_to_dm(circle_id, members_count, user_id = 41)
    begin
      return if circle_id.blank? || members_count.blank?
      Rails.logger.info("Request to create or activate private group conversation to DM service")
      req_body = { circleId: circle_id.to_s, userId: user_id.to_s, membersCount: members_count }
      req_url = Constants.get_dm_url + "/conversations/private-group"
      response = DmUtil.post_request_to_dm(req_url, req_body)
      response_body = JSON.parse(response.body)

      if response.code.to_i == 201
        response_body["success"]
      else
        Honeybadger.notify("Error while creating private group conversation on DM service", context: { response_body: response_body })
        false
      end
    rescue => e
      Honeybadger.notify(e)
      false
    end
  end

  def self.disable_channel_callback_to_dm(circle_id)
    begin
      return if circle_id.blank?
      Rails.logger.info("Request to disable channel conversation to DM service")
      req_body = { circleId: circle_id.to_s }
      req_url = Constants.get_dm_url + "/conversations/disable-channel"
      response = DmUtil.put_request_to_dm(req_url, req_body)
      response_body = JSON.parse(response.body)

      if response.code.to_i == 200
        response_body["success"]
      else
        Honeybadger.notify("Error while disabling channel conversation on DM service", context: { response_body: response_body })
        false
      end
    rescue => e
      Honeybadger.notify(e)
      false
    end
  end

  def self.disable_private_group_callback_to_dm(circle_id)
    begin
      return if circle_id.blank?
      Rails.logger.info("Request to disable private group conversation to DM service")
      req_body = { circleId: circle_id.to_s }
      req_url = Constants.get_dm_url + "/conversations/disable-private-group"
      response = DmUtil.put_request_to_dm(req_url, req_body)
      response_body = JSON.parse(response.body)
      if response.code.to_i == 200
        response_body["success"]
      else
        Honeybadger.notify("Error while disabling private group conversation on DM service", context: { response_body: response_body })
        false
      end
    rescue => e
      Honeybadger.notify(e)
      false
    end
  end

  def self.disable_circle_conversations_callback_to_dm(circle_id)
    begin
      return if circle_id.blank?
      Rails.logger.info("Request to disable circle conversations to DM service")
      req_body = { circleId: circle_id.to_s }
      req_url = Constants.get_dm_url + "/conversations/disable-circle-conversations"
      response = DmUtil.put_request_to_dm(req_url, req_body)
      response_body = JSON.parse(response.body)
      if response.code.to_i == 200
        response_body["success"]
      else
        Honeybadger.notify("Error while disabling circle conversations on DM service", context: { response_body: response_body })
        false
      end
    rescue => e
      Honeybadger.notify(e)
      false
    end
  end

  def self.send_circle_permission_group_update_to_dm(circle_ids)
    return if circle_ids.blank?

    req_url = Constants.get_dm_url + "/conversations/circles-permissions-changed"
    req_body = {
      circleIds: circle_ids.map(&:to_s)
    }

    response = DmUtil.post_request_to_dm(req_url, req_body)
    response_body = JSON.parse(response.body)
    if response.code.to_i == 201
      response_body['success']
    else
      Honeybadger.notify("Error in sending circle permission group update to DM", context: { response: response_body })
      false
    end
  end

  def self.send_user_circle_permission_update_to_dm(user_id, circle_id)
    return if user_id.blank? || circle_id.blank?

    req_url = Constants.get_dm_url + "/conversations/user-circle-permission-changed"
    req_body = {
      userId: user_id.to_s,
      circleId: circle_id.to_s
    }

    response = DmUtil.put_request_to_dm(req_url, req_body)
    response_body = JSON.parse(response.body)
    if response.code.to_i == 200
      response_body['success']
    else
      Honeybadger.notify("Error in sending user circle permission group update to DM", context: { response: response_body })
      false
    end
  end

  def self.put_request_to_dm(req_url, req_body)
    return if req_url.blank? || req_body.blank?

    Rails.logger.info("Request to DM service")
    Honeybadger.context({ req_url: req_url, req_body: req_body })

    url = URI(req_url)

    http = Net::HTTP.new(url.host, url.port)
    # http.use_ssl = true unless Rails.env.development?
    request = Net::HTTP::Put.new(url)

    jwt_token = JsonWebToken.get_token_to_send_dm_service
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{jwt_token}"

    request.body = req_body.to_json
    response = http.request(request)

    response
  end

  def self.post_request_to_dm(req_url, req_body)
    return if req_url.blank? || req_body.blank?

    Rails.logger.info("Request to DM service")
    Honeybadger.context({ req_url: req_url, req_body: req_body })

    url = URI(req_url)

    http = Net::HTTP.new(url.host, url.port)
    # http.use_ssl = true unless Rails.env.development?
    request = Net::HTTP::Post.new(url)

    jwt_token = JsonWebToken.get_token_to_send_dm_service
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{jwt_token}"

    if req_body[:messageData].present?
      user_id = req_body[:messageData][:senderId]
      return if user_id.blank?

      user_jwt_token = User.generate_user_jwt_token(user_id)
      request["X-User-Jwt-Token"] = user_jwt_token
    end

    request.body = req_body.to_json
    response = http.request(request)

    response
  end
end
