# frozen_string_literal: true

class FlowwApi
  TRACK_ACTIVITY_URL = "https://crm-integrations-apis.flowwai.work/api/sales_crm_core/track_activity/v1/"
  CONTACT_URL = "https://crm-integrations-apis.flowwai.work/api/sales_crm_core/identify_contact/v1/"

  def self.create_or_update_contact(user_id)
    user = User.find_by(id: user_id)
    return false if user.blank?

    # user.phone += 4000000000 if user.phone.to_i < 6000000000

    party_circle_id = user.affiliated_party_circle_id

    party = nil
    party = Circle.find_by(id: party_circle_id) if party_circle_id.present?

    user_role = user.get_badge_role

    payload = {
      "phone_number": {
        "phone_number": "#{user.phone}",
        "dial_code": "+91",
        "iso2_country_code": ""
      },
      "properties": [
        {
          "field_reference_id": "first_name",
          "value": "#{user.name}"
        },
        {
          "field_reference_id": "last_name",
          "value": ""
        },
        {
          "field_reference_id": "contact_custom_id",
          "value": "#{user.id}"
        },
        {
          "field_reference_id": "Village_Id",
          "value": "#{user.village_id}"
        },
        {
          "field_reference_id": "Village_Name",
          "value": "#{user.village.name_en}"
        },
        {
          "field_reference_id": "mandal_name",
          "value": "#{user.mandal.name_en}"
        },
        {
          "field_reference_id": "Mandal_Id",
          "value": "#{user.mandal_id}"
        },
        {
          "field_reference_id": "District_Name",
          "value": "#{user.district.name_en}"
        },
        {
          "field_reference_id": "praja_district_Id",
          "value": "#{user.district_id}"
        },
        {
          "field_reference_id": "State_Name",
          "value": "#{user.state.name_en}"
        },
        {
          "field_reference_id": "State_Id",
          "value": "#{user.state_id}"
        },
        {
          "field_reference_id": "role_name",
          "value": "#{user_role&.get_description}"
        },
        {
          "field_reference_id": "role_id",
          "value": "#{user_role&.role_id}"
        },
        {
          "field_reference_id": "affiliated_party_name",
          "value": "#{party&.name_en}"
        },
        {
          "field_reference_id": "affiliated_party_id",
          "value": "#{party&.id}"
        },
        {
          "field_reference_id": "admin_url",
          "value": "#{Constants.get_admin_host}#{Rails.application.routes.url_helpers.admin_user_path(user_id)}"
        },
        {
          "field_reference_id": "test_user",
          "value": user.is_test_user_for_floww? ? 'yes' : 'no'
        }
      ]
    }

    send_api_request(CONTACT_URL, payload)
  end

  def self.update_add_autopay_sales_lead_activity(user_id, lead_type:, lead_score:, release_build_number:,
                                                  package_to_pitch:, lead_source:, new_tag: "", auto_assign: true)
    user = User.find_by(id: user_id)
    return false if user.blank?

    payload = {
      "activity_reference_id": "Add_Lead_in_sales",
      "activity_details": [{
                             "field_reference_id": "activity_datetime",
                             "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
                           }, {
                             "field_reference_id": "lead_type",
                             "value": "#{lead_type}"
                           }, {
                             "field_reference_id": "SOP",
                             "value": LeadTypeSop.get_sop_text(lead_type)
                           }, {
                             "field_reference_id": "release_build_number",
                             "value": "#{release_build_number}"
                           }, {
                             "field_reference_id": "lead_score",
                             "value": "#{lead_score}"
                           }, {
                             "field_reference_id": "package_to_pitch",
                             "value": "#{package_to_pitch}"
                           }, {
                             "field_reference_id": "lead_source",
                             "value": "#{lead_source}"
                           }, {
                             "field_reference_id": "new_tag",
                             "value": "#{new_tag}"
                           },
                           {
                             "field_reference_id": "auto_assign",
                             "value": auto_assign ? "Yes" : "No"
                           },
                           {
                             "field_reference_id": "rm_dashboard",
                             "value": "https://jathara.thecircleapp.in/create-layout?user_id=#{user.id}"
                           },
                           {
                             "field_reference_id": "oe_dashboard",
                             "value": "#{Constants.get_admin_host}/admin/users/#{user.hashid}/oe_work_flow"
                           },
                           {
                             "field_reference_id": "boe_dashboard",
                             "value": "#{Constants.get_admin_host}/admin/users/#{user.hashid}/boe_work_flow"
                           }],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user.id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_add_lead_activity(user_id, poster_usage_count:, lead_type:)
    user = User.find_by(id: user_id)
    return false if user.blank?

    # user.phone += 4000000000 if user.phone.to_i < 6000000000

    payload = {
      "activity_reference_id": "Add_Lead",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "phone_number",
          "value": "{\"iso2_country_code\":\"IN\",\"dial_code\":\"+91\",\"phone_number\":\"#{user.phone}\"}"
        },
        {
          "field_reference_id": "free_poster_usage",
          "value": "#{poster_usage_count}"
        },
        {
          "field_reference_id": "lead_type",
          "value": "#{lead_type}"
        },
        {
          "field_reference_id": "SOP",
          "value": LeadTypeSop.get_sop_text(lead_type)
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user.id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_user_online_new_activity(user_id, release_build_number:)
    payload = {
      "activity_reference_id": "user_online_new",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "release_build_number",
          "value": "#{release_build_number}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_user_online_activity(user_id, premium_poster_usage:)
    payload = {
      "activity_reference_id": "user_online",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "Premium_poster_usage",
          "value": "#{premium_poster_usage}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_subscribed_no_layout_activity(user_id, lead_type:, release_build_number:, subscribed_package:, lead_score:)
    user = User.find_by(id: user_id)
    return false if user.blank?

    payload = {
      "activity_reference_id": "subscribed_no_layout",
      "activity_details": [{
                             "field_reference_id": "activity_datetime",
                             "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
                           }, {
                             "field_reference_id": "lead_type",
                             "value": "#{lead_type}"
                           }, {
                             "field_reference_id": "SOP",
                             "value": LeadTypeSop.get_sop_text(lead_type)
                           }, {
                             "field_reference_id": "release_build_number",
                             "value": "#{release_build_number}"
                           }, {
                             "field_reference_id": "subscribed_package",
                             "value": "#{subscribed_package}"
                           },
                           {
                             "field_reference_id": "lead_score",
                             "value": "#{lead_score}"
                           },
                           {
                             "field_reference_id": "rm_dashboard",
                             "value": "https://jathara.thecircleapp.in/create-layout?user_id=#{user.id}"
                           },
                           {
                             "field_reference_id": "oe_dashboard",
                             "value": "#{Constants.get_admin_host}/admin/users/#{user.hashid}/oe_work_flow"
                           },
                           {
                             "field_reference_id": "boe_dashboard",
                             "value": "#{Constants.get_admin_host}/admin/users/#{user.hashid}/boe_work_flow"
                           }],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user.id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_waiting_after_layout_activity(user_id, release_build_number:, package_to_pitch:)
    payload = {
      "activity_reference_id": "waiting_after_layout",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "release_build_number",
          "value": "#{release_build_number}"
        },
        {
          "field_reference_id": "package_to_pitch",
          "value": "#{package_to_pitch}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_layout_reviewed_activity(user_id, is_approved:, rejection_reason: '')
    payload = {
      "activity_reference_id": "layout_reviewed",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "review_status",
          "value": is_approved ? 'approved' : 'rejected'
        },
        {
          "field_reference_id": "reject_reason",
          "value": rejection_reason
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_layout_completed_activity(user_id, release_build_number:, package_to_pitch:)
    payload = {
      "activity_reference_id": "layout_completed",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "release_build_number",
          "value": "#{release_build_number}"
        },
        {
          "field_reference_id": "package_to_pitch",
          "value": "#{package_to_pitch}"
        },
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_assign_oe_activity(user_id)
    payload = {
      "activity_reference_id": "assign_oe",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_affiliated_party_activity(user_id, affiliated_party_id:)
    payload = {
      "activity_reference_id": "updated_aff_party_id",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "affiliated_party_id",
          "value": "#{affiliated_party_id}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_badge_setup_activity(user_id, user_hashid:)
    payload = {
      "activity_reference_id": "setup_badge",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "boe_dashboard_link",
          "value": "#{Constants.get_admin_host}/admin/users/#{user_hashid}/boe_work_flow"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_layout_setup_activity(user_id, user_hashid:)
    payload = {
      "activity_reference_id": "setup_layout",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "oe_dashboard",
          "value": "#{Constants.get_admin_host}/admin/users/#{user_hashid}/oe_work_flow"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_setup_autopay_mandate_activity(user_id, release_build_number:)
    payload = {
      "activity_reference_id": "Setup_mandate",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "release_build_number",
          "value": "#{release_build_number}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_incomplete_layout_badge_activity(user_id)
    payload = {
      "activity_reference_id": "incomplete_lb_data",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_pending_layout_approval_activity(user_id)
    payload = {
      "activity_reference_id": "pndg_layout_approval",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_trial_enabled_activity(user_id, trial_duration_in_days:, trial_end_date:, app_installed:, role_name:, role_id:)
    payload = {
      "activity_reference_id": "Trial_Enabled",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "Trial_Enabled_Status",
          "value": 'Yes'
        },
        {
          "field_reference_id": "App_Install_Status",
          "value": app_installed ? 'Installed' : 'Uninstalled'
        },
        {
          "field_reference_id": "Trial_Period",
          "value": "#{trial_duration_in_days}"
        },
        {
          "field_reference_id": "c_role_name",
          "value": "#{role_name}"
        },
        {
          "field_reference_id": "c_role_id",
          "value": "#{role_id}"
        },
        {
          "field_reference_id": "trial_end_date",
          "value": "#{trial_end_date}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_trial_low_usage_activity(user_id, premium_poster_shares:, trial_extended:, trial_period_left:)
    payload = {
      "activity_reference_id": "low_usage_lead",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "premium_poster_shares",
          "value": "#{premium_poster_shares}"
        },
        {
          "field_reference_id": "Trial_Extended",
          "value": "#{trial_extended ? 'Yes' : 'No'}"
        },
        {
          "field_reference_id": "Trial_period_left",
          "value": "#{trial_period_left}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_trial_used_activity(user_id)
    payload = {
      "activity_reference_id": "Trial_Used",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "Trial_used",
          "value": "Yes"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_qualified_pitch_activity(user_id, trial_remaining_days:, premium_poster_usage:)
    payload = {
      "activity_reference_id": "Qualified_Fl_Pitch",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "Trial_period_left",
          "value": "#{trial_remaining_days}"
        },
        {
          "field_reference_id": "Premium_poster_usage",
          "value": "#{premium_poster_usage}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_mandate_subscribed_activity(user_id, subscribed_package:)
    payload = {
      "activity_reference_id": "Subscribed",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "subscribed_package",
          "value": "#{subscribed_package}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_paid_activity(user_id, subscription_duration_in_months:, amount_paid:)
    payload = {
      "activity_reference_id": "Payment_activity",
      "activity_details": [
        {
          "field_reference_id": "activity_datetime",
          "value": Time.zone.now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
          "field_reference_id": "Subscription_Period",
          "value": "#{subscription_duration_in_months}"
        },
        {
          "field_reference_id": "Payment_Status",
          "value": "Yes"
        },
        {
          "field_reference_id": "Amount_Paid",
          "value": "#{amount_paid}"
        }
      ],
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.update_lead_activity(user_id, fields_hash: {})
    activity_details = []

    fields_hash.each do |key, value|
      activity_details << {
        "field_reference_id": key,
        "value": "#{value}"
      }
    end

    payload = {
      "activity_reference_id": "update_lead_new",
      "activity_details": activity_details,
      "contact_identification_type": "CUSTOM_ID",
      "custom_id_details": {
        "field_reference_id": "contact_custom_id",
        "value": "#{user_id}"
      }
    }

    Floww::SendApiRequest.perform_async(TRACK_ACTIVITY_URL, payload.to_json)
  end

  def self.send_api_request(url, payload)
    uri = URI.parse(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.request_uri)
    request.body = payload.to_json
    request["Content-Type"] = "application/json"
    request["x-api-key"] = "#{Rails.application.credentials[:floww_api_key]}"

    response = http.request(request)

    if response.code.to_i == 200
      response_body = JSON.parse(response.body)
      Rails.logger.info("Floww API response: #{response_body}:: payload: #{payload}")
      return response_body
    else
      Honeybadger.notify("Floww API error",
                         context: { body: response.body, code: response.code, payload: payload })
      Rails.logger.error("Floww API error: #{response.body}; code: #{response.code}:: payload: #{payload}")
      raise StandardError, "Floww API error: code: #{response.code}"
    end
  end
end
