# frozen_string_literal: true

class JuspayPaymentUtils
  # Reference https://juspay.io/in/docs/ec-headless/android/resources/error-codes
  SDK_FAILURE_ERROR_CODES = %w[JP_001 JP_002 JP_003 JP_004 JP_005 JP_007 JP_008 JP_009 JP_010 JP_011 JP_012 JP_014 JP_015 JP_016 JP_017 JP_018 JP_019 JP_020 JP_022 JP_851 JP_852]
  SDK_PROBABLE_SUCCESS_ERROR_CODES = %w[JP_006 JP_013 JP_023]

  # Status ID reference: https://juspay.io/in/docs/resources/docs/common-resources/transaction-status
  ORDER_STATUS_FAILURE_IDS = [22, 26, 27, 34]
  ORDER_STATUS_AUTO_REFUND_IDS = [36]
  ORDER_STATUS_SUCCESS_IDS = [21, 25]

  def self.generate_pg_id
    Nanoid.generate(alphabet: ('a'..'z').to_a.join + ('A'..'Z').to_a.join + ('0'..'9').to_a.join)
  end

  def self.create_customer(user)
    juspay_customer_id = nil

    payload = {
      object_reference_id: get_customer_id(user),
      mobile_number: user.phone,
      mobile_country_code: 91,
      first_name: "Praja User #{user.id}",
      email_address: user.email.present? ? user.email : "user-#{user.id}@praja.buzz",
      'options.get_client_auth_token': false,
    }
    response = post("/customers", payload)
    if response.present?
      juspay_customer_id = response.dig('id')
      UserMetadatum.create(user: user, key: Constants.juspay_customer_id_key, value: juspay_customer_id)
    end

    juspay_customer_id
  end

  def self.get_customer(user)
    get("/customers/#{get_customer_id(user)}", { 'options.get_client_auth_token': true })
  end

  def self.get_order_status(order_id)
    get("/orders/#{order_id}")
  end

  def self.get_customer_id(user)
    "praja_user_#{user.id}"
  end

  def self.host
    "https://api.juspay.in"
  end

  def self.post(endpoint, payload, should_raise_on_failure = true)
    url = URI(host + endpoint)
    response = send_request(url, payload, :post, should_raise_on_failure)
    JSON.parse(response.body)
  end

  def self.get(endpoint, query_params = {}, should_raise_on_failure = true)
    url = URI(host + endpoint)
    url.query = URI.encode_www_form(query_params) unless query_params.empty?
    response = send_request(url, nil, :get, should_raise_on_failure)
    JSON.parse(response.body)
  end

  def self.put(endpoint, payload, should_raise_on_failure = true)
    url = URI(host + endpoint)
    response = send_request(url, payload, :put, should_raise_on_failure)
    JSON.parse(response.body)
  end

  private

  def self.send_request(url, payload, method, should_raise_on_failure = true)
    Rails.logger.warn("[keep-it-forever] Juspay Request Started with method: #{method.to_s.capitalize} url: #{url} payload: #{payload} ")
    start_time = Time.zone.now

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = case method
              when :get
                Net::HTTP::Get.new(url)
              when :post
                Net::HTTP::Post.new(url)
              when :put
                Net::HTTP::Put.new(url)
              else
                raise "Invalid method: #{method}"
              end
    request["Content-Type"] = 'application/x-www-form-urlencoded'
    request["x-merchantid"] = 'praja'
    request["Authorization"] = "Basic #{Base64.encode64(Rails.application.credentials[:juspay_api_key] + ":").gsub("\n", "")}"
    request.body = URI.encode_www_form(payload) if payload.present?

    response = http.request(request)

    if response.code != '200'
      Honeybadger.context({ endpoint: url.path, payload: payload, response_code: response.code })
      Rails.logger.error("Juspay #{method.to_s.capitalize} Request failed with status code: #{response.code},
                          response: #{response}")

      if should_raise_on_failure
        raise "Juspay #{method.to_s.capitalize} Request failed with status code: #{response.code} body: #{response.body}"
      end
    end

    elapsed_time = Time.zone.now - start_time

    Rails.logger.warn("[keep-it-forever] Juspay Request Completed with method: #{method.to_s.capitalize} url: #{url} response: #{response.body} payload: #{payload} api_request_time: #{elapsed_time} seconds")
    response
  end
end
