class SubscriptionUtils
  def self.has_user_subscribed?(user_id, allow_grace_period: true)
    active_subscription_exists = UserPlan.where(user_id: user_id).where("end_date >= ?", Time.zone.now).exists?
    allow_grace_period ? (active_subscription_exists || is_user_in_grace_period?(user_id)) : active_subscription_exists
  end

  def self.is_user_in_grace_period?(user_id)
    grace_period_given_date = Metadatum.where(entity_type: "User", entity_id: user_id, key: Constants.grace_period_given_string).last&.value
    (grace_period_given_date.present? and (Time.zone.now < Time.zone.parse(grace_period_given_date).advance(months: Subscription::GRACE_PERIOD_IN_MONTHS)))
  end

  def self.get_user_subscribed_frame_ids(user_id)
    # UserProductSubscription
    #   .where(user_id: user_id, item_type: "Frame", active: true)
    #   .where("start_date <= ? AND end_date >= ?", Time.zone.now, Time.zone.now).pluck(:item_id)
    UserFrame.active.where(user_id: user_id).pluck(:frame_id)
  end

  def self.active_poster_premium_end_date(user_id)
    # UserProductSubscription
    #   .where(user_id: user_id, item_type: "Product", item_id: Constants.get_poster_product_id, active: true)
    #   .where("start_date <= ? AND end_date >= ?", Time.zone.now, Time.zone.now)
    #   .pluck(:start_date, :end_date).first
    UserPlan.where(user_id: user_id).where("end_date >= ?", Time.zone.now).pluck(:end_date).last
  end

  def self.last_poster_premium_end_date(user_id)
    UserPlan.where(user_id: user_id).pluck(:end_date).last
  end

  def self.get_subscription_status(user_id)
    return :subscribed if has_user_subscribed?(user_id)
  end

  #@deprecated
  def self.add_user_subscription(user_id, order_id, item_type, item_id, start_time, end_time, source)
    UserProductSubscription.create(
      user_id: user_id,
      item_type: item_type,
      item_id: item_id,
      order_id: order_id,
      start_date: start_time,
      end_date: end_time,
      source: source
    )
  end

  # @param [Order] order
  # Add subscriptions for all the products in the order
  # @deprecated
  def self.add_subscriptions_for_order(order)
    source = :orders
    user_subscription = get_last_poster_subscription(order.user_id)
    subscribing_user = order.user
    if user_subscription.present?
      # start_time is the end date of the last subscription if it is in the present, else it is the current time
      start_time = user_subscription.end_date.end_of_day < Time.zone.now.beginning_of_day ? Time.zone.now.beginning_of_day : user_subscription.end_date
    else
      start_time = Time.zone.now.beginning_of_day
    end

    remaining_trial_period_in_days = 0
    # update end_time to include the trial period if the user is in trial
    # if the user is in trial, the end_time is the remaining time of the trial + duration
    if subscribing_user.is_trial_user?
      trial_start_date, trial_duration = Metadatum.get_user_trial_start_date_and_duration(subscribing_user)
      trial_end_date = trial_start_date.advance(days: trial_duration - 1)
      remaining_trial_period_in_days = (trial_end_date - Time.zone.today).to_i + 1
    end

    order.order_items.each do |order_item|
      months = order_item.duration_in_months
      end_time = start_time.advance(months: months).end_of_day + remaining_trial_period_in_days.days
      add_user_subscription(order.user_id, order.id, order_item.item_type,
                            order_item.item_id, start_time, end_time, source)
    end

    # Add subscription for the referred user
    if order.referred_by.present?
      source = :referral
      referred_user_subscription = get_last_poster_subscription(order.referred_by)
      if referred_user_subscription.present?
        # start_time is the end date of the last subscription if it is in the present, else it is the current time
        start_time = referred_user_subscription.end_date < Time.zone.now ? Time.zone.now : referred_user_subscription.end_date
        end_time = start_time.advance(months: 1)
        add_user_subscription(order.referred_by, order.id, "Product", Constants.get_poster_product_id,
                              start_time, end_time, source)

        # Add subscription for the referred user's base package
        referred_user_base_package = get_last_ordered_poster_package(order.referred_by)
        if referred_user_base_package.present?
          last_package_bought_order = referred_user_base_package.order
          # fetch order items whose parent order id is not null, so that we can add the frames which are part of the base package
          last_package_bought_order.order_items.where.not(parent_order_item_id: nil).each do |order_item|
            add_user_subscription(order.referred_by, order.id, order_item.item_type,
                                  order_item.item_id, start_time, end_time, source)
          end
        end
      end
    end
  end

  # @deprecated
  def self.is_subscribed_to_frames?(user_id, frame_ids)
    subscribed_frame_ids = UserProductSubscription
                             .where(user_id: user_id, item_type: 'Frame', item_id: frame_ids)
                             .where("start_date <= ? AND end_date >= ?", Time.zone.now, Time.zone.now).pluck(:item_id)

    frame_ids == subscribed_frame_ids && frame_ids
  end

  # @deprecated
  def self.get_subscription_status_for_frames(user_id, frame_ids)
    return :subscribed if is_subscribed_to_frames?(user_id, frame_ids)

    # Check for open orders only as successful orders are not relevant here
    # successful orders would go to the subscribed state above
    # if they are out of subscribed state we need to create new orders
    order = Order.joins(:order_items).open.where(user_id: user_id, order_items: { item_type: "Frame", item_id: frame_ids }).last

    return :no_order_created if order.blank? # no banner, no call to action, no toast

    if order.pending?
      begin
        transaction = order.order_transactions.last
        CashfreePaymentUtils.get_transaction_status(transaction.transaction_id)
      rescue => exception
        Rails.logger.error(exception.message.to_s)
      end
      return :payment_processing # enable banner, disable call to action, show payment pending toast
    elsif order.last_transaction_failed?
      return :payment_failed # enable banner, enable call to action, show last transaction failed toast
    else
      return :order_created # enable banner, enable call to action, no toast
    end
  end

  # get the last subscription of the user for the poster product
  # @deprecated
  def self.get_last_poster_subscription(user_id)
    UserProductSubscription.where(user_id: user_id, item_type: "Product", item_id: Constants.get_poster_product_id,
                                  active: true).last
  end

  # get the last ordered poster package of the user
  # @deprecated
  def self.get_last_ordered_poster_package(user_id)
    UserProductSubscription.where(user_id: user_id, item_type: "Product", item_id: Constants.get_poster_product_id,
                                  active: true, source: :orders).last
  end

  # check whether user has ever subscribed to the product or not
  def self.has_user_ever_subscribed?(user_id)
    # UserProductSubscription
    #   .where(user_id: user_id, item_type: "Product", item_id: product_id, active: true)
    #   .exists?
    UserPlan.where(user_id: user_id).exists?
  end

  def self.subscribed_poster_package_multiple_times?(user_id:)
    success_subscription_charges = SubscriptionCharge.where(user_id: user_id, status: :success).last(3)
    non_auth_successful_charges = success_subscription_charges.select { |charge| charge.is_trial_charge? == false }
    return true if non_auth_successful_charges.count > 1
    false
  end

  def self.subscribed_poster_package_at_least_once?(user_id:)
    success_subscription_charges = SubscriptionCharge.where(user_id: user_id, status: :success).last(2)
    non_auth_successful_charges = success_subscription_charges.select { |charge| charge.is_trial_charge? == false }
    return true if non_auth_successful_charges.count > 0
    has_active_subscription = UserProductSubscription.where(user_id: user_id, item_type: "Product", item_id: Constants.get_poster_product_id,
                                    active: true).exists?
    return true if has_active_subscription
    false
  end

  # get the subscription status for mixpanel
  def self.get_subscription_status_for_mixpanel(user_id)
    user = User.find_by(id: user_id)
    last_user_plan = UserPlan.where(user_id:).last
    last_subscription_charge = SubscriptionCharge.where(user_id:).success_ever.last
    has_non_auth_successful_charge = subscribed_poster_package_at_least_once?(user_id:)
    is_user_in_grace_period = is_user_in_grace_period?(user_id)
    if last_user_plan.present?
      is_auth_charge = last_subscription_charge&.is_trial_charge? || false
      end_date = last_user_plan.end_date
      trial_end_date = Metadatum.get_trial_end_date_incld_extensions(user_id:)
      is_trial_expired_user = trial_end_date.present? && end_date < Time.zone.now
      return :in_trial_extension if user.in_subscription_extension? && is_auth_charge
      return :in_premium_extension if user.in_subscription_extension? && !is_auth_charge
      return :in_grace_period if is_user_in_grace_period
      return :subscribed if end_date > Time.zone.now && !is_auth_charge
      return :premium_expired if end_date < Time.zone.now && !is_auth_charge && !is_trial_expired_user &&
        has_non_auth_successful_charge
      return :autopay_setup_no_layout if end_date > Time.zone.now && is_auth_charge && !user.has_premium_layout?
      return :in_trial if is_auth_charge && end_date > Time.zone.now
      return :trial_expired if is_trial_expired_user
    end

    return :auto_pay_not_set_up if user.has_premium_layout?
    :no_order_created
  end

  # get the last subscription of the user for the poster product
  def self.get_subscription_duration_in_months_and_amount(user_id)
    user_plan = UserPlan.where(user_id: user_id).last
    return [0, 0] if user_plan.blank?
    plan = user_plan.plan
    # months, amount
    [plan.duration_in_months, user_plan.amount]
  end

  def self.get_duration_in_words(duration_in_months)
    if duration_in_months == 12
      'yearly'
    elsif duration_in_months == 6
      'half-yearly'
    else
      'monthly'
    end
  end
end
