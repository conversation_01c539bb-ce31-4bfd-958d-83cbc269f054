require 'uri'
require 'net/http'
require 'openssl'
require 'json'

class CashfreePaymentUtils
  def self.generate_payment_session_id(order)
    transaction = OrderTransaction.create(
      status: :CREATED,
      amount: order.payable_amount,
      gateway: :CASHFREE,
      order_id: order.id,
      transaction_id: self.generate_transaction_id
    )

    payload = {
      "order_amount": order.payable_amount,
      "order_currency": "INR",
      "order_id": "#{order.id}_#{transaction.transaction_id}",
      "customer_details": {
        "customer_id": "#{order.user.id}",
        "customer_phone": "#{order.user.phone}",
        "customer_name": "user #{order.user_id}",
        "customer_email": "#{order.user.email}"
      },
      "order_meta": {
        "return_url": "https://prajaapp.sng.link/D3x5b/oynu?_dl=prajaapp%3A%2F%2Fbuzz.praja.app%2Fpayments%2Fsuccess",
      },
      "order_tags": {
        "praja_order_id": "#{order.id}",
        "praja_transaction_id": "#{transaction.transaction_id}"
      }
    }

    response = cashfree_post('/orders', payload)
    transaction_data = response

    transaction.update(checkout_url: "session_id=#{transaction_data['payment_session_id']}",
                       gateway_transaction_id: transaction_data['cf_order_id'])

    return transaction_data['payment_session_id']
  end

  def self.update_transaction_status(params)
    data = params[:data]
    transaction_id = data['order']['order_tags']['praja_transaction_id']
    gateway_transaction_id = data['payment']['cf_payment_id']

    transaction = OrderTransaction.find_by(transaction_id: transaction_id)

    if transaction.order.successful?
      return
    end

    case params['type']
    when "PAYMENT_SUCCESS_WEBHOOK"
      transaction_status = "COMPLETED"
    when "PAYMENT_FAILED_WEBHOOK"
      transaction_status = "FAILED"
    when "PAYMENT_USER_DROPPED_WEBHOOK"
      transaction_status = "FAILED"
    else
      transaction_status = "UNKNOWN"
    end

    transaction.update(
      status: transaction_status,
      raw_pg_request: params,
      gateway_transaction_id: gateway_transaction_id
    )

    if transaction_status == 'COMPLETED'
      SubscriptionUtils.add_subscriptions_for_order(transaction.order)
      transaction.order.update(status: :successful)
      # previous_open_orders of user should be closed if there are any open orders
      transaction.order.user.orders.open.update(status: :closed)
    elsif transaction_status == 'FAILED'
      transaction.order.update(status: :last_transaction_failed)
    end
  end

  # @param [String] transaction
  # Get and update transaction status and other variables in the database
  def self.get_transaction_status(transaction_id)
    transaction = OrderTransaction.find_by(transaction_id: transaction_id)
    order = transaction.order

    response = cashfree_get("/orders/#{order.id}_#{transaction_id}")

    if response['order_status'] == 'PAID'
      transaction.update(status: 'COMPLETED')
      transaction.order.update(status: :successful)
      # previous_open_orders of user should be closed if there are any open orders
      transaction.order.user.orders.open.update(status: :closed)
    elsif response['order_status'] == 'TERMINATED' ||
      response['order_status'] == 'TERMINATION_REQUESTED' ||
      response['order_status'] == 'EXPIRED'
      transaction.update(status: 'FAILED')
      transaction.order.update(status: :last_transaction_failed)
    end
  end

  def self.get_subscription_charge_status(payment_id, subscription_id)
    cashfree_get("/subscriptions/#{subscription_id}/payments/#{payment_id}")
  end

  def self.cashfree_post_v1(endpoint, payload)
    url = URI(Rails.application.credentials[:cashfree_subscriptions_host] + endpoint)
    response = send_request(url, payload, :post)
    JSON.parse(response.body)
  end

  def self.cashfree_get_v1(endpoint, query_params = {})
    url = URI(Rails.application.credentials[:cashfree_subscriptions_host] + endpoint)
    url.query = URI.encode_www_form(query_params) unless query_params.empty?
    response = send_request(url, nil, :get)
    response.body
  end

  def self.cashfree_put_v1(endpoint, payload)
    url = URI(Rails.application.credentials[:cashfree_subscriptions_host] + endpoint)
    response = send_request(url, payload, :put)
    JSON.parse(response.body)
  end

  def self.cashfree_post(endpoint, payload)
    url = URI(Rails.application.credentials[:cashfree_host] + endpoint)
    response = send_request(url, payload, :post)
    JSON.parse(response.body)
  end

  def self.cashfree_get(endpoint, query_params = {})
    url = URI(Rails.application.credentials[:cashfree_host] + endpoint)
    url.query = URI.encode_www_form(query_params) unless query_params.empty?
    response = send_request(url, nil, :get)
    response.body
  end

  def self.calculate_signature(payload)
    # Order params by key
    ordered_payload = payload.sort.to_h

    # create a params string with key value pairs, like key1=value1key2=value2, and if key contains 'cf_'
    # then only include it in the string
    params_string = ordered_payload.map { |k, v| "#{k}#{v}" if k.to_s.include?('cf_') }.compact.join('')

    # create a sha256 hash of the params string
    hash_hmac = OpenSSL::HMAC.digest('SHA256',
                                     Rails.application.credentials[:cashfree_client_secret],
                                     params_string)

    # encode the hash_hmac in base64
    Base64.strict_encode64(hash_hmac)
  end

  private

  def self.send_request(url, payload, method)
    Rails.logger.warn("[keep-it-forever] Cashfree Request Started with method: #{method.to_s.capitalize} url: #{url} payload: #{payload} ")
    start_time = Time.zone.now

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = case method
              when :get
                Net::HTTP::Get.new(url)
              when :post
                Net::HTTP::Post.new(url)
              when :put
                Net::HTTP::Put.new(url)
              end
    request["Accept"] = 'application/json'
    request["Content-Type"] = 'application/json'
    request["x-client-id"] = Rails.application.credentials[:cashfree_client_id]
    request["x-client-secret"] = Rails.application.credentials[:cashfree_client_secret]
    request["x-api-version"] = "2023-08-01"
    request.body = payload.to_json if payload

    response = http.request(request)

    if response.code != '200'
      Honeybadger.context({ endpoint: url.path, payload: payload, response: response.body })
      Rails.logger.error("Cashfree #{method.to_s.capitalize} Request failed with status code: #{response.code},
                          response: #{response.body}")
      raise "Cashfree #{method.to_s.capitalize} Request failed with status code: #{response.code}"
    end

    elapsed_time = Time.zone.now - start_time

    Rails.logger.warn("[keep-it-forever] Cashfree Request Completed with method: #{method.to_s.capitalize} url: #{url} response: #{response.body} payload: #{payload} api_request_time: #{elapsed_time}")
    response

  end

  def self.generate_transaction_id
    SecureRandom.uuid.gsub('-', '')[0..20]
  end
end
