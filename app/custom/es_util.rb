class EsUtil
  HASHTAGS_INDEX_NAME = 'hashtags'.freeze
  SEARCH_ENTITIES_INDEX_NAME = 'search_entities_v2'.freeze
  RED_WORDS_INDEX_NAME = 'red_words_index'.freeze
  USER_INVITE_PHONES_INDEX_NAME_V2 = 'user_invite_phones_v2'.freeze
  NEW_POSTS_INDEX_NAME_V2 = 'new_posts_v2'.freeze

  def self.get_index_name(name)
    return name if Rails.env.production?

    [Rails.env, name].join('_')
  end

  def self.get_new_posts_index_v2
    get_index_name(NEW_POSTS_INDEX_NAME_V2)
  end

  def self.get_hashtags_index
    get_index_name(HASHTAGS_INDEX_NAME)
  end

  def self.get_search_entities_index
    get_index_name(SEARCH_ENTITIES_INDEX_NAME)
  end

  def self.get_red_word_index
    get_index_name(RED_WORDS_INDEX_NAME)
  end

  def self.get_user_invite_phones_index_v2
    get_index_name(USER_INVITE_PHONES_INDEX_NAME_V2)
  end
end
