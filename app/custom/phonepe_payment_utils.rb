# frozen_string_literal: true

class PhonepePaymentUtils
  ACCESS_TOKEN_REDIS_KEY = "phonepe_access_token"
  # Buffer time (in seconds) before token expiry to refresh the token
  # Set to 30 minutes (1800 seconds) before actual expiry
  TOKEN_EXPIRY_BUFFER = 1800

  # Gets the latest authentication token from Redis or fetches a new one if needed
  # @return [String, nil] The authentication token
  def self.get_access_token
    # Redis will automatically handle expiration, so if we get a token, it's valid
    cached_token = $redis.get(ACCESS_TOKEN_REDIS_KEY)

    # Return the cached token if it exists
    return cached_token if cached_token.present?

    # Token not found in cache or expired, fetch a new one
    response = fetch_access_token

    # Cache the token with expiry time
    if response.present? && response["access_token"].present? && response["expires_at"].present?
      access_token = response["access_token"]

      # Calculate expiry time (with buffer)
      expires_in = response["expires_at"].to_i - Time.zone.now.to_i - TOKEN_EXPIRY_BUFFER

      # Store only the access token with expiry
      if expires_in > 0
        $redis.setex(ACCESS_TOKEN_REDIS_KEY, expires_in, access_token)
      end

      return access_token
    end

    nil
  end

  # Fetches an authentication token from PhonePe OAuth endpoint
  # @return [Hash] The response containing the auth token
  def self.fetch_access_token
    endpoint = "/v1/oauth/token"

    # Create form data with credentials from Rails credentials
    form_data = {
      'client_id' => client_id,
      'client_version' => client_version,
      'client_secret' => client_secret,
      'grant_type' => "client_credentials"
    }

    # Use the post method with form-urlencoded content type
    post(endpoint, form_data, should_raise_on_failure: true, content_type: 'application/x-www-form-urlencoded')
  end

  # Makes a POST request to the PhonePe API
  # @param endpoint [String] The API endpoint
  # @param payload [Hash] The request payload
  # @param should_raise_on_failure [Boolean] Whether to raise an exception on failure
  # @param content_type [String] The content type for the request
  # @return [Hash] The parsed JSON response
  def self.post(endpoint, payload, should_raise_on_failure: true, content_type: 'application/json')
    url = URI(host + endpoint)
    response = send_request(url, payload, :post, should_raise_on_failure:, content_type:)
    JSON.parse(response.body)
  end

  # Makes a GET request to the PhonePe API
  # @param endpoint [String] The API endpoint
  # @param query_params [Hash] The query parameters
  # @param should_raise_on_failure [Boolean] Whether to raise an exception on failure
  # @return [Hash] The parsed JSON response
  def self.get(endpoint, query_params = {}, should_raise_on_failure: true)
    url = URI(host + endpoint)
    url.query = URI.encode_www_form(query_params) unless query_params.empty?
    response = send_request(url, nil, :get, should_raise_on_failure:)
    JSON.parse(response.body)
  end

  # Makes a PUT request to the PhonePe API
  # @param endpoint [String] The API endpoint
  # @param payload [Hash] The request payload
  # @param should_raise_on_failure [Boolean] Whether to raise an exception on failure
  # @return [Hash] The parsed JSON response
  def self.put(endpoint, payload, should_raise_on_failure: true)
    url = URI(host + endpoint)
    response = send_request(url, payload, :put, should_raise_on_failure:)
    JSON.parse(response.body)
  end

  private

  # Sends an HTTP request to the PhonePe API
  # @param url [URI] The request URL
  # @param payload [Hash] The request payload
  # @param method [Symbol] The HTTP method (:get, :post, :put)
  # @param should_raise_on_failure [Boolean] Whether to raise an exception on failure
  # @param content_type [String] The content type for the request
  # @return [Net::HTTPResponse] The HTTP response
  def self.send_request(url, payload, method, should_raise_on_failure: true, content_type: 'application/json')
    Rails.logger.info("PhonePe Request Started with method: #{method.to_s.capitalize} url: #{url} payload: #{payload}")
    start_time = Time.zone.now

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = case method
              when :get
                Net::HTTP::Get.new(url)
              when :post
                Net::HTTP::Post.new(url)
              when :put
                Net::HTTP::Put.new(url)
              else
                raise "Invalid method: #{method}"
              end

    # Set headers
    request["Content-Type"] = content_type
    request["Accept"] = 'application/json'

    # Set auth header if we have a token
    access_token = get_access_token if url.path != "/v1/oauth/token"
    request["Authorization"] = "Bearer #{access_token}" if access_token.present?

    # Set request body based on content type
    if payload.present?
      if content_type == 'application/x-www-form-urlencoded'
        request.body = URI.encode_www_form(payload)
      else
        request.body = payload.to_json
      end
    end

    response = http.request(request)

    if response.code.to_i != 200
      Honeybadger.context({ endpoint: url.path, payload: payload, response_code: response.code })
      error_message = "PhonePe #{method.to_s.capitalize} Request failed with status code: #{response.code}, response: #{response.body}"
      Rails.logger.error(error_message)

      if should_raise_on_failure
        raise error_message
      end
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.info("PhonePe Request Completed with method: #{method.to_s.capitalize} url: #{url} response: #{response.body} payload: #{payload} api_request_time: #{elapsed_time} seconds")

    response
  end

  # Returns the appropriate PhonePe API host URL based on the environment
  # @return [String] The PhonePe API host URL
  def self.host
    if Rails.env.production?
      "https://api.phonepe.com/apis/identity-manager"
    else
      "https://api-preprod.phonepe.com/apis/pg-sandbox"
    end
  end

  # Returns the client ID for PhonePe API based on the environment
  # @return [String] The client ID
  def self.client_id
    if Rails.env.production?
      Rails.application.credentials[:phonepe_client_id]
    else
      "PRAJAAPPONLINEUAT_250402"
    end
  end

  # Returns the client secret for PhonePe API based on the environment
  # @return [String] The client secret
  def self.client_secret
    if Rails.env.production?
      Rails.application.credentials[:phonepe_client_secret]
    else
      "MjA3NGM5YmQtM2Q4ZS00NDdkLWFhMTctYjVlM2NmYzU2MTFl"
    end
  end

  # Returns the client version for PhonePe API based on the environment
  # @return [String] The client version
  def self.client_version
    if Rails.env.production?
      Rails.application.credentials[:phonepe_client_version]
    else
      "1"
    end
  end

  # Returns the merchant ID for PhonePe API based on the environment
  # @return [String] The merchant ID
  def self.merchant_id
    if Rails.env.production?
      Rails.application.credentials[:phonepe_merchant_id]
    else
      "PRAJAAPPONLINEUAT"
    end
  end
end
