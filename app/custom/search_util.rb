class SearchUtil
  def self.search_query(value, logged_in_user, must_match_queries_to_be_added =[], must_not_match_queries_to_be_added=[])
    {
      "fields": %w[id type],
      "_source": false,
      "query": {
        "function_score": {
          "query": {
            "bool": {
              "should": [
                {
                  "multi_match": {
                    "query": value,
                    "fields": [
                      "name",
                      "name_en"
                    ],
                    "fuzziness": "AUTO"
                  }
                },
                {
                  "match": {
                    "phone": value
                  }
                },
                {
                  "match_phrase": {
                    "name": {
                      "query": value,
                      "boost": 2
                    }
                  }
                }
              ],
              "minimum_should_match": 1,
              "filter": {
                "bool": {
                  "must": [
                    {
                      "match": {
                        "active": true
                      }
                    }
                  ] + must_match_queries_to_be_added,
                  "must_not":[
                    {
                      "terms": {
                        "level": [
                          "mla_constituency",
                          "mp_constituency",
                          "private",
                          "legislative",
                          "executive",
                          "judiciary",
                          "private_group",
                          "profession"
                        ]
                      }
                    }
                  ] + must_not_match_queries_to_be_added
                }
              }
            }
          },
          "script_score": {
            "script": {
              "lang": "painless",
              "source": "" "
                  double score = 1;
                    if (params._source.village_id == params.villageId) {
                      score *= 36;
                    }
                    else if (params._source.mandal_id == params.mandalId) {
                      score *= 27;
                    }
                    else if (params._source.mla_constituency_id == params.mlaConstituencyId) {
                      score *= 24;
                    }
                    else if (params._source.mp_constituency_id == params.mpConstituencyId) {
                      score *= 18;
                    }
                    else if (params._source.district_id == params.districtId) {
                      score *= 18;
                    }
                    else if (params._source.state_id == params.stateId) {
                      score *= 3;
                    }
                    if (params._source.badge_grade_level != null && params._source.badge_grade_level != 1000) {
                    score = score/params._source.badge_grade_level;
                    }
                    else if(params._source.badge_grade_level == 1000){
                      score = score/20;
                    }
                    else {
                      if (!params.state_level_leader_ids.contains(params._source.id)) {
                        score = score/2;
                      }
                    }

                  return _score*score
                " "",
              "params": {
                "villageId": logged_in_user.village_id,
                "mandalId": logged_in_user.mandal_id,
                "mlaConstituencyId": logged_in_user.mla_constituency_id,
                "mpConstituencyId": logged_in_user.mp_constituency_id,
                "districtId": logged_in_user.district_id,
                "stateId": logged_in_user.state_id,
                "state_level_leader_ids": Circle.get_state_level_leader_ids
              }
            }
          },
          "boost_mode": "replace",
          "min_score": 0.000001
        }
      },
      "sort": {
        "_score": "desc",
        "followers_count": "desc"
      }
    }
  end
end
