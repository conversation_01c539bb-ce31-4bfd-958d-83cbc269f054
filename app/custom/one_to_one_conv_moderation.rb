class OneToOneConvModeration

  NUMBER_OF_BLOCKS_TO_CHECK_TO_BAN_NORMAL_USERS = 3
  NUMBER_OF_BLOCKS_TO_CHECK_TO_HIGH_FOLLOWING_USERS = 5
  NUMBER_OF_FOLLOWERS_TO_CHECK_FOR_BANNING_WITH_BLOCKS = 25
  USER_IDS_TO_SKIP_BLOCK_CHECK = [41]
  NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_BAN_CONV_CREATION = 5
  NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_BAN_CONV_CREATION = 10
  NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED = 10
  NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED = 25

  attr_reader :is_banned, :is_requested, :reason

  def initialize(sender_id:, is_sender_blocked_for_commenting:, sender_blocked_by_count:, sender_followers_count:, count_of_last_7_days_active_other_conversations_created:, count_of_last_24_hrs_active_other_conversations_created:, count_of_last_7_days_active_conversations_created:, count_of_last_24_hrs_active_conversations_created:)
    @sender_id = sender_id
    @is_sender_blocked_for_commenting = is_sender_blocked_for_commenting
    @sender_blocked_by_count = sender_blocked_by_count
    @sender_followers_count = sender_followers_count
    @count_of_last_7_days_active_other_conversations_created = count_of_last_7_days_active_other_conversations_created
    @count_of_last_24_hrs_active_other_conversations_created = count_of_last_24_hrs_active_other_conversations_created
    @count_of_last_7_days_active_conversations_created = count_of_last_7_days_active_conversations_created
    @count_of_last_24_hrs_active_conversations_created = count_of_last_24_hrs_active_conversations_created

    @is_banned = false
    @is_requested = false
    @reason = ""

    # moderation logic implementation
    if @count_of_last_7_days_active_other_conversations_created >= NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_BAN_CONV_CREATION
      @reason = DmUtil::BANNING_REASONS[:exceeded_max_other_conversations_with_in_7_days]
      @is_banned = true

    elsif @count_of_last_24_hrs_active_other_conversations_created >= NUMBER_OF_MAX_OTHER_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_BAN_CONV_CREATION
      @reason = DmUtil::BANNING_REASONS[:exceeded_max_other_conversations_with_in_24_hrs]
      @is_banned = true


      # checking user have less than 25 followers and blocked by 3 users
    elsif @sender_blocked_by_count >= NUMBER_OF_BLOCKS_TO_CHECK_TO_BAN_NORMAL_USERS && @sender_followers_count < NUMBER_OF_FOLLOWERS_TO_CHECK_FOR_BANNING_WITH_BLOCKS && !USER_IDS_TO_SKIP_BLOCK_CHECK.include?(@sender_id)
      @reason = DmUtil::BANNING_REASONS[:is_blocked_by_multiple_users]
      @is_banned = true


      # checking whether the number of blocks are 5 and having grater than 25 followers
    elsif @sender_blocked_by_count >= NUMBER_OF_BLOCKS_TO_CHECK_TO_HIGH_FOLLOWING_USERS && @sender_followers_count >= NUMBER_OF_FOLLOWERS_TO_CHECK_FOR_BANNING_WITH_BLOCKS && !USER_IDS_TO_SKIP_BLOCK_CHECK.include?(@sender_id)
      @reason = DmUtil::BANNING_REASONS[:is_blocked_by_multiple_users]
      @is_banned = true

    elsif @is_sender_blocked_for_commenting
      @reason = DmUtil::REQUESTING_REASONS[:is_comment_blocked_user]
      @is_requested = true

      # MOVING USER TO MOVE TO REQUESTED auth status.
    elsif @count_of_last_7_days_active_conversations_created >= NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_7_DAYS_TO_MOVE_TO_REQUESTED
      @reason = DmUtil::REQUESTING_REASONS[:exceeded_max_primary_conversations_with_in_7_days]
      @is_requested = true

    elsif @count_of_last_24_hrs_active_conversations_created >= NUMBER_OF_MAX_CONVERSATIONS_CREATED_BY_USER_WITHIN_24_HRS_TO_MOVE_TO_REQUESTED
      @reason = DmUtil::REQUESTING_REASONS[:exceeded_max_primary_conversations_with_in_24_hrs]
      @is_requested = true
    end

  end

  def banned?
    @is_banned
  end

  def requested?
    @is_requested
  end

  def reason
    @reason
  end

end
