# frozen_string_literal: true

class ZohoApi
  BASE_URL = "https://desk.zoho.com/api/v1"
  AUTH_BASE_URL = "https://accounts.zoho.com/oauth/v2"
  ACCESS_TOKEN_REDIS_KEY = "zoho_access_token"

  def self.create_contact(user)
    payload = {
      lastName: user.name,
      mobile: user.phone,
      city: user.village.name_en,
      state: user.state.name_en,
      cf: {
        praja_user_id: user.id,
        village_id: user.village_id,
        state_id: user.state_id,
        district_id: user.district_id,
        mandal_id: user.mandal_id,
      }
    }

    send_api_request('POST', 'contacts', payload)
  end

  # @!method send_api_request(method, url, payload)
  # This method is used to send API request to Zoho
  # @param method [String] HTTP method - POST, PUT, GET, PATCH, DELETE
  # @param url [String] API endpoint
  # @param payload [String] Request payload
  def self.send_api_request(method, url, payload)
    uri = URI.parse("#{BASE_URL}/#{url.gsub(%r{^/|/$}, '')}")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    zoho_access_token = get_latest_access_token
    raise "Zoho access token is blank" if zoho_access_token.blank?

    case method
    when "POST"
      request = Net::HTTP::Post.new(uri.request_uri)
    when "PUT"
      request = Net::HTTP::Put.new(uri.request_uri)
    when "GET"
      uri.query = URI.encode_www_form(payload)
      request = Net::HTTP::Get.new(uri.request_uri)
    when "PATCH"
      request = Net::HTTP::Patch.new(uri.request_uri)
    when "DELETE"
      request = Net::HTTP::Delete.new(uri.request_uri)
    else
      raise "Invalid method"
    end

    request["Authorization"] = "Zoho-oauthtoken #{zoho_access_token}"
    request["Content-Type"] = "application/json"

    request.body = payload.to_json if method != "GET"

    response = http.request(request)
    response_body = JSON.parse(response.body)

    if response.code == "200"
      response_body
    else
      Rails.logger.error("Zoho API request failed with response code: #{response.code} :: body: #{response_body} :: payload: #{payload}")
      raise "Zoho API request failed with response code: #{response.code}"
    end
  end

  def self.get_latest_access_token
    zoho_access_token = $redis.get(ACCESS_TOKEN_REDIS_KEY)
    if zoho_access_token.blank?
      zoho_access_token = generate_access_token
      if zoho_access_token.present?
        $redis.set(ACCESS_TOKEN_REDIS_KEY, zoho_access_token)
        $redis.expire(ACCESS_TOKEN_REDIS_KEY, 3500)
      end
    end
    zoho_access_token
  end

  def self.generate_access_token
    uri = URI.parse("#{AUTH_BASE_URL}/token")
    uri.query = URI.encode_www_form({
                                      client_id: Rails.application.credentials[:zoho_client_id],
                                      client_secret: Rails.application.credentials[:zoho_client_secret],
                                      redirect_uri: 'https://praja.app',
                                      grant_type: 'refresh_token',
                                      refresh_token: Rails.application.credentials[:zoho_refresh_token],
                                      scope: 'Desk.tickets.ALL,Desk.basic.CREATE,Desk.basic.READ,Desk.contacts.READ,Desk.contacts.WRITE,Desk.contacts.UPDATE,Desk.contacts.CREATE'
                                    })

    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.request_uri)

    response = http.request(request)
    response_body = JSON.parse(response.body)

    if response.code == "200"
      return response_body["access_token"]
    else
      Rails.logger.error("Zoho OAuth API request failed with response code: #{response.code} :: body: #{response_body}")
    end
  end
end
