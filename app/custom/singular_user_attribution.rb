class SingularUserAttribution
  include ActiveModel::Serializers::JSO<PERSON>

  attr_accessor :medium, :source, :campaign, :inviter, :invited_via, :creative_id, :installed_on

  def initialize(medium:, source:, campaign:, creative_id:, installed_on:, inviter: nil, invited_via: nil)
    @medium = medium
    @source = source
    @campaign = campaign
    @creative_id = creative_id
    @installed_on = installed_on
    @inviter = inviter
    @invited_via = invited_via
  end
end
