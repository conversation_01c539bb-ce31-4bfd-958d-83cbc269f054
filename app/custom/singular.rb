class Singular
  def self.shorten_link(long_link, cache_expires_in = 1.day)
    link = Rails.cache.fetch("shortened_link_#{Digest::SHA256.hexdigest(long_link)}", expires_in: cache_expires_in, skip_nil: true) do
      begin
        link = "https://s2s.singular.net/api/v1/s2s/shorten_link?a=#{Rails.application.credentials[:singular_sdk_api_key]}"
        shortener_url = URI.parse(link)

        resulting_body_parameters = {
          "long_link": long_link
        }
        res = Net::HTTP.post(shortener_url, resulting_body_parameters.to_json, "Content-Type" => "application/json")
        resp_body = JSON.parse(res.body)

        resp_body['short_link'] if res.code == "200" && resp_body['short_link'].present?
      end
    end

    link || long_link
  end
end
