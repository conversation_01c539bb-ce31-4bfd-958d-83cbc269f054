require 'base64'
require 'openssl'

class VerifySignatureUtil

  def self.verify(key_type, public_key_string, payload, signed_string, signature_algorithm)
    public_key = OpenSSL::PKey.const_get(key_type).new(Base64.decode64(public_key_string))
    signature  = Base64.decode64(signed_string)
    digest     = OpenSSL::Digest.new(signature_algorithm)
    public_key.verify(digest, signature, payload) # returns true or false
  end
end
