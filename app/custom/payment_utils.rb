require 'uri'
require 'net/http'
require 'openssl'
require 'json'

class PaymentUtils

  MERCHANT_ID = Rails.application.credentials[:phonepe_merchant_id]
  REDIRECT_URL = "%s/phone_pe/redirect"
  CALLBACK_URL = "%s/phone_pe/callback"
  SALT_KEYS = [
    {
      :index => '1',
      :key => Rails.application.credentials[:phonepe_api_key]
    }
  ]
  HOST = Rails.application.credentials[:phonepe_host]
  PAY_ENDPOINT = '/pg/v1/pay'
  TERMINAL_PAYMENT_STATES = %w[
    BAD_REQUEST
    AUTHORIZATION_FAILED
    PAYMENT_SUCCESS
    PAYMENT_ERROR
    TRANSACTION_NOT_FOUND
    PAYMENT_DECLINED
    TIMED_OUT
  ]

  def self.get_host_url(request)
    if Rails.env.production?
      return "https://#{request.host}"
    end
    if request.port == 80 || request.port == 443
      return "#{request.protocol}#{request.host}"
    else
      return "#{request.protocol}#{request.host}:#{request.port}"
    end
  end

  # @param [Order] order
  # @return {:url, :method, :transactionId}
  #
  # 1. Create a new transaction record for the order
  # 2. Make api call using the transaction id to phone-pe and get the redirect url
  # 3. Return the redirect url to the client
  def self.generate_checkout_url(order, host_url: 'https://api.thecircleapp.in')
    transaction = OrderTransaction.create(
      status: :CREATED,
      amount: order.payable_amount,
      gateway: :PHONEPE,
      order_id: order.id,
      transaction_id: self.generate_transaction_id
    )

    payload = {
      merchantId: MERCHANT_ID,
      merchantTransactionId: transaction.transaction_id,
      amount: (transaction.amount * 100).to_i,
      merchantUserId: transaction.order.user.id,
      redirectMode: 'POST',
      redirectUrl: REDIRECT_URL % host_url,
      callbackUrl: CALLBACK_URL % host_url,
      paymentInstrument: {
        type: 'PAY_PAGE'
      }
    }

    payload[:mobileNumber] = transaction.order.user.phone if transaction.order.user.phone.present?
    payload[:mobileNumber] = 9966611523 if transaction.order.user.internal?

    response = phonepe_post(PAY_ENDPOINT, payload)
    transaction_data = response['data']

    transaction.update(
      checkout_url: transaction_data['instrumentResponse']['redirectInfo']['url']
    )

    return {
      success: true,
      url: transaction_data['instrumentResponse']['redirectInfo']['url'],
      method: transaction_data['instrumentResponse']['redirectInfo']['method'],
      transactionId: transaction_data['merchantTransactionId'],
      share_text: transaction_data['instrumentResponse']['redirectInfo']['url'],
    }
  end

  # @param [String] transaction
  # Get and update transaction status and other variables in the database
  def self.get_transaction_status(transaction_id)
    response = phonepe_get("/pg/v1/status/#{MERCHANT_ID}/#{transaction_id}")
    update_transaction_status(response)
  end

  # @param [Hash] payload
  # Get transaction from the merchant transaction id provided in the payload
  # Check the status of the transaction on PG side
  # Update the transaction status and other variables in the database
  # If the transaction is successful, update the order status and create a subscription
  def self.update_transaction_status(payload_string)
    payload = JSON.parse(payload_string)

    code = payload['code']
    transaction_data = payload['data']
    transaction = OrderTransaction.find_by(transaction_id: transaction_data['merchantTransactionId'])

    if TERMINAL_PAYMENT_STATES.include?(transaction.status)
      return
    end

    gateway_transaction_id = transaction_data['transactionId']
    gateway_transaction_state = transaction_data['state']
    amount = transaction_data['amount']

    transaction.update(
      status: gateway_transaction_state,
      raw_pg_request: payload_string,
      gateway_transaction_id: gateway_transaction_id,
      amount: (amount / 100)
    )

    if transaction.order.successful?
      return
    end

    if code == 'PAYMENT_SUCCESS'
      if (amount / 100).to_i == (transaction.order.payable_amount).to_i
        transaction.order.update(:status => :successful)
        # previous_open_orders of user should be closed if there are any open orders
        transaction.order.user.orders.open.update(:status => :closed)
        SubscriptionUtils.add_subscriptions_for_order(transaction.order)
      end
    elsif code == 'PAYMENT_PENDING' || code == 'INTERNAL_SERVER_ERROR'
      order = transaction.order
      unless [:successful, :closed].include?(order.status.to_sym)
        order.update(status: :pending)
      end
    else
      order = transaction.order
      unless [:successful, :closed].include?(order.status.to_sym)
        order.update(status: :last_transaction_failed)
      end
    end
  end

  # @param [String] response
  # @param [String] x_verify
  # @return [Boolean]
  # Validates the callback payload from phone-pe
  def self.validate_payload(payload, x_verify)
    key_index = x_verify.split('###').last
    checksum = x_verify.split('###').first

    salt_key_hash = SALT_KEYS.select { |obj| obj[:index] == key_index }.first
    key = salt_key_hash[:key]

    sha256_hash = get_sha256_hash(payload + key)
    checksum == sha256_hash
  end

  private

  # @param [String] endpoint
  # @param [Hash] payload
  # @return [Hash]
  # Makes a post call to phone-pe api with the given payload and returns the response
  def self.phonepe_post(endpoint, payload)

    payload_str = payload.to_json.to_s

    url = URI(HOST + endpoint)
    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(url)
    request["Accept"] = 'application/json'
    request["Content-Type"] = 'application/json'
    request["X-Verify"] = generate_x_verify(endpoint, payload: payload_str)
    request.body = {
      "request": get_base64_encoded_string(payload_str)
    }.to_json

    response = http.request(request)

    if response.code != '200'
      Honeybadger.context({ endpoint: endpoint, payload: payload,
                            response: response.body })
      Rails.logger.error("PhonePe Post Request failed with status code: #{response.code},
                            response: #{response.body}")
      raise "PhonePe Post Request failed with status code: #{response.code}"
    end

    JSON.parse(response.body)
  end

  def self.phonepe_get(endpoint)
    url = URI(HOST + endpoint)

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = Net::HTTP::Get.new(url)
    request["Accept"] = 'application/json'
    request["Content-Type"] = 'application/json'
    request["X-VERIFY"] = generate_x_verify(endpoint)
    request["X-MERCHANT-ID"] = MERCHANT_ID

    response = http.request(request)

    if response.code != '200'
      print(response.body + "\n")
      raise 'Invalid response'
    end

    response.body
  end

  # @param [String] api_endpoint
  # @param [String] payload
  # @return [String]
  # Returns x_verify checksum for phone-pe api call
  def self.generate_x_verify(api_endpoint, payload: nil)
    salt_key_object = SALT_KEYS.sample
    index = salt_key_object[:index]
    key = salt_key_object[:key]
    payload_encoded = payload.nil? ? '' : get_base64_encoded_string(payload)
    get_sha256_hash(payload_encoded + api_endpoint + key) + '###' + index
  end

  # @param [String] string
  # @return [String]
  # Returns base64 encoded string without newlines
  def self.get_base64_encoded_string(string)
    Base64.strict_encode64(string)
  end

  # @param [String] string
  # @return [String]
  # Returns sha256 hash of the string
  def self.get_sha256_hash(string)
    sha256 = Digest::SHA2.new(256)
    sha256.hexdigest(string)
  end

  def self.generate_transaction_id
    SecureRandom.uuid.gsub('-', '')[0..20]
  end
end
