require 'open-uri'
require 'htmlcsstoimage'

class ElectionsUtil
  # TG_PARTIES = {
  #   "BRS" => {
  #     "color" => "#ed008c",
  #     'display_name' => 'BRS',
  #     'icon' => 'https://cdn.thecircleapp.in/production/photos/41/0e68af1304ee9ef75832de2d8fd0bfa1.png'
  #   },
  #   "CONG" => {
  #     "color" => "#0f823f",
  #     'display_name' => 'CONG+',
  #     'icon' => 'https://cdn.thecircleapp.in/production/photos/41/74ba8eb2c86bcbad32deb5f96842bbaa.png'
  #   },
  #   "BJP" => {
  #     "color" => "#f68121",
  #     'display_name' => 'BJP+',
  #     'icon' => 'https://cdn.thecircleapp.in/production/photos/41/5c0c341c7ad31372ef2fe97a0c03c343.png'
  #   },
  #   "AIMIM" => {
  #     "color" => "#0b6b4a",
  #     'display_name' => 'AIMIM',
  #     'icon' => 'https://cdn.thecircleapp.in/production/photos/41/05e08d25bc1b030d214bd6363d09e561.png'
  #   },
  #   "OTH" => {
  #     "color" => "#231f20",
  #     'display_name' => 'Others',
  #     "icon" => "https://cdn.thecircleapp.in/production/admin-media/10/30346fc17e3d91de34de51cf45133167.png",
  #   }
  # }
  #
  # def self.process_tg_elections_data_hash(data, win_data, admin_user_id)
  #   # sort data hash by value
  #   raise "Sum of seat count (lead + win) must be 119" if (data.values.sum + win_data.values.sum) != 119
  #
  #   # data = data.sort_by { |_k, v| [-v, _k] }.to_h
  #   updated_at = Time.zone.now.to_i
  #
  #   html_content = get_tg_election_results_html(data, win_data, updated_at)
  #   image_url = html_to_image(html_content, "tg-election-results/tg-elections-#{admin_user_id}-#{Time.zone.now.to_i}.png")
  #
  #   if image_url.present?
  #     $redis.set("tg_2023_election_data", {updated_at: updated_at, party_data: data, party_win_data: win_data}.to_json)
  #     $redis.set("tg_2023_election_toast_image_url", image_url)
  #   end
  # end
  #
  # def self.get_tg_election_results_html(data, win_data, updated_at)
  #   total_win_count = win_data.values.map(&:to_i).sum
  #   winner_declared = total_win_count >= 119
  #   winning_party = win_data.max_by { |_, value| value.to_i }[0]
  #
  #   data_hash = TG_PARTIES.map do |party, _|
  #     party_data = {}
  #     party_data['display_name'] = ElectionsUtil::TG_PARTIES[party]['display_name']
  #     party_data['color'] = ElectionsUtil::TG_PARTIES[party]['color']
  #     party_data['icon'] = ElectionsUtil::TG_PARTIES[party]['icon']
  #     party_data['seat_count'] = data[party] || 0
  #     party_data['win_count'] = win_data[party] || 0
  #     party_data['winner'] = winner_declared && winning_party == party
  #     party_data
  #   end
  #
  #   ActionController::Base.render(
  #     inline: File.read(Rails.root.join('app/views/tg_2023_election_results.html.erb')),
  #     locals: {
  #       updated_at_time: Time.zone.at(updated_at).strftime("%I:%M %p"),
  #       updated_at_date: Time.zone.at(updated_at).strftime("%d"),
  #       show_win_count: data_hash.any? { |obj| obj['win_count'].positive? },
  #       winner_declared: winner_declared,
  #       data_hash: data_hash.sort_by { |obj| [-obj['win_count'], obj['display_name']] },
  #     }
  #   )
  # end
  #
  # def self.get_constituency_winner_html(data)
  #   ActionController::Base.render(
  #     inline: File.read(Rails.root.join('app/views/constituency_winner.html.erb')),
  #     locals: {
  #       data: data
  #     }
  #   )
  # end
  #
  # def self.html_to_image(html_content, file_path)
  #   hcti_client = HTMLCSSToImage.new(
  #     user_id: Rails.application.credentials[:hcti_user_id],
  #     api_key: Rails.application.credentials[:hcti_api_key]
  #   )
  #   hcti_image = hcti_client.create_image(html_content, viewport_width: 1200, viewport_height: 700, ms_delay: 500)
  #
  #   image_url = nil
  #
  #   if hcti_image.present?
  #     Rails.logger.debug("image url - #{hcti_image.url}")
  #     image_file = URI.open(hcti_image.url)
  #     if image_file.present?
  #       image_url = upload_to_aws(image_file, file_path)
  #       image_file.close
  #     end
  #   end
  #
  #   image_url
  # end
  #
  # def self.get_constituency_winner_feed_toast(user)
  #   relation = CirclesRelation.where(first_circle_id: user.mla_constituency_id, relation: :MLA).first
  #   if relation.present?
  #     image_url = Metadatum.where(entity_type: 'Circle', entity_id: relation.second_circle_id, key: 'constituency_winner_image_url').last&.value
  #     if image_url.present?
  #       mla = relation.second_circle
  #       mla_party = CirclesRelation.where(first_circle_id: relation.second_circle_id, relation: "Leader2Party").first&.second_circle
  #       mla_party_name = mla_party&.short_name || "ఇండిపెండెంట్"
  #       short_link = Singular.shorten_link(
  #         "https://prajaapp.sng.link/A3x5b/vwtg?_dl=praja%3A%2F%2Fcircles/#{mla.id}&_ddl=praja%3A%2F%2Fcircles/#{mla.id}&paffid=#{user.id}"
  #       )
  #
  #       {
  #         "feed_type": "feed_toast",
  #         "feed_item_id": 'constituency_winner_2023_share',
  #         "header": "",
  #         "message": "",
  #         "image_url": Photo.static_replace_photo_url(
  #           image_url,
  #           "https://az-cdn.thecircleapp.in/fit-in/600x350/filters:quality(80)/"
  #         ),
  #         "image_width": 600,
  #         "image_height": 350,
  #         "header_font_size": 14.0,
  #         "message_font_size": 12.0,
  #         "is_removable": false,
  #         "is_shareable": true,
  #         "cta_text": "సర్కిల్ చూడండి",
  #         "cta_text_color": 0xffFFFFFF,
  #         "cta_url": "praja-app://buzz.praja.app/circles/#{mla.id}",
  #         "share_text": "*#{relation.first_circle.name}* నియోజకవర్గం నుండి *#{mla.name}* (#{mla_party_name}) "+
  #           "గారు MLA గా ఎంపికయ్యారు! మీ నియోజకవర్గం నుండి ఎంపికయిన MLA ఎవరో తెలుసుకోవడానికి తక్షణమే Praja App డౌన్లోడ్ చేసుకోండి!\n"+
  #           "👇👇👇\n"+
  #           "#{short_link}",
  #       }
  #     end
  #   end
  # end
  #
  # def self.get_tg_elections_feed_toast(user_id)
  #   tg_election_toast_image_url = $redis.get("tg_2023_election_toast_image_url")
  #   {
  #     "feed_type": "feed_toast",
  #     "feed_item_id": 'tg_elections_2023_share',
  #     "header": "",
  #     "message": "",
  #     "image_url": Photo.static_replace_photo_url(
  #       tg_election_toast_image_url,
  #       "https://l-cdn.praja.buzz/fit-in/1000x800/filters:quality(80)/"
  #     ),
  #     "image_width": 600,
  #     "image_height": 350,
  #     "header_font_size": 14.0,
  #     "message_font_size": 12.0,
  #     "is_removable": false,
  #     "is_shareable": true,
  #     "share_text": "తెలంగాణ 2023 ఎన్నికల ఫలితాలను *LIVE* లో తెలుసుకోవడానికి, తక్షణమే Praja App డౌన్లోడ్ చేసుకోండి!\n"+
  #       "👇👇👇\n"+
  #       "https://prajaapp.sng.link/A3x5b/urwc?paffid=#{user_id}",
  #   } if tg_election_toast_image_url.present?
  # end
  #
  # private
  # def self.upload_to_aws(data, file_path)
  #   resource = Aws::S3::Resource.new(
  #     region: 'ap-south-1',
  #     credentials: Aws::Credentials.new(
  #       Rails.application.credentials[:aws_access_key_id],
  #       Rails.application.credentials[:aws_secret_access_key]
  #     ),
  #   )
  #
  #   s3_object_path = Rails.env + "/" + file_path
  #
  #   begin
  #     resource.bucket(Rails.application.credentials[:aws_s3_bucket_name]).object(s3_object_path).put(body: data)
  #     return 'https://cdn.thecircleapp.in/' + s3_object_path
  #   rescue => exception
  #     Rails.logger.error("Error uploading to AWS - #{exception}")
  #     return nil
  #   end
  # end
end
